using Harmoni360.Domain.Common;
using Harmoni360.Domain.Enums;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;
using System.Text.Json;

namespace Harmoni360.Domain.Entities;

public class WorkflowExecutionLog : BaseEntity, IAuditableEntity
{

    [Required]
    public int WorkflowExecutionId { get; set; }

    [Required]
    [MaxLength(100)]
    public string ActivityId { get; set; } = string.Empty;

    [Required]
    [MaxLength(500)]
    public string ActivityName { get; set; } = string.Empty;

    [Required]
    [MaxLength(100)]
    public string ActivityType { get; set; } = string.Empty;

    [Required]
    public WorkflowLogEventType EventType { get; set; }

    [Required]
    public DateTime Timestamp { get; set; }

    [MaxLength(2000)]
    public string? Message { get; set; }

    public string? ActivityInput { get; set; }

    public string? ActivityOutput { get; set; }

    public string? Exception { get; set; }

    [MaxLength(500)]
    public string? Source { get; set; }

    public int? Sequence { get; set; }

    [MaxLength(100)]
    public string? ExecutedBy { get; set; }

    public TimeSpan? Duration { get; set; }

    [MaxLength(500)]
    public string? PayloadType { get; set; }

    public string? Payload { get; set; }

    public string? Properties { get; set; }

    // Audit properties
    public DateTime CreatedAt { get; private set; }
    public string CreatedBy { get; private set; } = string.Empty;
    public DateTime? LastModifiedAt { get; private set; }
    public string? LastModifiedBy { get; private set; }

    // Navigation properties
    [ForeignKey(nameof(WorkflowExecutionId))]
    public virtual WorkflowExecution WorkflowExecution { get; set; } = null!;

    // Helper methods
    public T? GetActivityInputData<T>() where T : class
    {
        if (string.IsNullOrEmpty(ActivityInput))
            return null;

        try
        {
            return JsonSerializer.Deserialize<T>(ActivityInput);
        }
        catch
        {
            return null;
        }
    }

    public void SetActivityInputData<T>(T? data) where T : class
    {
        ActivityInput = data != null ? JsonSerializer.Serialize(data) : null;
    }

    public T? GetActivityOutputData<T>() where T : class
    {
        if (string.IsNullOrEmpty(ActivityOutput))
            return null;

        try
        {
            return JsonSerializer.Deserialize<T>(ActivityOutput);
        }
        catch
        {
            return null;
        }
    }

    public void SetActivityOutputData<T>(T? data) where T : class
    {
        ActivityOutput = data != null ? JsonSerializer.Serialize(data) : null;
    }

    public Dictionary<string, object>? GetProperties()
    {
        if (string.IsNullOrEmpty(Properties))
            return null;

        try
        {
            return JsonSerializer.Deserialize<Dictionary<string, object>>(Properties);
        }
        catch
        {
            return null;
        }
    }

    public void SetProperties(Dictionary<string, object>? properties)
    {
        Properties = properties != null ? JsonSerializer.Serialize(properties) : null;
    }
}