using Harmoni360.Application.Common.Interfaces;
using Harmoni360.Domain.Entities;
using Harmoni360.Domain.Enums;
using Harmoni360.Domain.Interfaces;
using Microsoft.Extensions.Logging;

namespace Harmoni360.Infrastructure.Services;

/// <summary>
/// Service for handling approval workflows and authorization
/// </summary>
public class ApprovalService : IApprovalService
{
    private readonly IUserRepository _userRepository;
    private readonly ILogger<ApprovalService> _logger;
    
    public ApprovalService(
        IUserRepository userRepository,
        ILogger<ApprovalService> logger)
    {
        _userRepository = userRepository;
        _logger = logger;
    }
    
    public async Task<bool> CanApproveStatusTransitionAsync(
        RoleType userRole, 
        IncidentStatus fromStatus, 
        IncidentStatus toStatus)
    {
        // Define approval rules based on status transitions
        var transitionKey = $"{fromStatus}_{toStatus}";
        
        var approvalRules = new Dictionary<string, RoleType[]>
        {
            // Gate 1: Approve to start investigation
            { $"{IncidentStatus.Reported}_{IncidentStatus.UnderInvestigation}", 
                new[] { RoleType.HSEManager, RoleType.IncidentManager, RoleType.SuperAdmin, RoleType.Developer } },
            
            // Gate 2: Approve control measures  
            { $"{IncidentStatus.UnderInvestigation}_{IncidentStatus.InProgress}", 
                new[] { RoleType.HSEManager, RoleType.SafetyOfficer, RoleType.SuperAdmin, RoleType.Developer } },
            
            // Gate 3: Verify completion
            { $"{IncidentStatus.InProgress}_{IncidentStatus.PendingReview}", 
                new[] { RoleType.HSEManager, RoleType.SafetyOfficer, RoleType.IncidentManager, RoleType.SuperAdmin, RoleType.Developer } },
            
            // Gate 4: Final approval
            { $"{IncidentStatus.PendingReview}_{IncidentStatus.Approved}", 
                new[] { RoleType.HSEManager, RoleType.DepartmentHead, RoleType.SuperAdmin, RoleType.Developer } },
            
            // Closure approval
            { $"{IncidentStatus.Approved}_{IncidentStatus.Closed}", 
                new[] { RoleType.HSEManager, RoleType.DepartmentHead, RoleType.SuperAdmin, RoleType.Developer } }
        };
        
        if (approvalRules.TryGetValue(transitionKey, out var allowedRoles))
        {
            return allowedRoles.Contains(userRole);
        }
        
        // Default: Only SuperAdmin and Developer can approve undefined transitions
        return userRole == RoleType.SuperAdmin || userRole == RoleType.Developer;
    }
    
    public async Task<IEnumerable<RoleType>> GetRequiredRolesForTransitionAsync(
        IncidentStatus fromStatus, 
        IncidentStatus toStatus)
    {
        var transitionKey = $"{fromStatus}_{toStatus}";
        
        var approvalRules = new Dictionary<string, RoleType[]>
        {
            { $"{IncidentStatus.Reported}_{IncidentStatus.UnderInvestigation}", 
                new[] { RoleType.HSEManager, RoleType.IncidentManager } },
            { $"{IncidentStatus.UnderInvestigation}_{IncidentStatus.InProgress}", 
                new[] { RoleType.HSEManager, RoleType.SafetyOfficer } },
            { $"{IncidentStatus.InProgress}_{IncidentStatus.PendingReview}", 
                new[] { RoleType.HSEManager, RoleType.SafetyOfficer, RoleType.IncidentManager } },
            { $"{IncidentStatus.PendingReview}_{IncidentStatus.Approved}", 
                new[] { RoleType.HSEManager, RoleType.DepartmentHead } },
            { $"{IncidentStatus.Approved}_{IncidentStatus.Closed}", 
                new[] { RoleType.HSEManager, RoleType.DepartmentHead } }
        };
        
        if (approvalRules.TryGetValue(transitionKey, out var roles))
        {
            return roles;
        }
        
        return new[] { RoleType.SuperAdmin };
    }
    
    public async Task RecordApprovalAsync(
        int incidentId,
        string approverUserId,
        RoleType approverRole,
        IncidentStatus fromStatus,
        IncidentStatus toStatus,
        bool isApproved,
        string? comments = null)
    {
        // TODO: Implement approval history tracking
        // This would typically save to an ApprovalHistory table
        _logger.LogInformation(
            "Approval recorded for incident {IncidentId}: {FromStatus} -> {ToStatus} by {ApproverUserId} ({ApproverRole}). Approved: {IsApproved}",
            incidentId, fromStatus, toStatus, approverUserId, approverRole, isApproved);
    }
    
    public async Task<bool> IsUserAuthorizedAsync(string userId, params RoleType[] requiredRoles)
    {
        if (!int.TryParse(userId, out var userIdInt))
        {
            return false;
        }
        
        var user = await _userRepository.GetByIdWithRolesAsync(userIdInt);
        if (user == null)
        {
            return false;
        }
        
        // Check if user has any of the required roles
        var userRoleTypes = user.UserRoles
            .Where(ur => ur.Role != null)
            .Select(ur => ur.Role.RoleType)
            .ToList();
        
        // SuperAdmin and Developer always have access
        if (userRoleTypes.Contains(RoleType.SuperAdmin) || userRoleTypes.Contains(RoleType.Developer))
        {
            return true;
        }
        
        return requiredRoles.Any(requiredRole => userRoleTypes.Contains(requiredRole));
    }
    
    public async Task<RoleType?> GetUserHighestRoleAsync(string userId)
    {
        if (!int.TryParse(userId, out var userIdInt))
        {
            return null;
        }
        
        var user = await _userRepository.GetByIdWithRolesAsync(userIdInt);
        if (user == null)
        {
            return null;
        }
        
        var userRoleTypes = user.UserRoles
            .Where(ur => ur.Role != null)
            .Select(ur => ur.Role.RoleType)
            .OrderBy(rt => (int)rt) // Lower enum values = higher roles
            .ToList();
        
        return userRoleTypes.FirstOrDefault();
    }
}