define("vs/language/json/jsonMode",["require","require"],r=>{"use strict";var F,e,N,W=Object.create,o=Object.defineProperty,V=Object.getOwnPropertyDescriptor,U=Object.getOwnPropertyNames,O=Object.getPrototypeOf,H=Object.prototype.hasOwnProperty,K=(t=function(e){if(typeof r<"u")return r.apply(this,arguments);throw Error('Dynamic require of "'+e+'" is not supported')},typeof r<"u"?r:typeof Proxy<"u"?new Proxy(t,{get:(e,t)=>(typeof r<"u"?r:e)[t]}):t),n=(t,r,n,i)=>{if(r&&"object"==typeof r||"function"==typeof r)for(let e of U(r))H.call(t,e)||e===n||o(t,e,{get:()=>r[e],enumerable:!(i=V(r,e))||i.enumerable});return t},q=(e,t,r)=>(r=null!=e?W(O(e)):{},n(!t&&e&&e.__esModule?r:o(r,"default",{value:e,enumerable:!0}),e)),t=(F=(e,t)=>{var r=q(K("vs/editor/editor.api"));t.exports=r},()=>(e||F((e={exports:{}}).exports,e),e.exports)),X={},$=X,z={CompletionAdapter:()=>Ae,DefinitionAdapter:()=>Me,DiagnosticsAdapter:()=>Ie,DocumentColorAdapter:()=>Oe,DocumentFormattingEditProvider:()=>We,DocumentHighlightAdapter:()=>Re,DocumentLinkAdapter:()=>Ne,DocumentRangeFormattingEditProvider:()=>Ve,DocumentSymbolAdapter:()=>Le,FoldingRangeAdapter:()=>He,HoverAdapter:()=>Ee,ReferenceAdapter:()=>je,RenameAdapter:()=>De,SelectionRangeAdapter:()=>Ke,WorkerManager:()=>Se,fromPosition:()=>E,fromRange:()=>xe,getWorker:()=>function(){return new Promise((e,t)=>{if(!j)return t("JSON not registered!");e(j)})},setupMode:()=>function(n){let e=[],i=[],t=new Se(n);function r(){var r,{languageId:e,modeConfiguration:t}=n;at(i),t.documentFormattingEdits&&i.push(A.languages.registerDocumentFormattingEditProvider(e,new We(j))),t.documentRangeFormattingEdits&&i.push(A.languages.registerDocumentRangeFormattingEditProvider(e,new Ve(j))),t.completionItems&&i.push(A.languages.registerCompletionItemProvider(e,new Ae(j,[" ",":",'"']))),t.hovers&&i.push(A.languages.registerHoverProvider(e,new Ee(j))),t.documentSymbols&&i.push(A.languages.registerDocumentSymbolProvider(e,new Le(j))),t.tokens&&i.push(A.languages.setTokensProvider(e,(r=!0,{getInitialState:()=>new nt(null,null,!1,null),tokenize:(e,t)=>{{var[c,l,d,g=0]=[r,e,t];let n=0,i=!1;switch(d.scanError){case 2:l='"'+l,n=1;break;case 1:l="/*"+l,n=2}let o=Xe(l),a=d.lastWasColon,s=d.parents,u={tokens:[],endState:d.clone()};for(;;){let e=g+o.getPosition(),t="",r=o.scan();if(17===r)break;if(e===g+o.getPosition())throw new Error("Scanner did not advance, next 3 characters are: "+l.substr(o.getPosition(),3));switch(i&&(e-=n),i=0<n,r){case 1:s=D.push(s,0),t=$e,a=!1;break;case 2:s=D.pop(s),t=$e,a=!1;break;case 3:s=D.push(s,1),t=ze,a=!1;break;case 4:s=D.pop(s),t=ze,a=!1;break;case 6:t=Be,a=!0;break;case 5:t=Je,a=!1;break;case 8:case 9:t=Ge,a=!1;break;case 7:t=Qe,a=!1;break;case 10:var f=1===(s?s.type:0);t=a||f?Ye:et,a=!1;break;case 11:t=Ze,a=!1}if(c)switch(r){case 12:t=rt;break;case 13:t=tt}u.endState=new nt(d.getStateData(),o.getTokenError(),a,s),u.tokens.push({startIndex:e,scopes:t})}return u}}}))),t.colors&&i.push(A.languages.registerColorProvider(e,new Oe(j))),t.foldingRanges&&i.push(A.languages.registerFoldingRangeProvider(e,new He(j))),t.diagnostics&&i.push(new it(e,j,n)),t.selectionRanges&&i.push(A.languages.registerSelectionRangeProvider(e,new Ke(j)))}e.push(t),j=(...e)=>t.getLanguageServiceWorker(...e),r(),e.push(A.languages.setLanguageConfiguration(n.languageId,st));let o=n.modeConfiguration;return n.onDidChange(e=>{e.modeConfiguration!==o&&(o=e.modeConfiguration,r())}),e.push(ot(i)),ot(e)},toRange:()=>T,toTextEdit:()=>R};for(N in z)o($,N,{get:z[N],enumerable:!0});var B,i,a,s,u,c,l,J,d,G,g,Q,Y,Z,ee,te,re,f,h,p,m,ne,v,ie,oe,ae,b,se,ue,ce,le,k,w,de,C,ge,fe,he,_,pe,me,y,ve,S,be,ke,we,Ce,_e,ye,I,A={},Se=(v=A,t=q(t()),n(v,t,"default"),w&&n(w,t,"default"),class{constructor(e){this._defaults=e,this._worker=null,this._client=null,this._idleCheckInterval=window.setInterval(()=>this._checkIfIdle(),3e4),this._lastUsedTime=0,this._configChangeListener=this._defaults.onDidChange(()=>this._stopWorker())}_stopWorker(){this._worker&&(this._worker.dispose(),this._worker=null),this._client=null}dispose(){clearInterval(this._idleCheckInterval),this._configChangeListener.dispose(),this._stopWorker()}_checkIfIdle(){this._worker&&12e4<Date.now()-this._lastUsedTime&&this._stopWorker()}_getClient(){return this._lastUsedTime=Date.now(),this._client||(this._worker=A.editor.createWebWorker({moduleId:"vs/language/json/jsonWorker",label:this._defaults.languageId,createData:{languageSettings:this._defaults.diagnosticsOptions,languageId:this._defaults.languageId,enableSchemaRequest:this._defaults.diagnosticsOptions.enableSchemaRequest}}),this._client=this._worker.getProxy()),this._client}getLanguageServiceWorker(...t){let r;return this._getClient().then(e=>{r=e}).then(e=>{if(this._worker)return this._worker.withSyncedResources(t)}).then(e=>r)}});(L||{}).is=function(e){return"string"==typeof e},(B||(B={})).is=function(e){return"string"==typeof e},(a=i=i||{}).MIN_VALUE=0,a.MAX_VALUE=2147483647,a.is=function(e){return"number"==typeof e&&a.MIN_VALUE<=e&&e<=a.MAX_VALUE},(v=s=s||{}).create=function(e,t){return{line:e=e===Number.MAX_VALUE?i.MAX_VALUE:e,character:t=t===Number.MAX_VALUE?i.MAX_VALUE:t}},v.is=function(e){return I.objectLiteral(e)&&I.uinteger(e.line)&&I.uinteger(e.character)},(w=u=u||{}).create=function(e,t,r,n){if(I.uinteger(e)&&I.uinteger(t)&&I.uinteger(r)&&I.uinteger(n))return{start:s.create(e,t),end:s.create(r,n)};if(s.is(e)&&s.is(t))return{start:e,end:t};throw new Error(`Range#create called with invalid arguments[${e}, ${t}, ${r}, ${n}]`)},w.is=function(e){return I.objectLiteral(e)&&s.is(e.start)&&s.is(e.end)},(t=c=c||{}).create=function(e,t){return{uri:e,range:t}},t.is=function(e){return I.objectLiteral(e)&&u.is(e.range)&&(I.string(e.uri)||I.undefined(e.uri))},(L=S=S||{}).create=function(e,t,r,n){return{red:e,green:t,blue:r,alpha:n}},L.is=function(e){return I.objectLiteral(e)&&I.numberRange(e.red,0,1)&&I.numberRange(e.green,0,1)&&I.numberRange(e.blue,0,1)&&I.numberRange(e.alpha,0,1)},(v=l=l||{}).Comment="comment",v.Imports="imports",v.Region="region",(w=J=J||{}).create=function(e,t){return{location:e,message:t}},w.is=function(e){return I.defined(e)&&c.is(e.location)&&I.string(e.message)},(t=d=d||{}).Error=1,t.Warning=2,t.Information=3,t.Hint=4,(_||{}).is=function(e){return I.objectLiteral(e)&&I.string(e.href)},(S=y=y||{}).create=function(e,t,r,n,i,o){let a={range:e,message:t};return I.defined(r)&&(a.severity=r),I.defined(n)&&(a.code=n),I.defined(i)&&(a.source=i),I.defined(o)&&(a.relatedInformation=o),a},S.is=function(e){var t;return I.defined(e)&&u.is(e.range)&&I.string(e.message)&&(I.number(e.severity)||I.undefined(e.severity))&&(I.integer(e.code)||I.string(e.code)||I.undefined(e.code))&&(I.undefined(e.codeDescription)||I.string(null==(t=e.codeDescription)?void 0:t.href))&&(I.string(e.source)||I.undefined(e.source))&&(I.undefined(e.relatedInformation)||I.typedArray(e.relatedInformation,J.is))},(L=G=G||{}).create=function(e,t,...r){let n={title:e,command:t};return I.defined(r)&&0<r.length&&(n.arguments=r),n},L.is=function(e){return I.defined(e)&&I.string(e.title)&&I.string(e.command)},(v=m=m||{}).replace=function(e,t){return{range:e,newText:t}},v.insert=function(e,t){return{range:{start:e,end:e},newText:t}},v.del=function(e){return{range:e,newText:""}},v.is=function(e){return I.objectLiteral(e)&&I.string(e.newText)&&u.is(e.range)},(w=se=se||{}).create=function(e,t,r){let n={label:e};return void 0!==t&&(n.needsConfirmation=t),void 0!==r&&(n.description=r),n},w.is=function(e){return I.objectLiteral(e)&&I.string(e.label)&&(I.boolean(e.needsConfirmation)||void 0===e.needsConfirmation)&&(I.string(e.description)||void 0===e.description)},(g||(g={})).is=function(e){return I.string(e)},(t=Q=Q||{}).create=function(e,t){return{textDocument:e,edits:t}},t.is=function(e){return I.defined(e)&&te.is(e.textDocument)&&Array.isArray(e.edits)},(_=Y=Y||{}).create=function(e,t,r){let n={kind:"create",uri:e};return void 0===t||void 0===t.overwrite&&void 0===t.ignoreIfExists||(n.options=t),void 0!==r&&(n.annotationId=r),n},_.is=function(e){return e&&"create"===e.kind&&I.string(e.uri)&&(void 0===e.options||(void 0===e.options.overwrite||I.boolean(e.options.overwrite))&&(void 0===e.options.ignoreIfExists||I.boolean(e.options.ignoreIfExists)))&&(void 0===e.annotationId||g.is(e.annotationId))},(y=Z=Z||{}).create=function(e,t,r,n){let i={kind:"rename",oldUri:e,newUri:t};return void 0===r||void 0===r.overwrite&&void 0===r.ignoreIfExists||(i.options=r),void 0!==n&&(i.annotationId=n),i},y.is=function(e){return e&&"rename"===e.kind&&I.string(e.oldUri)&&I.string(e.newUri)&&(void 0===e.options||(void 0===e.options.overwrite||I.boolean(e.options.overwrite))&&(void 0===e.options.ignoreIfExists||I.boolean(e.options.ignoreIfExists)))&&(void 0===e.annotationId||g.is(e.annotationId))},(S=ee=ee||{}).create=function(e,t,r){let n={kind:"delete",uri:e};return void 0===t||void 0===t.recursive&&void 0===t.ignoreIfNotExists||(n.options=t),void 0!==r&&(n.annotationId=r),n},S.is=function(e){return e&&"delete"===e.kind&&I.string(e.uri)&&(void 0===e.options||(void 0===e.options.recursive||I.boolean(e.options.recursive))&&(void 0===e.options.ignoreIfNotExists||I.boolean(e.options.ignoreIfNotExists)))&&(void 0===e.annotationId||g.is(e.annotationId))},(x||(x={})).is=function(e){let t=e;return t&&(void 0!==t.changes||void 0!==t.documentChanges)&&(void 0===t.documentChanges||t.documentChanges.every(e=>I.string(e.kind)?Y.is(e)||Z.is(e)||ee.is(e):Q.is(e)))},(L=te=te||{}).create=function(e,t){return{uri:e,version:t}},L.is=function(e){return I.defined(e)&&I.string(e.uri)&&(null===e.version||I.integer(e.version))},(f=re=re||{}).PlainText="plaintext",f.Markdown="markdown",f.is=function(e){return e===f.PlainText||e===f.Markdown},(h||(h={})).is=function(e){var t=e;return I.objectLiteral(e)&&re.is(t.kind)&&I.string(t.value)},(m=p=p||{}).Text=1,m.Method=2,m.Function=3,m.Constructor=4,m.Field=5,m.Variable=6,m.Class=7,m.Interface=8,m.Module=9,m.Property=10,m.Unit=11,m.Value=12,m.Enum=13,m.Keyword=14,m.Snippet=15,m.Color=16,m.File=17,m.Reference=18,m.Folder=19,m.EnumMember=20,m.Constant=21,m.Struct=22,m.Event=23,m.Operator=24,m.TypeParameter=25,(v=ne=ne||{}).PlainText=1,v.Snippet=2,(ie||{}).is=function(e){return e&&(I.string(e.detail)||void 0===e.detail)&&(I.string(e.description)||void 0===e.description)},(oe||{}).create=function(e){return{label:e}},(ae||{}).create=function(e,t){return{items:e||[],isIncomplete:!!t}},(se=b=b||{}).fromPlainText=function(e){return e.replace(/[\\`*_{}[\]()#+\-.!]/g,"\\$&")},se.is=function(e){return I.string(e)||I.objectLiteral(e)&&I.string(e.language)&&I.string(e.value)},(ue||{}).is=function(e){var t=e;return!!t&&I.objectLiteral(t)&&(h.is(t.contents)||b.is(t.contents)||I.typedArray(t.contents,b.is))&&(void 0===e.range||u.is(e.range))},(ce||{}).create=function(e,t){return t?{label:e,documentation:t}:{label:e}},(le||{}).create=function(e,t,...r){let n={label:e};return I.defined(t)&&(n.documentation=t),I.defined(r)?n.parameters=r:n.parameters=[],n},(w=k=k||{}).Text=1,w.Read=2,w.Write=3,(de||{}).create=function(e,t){let r={range:e};return I.number(t)&&(r.kind=t),r},(t=C=C||{}).File=1,t.Module=2,t.Namespace=3,t.Package=4,t.Class=5,t.Method=6,t.Property=7,t.Field=8,t.Constructor=9,t.Enum=10,t.Interface=11,t.Function=12,t.Variable=13,t.Constant=14,t.String=15,t.Number=16,t.Boolean=17,t.Array=18,t.Object=19,t.Key=20,t.Null=21,t.EnumMember=22,t.Struct=23,t.Event=24,t.Operator=25,t.TypeParameter=26,(ge||{}).create=function(e,t,r,n,i){let o={name:e,kind:t,location:{uri:n,range:r}};return i&&(o.containerName=i),o},(fe||{}).create=function(e,t,r,n){return void 0!==n?{name:e,kind:t,location:{uri:r,range:n}}:{name:e,kind:t,location:{uri:r}}},(_=he=he||{}).Invoked=1,_.Automatic=2,(pe||{}).is=function(e){return I.objectLiteral(e)&&(void 0===e.resultId||"string"==typeof e.resultId)&&Array.isArray(e.data)&&(0===e.data.length||"number"==typeof e.data[0])},(y=me=me||{}).Type=1,y.Parameter=2,y.is=function(e){return 1===e||2===e},(S=ve=ve||{}).create=function(e){return{value:e}},S.is=function(e){return I.objectLiteral(e)&&(void 0===e.tooltip||I.string(e.tooltip)||h.is(e.tooltip))&&(void 0===e.location||c.is(e.location))&&(void 0===e.command||G.is(e.command))},(be||{}).createSnippet=function(e){return{kind:"snippet",value:e}},(ke||{}).create=function(e,t,r,n){return{insertText:e,filterText:t,range:r,command:n}},(we||{}).create=function(e){return{items:e}},(Ce||{}).create=function(e,t){return{range:e,text:t}},(_e||{}).create=function(e,t){return{triggerKind:e,selectedCompletionInfo:t}},(ye||{}).is=function(e){return I.objectLiteral(e)&&B.is(e.uri)&&I.string(e.name)};{var x=I=I||{};let n=Object.prototype.toString;x.defined=function(e){return typeof e<"u"},x.undefined=function(e){return"u"<typeof e},x.boolean=function(e){return!0===e||!1===e},x.string=function(e){return"[object String]"===n.call(e)},x.number=function(e){return"[object Number]"===n.call(e)},x.numberRange=function(e,t,r){return"[object Number]"===n.call(e)&&t<=e&&e<=r},x.integer=function(e){return"[object Number]"===n.call(e)&&-2147483648<=e&&e<=2147483647},x.uinteger=function(e){return"[object Number]"===n.call(e)&&0<=e&&e<=2147483647},x.func=function(e){return"[object Function]"===n.call(e)},x.objectLiteral=function(e){return null!==e&&"object"==typeof e},x.typedArray=function(e,t){return Array.isArray(e)&&e.every(t)}}var Ie=class{constructor(e,t,r){this._languageId=e,this._worker=t,this._disposables=[],this._listener=Object.create(null);let n=t=>{let r=t.getLanguageId();if(r===this._languageId){let e;this._listener[t.uri.toString()]=t.onDidChangeContent(()=>{window.clearTimeout(e),e=window.setTimeout(()=>this._doValidate(t.uri,r),500)}),this._doValidate(t.uri,r)}},i=e=>{A.editor.setModelMarkers(e,this._languageId,[]);let t=e.uri.toString(),r=this._listener[t];r&&(r.dispose(),delete this._listener[t])};this._disposables.push(A.editor.onDidCreateModel(n)),this._disposables.push(A.editor.onWillDisposeModel(i)),this._disposables.push(A.editor.onDidChangeModelLanguage(e=>{i(e.model),n(e.model)})),this._disposables.push(r(e=>{A.editor.getModels().forEach(e=>{e.getLanguageId()===this._languageId&&(i(e),n(e))})})),this._disposables.push({dispose:()=>{for(var e in A.editor.getModels().forEach(i),this._listener)this._listener[e].dispose()}}),A.editor.getModels().forEach(n)}dispose(){this._disposables.forEach(e=>e&&e.dispose()),this._disposables.length=0}_doValidate(n,i){this._worker(n).then(e=>e.doValidation(n.toString())).then(e=>{let t=e.map(e=>{var t="number"==typeof e.code?String(e.code):e.code;return{severity:function(e){switch(e){case d.Error:return A.MarkerSeverity.Error;case d.Warning:return A.MarkerSeverity.Warning;case d.Information:return A.MarkerSeverity.Info;case d.Hint:return A.MarkerSeverity.Hint;default:return A.MarkerSeverity.Info}}(e.severity),startLineNumber:e.range.start.line+1,startColumn:e.range.start.character+1,endLineNumber:e.range.end.line+1,endColumn:e.range.end.character+1,message:e.message,code:t,source:e.source}}),r=A.editor.getModel(n);r&&r.getLanguageId()===i&&A.editor.setModelMarkers(r,i,t)}).then(void 0,e=>{console.error(e)})}},Ae=class{constructor(e,t){this._worker=e,this._triggerCharacters=t}get triggerCharacters(){return this._triggerCharacters}provideCompletionItems(i,o,e,t){let r=i.uri;return this._worker(r).then(e=>e.doComplete(r.toString(),E(o))).then(r=>{if(r){let e=i.getWordUntilPosition(o),n=new A.Range(o.lineNumber,e.startColumn,o.lineNumber,e.endColumn),t=r.items.map(e=>{let t={label:e.label,insertText:e.insertText||e.label,sortText:e.sortText,filterText:e.filterText,documentation:e.documentation,detail:e.detail,command:(r=e.command)&&"editor.action.triggerSuggest"===r.command?{id:r.command,title:r.title,arguments:r.arguments}:void 0,range:n,kind:function(e){var t=A.languages.CompletionItemKind;switch(e){case p.Text:return t.Text;case p.Method:return t.Method;case p.Function:return t.Function;case p.Constructor:return t.Constructor;case p.Field:return t.Field;case p.Variable:return t.Variable;case p.Class:return t.Class;case p.Interface:return t.Interface;case p.Module:return t.Module;case p.Property:return t.Property;case p.Unit:return t.Unit;case p.Value:return t.Value;case p.Enum:return t.Enum;case p.Keyword:return t.Keyword;case p.Snippet:return t.Snippet;case p.Color:return t.Color;case p.File:return t.File;case p.Reference:return t.Reference}return t.Property}(e.kind)};var r;return e.textEdit&&(typeof(r=e.textEdit).insert<"u"&&typeof r.replace<"u"?t.range={insert:T(e.textEdit.insert),replace:T(e.textEdit.replace)}:t.range=T(e.textEdit.range),t.insertText=e.textEdit.newText),e.additionalTextEdits&&(t.additionalTextEdits=e.additionalTextEdits.map(R)),e.insertTextFormat===ne.Snippet&&(t.insertTextRules=A.languages.CompletionItemInsertTextRule.InsertAsSnippet),t});return{isIncomplete:r.isIncomplete,suggestions:t}}})}};function E(e){if(e)return{character:e.column-1,line:e.lineNumber-1}}function xe(e){if(e)return{start:{line:e.startLineNumber-1,character:e.startColumn-1},end:{line:e.endLineNumber-1,character:e.endColumn-1}}}function T(e){if(e)return new A.Range(e.start.line+1,e.start.character+1,e.end.line+1,e.end.character+1)}function R(e){if(e)return{range:T(e.range),text:e.newText}}var Ee=class{constructor(e){this._worker=e}provideHover(e,t,r){let n=e.uri;return this._worker(n).then(e=>e.doHover(n.toString(),E(t))).then(e=>{if(e)return{range:T(e.range),contents:function(e){if(e)return Array.isArray(e)?e.map(Te):[Te(e)]}(e.contents)}})}};function Te(e){return"string"==typeof e?{value:e}:(t=e)&&"object"==typeof t&&"string"==typeof t.kind?"plaintext"===e.kind?{value:e.value.replace(/[\\`*_{}[\]()#+\-.!]/g,"\\$&")}:{value:e.value}:{value:"```"+e.language+`
`+e.value+"\n```\n"};var t}var Re=class{constructor(e){this._worker=e}provideDocumentHighlights(e,t,r){let n=e.uri;return this._worker(n).then(e=>e.findDocumentHighlights(n.toString(),E(t))).then(e=>{if(e)return e.map(e=>({range:T(e.range),kind:function(e){switch(e){case k.Read:return A.languages.DocumentHighlightKind.Read;case k.Write:return A.languages.DocumentHighlightKind.Write;case k.Text:return A.languages.DocumentHighlightKind.Text}return A.languages.DocumentHighlightKind.Text}(e.kind)}))})}},Me=class{constructor(e){this._worker=e}provideDefinition(e,t,r){let n=e.uri;return this._worker(n).then(e=>e.findDefinition(n.toString(),E(t))).then(e=>{if(e)return[Pe(e)]})}};function Pe(e){return{uri:A.Uri.parse(e.uri),range:T(e.range)}}var je=class{constructor(e){this._worker=e}provideReferences(e,t,r,n){let i=e.uri;return this._worker(i).then(e=>e.findReferences(i.toString(),E(t))).then(e=>{if(e)return e.map(Pe)})}},De=class{constructor(e){this._worker=e}provideRenameEdits(e,t,r,n){let i=e.uri;return this._worker(i).then(e=>e.doRename(i.toString(),E(t),r)).then(e=>{var t=e;if(t&&t.changes){let e=[];for(var r in t.changes){var n,i=A.Uri.parse(r);for(n of t.changes[r])e.push({resource:i,versionId:void 0,textEdit:{range:T(n.range),text:n.newText}})}return{edits:e}}})}},Le=class{constructor(e){this._worker=e}provideDocumentSymbols(e,t){let r=e.uri;return this._worker(r).then(e=>e.findDocumentSymbols(r.toString())).then(e=>{if(e)return e.map(e=>"children"in e?function t(e){return{name:e.name,detail:e.detail??"",kind:Fe(e.kind),range:T(e.range),selectionRange:T(e.selectionRange),tags:e.tags??[],children:(e.children??[]).map(e=>t(e))}}(e):{name:e.name,detail:"",containerName:e.containerName,kind:Fe(e.kind),range:T(e.location.range),selectionRange:T(e.location.range),tags:[]})})}};function Fe(e){var t=A.languages.SymbolKind;switch(e){case C.File:return t.File;case C.Module:return t.Module;case C.Namespace:return t.Namespace;case C.Package:return t.Package;case C.Class:return t.Class;case C.Method:return t.Method;case C.Property:return t.Property;case C.Field:return t.Field;case C.Constructor:return t.Constructor;case C.Enum:return t.Enum;case C.Interface:return t.Interface;case C.Function:return t.Function;case C.Variable:return t.Variable;case C.Constant:return t.Constant;case C.String:return t.String;case C.Number:return t.Number;case C.Boolean:return t.Boolean;case C.Array:return t.Array}return t.Function}var Ne=class{constructor(e){this._worker=e}provideLinks(e,t){let r=e.uri;return this._worker(r).then(e=>e.findDocumentLinks(r.toString())).then(e=>{if(e)return{links:e.map(e=>({range:T(e.range),url:e.target}))}})}},We=class{constructor(e){this._worker=e}provideDocumentFormattingEdits(e,t,r){let n=e.uri;return this._worker(n).then(e=>e.format(n.toString(),null,Ue(t)).then(e=>{if(e&&0!==e.length)return e.map(R)}))}},Ve=class{constructor(e){this._worker=e,this.canFormatMultipleRanges=!1}provideDocumentRangeFormattingEdits(e,t,r,n){let i=e.uri;return this._worker(i).then(e=>e.format(i.toString(),xe(t),Ue(r)).then(e=>{if(e&&0!==e.length)return e.map(R)}))}};function Ue(e){return{tabSize:e.tabSize,insertSpaces:e.insertSpaces}}var Oe=class{constructor(e){this._worker=e}provideDocumentColors(e,t){let r=e.uri;return this._worker(r).then(e=>e.findDocumentColors(r.toString())).then(e=>{if(e)return e.map(e=>({color:e.color,range:T(e.range)}))})}provideColorPresentations(e,t,r){let n=e.uri;return this._worker(n).then(e=>e.getColorPresentations(n.toString(),t.color,xe(t.range))).then(e=>{if(e)return e.map(e=>{let t={label:e.label};return e.textEdit&&(t.textEdit=R(e.textEdit)),e.additionalTextEdits&&(t.additionalTextEdits=e.additionalTextEdits.map(R)),t})})}},He=class{constructor(e){this._worker=e}provideFoldingRanges(e,t,r){let n=e.uri;return this._worker(n).then(e=>e.getFoldingRanges(n.toString(),t)).then(e=>{if(e)return e.map(e=>{let t={start:e.startLine+1,end:e.endLine+1};return typeof e.kind<"u"&&(t.kind=function(e){switch(e){case l.Comment:return A.languages.FoldingRangeKind.Comment;case l.Imports:return A.languages.FoldingRangeKind.Imports;case l.Region:return A.languages.FoldingRangeKind.Region}}(e.kind)),t})})}},Ke=class{constructor(e){this._worker=e}provideSelectionRanges(e,t,r){let n=e.uri;return this._worker(n).then(e=>e.getSelectionRanges(n.toString(),t.map(E))).then(e=>{if(e)return e.map(e=>{let t=[];for(;e;)t.push({range:T(e.range)}),e=e.parent;return t})})}};function qe(e){return 32===e||9===e}function M(e){return 10===e||13===e}function P(e){return 48<=e&&e<=57}new Array(20).fill(0).map((e,t)=>" ".repeat(t)),new Array(200).fill(0).map((e,t)=>`
`+" ".repeat(t)),new Array(200).fill(0).map((e,t)=>"\r"+" ".repeat(t)),new Array(200).fill(0).map((e,t)=>`\r
`+" ".repeat(t)),new Array(200).fill(0).map((e,t)=>`
`+"\t".repeat(t)),new Array(200).fill(0).map((e,t)=>"\r"+"\t".repeat(t)),new Array(200).fill(0).map((e,t)=>`\r
`+"\t".repeat(t));var j,Xe=function(o,e=!1){let i=o.length,a=0,s="",t=0,u=16,c=0,l=0,d=0,g=0,f=0;function h(){let e="",t=a;for(;;){if(a>=i){e+=o.substring(t,a),f=2;break}var r=o.charCodeAt(a);if(34===r){e+=o.substring(t,a),a++;break}if(92===r){if(e+=o.substring(t,a),++a>=i){f=2;break}switch(o.charCodeAt(a++)){case 34:e+='"';break;case 92:e+="\\";break;case 47:e+="/";break;case 98:e+="\b";break;case 102:e+="\f";break;case 110:e+=`
`;break;case 114:e+="\r";break;case 116:e+="\t";break;case 117:var n=function(e,t){let r=0,n=0;for(;r<e||!t;){var i=o.charCodeAt(a);if(48<=i&&i<=57)n=16*n+i-48;else if(65<=i&&i<=70)n=16*n+i-65+10;else{if(!(97<=i&&i<=102))break;n=16*n+i-97+10}a++,r++}return n=r<e?-1:n}(4,!0);0<=n?e+=String.fromCharCode(n):f=4;break;default:f=5}t=a}else{if(0<=r&&r<=31){if(M(r)){e+=o.substring(t,a),f=2;break}f=6}a++}}return e}function r(){if(s="",f=0,t=a,l=c,g=d,a>=i)return t=i,u=17;let e=o.charCodeAt(a);if(qe(e)){for(;a++,s+=String.fromCharCode(e),qe(e=o.charCodeAt(a)););return u=15}if(M(e))return a++,s+=String.fromCharCode(e),13===e&&10===o.charCodeAt(a)&&(a++,s+=`
`),c++,d=a,u=14;switch(e){case 123:return a++,u=1;case 125:return a++,u=2;case 91:return a++,u=3;case 93:return a++,u=4;case 58:return a++,u=6;case 44:return a++,u=5;case 34:return a++,s=h(),u=10;case 47:var r=a-1;if(47===o.charCodeAt(a+1)){for(a+=2;a<i&&!M(o.charCodeAt(a));)a++;return s=o.substring(r,a),u=12}if(42!==o.charCodeAt(a+1))return s+=String.fromCharCode(e),a++,u=16;{a+=2;let e=i-1,t=!1;for(;a<e;){var n=o.charCodeAt(a);if(42===n&&47===o.charCodeAt(a+1)){a+=2,t=!0;break}a++,M(n)&&(13===n&&10===o.charCodeAt(a)&&a++,c++,d=a)}return t||(a++,f=1),s=o.substring(r,a),u=13}case 45:if(s+=String.fromCharCode(e),++a===i||!P(o.charCodeAt(a)))return u=16;case 48:case 49:case 50:case 51:case 52:case 53:case 54:case 55:case 56:case 57:return s+=function(){var e=a;if(48===o.charCodeAt(a))a++;else for(a++;a<o.length&&P(o.charCodeAt(a));)a++;if(a<o.length&&46===o.charCodeAt(a)){if(!(++a<o.length&&P(o.charCodeAt(a))))return f=3,o.substring(e,a);for(a++;a<o.length&&P(o.charCodeAt(a));)a++}let t=a;if(a<o.length&&(69===o.charCodeAt(a)||101===o.charCodeAt(a)))if((++a<o.length&&43===o.charCodeAt(a)||45===o.charCodeAt(a))&&a++,a<o.length&&P(o.charCodeAt(a))){for(a++;a<o.length&&P(o.charCodeAt(a));)a++;t=a}else f=3;return o.substring(e,t)}(),u=11;default:for(;a<i&&function(e){if(qe(e)||M(e))return;switch(e){case 125:case 93:case 123:case 91:case 34:case 58:case 44:case 47:return}return 1}(e);)a++,e=o.charCodeAt(a);if(t===a)return s+=String.fromCharCode(e),a++,u=16;switch(s=o.substring(t,a)){case"true":return u=8;case"false":return u=9;case"null":return u=7}return u=16}}return{setPosition:function(e){a=e,s="",t=0,u=16,f=0},getPosition:()=>a,scan:e?function(){let e;for(;12<=(e=r())&&e<=15;);return e}:r,getToken:()=>u,getTokenValue:()=>s,getTokenOffset:()=>t,getTokenLength:()=>a-t,getTokenStartLine:()=>l,getTokenStartCharacter:()=>t-g,getTokenError:()=>f}},$e="delimiter.bracket.json",ze="delimiter.array.json",Be="delimiter.colon.json",Je="delimiter.comma.json",Ge="keyword.json",Qe="keyword.json",Ye="string.value.json",Ze="number.json",et="string.key.json",tt="comment.block.json",rt="comment.line.json",D=class ut{constructor(e,t){this.parent=e,this.type=t}static pop(e){return e?e.parent:null}static push(e,t){return new ut(e,t)}static equals(e,t){if(!e&&!t)return!0;if(!e||!t)return!1;for(;e&&t;){if(e===t)return!0;if(e.type!==t.type)return!1;e=e.parent,t=t.parent}return!0}},nt=class ct{constructor(e,t,r,n){this._state=e,this.scanError=t,this.lastWasColon=r,this.parents=n}clone(){return new ct(this._state,this.scanError,this.lastWasColon,this.parents)}equals(e){return e===this||!!(e&&e instanceof ct)&&(this.scanError===e.scanError&&this.lastWasColon===e.lastWasColon&&D.equals(this.parents,e.parents))}getStateData(){return this._state}setStateData(e){this._state=e}},it=class extends Ie{constructor(e,t,r){super(e,t,r.onDidChange),this._disposables.push(A.editor.onWillDisposeModel(e=>{this._resetSchema(e.uri)})),this._disposables.push(A.editor.onDidChangeModelLanguage(e=>{this._resetSchema(e.model.uri)}))}_resetSchema(t){this._worker().then(e=>{e.resetSchema(t.toString())})}};function ot(e){return{dispose:()=>at(e)}}function at(e){for(;e.length;)e.pop().dispose()}var L,st={wordPattern:/(-?\d*\.\d\w*)|([^\[\{\]\}\:\"\,\s]+)/g,comments:{lineComment:"//",blockComment:["/*","*/"]},brackets:[["{","}"],["[","]"]],autoClosingPairs:[{open:"{",close:"}",notIn:["string"]},{open:"[",close:"]",notIn:["string"]},{open:'"',close:'"',notIn:["string"]}]};return L=X,n(o({},"__esModule",{value:!0}),L)});