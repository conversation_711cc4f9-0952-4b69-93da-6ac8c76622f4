using Harmoni360.Domain.Common;
using Harmoni360.Domain.Enums;

namespace Harmoni360.Domain.Authorization;

/// <summary>
/// Defines workflow-specific permissions for role-based access control
/// </summary>
public enum WorkflowPermission
{
    // Workflow Definition Management
    [PermissionDescription("View workflow definitions")]
    ViewWorkflowDefinitions = 1,

    [PermissionDescription("Create new workflow definitions")]
    CreateWorkflowDefinitions = 2,

    [PermissionDescription("Edit existing workflow definitions")]
    EditWorkflowDefinitions = 3,

    [PermissionDescription("Delete workflow definitions")]
    DeleteWorkflowDefinitions = 4,

    [PermissionDescription("Publish workflow definitions")]
    PublishWorkflowDefinitions = 5,

    // Workflow Execution Management
    [PermissionDescription("Execute workflows")]
    ExecuteWorkflows = 10,

    [PermissionDescription("Start new workflow instances")]
    StartWorkflowInstances = 11,

    [PermissionDescription("Cancel running workflow instances")]
    CancelWorkflowInstances = 12,

    [PermissionDescription("Suspend workflow instances")]
    SuspendWorkflowInstances = 13,

    [PermissionDescription("Resume suspended workflow instances")]
    ResumeWorkflowInstances = 14,

    // Workflow Monitoring
    [PermissionDescription("View workflow execution logs")]
    ViewWorkflowLogs = 20,

    [PermissionDescription("View workflow execution metrics")]
    ViewWorkflowMetrics = 21,

    [PermissionDescription("Export workflow audit reports")]
    ExportWorkflowReports = 22,

    [PermissionDescription("View workflow performance data")]
    ViewWorkflowPerformance = 23,

    // Workflow Administration
    [PermissionDescription("Manage workflow system settings")]
    ManageWorkflowSettings = 30,

    [PermissionDescription("Configure workflow integrations")]
    ConfigureWorkflowIntegrations = 31,

    [PermissionDescription("Manage workflow user access")]
    ManageWorkflowAccess = 32,

    [PermissionDescription("Perform workflow system maintenance")]
    WorkflowSystemMaintenance = 33,

    // Activity-Level Permissions
    [PermissionDescription("Execute system activities")]
    ExecuteSystemActivities = 40,

    [PermissionDescription("Execute human task activities")]
    ExecuteHumanTasks = 41,

    [PermissionDescription("Execute approval activities")]
    ExecuteApprovals = 42,

    [PermissionDescription("Execute notification activities")]
    ExecuteNotifications = 43,

    [PermissionDescription("Execute integration activities")]
    ExecuteIntegrations = 44,

    // Elsa Studio Access
    [PermissionDescription("Access Elsa Studio interface")]
    AccessElsaStudio = 50,

    [PermissionDescription("Design workflows in Elsa Studio")]
    DesignWorkflowsInStudio = 51,

    [PermissionDescription("Debug workflows in Elsa Studio")]
    DebugWorkflowsInStudio = 52
}

/// <summary>
/// Maps workflow permissions to role types for easy authorization
/// </summary>
public static class WorkflowPermissionMap
{
    /// <summary>
    /// Gets all permissions available for a specific role
    /// </summary>
    public static IEnumerable<WorkflowPermission> GetPermissionsForRole(RoleType role)
    {
        return role switch
        {
            RoleType.SuperAdmin => GetAllPermissions(),
            RoleType.Developer => GetDeveloperPermissions(),
            RoleType.HSEManager => GetManagerPermissions(),
            RoleType.SafetyOfficer => GetOfficerPermissions(),
            RoleType.SecurityOfficer => GetOfficerPermissions(),
            RoleType.Viewer => GetEmployeePermissions(),
            _ => Array.Empty<WorkflowPermission>()
        };
    }

    /// <summary>
    /// Gets all available workflow permissions
    /// </summary>
    public static IEnumerable<WorkflowPermission> GetAllPermissions()
    {
        return Enum.GetValues<WorkflowPermission>();
    }

    /// <summary>
    /// Developer permissions for workflow design and debugging
    /// </summary>
    private static IEnumerable<WorkflowPermission> GetDeveloperPermissions()
    {
        return new[]
        {
            // Full workflow definition management
            WorkflowPermission.ViewWorkflowDefinitions,
            WorkflowPermission.CreateWorkflowDefinitions,
            WorkflowPermission.EditWorkflowDefinitions,
            WorkflowPermission.DeleteWorkflowDefinitions,
            WorkflowPermission.PublishWorkflowDefinitions,

            // Workflow execution
            WorkflowPermission.ExecuteWorkflows,
            WorkflowPermission.StartWorkflowInstances,
            WorkflowPermission.CancelWorkflowInstances,
            WorkflowPermission.SuspendWorkflowInstances,
            WorkflowPermission.ResumeWorkflowInstances,

            // Monitoring and debugging
            WorkflowPermission.ViewWorkflowLogs,
            WorkflowPermission.ViewWorkflowMetrics,
            WorkflowPermission.ExportWorkflowReports,
            WorkflowPermission.ViewWorkflowPerformance,

            // Activity execution
            WorkflowPermission.ExecuteSystemActivities,
            WorkflowPermission.ExecuteHumanTasks,
            WorkflowPermission.ExecuteApprovals,
            WorkflowPermission.ExecuteNotifications,
            WorkflowPermission.ExecuteIntegrations,

            // Elsa Studio access
            WorkflowPermission.AccessElsaStudio,
            WorkflowPermission.DesignWorkflowsInStudio,
            WorkflowPermission.DebugWorkflowsInStudio
        };
    }

    /// <summary>
    /// HSE Manager permissions for workflow oversight and management
    /// </summary>
    private static IEnumerable<WorkflowPermission> GetManagerPermissions()
    {
        return new[]
        {
            // View and execute workflows
            WorkflowPermission.ViewWorkflowDefinitions,
            WorkflowPermission.ExecuteWorkflows,
            WorkflowPermission.StartWorkflowInstances,
            WorkflowPermission.CancelWorkflowInstances,

            // Monitoring and reporting
            WorkflowPermission.ViewWorkflowLogs,
            WorkflowPermission.ViewWorkflowMetrics,
            WorkflowPermission.ExportWorkflowReports,
            WorkflowPermission.ViewWorkflowPerformance,

            // Activity execution
            WorkflowPermission.ExecuteHumanTasks,
            WorkflowPermission.ExecuteApprovals,
            WorkflowPermission.ExecuteNotifications,

            // Limited Elsa Studio access
            WorkflowPermission.AccessElsaStudio
        };
    }

    /// <summary>
    /// HSE Officer permissions for day-to-day workflow operations
    /// </summary>
    private static IEnumerable<WorkflowPermission> GetOfficerPermissions()
    {
        return new[]
        {
            // View and execute workflows
            WorkflowPermission.ViewWorkflowDefinitions,
            WorkflowPermission.ExecuteWorkflows,
            WorkflowPermission.StartWorkflowInstances,

            // Basic monitoring
            WorkflowPermission.ViewWorkflowLogs,
            WorkflowPermission.ViewWorkflowMetrics,

            // Activity execution
            WorkflowPermission.ExecuteHumanTasks,
            WorkflowPermission.ExecuteApprovals,
            WorkflowPermission.ExecuteNotifications
        };
    }

    /// <summary>
    /// Employee permissions for basic workflow participation
    /// </summary>
    private static IEnumerable<WorkflowPermission> GetEmployeePermissions()
    {
        return new[]
        {
            // Basic workflow participation
            WorkflowPermission.ExecuteWorkflows,
            WorkflowPermission.ExecuteHumanTasks,
            WorkflowPermission.ExecuteNotifications
        };
    }

    /// <summary>
    /// Checks if a role has a specific workflow permission
    /// </summary>
    public static bool HasPermission(RoleType role, WorkflowPermission permission)
    {
        return GetPermissionsForRole(role).Contains(permission);
    }

    /// <summary>
    /// Gets the minimum role required for a specific permission
    /// </summary>
    public static RoleType GetMinimumRoleForPermission(WorkflowPermission permission)
    {
        var roleHierarchy = new[] 
        { 
            RoleType.Viewer, 
            RoleType.SafetyOfficer,
            RoleType.SecurityOfficer,
            RoleType.HSEManager, 
            RoleType.Developer, 
            RoleType.SuperAdmin 
        };

        foreach (var role in roleHierarchy)
        {
            if (HasPermission(role, permission))
                return role;
        }

        return RoleType.SuperAdmin; // Default to highest permission level
    }
}

/// <summary>
/// Attribute for describing workflow permissions
/// </summary>
[AttributeUsage(AttributeTargets.Field)]
public class PermissionDescriptionAttribute : Attribute
{
    public string Description { get; }

    public PermissionDescriptionAttribute(string description)
    {
        Description = description;
    }
}