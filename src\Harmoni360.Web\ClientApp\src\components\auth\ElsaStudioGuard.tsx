import React from 'react';
import { Navigate } from 'react-router-dom';
import { useAuth } from '../../hooks/useAuth';
import { RoleType } from '../../types/permissions';
import UnauthorizedAccess from '../common/UnauthorizedAccess';

interface ElsaStudioGuardProps {
  children: React.ReactNode;
}

/**
 * Guard component specifically for Elsa Studio access
 * Only allows SuperAdmin and Developer roles
 */
const ElsaStudioGuard: React.FC<ElsaStudioGuardProps> = ({ children }) => {
  const { user, isAuthenticated, isLoading } = useAuth();

  // Show loading while checking authentication
  if (isLoading) {
    return (
      <div className="d-flex justify-content-center align-items-center min-vh-100">
        <div className="spinner-border text-primary" role="status">
          <span className="visually-hidden">Loading...</span>
        </div>
      </div>
    );
  }

  // Additional check for tokens in case Redux auth state is out of sync
  const hasTokenInStorage = !!localStorage.getItem('harmoni360_token');
  const hasUserInStorage = !!localStorage.getItem('user');

  // Redirect to login if not authenticated and no tokens found
  if (!isAuthenticated && !hasTokenInStorage) {
    return <Navigate to="/login" replace />;
  }

  // Check if user has SuperAdmin or Developer role
  let hasRequiredRole = false;
  
  if (user?.roles) {
    hasRequiredRole = user.roles.includes(RoleType.SuperAdmin) || 
                     user.roles.includes(RoleType.Developer);
  } else if (hasTokenInStorage && hasUserInStorage) {
    // Try to get user from localStorage if Redux state is not synced
    try {
      const storedUser = JSON.parse(localStorage.getItem('user') || '{}');
      hasRequiredRole = storedUser.roles?.includes(RoleType.SuperAdmin) || 
                       storedUser.roles?.includes(RoleType.Developer);
    } catch (e) {
      // Silent error handling for localStorage parsing
    }
  }

  // Show unauthorized access page if user doesn't have required role
  if (!hasRequiredRole && hasTokenInStorage) {
    return (
      <UnauthorizedAccess
        title="Elsa Studio Access Restricted"
        message="Access to Elsa Studio is restricted to System Administrators and Developers only."
        requiredRole="SuperAdmin or Developer"
        requiredModule="Workflow Management"
        showContactSupport={true}
        showBackButton={true}
        showHomeButton={true}
      />
    );
  }

  // User has required role, render children
  return <>{children}</>;
};

export default ElsaStudioGuard;