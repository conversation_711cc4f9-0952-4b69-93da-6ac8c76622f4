using Microsoft.EntityFrameworkCore.Migrations;

#nullable disable

namespace Harmoni360.Infrastructure.Migrations
{
    /// <inheritdoc />
    public partial class FixWorkflowExecutionLogEventTypeColumn : Migration
    {
        /// <inheritdoc />
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            // Check if EventName column exists and rename it to EventType
            migrationBuilder.Sql(@"
                DO $$
                BEGIN
                    IF EXISTS (
                        SELECT 1 
                        FROM information_schema.columns 
                        WHERE table_name = 'WorkflowExecutionLogs' 
                        AND column_name = 'EventName'
                        AND table_schema = 'public'
                    ) THEN
                        -- Drop the old index if it exists
                        DROP INDEX IF EXISTS ""IX_WorkflowExecutionLogs_EventName"";
                        
                        -- Add new EventType column as integer
                        ALTER TABLE ""WorkflowExecutionLogs"" 
                        ADD COLUMN ""EventType"" integer;
                        
                        -- Update EventType based on EventName if possible
                        -- Set default value of 1 (WorkflowStarted) for existing records
                        UPDATE ""WorkflowExecutionLogs"" 
                        SET ""EventType"" = 1 
                        WHERE ""EventType"" IS NULL;
                        
                        -- Make EventType NOT NULL
                        ALTER TABLE ""WorkflowExecutionLogs"" 
                        ALTER COLUMN ""EventType"" SET NOT NULL;
                        
                        -- Drop the old EventName column
                        ALTER TABLE ""WorkflowExecutionLogs"" 
                        DROP COLUMN ""EventName"";
                        
                        -- Create new index
                        CREATE INDEX ""IX_WorkflowExecutionLogs_EventType"" 
                        ON ""WorkflowExecutionLogs"" (""EventType"");
                    END IF;
                END
                $$;
            ");
        }

        /// <inheritdoc />
        protected override void Down(MigrationBuilder migrationBuilder)
        {
            // Reverse the changes - convert EventType back to EventName
            migrationBuilder.Sql(@"
                DO $$
                BEGIN
                    IF EXISTS (
                        SELECT 1 
                        FROM information_schema.columns 
                        WHERE table_name = 'WorkflowExecutionLogs' 
                        AND column_name = 'EventType'
                        AND table_schema = 'public'
                    ) THEN
                        -- Drop the EventType index
                        DROP INDEX IF EXISTS ""IX_WorkflowExecutionLogs_EventType"";
                        
                        -- Add EventName column
                        ALTER TABLE ""WorkflowExecutionLogs"" 
                        ADD COLUMN ""EventName"" character varying(200);
                        
                        -- Set default EventName based on EventType
                        UPDATE ""WorkflowExecutionLogs"" 
                        SET ""EventName"" = 'Unknown'
                        WHERE ""EventName"" IS NULL;
                        
                        -- Make EventName NOT NULL
                        ALTER TABLE ""WorkflowExecutionLogs"" 
                        ALTER COLUMN ""EventName"" SET NOT NULL;
                        
                        -- Drop EventType column
                        ALTER TABLE ""WorkflowExecutionLogs"" 
                        DROP COLUMN ""EventType"";
                        
                        -- Create EventName index
                        CREATE INDEX ""IX_WorkflowExecutionLogs_EventName"" 
                        ON ""WorkflowExecutionLogs"" (""EventName"");
                    END IF;
                END
                $$;
            ");
        }
    }
}