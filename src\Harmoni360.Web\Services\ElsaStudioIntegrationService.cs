using System.IdentityModel.Tokens.Jwt;
using System.Security.Claims;
using System.Text.Json;
using Harmoni360.Application.Common.Interfaces;
using Harmoni360.Domain.Enums;

namespace Harmoni360.Web.Services;

public interface IElsaStudioIntegrationService
{
    string GenerateElsaStudioUrl(string jwtToken, string? returnPath = null);
    bool CanUserAccessElsaStudio(ClaimsPrincipal user);
    string GetElsaStudioRedirectUrl(ClaimsPrincipal user, string? returnUrl = null);
    Task<Dictionary<string, object>> GetUserContextForElsaAsync(ClaimsPrincipal user);
}

public class ElsaStudioIntegrationService : IElsaStudioIntegrationService
{
    private readonly IConfiguration _configuration;
    private readonly ICurrentUserService _currentUserService;
    private readonly ILogger<ElsaStudioIntegrationService> _logger;

    public ElsaStudioIntegrationService(
        IConfiguration configuration, 
        ICurrentUserService currentUserService,
        ILogger<ElsaStudioIntegrationService> logger)
    {
        _configuration = configuration;
        _currentUserService = currentUserService;
        _logger = logger;
    }

    public string GenerateElsaStudioUrl(string jwtToken, string? returnPath = null)
    {
        var baseUrl = _configuration["ElsaStudio:BaseUrl"] ?? "/elsa-studio";
        var url = $"{baseUrl}?token={Uri.EscapeDataString(jwtToken)}";
        
        if (!string.IsNullOrEmpty(returnPath))
        {
            url += $"&returnPath={Uri.EscapeDataString(returnPath)}";
        }

        return url;
    }

    public bool CanUserAccessElsaStudio(ClaimsPrincipal user)
    {
        if (!user.Identity?.IsAuthenticated == true)
        {
            return false;
        }

        // Check if user has required roles
        var roles = user.Claims
            .Where(c => c.Type == ClaimTypes.Role)
            .Select(c => c.Value)
            .ToList();

        var hasRequiredRole = roles.Contains(RoleType.SuperAdmin.ToString()) ||
                             roles.Contains(RoleType.Developer.ToString()) ||
                             roles.Contains(RoleType.HSEManager.ToString());

        _logger.LogDebug("User {UserName} access check: HasRequiredRole={HasAccess}, Roles=[{Roles}]", 
            user.Identity.Name, hasRequiredRole, string.Join(", ", roles));

        return hasRequiredRole;
    }

    public string GetElsaStudioRedirectUrl(ClaimsPrincipal user, string? returnUrl = null)
    {
        if (!CanUserAccessElsaStudio(user))
        {
            throw new UnauthorizedAccessException("User does not have permission to access Elsa Studio");
        }

        // Get JWT token from user claims or generate new one
        var jwtToken = ExtractJwtFromUser(user);
        if (string.IsNullOrEmpty(jwtToken))
        {
            throw new InvalidOperationException("Cannot generate Elsa Studio URL without valid JWT token");
        }

        return GenerateElsaStudioUrl(jwtToken, returnUrl);
    }

    public async Task<Dictionary<string, object>> GetUserContextForElsaAsync(ClaimsPrincipal user)
    {
        var context = new Dictionary<string, object>
        {
            ["UserId"] = user.FindFirst(ClaimTypes.NameIdentifier)?.Value ?? "unknown",
            ["UserName"] = user.Identity?.Name ?? "Unknown User",
            ["Email"] = user.FindFirst(ClaimTypes.Email)?.Value ?? "",
            ["Roles"] = user.Claims
                .Where(c => c.Type == ClaimTypes.Role)
                .Select(c => c.Value)
                .ToArray(),
            ["Permissions"] = await GetUserPermissionsAsync(user),
            ["Department"] = user.FindFirst("department")?.Value ?? "",
            ["AccessLevel"] = DetermineAccessLevel(user),
            ["LastAccessed"] = DateTime.UtcNow,
            ["SessionId"] = Guid.NewGuid().ToString()
        };

        return context;
    }

    private string? ExtractJwtFromUser(ClaimsPrincipal user)
    {
        // Try to get the original JWT token from claims
        var tokenClaim = user.FindFirst("jwt_token");
        if (tokenClaim != null)
        {
            return tokenClaim.Value;
        }

        // If not available, we would need to generate a new token
        // This would require access to the JWT service
        _logger.LogWarning("JWT token not found in user claims, may need to regenerate");
        return null;
    }

    private async Task<string[]> GetUserPermissionsAsync(ClaimsPrincipal user)
    {
        // Extract permissions from claims or fetch from database
        var permissions = user.Claims
            .Where(c => c.Type == "permission")
            .Select(c => c.Value)
            .ToArray();

        if (!permissions.Any())
        {
            // Default permissions based on role
            var roles = user.Claims
                .Where(c => c.Type == ClaimTypes.Role)
                .Select(c => c.Value)
                .ToList();

            permissions = roles.Contains(RoleType.SuperAdmin.ToString()) 
                ? new[] { "workflow.create", "workflow.edit", "workflow.delete", "workflow.execute", "workflow.admin" }
                : roles.Contains(RoleType.Developer.ToString())
                ? new[] { "workflow.create", "workflow.edit", "workflow.execute" }
                : roles.Contains(RoleType.HSEManager.ToString())
                ? new[] { "workflow.execute", "workflow.view" }
                : new[] { "workflow.view" };
        }

        return await Task.FromResult(permissions);
    }

    private string DetermineAccessLevel(ClaimsPrincipal user)
    {
        var roles = user.Claims
            .Where(c => c.Type == ClaimTypes.Role)
            .Select(c => c.Value)
            .ToList();

        if (roles.Contains(RoleType.SuperAdmin.ToString()))
            return "Admin";
        if (roles.Contains(RoleType.Developer.ToString()))
            return "Developer";
        if (roles.Contains(RoleType.HSEManager.ToString()))
            return "Manager";
        
        return "User";
    }
}

/// <summary>
/// Extension methods for Elsa Studio integration
/// </summary>
public static class ElsaStudioExtensions
{
    public static void AddElsaStudioIntegration(this IServiceCollection services)
    {
        services.AddScoped<IElsaStudioIntegrationService, ElsaStudioIntegrationService>();
    }

    public static async Task<IResult> CreateElsaStudioRedirect(
        this IElsaStudioIntegrationService service, 
        ClaimsPrincipal user, 
        string? returnUrl = null)
    {
        try
        {
            if (!service.CanUserAccessElsaStudio(user))
            {
                return Results.Forbid();
            }

            var redirectUrl = service.GetElsaStudioRedirectUrl(user, returnUrl);
            return Results.Redirect(redirectUrl);
        }
        catch (UnauthorizedAccessException)
        {
            return Results.Forbid();
        }
        catch (Exception ex)
        {
            // Log error and return generic error
            return Results.Problem("Failed to generate Elsa Studio access URL");
        }
    }
}