define("vs/language/json/jsonWorker",["require","require"],e=>{"use strict";return(()=>{var c,u=Object.defineProperty,x=Object.getOwnPropertyDescriptor,O=Object.getOwnPropertyNames,N=Object.prototype.hasOwnProperty,P={},I=P,L={JSONWorker:()=>ir,create:()=>function(e,t){return new ir(e,t)}};for(c in L)u(I,c,{get:L[c],enumerable:!0});function E(a,e=!1){let i=a.length,s=0,o="",t=0,l=16,c=0,u=0,h=0,f=0,d=0;function m(){let e="",t=s;for(;;){if(s>=i){e+=a.substring(t,s),d=2;break}var r=a.charCodeAt(s);if(34===r){e+=a.substring(t,s),s++;break}if(92===r){if(e+=a.substring(t,s),++s>=i){d=2;break}switch(a.charCodeAt(s++)){case 34:e+='"';break;case 92:e+="\\";break;case 47:e+="/";break;case 98:e+="\b";break;case 102:e+="\f";break;case 110:e+=`
`;break;case 114:e+="\r";break;case 116:e+="\t";break;case 117:var n=function(e,t){let r=0,n=0;for(;r<e||!t;){var i=a.charCodeAt(s);if(48<=i&&i<=57)n=16*n+i-48;else if(65<=i&&i<=70)n=16*n+i-65+10;else{if(!(97<=i&&i<=102))break;n=16*n+i-97+10}s++,r++}return n=r<e?-1:n}(4,!0);0<=n?e+=String.fromCharCode(n):d=4;break;default:d=5}t=s}else{if(0<=r&&r<=31){if(V(r)){e+=a.substring(t,s),d=2;break}d=6}s++}}return e}function r(){if(o="",d=0,t=s,u=c,f=h,s>=i)return t=i,l=17;let e=a.charCodeAt(s);if(M(e)){for(;s++,o+=String.fromCharCode(e),M(e=a.charCodeAt(s)););return l=15}if(V(e))return s++,o+=String.fromCharCode(e),13===e&&10===a.charCodeAt(s)&&(s++,o+=`
`),c++,h=s,l=14;switch(e){case 123:return s++,l=1;case 125:return s++,l=2;case 91:return s++,l=3;case 93:return s++,l=4;case 58:return s++,l=6;case 44:return s++,l=5;case 34:return s++,o=m(),l=10;case 47:var r=s-1;if(47===a.charCodeAt(s+1)){for(s+=2;s<i&&!V(a.charCodeAt(s));)s++;return o=a.substring(r,s),l=12}if(42!==a.charCodeAt(s+1))return o+=String.fromCharCode(e),s++,l=16;{s+=2;let e=i-1,t=!1;for(;s<e;){var n=a.charCodeAt(s);if(42===n&&47===a.charCodeAt(s+1)){s+=2,t=!0;break}s++,V(n)&&(13===n&&10===a.charCodeAt(s)&&s++,c++,h=s)}return t||(s++,d=1),o=a.substring(r,s),l=13}case 45:if(o+=String.fromCharCode(e),++s===i||!j(a.charCodeAt(s)))return l=16;case 48:case 49:case 50:case 51:case 52:case 53:case 54:case 55:case 56:case 57:return o+=function(){var e=s;if(48===a.charCodeAt(s))s++;else for(s++;s<a.length&&j(a.charCodeAt(s));)s++;if(s<a.length&&46===a.charCodeAt(s)){if(!(++s<a.length&&j(a.charCodeAt(s))))return d=3,a.substring(e,s);for(s++;s<a.length&&j(a.charCodeAt(s));)s++}let t=s;if(s<a.length&&(69===a.charCodeAt(s)||101===a.charCodeAt(s)))if((++s<a.length&&43===a.charCodeAt(s)||45===a.charCodeAt(s))&&s++,s<a.length&&j(a.charCodeAt(s))){for(s++;s<a.length&&j(a.charCodeAt(s));)s++;t=s}else d=3;return a.substring(e,t)}(),l=11;default:for(;s<i&&function(e){if(M(e)||V(e))return;switch(e){case 125:case 93:case 123:case 91:case 34:case 58:case 44:case 47:return}return 1}(e);)s++,e=a.charCodeAt(s);if(t===s)return o+=String.fromCharCode(e),s++,l=16;switch(o=a.substring(t,s)){case"true":return l=8;case"false":return l=9;case"null":return l=7}return l=16}}return{setPosition:function(e){s=e,o="",t=0,l=16,d=0},getPosition:()=>s,scan:e?function(){let e;for(;12<=(e=r())&&e<=15;);return e}:r,getToken:()=>l,getTokenValue:()=>o,getTokenOffset:()=>t,getTokenLength:()=>s-t,getTokenStartLine:()=>u,getTokenStartCharacter:()=>t-f,getTokenError:()=>d}}function M(e){return 32===e||9===e}function V(e){return 10===e||13===e}function j(e){return 48<=e&&e<=57}var $,T=new Array(20).fill(0).map((e,t)=>" ".repeat(t)),e=200,F={" ":{"\n":new Array(e).fill(0).map((e,t)=>`
`+" ".repeat(t)),"\r":new Array(e).fill(0).map((e,t)=>"\r"+" ".repeat(t)),"\r\n":new Array(e).fill(0).map((e,t)=>`\r
`+" ".repeat(t))},"\t":{"\n":new Array(e).fill(0).map((e,t)=>`
`+"\t".repeat(t)),"\r":new Array(e).fill(0).map((e,t)=>"\r"+"\t".repeat(t)),"\r\n":new Array(e).fill(0).map((e,t)=>`\r
`+"\t".repeat(t))}},D=[`
`,"\r",`\r
`];function R(n,i,a){let t,r,s,o,l;if(i){for(o=i.offset,l=o+i.length,s=o;0<s&&!U(n,s-1);)s--;let e=l;for(;e<n.length&&!U(n,e);)e++;r=n.substring(s,e),t=function(e,t){let r=0,n=0,i=t.tabSize||4;for(;r<e.length;){var a=e.charAt(r);if(a===T[1])n++;else{if("\t"!==a)break;n+=i}r++}return Math.floor(n/i)}(r,a)}else r=n,t=0,s=0,o=0,l=n.length;let c=function(e,t){for(let e=0;e<t.length;e++){var r=t.charAt(e);if("\r"===r)return e+1<t.length&&t.charAt(e+1)===`
`?`\r
`:"\r";if(r===`
`)return`
`}return e&&e.eol||`
`}(a,n),u=D.includes(c),h=0,f=0,d,m="\t"===(d=a.insertSpaces?T[a.tabSize||4]??_(T[1],a.tabSize||4):"\t")?"\t":" ",p=E(r,!1),g=!1;function y(){if(1<h)return _(c,h)+_(d,t+f);var e=d.length*(t+f);return!u||e>F[m][c].length?c+_(d,t+f):e<=0?c:F[m][c][e]}function v(){let e=p.scan();for(h=0;15===e||14===e;)14===e&&a.keepLines?h+=1:14===e&&(h=1),e=p.scan();return g=16===e||0!==p.getTokenError(),e}let b=[];function S(e,t,r){g||i&&!(t<l&&r>o)||n.substring(t,r)===e||b.push({offset:t,length:r-t,content:e})}let x=v();var e;for(a.keepLines&&0<h&&S(_(c,h),0,0),17!==x&&(e=p.getTokenOffset()+s,S(d.length*t<20&&a.insertSpaces?T[d.length*t]:_(d,t),s,e));17!==x;){let e=p.getTokenOffset()+p.getTokenLength()+s,t=v(),r="",n=!1;for(;0===h&&(12===t||13===t);){var k=p.getTokenOffset()+s;S(T[1],e,k),e=p.getTokenOffset()+p.getTokenLength()+s,n=12===t,r=n?y():"",t=v()}if(2===t)1!==x&&f--,a.keepLines&&0<h||!a.keepLines&&1!==x?r=y():a.keepLines&&(r=T[1]);else if(4===t)3!==x&&f--,a.keepLines&&0<h||!a.keepLines&&3!==x?r=y():a.keepLines&&(r=T[1]);else{switch(x){case 3:case 1:f++,r=a.keepLines&&0<h||!a.keepLines?y():T[1];break;case 5:r=a.keepLines&&0<h||!a.keepLines?y():T[1];break;case 12:r=y();break;case 13:0<h?r=y():n||(r=T[1]);break;case 6:a.keepLines&&0<h?r=y():n||(r=T[1]);break;case 10:a.keepLines&&0<h?r=y():6!==t||n||(r="");break;case 7:case 8:case 9:case 11:case 2:case 4:a.keepLines&&0<h?r=y():12!==t&&13!==t||n?5!==t&&17!==t&&(g=!0):r=T[1];break;case 16:g=!0}0<h&&(12===t||13===t)&&(r=y())}17===t&&(r=a.keepLines&&0<h?y():a.insertFinalNewline?c:"");var A=p.getTokenOffset()+s;S(r,e,A),x=t}return b}function _(t,r){let n="";for(let e=0;e<r;e++)n+=t;return n}function U(e,t){return-1!==`\r
`.indexOf(e.charAt(t))}($=$||{}).DEFAULT={allowTrailingComma:!1};var q,B,J,k,A,K,W,z,re,y,H,G,X,Z,Q,Y,ee,te,r,me,pe,v,b,ge,ye,p,ve,be,Se,xe,ke,Ae,Te,Ce,t,we,Oe,Ne,Pe,Ie,Le,Ee,C=E,Me=(0,0,function(e,m=[],p=$.DEFAULT){let g=null,y=[],v=[];function b(e){Array.isArray(y)?y.push(e):null!==g&&(y[g]=e)}{var[e,p,S=$.DEFAULT]=[e,{onObjectBegin:()=>{var e={};b(e),v.push(y),y=e,g=null},onObjectProperty:e=>{g=e},onObjectEnd:()=>{y=v.pop()},onArrayBegin:()=>{var e=[];b(e),v.push(y),y=e,g=null},onArrayEnd:()=>{y=v.pop()},onLiteralValue:b,onError:(e,t,r)=>{m.push({error:e,offset:t,length:r})}},p];let n=E(e,!1),r=[];function x(e){return e?()=>e(n.getTokenOffset(),n.getTokenLength(),n.getTokenStartLine(),n.getTokenStartCharacter()):()=>!0}function k(e){return e?()=>e(n.getTokenOffset(),n.getTokenLength(),n.getTokenStartLine(),n.getTokenStartCharacter(),()=>r.slice()):()=>!0}function A(t){return t?e=>t(e,n.getTokenOffset(),n.getTokenLength(),n.getTokenStartLine(),n.getTokenStartCharacter()):()=>!0}function T(t){return t?e=>t(e,n.getTokenOffset(),n.getTokenLength(),n.getTokenStartLine(),n.getTokenStartCharacter(),()=>r.slice()):()=>!0}let t=k(p.onObjectBegin),i=T(p.onObjectProperty),a=x(p.onObjectEnd),s=k(p.onArrayBegin),o=x(p.onArrayEnd),l=T(p.onLiteralValue),c=A(p.onSeparator),u=x(p.onComment),h=A(p.onError),f=S&&S.disallowComments,d=S&&S.allowTrailingComma;function C(){for(;;){var e=n.scan();switch(n.getTokenError()){case 4:w(14);break;case 5:w(15);break;case 3:w(13);break;case 1:f||w(11);break;case 2:w(12);break;case 6:w(16)}switch(e){case 12:case 13:f?w(10):u();break;case 16:w(1);break;case 15:case 14:break;default:return e}}}function w(e,t=[],r=[]){if(h(e),0<t.length+r.length){let e=n.getToken();for(;17!==e;){if(-1!==t.indexOf(e)){C();break}if(-1!==r.indexOf(e))break;e=C()}}}function O(e){var t=n.getTokenValue();return e?l(t):(i(t),r.push(t)),C(),!0}function N(){t(),C();let e=!1;for(;2!==n.getToken()&&17!==n.getToken();){if(5===n.getToken()){if(e||w(4,[],[]),c(","),C(),2===n.getToken()&&d)break}else e&&w(6,[],[]);(10!==n.getToken()?(w(3,[],[2,5]),0):(O(!1),6===n.getToken()?(c(":"),C(),P()||w(4,[],[2,5])):w(5,[],[2,5]),r.pop(),1))||w(4,[],[2,5]),e=!0}return a(),2!==n.getToken()?w(7,[2],[]):C(),!0}function P(){switch(n.getToken()){case 3:{s(),C();let e=!0,t=!1;for(;4!==n.getToken()&&17!==n.getToken();){if(5===n.getToken()){if(t||w(4,[],[]),c(","),C(),4===n.getToken()&&d)break}else t&&w(6,[],[]);e?(r.push(0),e=!1):r[r.length-1]++,P()||w(4,[],[4,5]),t=!0}return o(),e||r.pop(),4!==n.getToken()?w(8,[4],[]):C(),1;return}case 1:return N();case 10:return O(!0);default:switch(n.getToken()){case 11:let e=n.getTokenValue(),t=Number(e);isNaN(t)&&(w(2),t=0),l(t);break;case 7:l(null);break;case 8:l(!0);break;case 9:l(!1);break;default:return}return C(),1}}C(),17===n.getToken()?S.allowEmptyContent||w(4,[],[]):P()?17!==n.getToken()&&w(9,[],[]):w(4,[],[])}return y[0]}),Ve=function t(e,r,n=!1){if([s,o,l=!1]=[e,r,n],o>=s.offset&&o<s.offset+s.length||l&&o===s.offset+s.length){var i=e.children;if(Array.isArray(i))for(let e=0;e<i.length&&i[e].offset<=r;e++){var a=t(i[e],r,n);if(a)return a}return e}var s,o,l},je=function e(t){if(!t.parent||!t.parent.children)return[];let r=e(t.parent);var n;return"property"===t.parent.type?(n=t.parent.children[0].value,r.push(n)):"array"===t.parent.type&&-1!==(n=t.parent.children.indexOf(t))&&r.push(n),r},$e=function t(r){switch(r.type){case"array":return r.children.map(t);case"object":let e=Object.create(null);for(var n of r.children){var i=n.children[1];i&&(e[n.children[0].value]=t(i))}return e;case"null":case"string":case"number":case"boolean":return r.value;default:return}};function Fe(i,a){if(i===a)return 1;if(null!=i&&null!=a&&typeof i==typeof a&&"object"==typeof i&&Array.isArray(i)===Array.isArray(a)){let r,n;if(Array.isArray(i)){if(i.length!==a.length)return;for(r=0;r<i.length;r++)if(!Fe(i[r],a[r]))return}else{let e=[];for(n in i)e.push(n);e.sort();let t=[];for(n in a)t.push(n);if(t.sort(),!Fe(e,t))return;for(r=0;r<e.length;r++)if(!Fe(i[e[r]],a[e[r]]))return}return 1}}function ne(e){return"number"==typeof e}function ie(e){return typeof e<"u"}function ae(e){return"boolean"==typeof e}function De(e){return"string"==typeof e}function se(e){return"object"==typeof e&&null!==e&&!Array.isArray(e)}function Re(e,t){var r=e.length-t.length;return 0<r?e.lastIndexOf(t)===r:0==r&&e===t}function _e(e){let t="";!function(t,r){if(!(t.length<r.length)){for(let e=0;e<r.length;e++)if(t[e]!==r[e])return;return 1}}(e,"(?i)")||(e=e.substring(4),t="i");try{return new RegExp(e,t+"u")}catch{try{return new RegExp(e,t)}catch{return}}}function Ue(t){let r=0;for(let e=0;e<t.length;e++){r++;var n=t.charCodeAt(e);55296<=n&&n<=56319&&e++}return r}(a||{}).is=function(e){return"string"==typeof e},(q||(q={})).is=function(e){return"string"==typeof e},(J=B=B||{}).MIN_VALUE=0,J.MAX_VALUE=2147483647,J.is=function(e){return"number"==typeof e&&J.MIN_VALUE<=e&&e<=J.MAX_VALUE},(e=k=k||{}).create=function(e,t){return{line:e=e===Number.MAX_VALUE?B.MAX_VALUE:e,character:t=t===Number.MAX_VALUE?B.MAX_VALUE:t}},e.is=function(e){return o.objectLiteral(e)&&o.uinteger(e.line)&&o.uinteger(e.character)},(a=A=A||{}).create=function(e,t,r,n){if(o.uinteger(e)&&o.uinteger(t)&&o.uinteger(r)&&o.uinteger(n))return{start:k.create(e,t),end:k.create(r,n)};if(k.is(e)&&k.is(t))return{start:e,end:t};throw new Error(`Range#create called with invalid arguments[${e}, ${t}, ${r}, ${n}]`)},a.is=function(e){return o.objectLiteral(e)&&k.is(e.start)&&k.is(e.end)},(e=K=K||{}).create=function(e,t){return{uri:e,range:t}},e.is=function(e){return o.objectLiteral(e)&&A.is(e.range)&&(o.string(e.uri)||o.undefined(e.uri))},(a=i=i||{}).create=function(e,t,r,n){return{red:e,green:t,blue:r,alpha:n}},a.is=function(e){return o.objectLiteral(e)&&o.numberRange(e.red,0,1)&&o.numberRange(e.green,0,1)&&o.numberRange(e.blue,0,1)&&o.numberRange(e.alpha,0,1)},(e=W=W||{}).Comment="comment",e.Imports="imports",e.Region="region",(i=z=z||{}).create=function(e,t){return{location:e,message:t}},i.is=function(e){return o.defined(e)&&K.is(e.location)&&o.string(e.message)},(a=re=re||{}).Error=1,a.Warning=2,a.Information=3,a.Hint=4,(t||{}).is=function(e){return o.objectLiteral(e)&&o.string(e.href)},(e=y=y||{}).create=function(e,t,r,n,i,a){let s={range:e,message:t};return o.defined(r)&&(s.severity=r),o.defined(n)&&(s.code=n),o.defined(i)&&(s.source=i),o.defined(a)&&(s.relatedInformation=a),s},e.is=function(e){var t;return o.defined(e)&&A.is(e.range)&&o.string(e.message)&&(o.number(e.severity)||o.undefined(e.severity))&&(o.integer(e.code)||o.string(e.code)||o.undefined(e.code))&&(o.undefined(e.codeDescription)||o.string(null==(t=e.codeDescription)?void 0:t.href))&&(o.string(e.source)||o.undefined(e.source))&&(o.undefined(e.relatedInformation)||o.typedArray(e.relatedInformation,z.is))},(i=H=H||{}).create=function(e,t,...r){let n={title:e,command:t};return o.defined(r)&&0<r.length&&(n.arguments=r),n},i.is=function(e){return o.defined(e)&&o.string(e.title)&&o.string(e.command)},(a=G=G||{}).replace=function(e,t){return{range:e,newText:t}},a.insert=function(e,t){return{range:{start:e,end:e},newText:t}},a.del=function(e){return{range:e,newText:""}},a.is=function(e){return o.objectLiteral(e)&&o.string(e.newText)&&A.is(e.range)},(t=l=l||{}).create=function(e,t,r){let n={label:e};return void 0!==t&&(n.needsConfirmation=t),void 0!==r&&(n.description=r),n},t.is=function(e){return o.objectLiteral(e)&&o.string(e.label)&&(o.boolean(e.needsConfirmation)||void 0===e.needsConfirmation)&&(o.string(e.description)||void 0===e.description)},(X||(X={})).is=function(e){return o.string(e)},(e=Z=Z||{}).create=function(e,t){return{textDocument:e,edits:t}},e.is=function(e){return o.defined(e)&&te.is(e.textDocument)&&Array.isArray(e.edits)},(i=Q=Q||{}).create=function(e,t,r){let n={kind:"create",uri:e};return void 0===t||void 0===t.overwrite&&void 0===t.ignoreIfExists||(n.options=t),void 0!==r&&(n.annotationId=r),n},i.is=function(e){return e&&"create"===e.kind&&o.string(e.uri)&&(void 0===e.options||(void 0===e.options.overwrite||o.boolean(e.options.overwrite))&&(void 0===e.options.ignoreIfExists||o.boolean(e.options.ignoreIfExists)))&&(void 0===e.annotationId||X.is(e.annotationId))},(a=Y=Y||{}).create=function(e,t,r,n){let i={kind:"rename",oldUri:e,newUri:t};return void 0===r||void 0===r.overwrite&&void 0===r.ignoreIfExists||(i.options=r),void 0!==n&&(i.annotationId=n),i},a.is=function(e){return e&&"rename"===e.kind&&o.string(e.oldUri)&&o.string(e.newUri)&&(void 0===e.options||(void 0===e.options.overwrite||o.boolean(e.options.overwrite))&&(void 0===e.options.ignoreIfExists||o.boolean(e.options.ignoreIfExists)))&&(void 0===e.annotationId||X.is(e.annotationId))},(l=ee=ee||{}).create=function(e,t,r){let n={kind:"delete",uri:e};return void 0===t||void 0===t.recursive&&void 0===t.ignoreIfNotExists||(n.options=t),void 0!==r&&(n.annotationId=r),n},l.is=function(e){return e&&"delete"===e.kind&&o.string(e.uri)&&(void 0===e.options||(void 0===e.options.recursive||o.boolean(e.options.recursive))&&(void 0===e.options.ignoreIfNotExists||o.boolean(e.options.ignoreIfNotExists)))&&(void 0===e.annotationId||X.is(e.annotationId))},(xe||(xe={})).is=function(e){let t=e;return t&&(void 0!==t.changes||void 0!==t.documentChanges)&&(void 0===t.documentChanges||t.documentChanges.every(e=>o.string(e.kind)?Q.is(e)||Y.is(e)||ee.is(e):Z.is(e)))},(t=te=te||{}).create=function(e,t){return{uri:e,version:t}},t.is=function(e){return o.defined(e)&&o.string(e.uri)&&(null===e.version||o.integer(e.version))},(me=r=r||{}).PlainText="plaintext",me.Markdown="markdown",me.is=function(e){return e===me.PlainText||e===me.Markdown},(pe||(pe={})).is=function(e){var t=e;return o.objectLiteral(e)&&r.is(t.kind)&&o.string(t.value)},(e=v=v||{}).Text=1,e.Method=2,e.Function=3,e.Constructor=4,e.Field=5,e.Variable=6,e.Class=7,e.Interface=8,e.Module=9,e.Property=10,e.Unit=11,e.Value=12,e.Enum=13,e.Keyword=14,e.Snippet=15,e.Color=16,e.File=17,e.Reference=18,e.Folder=19,e.EnumMember=20,e.Constant=21,e.Struct=22,e.Event=23,e.Operator=24,e.TypeParameter=25,(i=b=b||{}).PlainText=1,i.Snippet=2,(qe||{}).is=function(e){return e&&(o.string(e.detail)||void 0===e.detail)&&(o.string(e.description)||void 0===e.description)},(ge||(ge={})).create=function(e){return{label:e}},(n||{}).create=function(e,t){return{items:e||[],isIncomplete:!!t}},(a=ye=ye||{}).fromPlainText=function(e){return e.replace(/[\\`*_{}[\]()#+\-.!]/g,"\\$&")},a.is=function(e){return o.string(e)||o.objectLiteral(e)&&o.string(e.language)&&o.string(e.value)},(s||{}).is=function(e){var t=e;return!!t&&o.objectLiteral(t)&&(pe.is(t.contents)||ye.is(t.contents)||o.typedArray(t.contents,ye.is))&&(void 0===e.range||A.is(e.range))},(Nt||{}).create=function(e,t){return t?{label:e,documentation:t}:{label:e}},(d||{}).create=function(e,t,...r){let n={label:e};return o.defined(t)&&(n.documentation=t),o.defined(r)?n.parameters=r:n.parameters=[],n},(Ft||{}).create=function(e,t){let r={range:e};return o.number(t)&&(r.kind=t),r},(l=p=p||{}).File=1,l.Module=2,l.Namespace=3,l.Package=4,l.Class=5,l.Method=6,l.Property=7,l.Field=8,l.Constructor=9,l.Enum=10,l.Interface=11,l.Function=12,l.Variable=13,l.Constant=14,l.String=15,l.Number=16,l.Boolean=17,l.Array=18,l.Object=19,l.Key=20,l.Null=21,l.EnumMember=22,l.Struct=23,l.Event=24,l.Operator=25,l.TypeParameter=26,(ve||{}).create=function(e,t,r,n,i){let a={name:e,kind:t,location:{uri:n,range:r}};return i&&(a.containerName=i),a},(be||{}).create=function(e,t,r,n){return void 0!==n?{name:e,kind:t,location:{uri:r,range:n}}:{name:e,kind:t,location:{uri:r}}},(xe=Se=Se||{}).Invoked=1,xe.Automatic=2,(Ae=ke=ke||{}).create=function(e,t){return{range:e,parent:t}},Ae.is=function(e){return o.objectLiteral(e)&&A.is(e.range)&&(void 0===e.parent||Ae.is(e.parent))},(Te||{}).is=function(e){return o.objectLiteral(e)&&(void 0===e.resultId||"string"==typeof e.resultId)&&Array.isArray(e.data)&&(0===e.data.length||"number"==typeof e.data[0])},(t=Ce=Ce||{}).Type=1,t.Parameter=2,t.is=function(e){return 1===e||2===e},(e=we=we||{}).create=function(e){return{value:e}},e.is=function(e){return o.objectLiteral(e)&&(void 0===e.tooltip||o.string(e.tooltip)||pe.is(e.tooltip))&&(void 0===e.location||K.is(e.location))&&(void 0===e.command||H.is(e.command))},(Oe||{}).createSnippet=function(e){return{kind:"snippet",value:e}},(Ne||{}).create=function(e,t,r,n){return{insertText:e,filterText:t,range:r,command:n}},(Pe||{}).create=function(e){return{items:e}},(Ie||{}).create=function(e,t){return{range:e,text:t}},(Le||{}).create=function(e,t){return{triggerKind:e,selectedCompletionInfo:t}},(Ee||{}).is=function(e){return o.objectLiteral(e)&&q.is(e.uri)&&o.string(e.name)};var o;{var i=o=o||{};let n=Object.prototype.toString;i.defined=function(e){return typeof e<"u"},i.undefined=function(e){return"u"<typeof e},i.boolean=function(e){return!0===e||!1===e},i.string=function(e){return"[object String]"===n.call(e)},i.number=function(e){return"[object Number]"===n.call(e)},i.numberRange=function(e,t,r){return"[object Number]"===n.call(e)&&t<=e&&e<=r},i.integer=function(e){return"[object Number]"===n.call(e)&&-2147483648<=e&&e<=2147483647},i.uinteger=function(e){return"[object Number]"===n.call(e)&&0<=e&&e<=2147483647},i.func=function(e){return"[object Function]"===n.call(e)},i.objectLiteral=function(e){return null!==e&&"object"==typeof e},i.typedArray=function(e,t){return Array.isArray(e)&&e.every(t)}}var m,qe,oe,n,le,a,Be,Je,Ke=class fr{constructor(e,t,r,n){this._uri=e,this._languageId=t,this._version=r,this._content=n,this._lineOffsets=void 0}get uri(){return this._uri}get languageId(){return this._languageId}get version(){return this._version}getText(e){var t;return e?(t=this.offsetAt(e.start),e=this.offsetAt(e.end),this._content.substring(t,e)):this._content}update(e,t){for(var a of e)if(fr.isIncremental(a)){var s=ze(a.range),o=this.offsetAt(s.start),l=this.offsetAt(s.end);this._content=this._content.substring(0,o)+a.text+this._content.substring(l,this._content.length);let r=Math.max(s.start.line,0),e=Math.max(s.end.line,0),n=this._lineOffsets,i=We(a.text,!1,o);if(e-r===i.length)for(let e=0,t=i.length;e<t;e++)n[e+r+1]=i[e];else i.length<1e4?n.splice(r+1,e-r,...i):this._lineOffsets=n=n.slice(0,r+1).concat(i,n.slice(e+1));var c=a.text.length-(l-o);if(0!=c)for(let e=r+1+i.length,t=n.length;e<t;e++)n[e]=n[e]+c}else{if(!fr.isFull(a))throw new Error("Unknown change event received");this._content=a.text,this._lineOffsets=void 0}this._version=t}getLineOffsets(){return void 0===this._lineOffsets&&(this._lineOffsets=We(this._content,!0)),this._lineOffsets}positionAt(e){e=Math.max(Math.min(e,this._content.length),0);let t=this.getLineOffsets(),r=0,n=t.length;if(0===n)return{line:0,character:e};for(;r<n;){var i=Math.floor((r+n)/2);t[i]>e?n=i:r=i+1}var a=r-1;return{line:a,character:e-t[a]}}offsetAt(e){var t=this.getLineOffsets();if(e.line>=t.length)return this._content.length;if(e.line<0)return 0;var r=t[e.line],t=e.line+1<t.length?t[e.line+1]:this._content.length;return Math.max(Math.min(r+e.character,t),r)}get lineCount(){return this.getLineOffsets().length}static isIncremental(e){return null!=e&&"string"==typeof e.text&&void 0!==e.range&&(void 0===e.rangeLength||"number"==typeof e.rangeLength)}static isFull(e){return null!=e&&"string"==typeof e.text&&void 0===e.range&&void 0===e.rangeLength}};function We(t,e,r=0){let n=e?[r]:[];for(let e=0;e<t.length;e++){var i=t.charCodeAt(e);13!==i&&10!==i||(13===i&&e+1<t.length&&10===t.charCodeAt(e+1)&&e++,n.push(r+e+1))}return n}function ze(e){var t=e.start,r=e.end;return t.line>r.line||t.line===r.line&&t.character>r.character?{start:r,end:t}:e}function He(e){var t=ze(e.range);return t!==e.range?{newText:e.newText,range:t}:e}function ce(...e){let r=e[0],t,n,i;if("string"==typeof r)t=r,n=r,e.splice(0,1),i=e&&"object"==typeof e[0]?e[0]:e;else{if(r instanceof Array){e=e.slice(1);if(r.length!==e.length+1)throw new Error("expected a string as the first argument to l10n.t");let t=r[0];for(let e=1;e<r.length;e++)t+=`{${e-1}}`+r[e];return ce(t,...e)}n=r.message,t=n,r.comment&&0<r.comment.length&&(t+="/"+(Array.isArray(r.comment)?r.comment.join(""):r.comment)),i=r.args??{}}e=Je?.[t];return e?"string"==typeof e?Xe(e,i):e.comment?Xe(e.message,i):Xe(n,i):Xe(n,i)}(qe=m=m||{}).create=function(e,t,r,n){return new Ke(e,t,r,n)},qe.update=function(e,t,r){if(e instanceof Ke)return e.update(t,r),e;throw new Error("TextDocument.update: document must be created by TextDocument.create")},qe.applyEdits=function(e,t){let r=e.getText(),n=function e(t,r){if(t.length<=1)return t;let n=t.length/2|0,i=t.slice(0,n),a=t.slice(n);e(i,r),e(a,r);let s=0,o=0,l=0;for(;s<i.length&&o<a.length;)r(i[s],a[o])<=0?t[l++]=i[s++]:t[l++]=a[o++];for(;s<i.length;)t[l++]=i[s++];for(;o<a.length;)t[l++]=a[o++];return t}(t.map(He),(e,t)=>{var r=e.range.start.line-t.range.start.line;return 0==r?e.range.start.character-t.range.start.character:r}),i=0,a=[];for(var s of n){var o=e.offsetAt(s.range.start);if(o<i)throw new Error("Overlapping edit");o>i&&a.push(r.substring(i,o)),s.newText.length&&a.push(s.newText),i=e.offsetAt(s.range.end)}return a.push(r.substr(i)),a.join("")},(n=oe=oe||{})[n.Undefined=0]="Undefined",n[n.EnumValueMismatch=1]="EnumValueMismatch",n[n.Deprecated=2]="Deprecated",n[n.UnexpectedEndOfComment=257]="UnexpectedEndOfComment",n[n.UnexpectedEndOfString=258]="UnexpectedEndOfString",n[n.UnexpectedEndOfNumber=259]="UnexpectedEndOfNumber",n[n.InvalidUnicode=260]="InvalidUnicode",n[n.InvalidEscapeCharacter=261]="InvalidEscapeCharacter",n[n.InvalidCharacter=262]="InvalidCharacter",n[n.PropertyExpected=513]="PropertyExpected",n[n.CommaExpected=514]="CommaExpected",n[n.ColonExpected=515]="ColonExpected",n[n.ValueExpected=516]="ValueExpected",n[n.CommaOrCloseBacketExpected=517]="CommaOrCloseBacketExpected",n[n.CommaOrCloseBraceExpected=518]="CommaOrCloseBraceExpected",n[n.TrailingComma=519]="TrailingComma",n[n.DuplicateKey=520]="DuplicateKey",n[n.CommentNotPermitted=521]="CommentNotPermitted",n[n.PropertyKeysMustBeDoublequoted=528]="PropertyKeysMustBeDoublequoted",n[n.SchemaResolveError=768]="SchemaResolveError",n[n.SchemaUnsupportedFeature=769]="SchemaUnsupportedFeature",(a=le=le||{})[a.v3=3]="v3",a[a.v4=4]="v4",a[a.v6=6]="v6",a[a.v7=7]="v7",a[a.v2019_09=19]="v2019_09",a[a.v2020_12=20]="v2020_12",(Be=Be||{}).LATEST={textDocument:{completion:{completionItem:{documentationFormat:[r.Markdown,r.PlainText],commitCharactersSupport:!0,labelDetailsSupport:!0}}}};var Ge=/{([^}]+)}/g;function Xe(e,r){return 0===Object.keys(r).length?e:e.replace(Ge,(e,t)=>r[t]??e)}var Ze={"color-hex":{errorMessage:ce("Invalid color format. Use #RGB, #RGBA, #RRGGBB or #RRGGBBAA."),pattern:/^#([0-9A-Fa-f]{3,4}|([0-9A-Fa-f]{2}){3,4})$/},"date-time":{errorMessage:ce("String is not a RFC3339 date-time."),pattern:/^(\d{4})-(0[1-9]|1[0-2])-(0[1-9]|[12][0-9]|3[01])T([01][0-9]|2[0-3]):([0-5][0-9]):([0-5][0-9]|60)(\.[0-9]+)?(Z|(\+|-)([01][0-9]|2[0-3]):([0-5][0-9]))$/i},date:{errorMessage:ce("String is not a RFC3339 date."),pattern:/^(\d{4})-(0[1-9]|1[0-2])-(0[1-9]|[12][0-9]|3[01])$/i},time:{errorMessage:ce("String is not a RFC3339 time."),pattern:/^([01][0-9]|2[0-3]):([0-5][0-9]):([0-5][0-9]|60)(\.[0-9]+)?(Z|(\+|-)([01][0-9]|2[0-3]):([0-5][0-9]))$/i},email:{errorMessage:ce("String is not an e-mail address."),pattern:/^(([^<>()\[\]\\.,;:\s@"]+(\.[^<>()\[\]\\.,;:\s@"]+)*)|(".+"))@((\[[0-9]{1,3}\.[0-9]{1,3}\.[0-9]{1,3}\.[0-9]{1,3}])|(([a-zA-Z0-9-]+\.)+[a-zA-Z]{2,}))$/},hostname:{errorMessage:ce("String is not a hostname."),pattern:/^(?=.{1,253}\.?$)[a-z0-9](?:[a-z0-9-]{0,61}[a-z0-9])?(?:\.[a-z0-9](?:[-0-9a-z]{0,61}[0-9a-z])?)*\.?$/i},ipv4:{errorMessage:ce("String is not an IPv4 address."),pattern:/^(?:(?:25[0-5]|2[0-4]\d|1\d\d|[1-9]?\d)\.){3}(?:25[0-5]|2[0-4]\d|1\d\d|[1-9]?\d)$/},ipv6:{errorMessage:ce("String is not an IPv6 address."),pattern:/^((([0-9a-f]{1,4}:){7}([0-9a-f]{1,4}|:))|(([0-9a-f]{1,4}:){6}(:[0-9a-f]{1,4}|((25[0-5]|2[0-4]\d|1\d\d|[1-9]?\d)(\.(25[0-5]|2[0-4]\d|1\d\d|[1-9]?\d)){3})|:))|(([0-9a-f]{1,4}:){5}(((:[0-9a-f]{1,4}){1,2})|:((25[0-5]|2[0-4]\d|1\d\d|[1-9]?\d)(\.(25[0-5]|2[0-4]\d|1\d\d|[1-9]?\d)){3})|:))|(([0-9a-f]{1,4}:){4}(((:[0-9a-f]{1,4}){1,3})|((:[0-9a-f]{1,4})?:((25[0-5]|2[0-4]\d|1\d\d|[1-9]?\d)(\.(25[0-5]|2[0-4]\d|1\d\d|[1-9]?\d)){3}))|:))|(([0-9a-f]{1,4}:){3}(((:[0-9a-f]{1,4}){1,4})|((:[0-9a-f]{1,4}){0,2}:((25[0-5]|2[0-4]\d|1\d\d|[1-9]?\d)(\.(25[0-5]|2[0-4]\d|1\d\d|[1-9]?\d)){3}))|:))|(([0-9a-f]{1,4}:){2}(((:[0-9a-f]{1,4}){1,5})|((:[0-9a-f]{1,4}){0,3}:((25[0-5]|2[0-4]\d|1\d\d|[1-9]?\d)(\.(25[0-5]|2[0-4]\d|1\d\d|[1-9]?\d)){3}))|:))|(([0-9a-f]{1,4}:){1}(((:[0-9a-f]{1,4}){1,6})|((:[0-9a-f]{1,4}){0,4}:((25[0-5]|2[0-4]\d|1\d\d|[1-9]?\d)(\.(25[0-5]|2[0-4]\d|1\d\d|[1-9]?\d)){3}))|:))|(:(((:[0-9a-f]{1,4}){1,7})|((:[0-9a-f]{1,4}){0,5}:((25[0-5]|2[0-4]\d|1\d\d|[1-9]?\d)(\.(25[0-5]|2[0-4]\d|1\d\d|[1-9]?\d)){3}))|:)))$/i}},s=class{constructor(e,t,r=0){this.offset=t,this.length=r,this.parent=e}get children(){return[]}toString(){return"type: "+this.type+" ("+this.offset+"/"+this.length+")"+(this.parent?" parent: {"+this.parent.toString()+"}":"")}},Qe=class extends s{constructor(e,t){super(e,t),this.type="null",this.value=null}},Ye=class extends s{constructor(e,t,r){super(e,r),this.type="boolean",this.value=t}},et=class extends s{constructor(e,t){super(e,t),this.type="array",this.items=[]}get children(){return this.items}},tt=class extends s{constructor(e,t){super(e,t),this.type="number",this.isInteger=!0,this.value=Number.NaN}},rt=class extends s{constructor(e,t,r){super(e,t,r),this.type="string",this.value=""}},nt=class extends s{constructor(e,t,r){super(e,t),this.type="property",this.colonOffset=-1,this.keyNode=r}get children(){return this.valueNode?[this.keyNode,this.valueNode]:[this.keyNode]}},it=class extends s{constructor(e,t){super(e,t),this.type="object",this.properties=[]}get children(){return this.properties}};function ue(e){return ae(e)?e?{}:{not:{}}:e}var at={"http://json-schema.org/draft-03/schema#":le.v3,"http://json-schema.org/draft-04/schema#":le.v4,"http://json-schema.org/draft-06/schema#":le.v6,"http://json-schema.org/draft-07/schema#":le.v7,"https://json-schema.org/draft/2019-09/schema":le.v2019_09,"https://json-schema.org/draft/2020-12/schema":le.v2020_12},st=class{constructor(e){this.schemaDraft=e}},ot=class dr{constructor(e=-1,t){this.focusOffset=e,this.exclude=t,this.schemas=[]}add(e){this.schemas.push(e)}merge(e){Array.prototype.push.apply(this.schemas,e.schemas)}include(e){return(-1===this.focusOffset||ut(e,this.focusOffset))&&e!==this.exclude}newSub(){return new dr(-1,this.exclude)}},lt=class{constructor(){}get schemas(){return[]}add(e){}merge(e){}include(e){return!0}newSub(){return this}},he=(lt.instance=new lt,class{constructor(){this.problems=[],this.propertiesMatches=0,this.processedProperties=new Set,this.propertiesValueMatches=0,this.primaryValueMatches=0,this.enumValueMatch=!1,this.enumValues=void 0}hasProblems(){return!!this.problems.length}merge(e){this.problems=this.problems.concat(e.problems),this.propertiesMatches+=e.propertiesMatches,this.propertiesValueMatches+=e.propertiesValueMatches,this.mergeProcessedProperties(e)}mergeEnumValues(e){if(!this.enumValueMatch&&!e.enumValueMatch&&this.enumValues&&e.enumValues){this.enumValues=this.enumValues.concat(e.enumValues);for(var t of this.problems)t.code===oe.EnumValueMismatch&&(t.message=ce("Value is not accepted. Valid values: {0}.",this.enumValues.map(e=>JSON.stringify(e)).join(", ")))}}mergePropertyMatch(e){this.problems=this.problems.concat(e.problems),this.propertiesMatches++,(e.enumValueMatch||!e.hasProblems()&&e.propertiesMatches)&&this.propertiesValueMatches++,e.enumValueMatch&&e.enumValues&&1===e.enumValues.length&&this.primaryValueMatches++}mergeProcessedProperties(e){e.processedProperties.forEach(e=>this.processedProperties.add(e))}compare(e){var t=this.hasProblems();return t!==e.hasProblems()?t?-1:1:this.enumValueMatch!==e.enumValueMatch?e.enumValueMatch?-1:1:this.primaryValueMatches!==e.primaryValueMatches?this.primaryValueMatches-e.primaryValueMatches:this.propertiesValueMatches!==e.propertiesValueMatches?this.propertiesValueMatches-e.propertiesValueMatches:this.propertiesMatches-e.propertiesMatches}});function fe(e){return $e(e)}function ct(e){return je(e)}function ut(e,t,r=!1){return t>=e.offset&&t<e.offset+e.length||r&&t===e.offset+e.length}var ht=class{constructor(e,t=[],r=[]){this.root=e,this.syntaxErrors=t,this.comments=r}getNodeFromOffset(e,t=!1){if(this.root)return Ve(this.root,e,t)}visit(i){if(this.root){let n=e=>{let t=i(e),r=e.children;if(Array.isArray(r))for(let e=0;e<r.length&&t;e++)t=n(r[e]);return t};n(this.root)}}validate(r,t,n=re.Warning,i){if(this.root&&t){let e=new he;return de(this.root,t,e,lt.instance,new st(i??ft(t))),e.problems.map(e=>{var t=A.create(r.positionAt(e.location.offset),r.positionAt(e.location.offset+e.location.length));return y.create(t,e.message,e.severity??n,e.code)})}}getMatchingSchemas(e,t=-1,r){return this.root&&e?(t=new ot(t,r),r=ft(e),r=new st(r),de(this.root,e,new he,t,r),t.schemas):[]}};function ft(e,t=le.v2020_12){e=e.$schema;return e?at[e]??t:t}function de(e,f,d,m,p){if(e&&m.include(e)){if("property"===e.type)return de(e.valueNode,f,d,m,p);let h=e;switch(function(){function e(e){return h.type===e||"integer"===e&&"number"===h.type&&h.isInteger}if(Array.isArray(f.type)?f.type.some(e)||d.problems.push({location:{offset:h.offset,length:h.length},message:f.errorMessage||ce("Incorrect type. Expected one of {0}.",f.type.join(", "))}):!f.type||e(f.type)||d.problems.push({location:{offset:h.offset,length:h.length},message:f.errorMessage||ce('Incorrect type. Expected "{0}".',f.type)}),Array.isArray(f.allOf))for(var t of f.allOf){var r=new he,n=m.newSub();de(h,ue(t),r,n,p),d.merge(r),m.merge(n)}var i=ue(f.not);if(i){let e=new he,t=m.newSub();de(h,i,e,t,p),e.hasProblems()||d.problems.push({location:{offset:h.offset,length:h.length},message:f.errorMessage||ce("Matches a schema that is not allowed.")});for(var a of t.schemas)a.inverted=!a.inverted,m.add(a)}let s=(e,n)=>{let i=[],a;for(var s of e){let e=ue(s),t=new he,r=m.newSub();de(h,e,t,r,p),t.hasProblems()||i.push(e),a?n||t.hasProblems()||a.validationResult.hasProblems()?0<(s=t.compare(a.validationResult))?a={schema:e,validationResult:t,matchingSchemas:r}:0===s&&(a.matchingSchemas.merge(r),a.validationResult.mergeEnumValues(t)):(a.matchingSchemas.merge(r),a.validationResult.propertiesMatches+=t.propertiesMatches,a.validationResult.propertiesValueMatches+=t.propertiesValueMatches,a.validationResult.mergeProcessedProperties(t)):a={schema:e,validationResult:t,matchingSchemas:r}}return 1<i.length&&n&&d.problems.push({location:{offset:h.offset,length:1},message:ce("Matches multiple schemas when only one must validate.")}),a&&(d.merge(a.validationResult),m.merge(a.matchingSchemas)),i.length},o=(Array.isArray(f.anyOf)&&s(f.anyOf,!1),Array.isArray(f.oneOf)&&s(f.oneOf,!0),e=>{var t=new he,r=m.newSub();de(h,ue(e),t,r,p),d.merge(t),m.merge(r)}),l=ue(f.if);if(l&&((e,t,r)=>{let n=ue(e),i=new he,a=m.newSub();de(h,n,i,a,p),m.merge(a),d.mergeProcessedProperties(i),i.hasProblems()?r&&o(r):t&&o(t)})(l,ue(f.then),ue(f.else)),Array.isArray(f.enum)){let e=fe(h),t=!1;for(var c of f.enum)if(Fe(e,c)){t=!0;break}d.enumValues=f.enum,(d.enumValueMatch=t)||d.problems.push({location:{offset:h.offset,length:h.length},code:oe.EnumValueMismatch,message:f.errorMessage||ce("Value is not accepted. Valid values: {0}.",f.enum.map(e=>JSON.stringify(e)).join(", "))})}ie(f.const)&&(Fe(fe(h),f.const)?d.enumValueMatch=!0:(d.problems.push({location:{offset:h.offset,length:h.length},code:oe.EnumValueMismatch,message:f.errorMessage||ce("Value must be {0}.",JSON.stringify(f.const))}),d.enumValueMatch=!1),d.enumValues=[f.const]);i=f.deprecationMessage;{var u;(i||f.deprecated)&&(i=i||ce("Value is deprecated"),u="property"===h.parent?.type?h.parent:h,d.problems.push({location:{offset:u.offset,length:u.length},severity:re.Warning,message:i,code:oe.Deprecated}))}}(),h.type){case"object":{var a=h;let i=Object.create(null),r=new Set;for(var t of a.properties){var F=t.keyNode.value;i[F]=t.valueNode,r.add(F)}if(Array.isArray(f.required))for(var D of f.required){var n;i[D]||(n=a.parent&&"property"===a.parent.type&&a.parent.keyNode,n=n?{offset:n.offset,length:n.length}:{offset:a.offset,length:1},d.problems.push({location:n,message:ce('Missing property "{0}".',D)}))}var s=e=>{r.delete(e),d.processedProperties.add(e)};if(f.properties)for(var o of Object.keys(f.properties)){s(o);var l,c=f.properties[o],u=i[o];u&&(ae(c)?c?(d.propertiesMatches++,d.propertiesValueMatches++):(l=u.parent,d.problems.push({location:{offset:l.keyNode.offset,length:l.keyNode.length},message:f.errorMessage||ce("Property {0} is not allowed.",o)})):(l=new he,de(u,c,l,m,p),d.mergePropertyMatch(l)))}if(f.patternProperties)for(var R of Object.keys(f.patternProperties)){let t=_e(R);if(t){let e=[];for(var g of r){var y,v,b;t.test(g)&&(e.push(g),(y=i[g])&&(ae(v=f.patternProperties[R])?v?(d.propertiesMatches++,d.propertiesValueMatches++):(b=y.parent,d.problems.push({location:{offset:b.keyNode.offset,length:b.keyNode.length},message:f.errorMessage||ce("Property {0} is not allowed.",g)})):(b=new he,de(y,v,b,m,p),d.mergePropertyMatch(b))))}e.forEach(s)}}var S=f.additionalProperties;if(void 0!==S)for(var x of r){s(x);var k,A=i[x];A&&(!1===S?(k=A.parent,d.problems.push({location:{offset:k.keyNode.offset,length:k.keyNode.length},message:f.errorMessage||ce("Property {0} is not allowed.",x)})):!0!==S&&(k=new he,de(A,S,k,m,p),d.mergePropertyMatch(k)))}var T=f.unevaluatedProperties;if(void 0!==T){let e=[];for(var C of r){var w,O;d.processedProperties.has(C)||(e.push(C),(w=i[C])&&(!1===T?(O=w.parent,d.problems.push({location:{offset:O.keyNode.offset,length:O.keyNode.length},message:f.errorMessage||ce("Property {0} is not allowed.",C)})):!0!==T&&(O=new he,de(w,T,O,m,p),d.mergePropertyMatch(O))))}e.forEach(s)}if(ne(f.maxProperties)&&a.properties.length>f.maxProperties&&d.problems.push({location:{offset:a.offset,length:a.length},message:ce("Object has more properties than limit of {0}.",f.maxProperties)}),ne(f.minProperties)&&a.properties.length<f.minProperties&&d.problems.push({location:{offset:a.offset,length:a.length},message:ce("Object has fewer properties than the required number of {0}",f.minProperties)}),f.dependentRequired)for(var N in f.dependentRequired){var _=i[N],U=f.dependentRequired[N];_&&Array.isArray(U)&&E(N,U)}if(f.dependentSchemas)for(var P in f.dependentSchemas){var q=i[P],B=f.dependentSchemas[P];q&&se(B)&&E(P,B)}if(f.dependencies)for(var I in f.dependencies)i[I]&&E(I,f.dependencies[I]);var J=ue(f.propertyNames);if(J)for(var L of a.properties){L=L.keyNode;L&&de(L,J,d,lt.instance,p)}function E(e,t){if(Array.isArray(t))for(var r of t)i[r]?d.propertiesValueMatches++:d.problems.push({location:{offset:a.offset,length:a.length},message:ce("Object is missing property {0} required by property {1}.",r,e)});else{var n,t=ue(t);t&&(n=new he,de(a,t,n,m,p),d.mergePropertyMatch(n))}}}break;case"array":{var i=h;let e,t,r=(t=p.schemaDraft>=le.v2020_12?(e=f.prefixItems,Array.isArray(f.items)?void 0:f.items):(e=Array.isArray(f.items)?f.items:void 0,Array.isArray(f.items)?f.additionalItems:f.items),0);if(void 0!==e)for(var K=Math.min(e.length,i.items.length);r<K;r++){var W=ue(e[r]),z=new he,H=i.items[r];H&&(de(H,W,z,m,p),d.mergePropertyMatch(z)),d.processedProperties.add(String(r))}if(void 0!==t&&r<i.items.length)if("boolean"==typeof t)for(!1===t&&d.problems.push({location:{offset:i.offset,length:i.length},message:ce("Array has too many items according to schema. Expected {0} or fewer.",r)});r<i.items.length;r++)d.processedProperties.add(String(r)),d.propertiesValueMatches++;else for(;r<i.items.length;r++){var G=new he;de(i.items[r],t,G,m,p),d.mergePropertyMatch(G),d.processedProperties.add(String(r))}var X=ue(f.contains);if(X){let n=0;for(let r=0;r<i.items.length;r++){let e=i.items[r],t=new he;de(e,X,t,lt.instance,p),t.hasProblems()||(n++,p.schemaDraft>=le.v2020_12&&d.processedProperties.add(String(r)))}0!==n||ne(f.minContains)||d.problems.push({location:{offset:i.offset,length:i.length},message:f.errorMessage||ce("Array does not contain required item.")}),ne(f.minContains)&&n<f.minContains&&d.problems.push({location:{offset:i.offset,length:i.length},message:f.errorMessage||ce("Array has too few items that match the contains contraint. Expected {0} or more.",f.minContains)}),ne(f.maxContains)&&n>f.maxContains&&d.problems.push({location:{offset:i.offset,length:i.length},message:f.errorMessage||ce("Array has too many items that match the contains contraint. Expected {0} or less.",f.maxContains)})}var Z=f.unevaluatedItems;if(void 0!==Z)for(let e=0;e<i.items.length;e++){var M;d.processedProperties.has(String(e))||(!1===Z?d.problems.push({location:{offset:i.offset,length:i.length},message:ce("Item does not match any validation rule from the array.")}):(M=new he,de(i.items[e],f.unevaluatedItems,M,m,p),d.mergePropertyMatch(M))),d.processedProperties.add(String(e)),d.propertiesValueMatches++}if(ne(f.minItems)&&i.items.length<f.minItems&&d.problems.push({location:{offset:i.offset,length:i.length},message:ce("Array has too few items. Expected {0} or more.",f.minItems)}),ne(f.maxItems)&&i.items.length>f.maxItems&&d.problems.push({location:{offset:i.offset,length:i.length},message:ce("Array has too many items. Expected {0} or fewer.",f.maxItems)}),!0===f.uniqueItems){let n=fe(i);!function(){for(let t=0;t<n.length-1;t++){var r=n[t];for(let e=t+1;e<n.length;e++)if(Fe(r,n[e]))return!0}return!1}()||d.problems.push({location:{offset:i.offset,length:i.length},message:ce("Array has duplicate items.")})}}break;case"string":var Q,r=h;if(ne(f.minLength)&&Ue(r.value)<f.minLength&&d.problems.push({location:{offset:r.offset,length:r.length},message:ce("String is shorter than the minimum length of {0}.",f.minLength)}),ne(f.maxLength)&&Ue(r.value)>f.maxLength&&d.problems.push({location:{offset:r.offset,length:r.length},message:ce("String is longer than the maximum length of {0}.",f.maxLength)}),!De(f.pattern)||_e(f.pattern)?.test(r.value)||d.problems.push({location:{offset:r.offset,length:r.length},message:f.patternErrorMessage||f.errorMessage||ce('String does not match the pattern of "{0}".',f.pattern)}),f.format)switch(f.format){case"uri":case"uri-reference":{let e;r.value?(Q=/^(([^:/?#]+?):)?(\/\/([^/?#]*))?([^?#]*)(\?([^#]*))?(#(.*))?/.exec(r.value))?Q[2]||"uri"!==f.format||(e=ce("URI with a scheme is expected.")):e=ce("URI is expected."):e=ce("URI expected."),e&&d.problems.push({location:{offset:r.offset,length:r.length},message:f.patternErrorMessage||f.errorMessage||ce("String is not a URI: {0}",e)})}break;case"color-hex":case"date-time":case"date":case"time":case"email":case"hostname":case"ipv4":case"ipv6":let e=Ze[f.format];r.value&&e.pattern.exec(r.value)||d.problems.push({location:{offset:r.offset,length:r.length},message:f.patternErrorMessage||f.errorMessage||e.errorMessage})}break;case"number":var V=h,j=V.value;function Y(e){e=/^(-?\d+)(?:\.(\d+))?(?:e([-+]\d+))?$/.exec(e.toString());return e&&{value:Number(e[1]+(e[2]||"")),multiplier:(e[2]?.length||0)-(parseInt(e[3])||0)}}if(ne(f.multipleOf)){let r=-1;if(Number.isInteger(f.multipleOf))r=j%f.multipleOf;else{let e=Y(f.multipleOf),t=Y(j);e&&t&&($=10**Math.abs(t.multiplier-e.multiplier),t.multiplier<e.multiplier?t.value*=$:e.value*=$,r=t.value%e.value)}0!==r&&d.problems.push({location:{offset:V.offset,length:V.length},message:ce("Value is not divisible by {0}.",f.multipleOf)})}function ee(e,t){return ne(t)?t:ae(t)&&t?e:void 0}function te(e,t){if(!ae(t)||!t)return e}ne($=ee(f.minimum,f.exclusiveMinimum))&&j<=$&&d.problems.push({location:{offset:V.offset,length:V.length},message:ce("Value is below the exclusive minimum of {0}.",$)});var $=ee(f.maximum,f.exclusiveMaximum),$=(ne($)&&$<=j&&d.problems.push({location:{offset:V.offset,length:V.length},message:ce("Value is above the exclusive maximum of {0}.",$)}),te(f.minimum,f.exclusiveMinimum)),$=(ne($)&&j<$&&d.problems.push({location:{offset:V.offset,length:V.length},message:ce("Value is below the minimum of {0}.",$)}),te(f.maximum,f.exclusiveMaximum));ne($)&&$<j&&d.problems.push({location:{offset:V.offset,length:V.length},message:ce("Value is above the maximum of {0}.",$)})}m.add({node:h,schema:f})}}function dt(a,e){let s=[],o=-1,l=a.getText(),c=C(l,!1),t=e&&e.collectComments?[]:void 0;function u(){for(;;){var e=c.scan();switch(function(){switch(c.getTokenError()){case 4:return f(ce("Invalid unicode sequence in string."),oe.InvalidUnicode);case 5:return f(ce("Invalid escape character in string."),oe.InvalidEscapeCharacter);case 3:return f(ce("Unexpected end of number."),oe.UnexpectedEndOfNumber);case 1:return f(ce("Unexpected end of comment."),oe.UnexpectedEndOfComment);case 2:return f(ce("Unexpected end of string."),oe.UnexpectedEndOfString);case 6:return f(ce("Invalid characters in string. Control characters must be escaped."),oe.InvalidCharacter)}}(),e){case 12:case 13:Array.isArray(t)&&t.push(A.create(a.positionAt(c.getTokenOffset()),a.positionAt(c.getTokenOffset()+c.getTokenLength())));break;case 15:case 14:break;default:return e}}}function h(e,t,r,n,i=re.Error){0!==s.length&&r===o||(n=A.create(a.positionAt(r),a.positionAt(n)),s.push(y.create(n,e,i,t,a.languageId)),o=r)}function f(e,t,r=void 0,n=[],i=[]){let a=c.getTokenOffset(),s=c.getTokenOffset()+c.getTokenLength();if(a===s&&0<a){for(a--;0<a&&/\s/.test(l.charAt(a));)a--;s=a+1}if(h(e,t,a,s),r&&d(r,!1),0<n.length+i.length){let e=c.getToken();for(;17!==e;){if(-1!==n.indexOf(e)){u();break}if(-1!==i.indexOf(e))break;e=u()}}return r}function d(e,t){return e.length=c.getTokenOffset()+c.getTokenLength()-e.offset,t&&u(),e}let m=new rt(void 0,0,0);function r(n){if(1===c.getToken()){let e=new it(n,c.getTokenOffset()),t=Object.create(null),r=(u(),!1);for(;2!==c.getToken()&&17!==c.getToken();){if(5===c.getToken()){r||f(ce("Property expected"),oe.PropertyExpected);var i=c.getTokenOffset();if(u(),2===c.getToken()){r&&h(ce("Trailing comma"),oe.TrailingComma,i,i+1);continue}}else r&&f(ce("Expected comma"),oe.CommaExpected);i=function(e,t){let r=new nt(e,c.getTokenOffset(),m),n=p(r);if(!n){if(16!==c.getToken())return;{f(ce("Property keys must be doublequoted"),oe.PropertyKeysMustBeDoublequoted);let e=new rt(r,c.getTokenOffset(),c.getTokenLength());e.value=c.getTokenValue(),n=e,u()}}if("//"!==(r.keyNode=n).value&&((e=t[n.value])?(h(ce("Duplicate object key"),oe.DuplicateKey,r.keyNode.offset,r.keyNode.offset+r.keyNode.length,re.Warning),se(e)&&h(ce("Duplicate object key"),oe.DuplicateKey,e.keyNode.offset,e.keyNode.offset+e.keyNode.length,re.Warning),t[n.value]=!0):t[n.value]=r),6===c.getToken())r.colonOffset=c.getTokenOffset(),u();else if(f(ce("Colon expected"),oe.ColonExpected),10===c.getToken()&&a.positionAt(n.offset+n.length).line<a.positionAt(c.getTokenOffset()).line)return r.length=n.length,r;return(e=g(r))?(r.valueNode=e,r.length=e.offset+e.length-r.offset,r):f(ce("Value expected"),oe.ValueExpected,r,[],[2,5])}(e,t);i?e.properties.push(i):f(ce("Property expected"),oe.PropertyExpected,void 0,[],[2,5]),r=!0}return 2!==c.getToken()?f(ce("Expected comma or closing brace"),oe.CommaOrCloseBraceExpected,e):d(e,!0)}}function p(t){if(10===c.getToken()){let e=new rt(t,c.getTokenOffset());return e.value=c.getTokenValue(),d(e,!0)}}function g(e){return function(r){if(3===c.getToken()){let e=new et(r,c.getTokenOffset()),t=(u(),!1);for(;4!==c.getToken()&&17!==c.getToken();){if(5===c.getToken()){t||f(ce("Value expected"),oe.ValueExpected);var n=c.getTokenOffset();if(u(),4===c.getToken()){t&&h(ce("Trailing comma"),oe.TrailingComma,n,n+1);continue}}else t&&f(ce("Expected comma"),oe.CommaExpected);n=g(e);n?e.items.push(n):f(ce("Value expected"),oe.ValueExpected,void 0,[],[4,5]),t=!0}return 4!==c.getToken()?f(ce("Expected comma or closing bracket"),oe.CommaOrCloseBacketExpected,e):d(e,!0)}}(e)||r(e)||p(e)||function(e){if(11===c.getToken()){let t=new tt(e,c.getTokenOffset());if(0===c.getTokenError()){let e=c.getTokenValue();try{var r=JSON.parse(e);if(!ne(r))return f(ce("Invalid number format."),oe.Undefined,t);t.value=r}catch{return f(ce("Invalid number format."),oe.Undefined,t)}t.isInteger=-1===e.indexOf(".")}return d(t,!0)}}(e)||function(e){switch(c.getToken()){case 7:return d(new Qe(e,c.getTokenOffset()),!0);case 8:return d(new Ye(e,!0,c.getTokenOffset()),!0);case 9:return d(new Ye(e,!1,c.getTokenOffset()),!0);default:return}}(e)}let n;return 17!==u()&&((n=g(n))?17!==c.getToken()&&f(ce("End of file expected."),oe.Undefined):f(ce("Expected a JSON object, array or literal."),oe.Undefined)),new ht(n,s,t)}var mt=class{constructor(e,t=[],r=Promise,n={}){this.schemaService=e,this.contributions=t,this.promiseConstructor=r,this.clientCapabilities=n}doResolve(r){for(let t=this.contributions.length-1;0<=t;t--){let e=this.contributions[t].resolveCompletion;if(e){var n=e(r);if(n)return n}}return this.promiseConstructor.resolve(r)}doComplete(o,t,l){let c={items:[],isIncomplete:!1},u=o.getText(),h=o.offsetAt(t),f=l.getNodeFromOffset(h,!0);if(this.isInComment(o,f?f.offset:0,h))return Promise.resolve(c);var e;f&&h===f.offset+f.length&&0<h&&(e=u[h-1],("object"===f.type&&"}"===e||"array"===f.type&&"]"===e)&&(f=f.parent));let d=this.getCurrentWord(o,h),m;if(!f||"string"!==f.type&&"number"!==f.type&&"boolean"!==f.type&&"null"!==f.type){let e=h-d.length;0<e&&'"'===u[e-1]&&e--,m=A.create(o.positionAt(e),t)}else m=A.create(o.positionAt(f.offset),o.positionAt(f.offset+f.length));let p=new Map,g={add:e=>{let t=e.label,r=p.get(t);var n;r?(r.documentation||(r.documentation=e.documentation),r.detail||(r.detail=e.detail),r.labelDetails||(r.labelDetails=e.labelDetails)):(60<(t=t.replace(/[\n]/g,"↵")).length&&(n=t.substr(0,57).trim()+"...",p.has(n)||(t=n)),e.textEdit=G.replace(m,e.insertText),e.label=t,p.set(t,e),c.items.push(e))},setAsIncomplete:()=>{c.isIncomplete=!0},error:e=>{console.error(e)},getNumberOfProposals:()=>c.items.length};return this.schemaService.getSchemaForResource(o.uri,l).then(e=>{let n=[],i=!0,a="",s;var t;if(!f||"string"!==f.type||(t=f.parent)&&"property"===t.type&&t.keyNode===f&&(i=!t.valueNode,s=t,a=u.substr(f.offset+1,f.length-2),t&&(f=t.parent)),f&&"object"===f.type){if(f.offset===h)return c;f.properties.forEach(e=>{s&&s===e||p.set(e.keyNode.value,ge.create("__"))});let t="",r=(i&&(t=this.evaluateSeparatorAfter(o,o.offsetAt(m.end))),e?this.getPropertyCompletions(e,l,f,i,t,g):this.getSchemaLessPropertyCompletions(l,f,a,g),ct(f));this.contributions.forEach(e=>{e=e.collectPropertyCompletions(o.uri,r,d,i,""===t,g);e&&n.push(e)}),!e&&0<d.length&&'"'!==u.charAt(h-d.length-1)&&(g.add({kind:v.Property,label:this.getLabelForValue(d),insertText:this.getInsertTextForProperty(d,void 0,!1,t),insertTextFormat:b.Snippet,documentation:""}),g.setAsIncomplete())}let r={};return e?this.getValueCompletions(e,l,f,h,o,g,r):this.getSchemaLessValueCompletions(l,f,h,o,g),0<this.contributions.length&&this.getContributedValueCompletions(l,f,h,o,g,n),this.promiseConstructor.all(n).then(()=>{if(0===g.getNumberOfProposals()){let e=h;!f||"string"!==f.type&&"number"!==f.type&&"boolean"!==f.type&&"null"!==f.type||(e=f.offset+f.length);var t=this.evaluateSeparatorAfter(o,e);this.addFillerValueCompletions(r,t,g)}return c})})}getPropertyCompletions(e,t,n,i,a,s){t.getMatchingSchemas(e.schema,n.offset).forEach(t=>{if(t.node===n&&!t.inverted){let e=t.schema.properties,n=(e&&Object.keys(e).forEach(t=>{var r=e[t];if("object"==typeof r&&!r.deprecationMessage&&!r.doNotSuggest){let e={kind:v.Property,label:t,insertText:this.getInsertTextForProperty(t,r,i,a),insertTextFormat:b.Snippet,filterText:this.getFilterTextForValue(t),documentation:this.fromMarkup(r.markdownDescription)||r.description||""};void 0!==r.suggestSortText&&(e.sortText=r.suggestSortText),e.insertText&&Re(e.insertText,"$1"+a)&&(e.command={title:"Suggest",command:"editor.action.triggerSuggest"}),s.add(e)}}),t.schema.propertyNames);if("object"==typeof n&&!n.deprecationMessage&&!n.doNotSuggest){var r=(e,t=void 0)=>{let r={kind:v.Property,label:e,insertText:this.getInsertTextForProperty(e,void 0,i,a),insertTextFormat:b.Snippet,filterText:this.getFilterTextForValue(e),documentation:t||this.fromMarkup(n.markdownDescription)||n.description||""};void 0!==n.suggestSortText&&(r.sortText=n.suggestSortText),r.insertText&&Re(r.insertText,"$1"+a)&&(r.command={title:"Suggest",command:"editor.action.triggerSuggest"}),s.add(r)};if(n.enum)for(let t=0;t<n.enum.length;t++){let e;n.markdownEnumDescriptions&&t<n.markdownEnumDescriptions.length?e=this.fromMarkup(n.markdownEnumDescriptions[t]):n.enumDescriptions&&t<n.enumDescriptions.length&&(e=n.enumDescriptions[t]),r(n.enum[t],e)}n.const&&r(n.const)}}})}getSchemaLessPropertyCompletions(e,r,t,n){let i=e=>{e.properties.forEach(e=>{e=e.keyNode.value;n.add({kind:v.Property,label:e,insertText:this.getInsertTextForValue(e,""),insertTextFormat:b.Snippet,filterText:this.getFilterTextForValue(e),documentation:""})})};if(r.parent)if("property"===r.parent.type){let t=r.parent.keyNode.value;e.visit(e=>("property"===e.type&&e!==r.parent&&e.keyNode.value===t&&e.valueNode&&"object"===e.valueNode.type&&i(e.valueNode),!0))}else"array"===r.parent.type&&r.parent.items.forEach(e=>{"object"===e.type&&e!==r&&i(e)});else"object"===r.type&&n.add({kind:v.Property,label:"$schema",insertText:this.getInsertTextForProperty("$schema",void 0,!0,""),insertTextFormat:b.Snippet,documentation:"",filterText:this.getFilterTextForValue("$schema")})}getSchemaLessValueCompletions(e,r,n,t,i){let a=n;if(!r||"string"!==r.type&&"number"!==r.type&&"boolean"!==r.type&&"null"!==r.type||(a=r.offset+r.length,r=r.parent),!r)return i.add({kind:this.getSuggestionKind("object"),label:"Empty object",insertText:this.getInsertTextForValue({},""),insertTextFormat:b.Snippet,documentation:""}),void i.add({kind:this.getSuggestionKind("array"),label:"Empty array",insertText:this.getInsertTextForValue([],""),insertTextFormat:b.Snippet,documentation:""});let s=this.evaluateSeparatorAfter(t,a),o=e=>{e.parent&&!ut(e.parent,n,!0)&&i.add({kind:this.getSuggestionKind(e.type),label:this.getLabelTextForMatchingNode(e,t),insertText:this.getInsertTextForMatchingNode(e,t,s),insertTextFormat:b.Snippet,documentation:""}),"boolean"===e.type&&this.addBooleanValueCompletion(!e.value,s,i)};if("property"===r.type&&n>(r.colonOffset||0)){var l=r.valueNode;if(l&&(n>l.offset+l.length||"object"===l.type||"array"===l.type))return;let t=r.keyNode.value;e.visit(e=>("property"===e.type&&e.keyNode.value===t&&e.valueNode&&o(e.valueNode),!0)),"$schema"===t&&r.parent&&!r.parent.parent&&this.addDollarSchemaCompletions(s,i)}if("array"===r.type)if(r.parent&&"property"===r.parent.type){let t=r.parent.keyNode.value;e.visit(e=>("property"===e.type&&e.keyNode.value===t&&e.valueNode&&"array"===e.valueNode.type&&e.valueNode.items.forEach(o),!0))}else r.items.forEach(o)}getValueCompletions(e,t,r,n,i,a,s){let o=n,l,c;if(!r||"string"!==r.type&&"number"!==r.type&&"boolean"!==r.type&&"null"!==r.type||(o=r.offset+r.length,r=(c=r).parent),r){if("property"===r.type&&n>(r.colonOffset||0)){var u=r.valueNode;if(u&&n>u.offset+u.length)return;l=r.keyNode.value,r=r.parent}if(r&&(void 0!==l||"array"===r.type)){var h,f,d,m=this.evaluateSeparatorAfter(i,o);for(h of t.getMatchingSchemas(e.schema,r.offset,c))if(h.node===r&&!h.inverted&&h.schema){if("array"===r.type&&h.schema.items){let e=a;if(h.schema.uniqueItems){let t=new Set;r.children.forEach(e=>{"array"!==e.type&&"object"!==e.type&&t.add(this.getLabelForValue(fe(e)))}),e={...a,add(e){t.has(e.label)||a.add(e)}}}Array.isArray(h.schema.items)?(f=this.findItemAtOffset(r,i,n))<h.schema.items.length&&this.addSchemaValueCompletions(h.schema.items[f],m,e,s):this.addSchemaValueCompletions(h.schema.items,m,e,s)}if(void 0!==l){let e=!1;if(!h.schema.properties||(f=h.schema.properties[l])&&(e=!0,this.addSchemaValueCompletions(f,m,a,s)),h.schema.patternProperties&&!e)for(var p of Object.keys(h.schema.patternProperties))_e(p)?.test(l)&&(e=!0,p=h.schema.patternProperties[p],this.addSchemaValueCompletions(p,m,a,s));h.schema.additionalProperties&&!e&&(d=h.schema.additionalProperties,this.addSchemaValueCompletions(d,m,a,s))}}"$schema"!==l||r.parent||this.addDollarSchemaCompletions(m,a),s.boolean&&(this.addBooleanValueCompletion(!0,m,a),this.addBooleanValueCompletion(!1,m,a)),s.null&&this.addNullValueCompletion(m,a)}}else this.addSchemaValueCompletions(e.schema,"",a,s)}getContributedValueCompletions(e,n,t,i,a,s){if(n){if((n="string"!==n.type&&"number"!==n.type&&"boolean"!==n.type&&"null"!==n.type?n:n.parent)&&"property"===n.type&&t>(n.colonOffset||0)){let r=n.keyNode.value,e=n.valueNode;if((!e||t<=e.offset+e.length)&&n.parent){let t=ct(n.parent);this.contributions.forEach(e=>{e=e.collectValueCompletions(i.uri,t,r,a);e&&s.push(e)})}}}else this.contributions.forEach(e=>{e=e.collectDefaultCompletions(i.uri,a);e&&s.push(e)})}addSchemaValueCompletions(e,t,r,n){"object"==typeof e&&(this.addEnumValueCompletions(e,t,r),this.addDefaultValueCompletions(e,t,r),this.collectTypes(e,n),Array.isArray(e.allOf)&&e.allOf.forEach(e=>this.addSchemaValueCompletions(e,t,r,n)),Array.isArray(e.anyOf)&&e.anyOf.forEach(e=>this.addSchemaValueCompletions(e,t,r,n)),Array.isArray(e.oneOf)&&e.oneOf.forEach(e=>this.addSchemaValueCompletions(e,t,r,n)))}addDefaultValueCompletions(n,l,c,u=0){let h=!1;if(ie(n.default)){let t=n.type,r=n.default;for(let e=u;0<e;e--)r=[r],t="array";let e={kind:this.getSuggestionKind(t),label:this.getLabelForValue(r),insertText:this.getInsertTextForValue(r,l),insertTextFormat:b.Snippet};this.doesSupportsLabelDetails()?e.labelDetails={description:ce("Default value")}:e.detail=ce("Default value"),c.add(e),h=!0}Array.isArray(n.examples)&&n.examples.forEach(e=>{let t=n.type,r=e;for(let e=u;0<e;e--)r=[r],t="array";c.add({kind:this.getSuggestionKind(t),label:this.getLabelForValue(r),insertText:this.getInsertTextForValue(r,l),insertTextFormat:b.Snippet}),h=!0}),Array.isArray(n.defaultSnippets)&&n.defaultSnippets.forEach(e=>{let i=n.type,t=e.body,a=e.label,s,o;if(ie(t)){n.type;for(let e=u;0<e;e--)t=[t];s=this.getInsertTextForSnippetValue(t,l),o=this.getFilterTextForSnippetValue(t),a=a||this.getLabelForSnippetValue(t)}else{if("string"!=typeof e.bodyText)return;{let t="",r="",n="";for(let e=u;0<e;e--)t=t+n+`[
`,r=r+`
`+n+"]",n+="\t",i="array";s=t+n+e.bodyText.split(`
`).join(`
`+n)+r+l,a=a||s,o=s.replace(/[\n]/g,"")}}c.add({kind:this.getSuggestionKind(i),label:a,documentation:this.fromMarkup(e.markdownDescription)||e.description,insertText:s,insertTextFormat:b.Snippet,filterText:o}),h=!0}),!h&&"object"==typeof n.items&&!Array.isArray(n.items)&&u<5&&this.addDefaultValueCompletions(n.items,l,c,u+1)}addEnumValueCompletions(n,i,a){if(ie(n.const)&&a.add({kind:this.getSuggestionKind(n.type),label:this.getLabelForValue(n.const),insertText:this.getInsertTextForValue(n.const,i),insertTextFormat:b.Snippet,documentation:this.fromMarkup(n.markdownDescription)||n.description}),Array.isArray(n.enum))for(let r=0,e=n.enum.length;r<e;r++){let e=n.enum[r],t=this.fromMarkup(n.markdownDescription)||n.description;n.markdownEnumDescriptions&&r<n.markdownEnumDescriptions.length&&this.doesSupportMarkdown()?t=this.fromMarkup(n.markdownEnumDescriptions[r]):n.enumDescriptions&&r<n.enumDescriptions.length&&(t=n.enumDescriptions[r]),a.add({kind:this.getSuggestionKind(n.type),label:this.getLabelForValue(e),insertText:this.getInsertTextForValue(e,i),insertTextFormat:b.Snippet,documentation:t})}}collectTypes(t,r){if(!Array.isArray(t.enum)&&!ie(t.const)){let e=t.type;Array.isArray(e)?e.forEach(e=>r[e]=!0):e&&(r[e]=!0)}}addFillerValueCompletions(e,t,r){e.object&&r.add({kind:this.getSuggestionKind("object"),label:"{}",insertText:this.getInsertTextForGuessedValue({},t),insertTextFormat:b.Snippet,detail:ce("New object"),documentation:""}),e.array&&r.add({kind:this.getSuggestionKind("array"),label:"[]",insertText:this.getInsertTextForGuessedValue([],t),insertTextFormat:b.Snippet,detail:ce("New array"),documentation:""})}addBooleanValueCompletion(e,t,r){r.add({kind:this.getSuggestionKind("boolean"),label:e?"true":"false",insertText:this.getInsertTextForValue(e,t),insertTextFormat:b.Snippet,documentation:""})}addNullValueCompletion(e,t){t.add({kind:this.getSuggestionKind("null"),label:"null",insertText:"null"+e,insertTextFormat:b.Snippet,documentation:""})}addDollarSchemaCompletions(t,r){this.schemaService.getRegisteredSchemaIds(e=>"http"===e||"https"===e).forEach(e=>{e.startsWith("http://json-schema.org/draft-")&&(e+="#"),r.add({kind:v.Module,label:this.getLabelForValue(e),filterText:this.getFilterTextForValue(e),insertText:this.getInsertTextForValue(e,t),insertTextFormat:b.Snippet,documentation:""})})}getLabelForValue(e){return JSON.stringify(e)}getValueFromLabel(e){return JSON.parse(e)}getFilterTextForValue(e){return JSON.stringify(e)}getFilterTextForSnippetValue(e){return JSON.stringify(e).replace(/\$\{\d+:([^}]+)\}|\$\d+/g,"$1")}getLabelForSnippetValue(e){return JSON.stringify(e).replace(/\$\{\d+:([^}]+)\}|\$\d+/g,"$1")}getInsertTextForPlainText(e){return e.replace(/[\\\$\}]/g,"\\$&")}getInsertTextForValue(e,t){e=JSON.stringify(e,null,"\t");return"{}"===e?"{$1}"+t:"[]"===e?"[$1]"+t:this.getInsertTextForPlainText(e+t)}getInsertTextForSnippetValue(e,t){return function r(n,e,i){if(null===n||"object"!=typeof n)return i(n);var a=e+"\t";if(Array.isArray(n)){if(0===n.length)return"[]";let t=`[
`;for(let e=0;e<n.length;e++)t+=a+r(n[e],a,i),e<n.length-1&&(t+=","),t+=`
`;return t+=e+"]"}{var s=Object.keys(n);if(0===s.length)return"{}";let t=`{
`;for(let e=0;e<s.length;e++){var o=s[e];t+=a+JSON.stringify(o)+": "+r(n[o],a,i),e<s.length-1&&(t+=","),t+=`
`}return t+=e+"}"}}(e,"",e=>"string"==typeof e&&"^"===e[0]?e.substr(1):JSON.stringify(e))+t}getInsertTextForGuessedValue(t,r){switch(typeof t){case"object":return null===t?"${1:null}"+r:this.getInsertTextForValue(t,r);case"string":let e=JSON.stringify(t);return e=e.substr(1,e.length-2),'"${1:'+(e=this.getInsertTextForPlainText(e))+'}"'+r;case"number":case"boolean":return"${1:"+JSON.stringify(t)+"}"+r}return this.getInsertTextForValue(t,r)}getSuggestionKind(e){var t;if(!(e=Array.isArray(e)?0<(t=e).length?t[0]:void 0:e))return v.Value;switch(e){case"string":return v.Value;case"object":return v.Module;case"property":return v.Property;default:return v.Value}}getLabelTextForMatchingNode(e,t){switch(e.type){case"array":return"[]";case"object":return"{}";default:return t.getText().substr(e.offset,e.length)}}getInsertTextForMatchingNode(e,t,r){switch(e.type){case"array":return this.getInsertTextForValue([],r);case"object":return this.getInsertTextForValue({},r);default:var n=t.getText().substr(e.offset,e.length)+r;return this.getInsertTextForPlainText(n)}}getInsertTextForProperty(e,t,r,n){var i=this.getInsertTextForValue(e,"");if(!r)return i;let a=i+": ",s,o=0;if(t&&(Array.isArray(t.defaultSnippets)&&(1===t.defaultSnippets.length&&ie(e=t.defaultSnippets[0].body)&&(s=this.getInsertTextForSnippetValue(e,"")),o+=t.defaultSnippets.length),t.enum&&(s||1!==t.enum.length||(s=this.getInsertTextForGuessedValue(t.enum[0],"")),o+=t.enum.length),ie(t.const)&&(s=s||this.getInsertTextForGuessedValue(t.const,""),o++),ie(t.default)&&(s=s||this.getInsertTextForGuessedValue(t.default,""),o++),Array.isArray(t.examples)&&t.examples.length&&(s=s||this.getInsertTextForGuessedValue(t.examples[0],""),o+=t.examples.length),0===o)){let e=Array.isArray(t.type)?t.type[0]:t.type;switch(e||(t.properties?e="object":t.items&&(e="array")),e){case"boolean":s="$1";break;case"string":s='"$1"';break;case"object":s="{$1}";break;case"array":s="[$1]";break;case"number":case"integer":s="${1:0}";break;case"null":s="${1:null}";break;default:return i}}return a+(s=!s||1<o?"$1":s)+n}getCurrentWord(e,t){let r=t-1,n=e.getText();for(;0<=r&&-1===` 	
\r":{[,]}`.indexOf(n.charAt(r));)r--;return n.substring(r+1,t)}evaluateSeparatorAfter(e,t){let r=C(e.getText(),!0);switch(r.setPosition(t),r.scan()){case 5:case 2:case 4:case 17:return"";default:return","}}findItemAtOffset(e,t,r){let n=C(t.getText(),!0),i=e.items;for(let e=i.length-1;0<=e;e--){var a=i[e];if(r>a.offset+a.length)return n.setPosition(a.offset+a.length),5===n.scan()&&r>=n.getTokenOffset()+n.getTokenLength()?e+1:e;if(r>=a.offset)return e}return 0}isInComment(e,t,r){let n=C(e.getText(),!1),i=(n.setPosition(t),n.scan());for(;17!==i&&n.getTokenOffset()+n.getTokenLength()<r;)i=n.scan();return(12===i||13===i)&&n.getTokenOffset()<=r}fromMarkup(e){if(e&&this.doesSupportMarkdown())return{kind:r.Markdown,value:e}}doesSupportMarkdown(){if(!ie(this.supportsMarkdown)){let e=this.clientCapabilities.textDocument?.completion?.completionItem?.documentationFormat;this.supportsMarkdown=Array.isArray(e)&&-1!==e.indexOf(r.Markdown)}return this.supportsMarkdown}doesSupportsCommitCharacters(){return ie(this.supportsCommitCharacters)||(this.labelDetailsSupport=this.clientCapabilities.textDocument?.completion?.completionItem?.commitCharactersSupport),this.supportsCommitCharacters}doesSupportsLabelDetails(){return ie(this.labelDetailsSupport)||(this.labelDetailsSupport=this.clientCapabilities.textDocument?.completion?.completionItem?.labelDetailsSupport),this.labelDetailsSupport}},pt=class{constructor(e,t=[],r){this.schemaService=e,this.contributions=t,this.promise=r||Promise}doHover(r,e,o){let t=r.offsetAt(e),l=o.getNodeFromOffset(t);if(!l||("object"===l.type||"array"===l.type)&&t>l.offset+1&&t<l.offset+l.length-1)return this.promise.resolve(null);e=l;if("string"===l.type){var n=l.parent;if(n&&"property"===n.type&&n.keyNode===l&&!(l=n.valueNode))return this.promise.resolve(null)}let i=A.create(r.positionAt(e.offset),r.positionAt(e.offset+e.length)),c=e=>({contents:e,range:i}),a=ct(l);for(let t=this.contributions.length-1;0<=t;t--){let e=this.contributions[t].getInfoContribution(r.uri,a);if(e)return e.then(e=>c(e))}return this.schemaService.getSchemaForResource(r.uri,o).then(s=>{if(s&&l){let e=o.getMatchingSchemas(s.schema,l.offset),r,n,i,a,t=(e.every(e=>{var t;return e.node===l&&!e.inverted&&e.schema&&(r=r||e.schema.title,n=n||e.schema.markdownDescription||gt(e.schema.description),e.schema.enum)&&(t=e.schema.enum.indexOf(fe(l)),e.schema.markdownEnumDescriptions?i=e.schema.markdownEnumDescriptions[t]:e.schema.enumDescriptions&&(i=gt(e.schema.enumDescriptions[t])),i&&"string"!=typeof(a=e.schema.enum[t])&&(a=JSON.stringify(a))),!0}),"");return r&&(t=gt(r)),n&&(0<t.length&&(t+=`

`),t+=n),i&&(0<t.length&&(t+=`

`),t+=`\`${s=a,-1!==s.indexOf("`")?"`` "+s+" ``":s}\`: `+i),c([t])}return null})}};function gt(e){if(e)return e.replace(/([^\n\r])(\r?\n)([^\n\r])/gm,`$1

$3`).replace(/[\\`*_{}[\]()#+\-.!]/g,"\\$&")}var yt=class{constructor(e,t){this.jsonSchemaService=e,this.promise=t,this.validationEnabled=!0}configure(e){e&&(this.validationEnabled=!1!==e.validate,this.commentSeverity=e.allowComments?void 0:re.Error)}doValidation(l,c,u,e){if(!this.validationEnabled)return this.promise.resolve([]);let h=[],r={},f=e=>{var t=e.range.start.line+" "+e.range.start.character+" "+e.message;r[t]||(r[t]=!0,h.push(e))},t=t=>{let e=u?.trailingCommas?bt(u.trailingCommas):re.Error,r=u?.comments?bt(u.comments):this.commentSeverity,n=u?.schemaValidation?bt(u.schemaValidation):re.Warning,i=u?.schemaRequest?bt(u.schemaRequest):re.Warning;if(t){var a=(e,t)=>{var r,n;c.root&&i&&((n="object"===(r=c.root).type?r.properties[0]:void 0)&&"$schema"===n.keyNode.value?(n=n.valueNode||n,n=A.create(l.positionAt(n.offset),l.positionAt(n.offset+n.length)),f(y.create(n,e,i,t))):(n=A.create(l.positionAt(r.offset),l.positionAt(r.offset+1)),f(y.create(n,e,i,t))))};if(t.errors.length)a(t.errors[0],oe.SchemaResolveError);else if(n){for(var s of t.warnings)a(s,oe.SchemaUnsupportedFeature);let e=c.validate(l,t.schema,n,u?.schemaDraft);e&&e.forEach(f)}!function t(e){if(e&&"object"==typeof e){if(ae(e.allowComments))return e.allowComments;if(e.allOf)for(var r of e.allOf){let e=t(r);if(ae(e))return e}}}(t.schema)||(r=void 0),function t(r){if(r&&"object"==typeof r){if(ae(r.allowTrailingCommas))return r.allowTrailingCommas;let e=r;if(ae(e.allowsTrailingCommas))return e.allowsTrailingCommas;if(r.allOf)for(var n of r.allOf){let e=t(n);if(ae(e))return e}}}(t.schema)&&(e=void 0)}for(var o of c.syntaxErrors){if(o.code===oe.TrailingComma){if("number"!=typeof e)continue;o.severity=e}f(o)}if("number"==typeof r){let t=ce("Comments are not permitted in JSON.");c.comments.forEach(e=>{f(y.create(e,t,r,oe.CommentNotPermitted))})}return h};var n;return e?(n=e.id||"schemaservice://untitled/"+vt++,this.jsonSchemaService.registerExternalSchema({uri:n,schema:e}).getResolvedSchema().then(e=>t(e))):this.jsonSchemaService.getSchemaForResource(l.uri,c).then(e=>t(e))}getLanguageStatus(e,t){return{schemas:this.jsonSchemaService.getSchemaURIsForResource(e.uri,t)}}},vt=0;function bt(e){switch(e){case"error":return re.Error;case"warning":return re.Warning;case"ignore":return}}function h(e){return e<48?0:e<=57?e-48:(e<97&&(e+=32),97<=e&&e<=102?e-97+10:0)}var St=class{constructor(e){this.schemaService=e}findDocumentSymbols(a,t,r={resultLimit:Number.MAX_VALUE}){t=t.root;if(!t)return[];let s=r.resultLimit||Number.MAX_VALUE,n=a.uri;if(("vscode://defaultsettings/keybindings.json"===n||Re(n.toLowerCase(),"/user/keybindings.json"))&&"array"===t.type){let e=[];for(var i of t.items)if("object"===i.type)for(var o of i.properties)if("key"===o.keyNode.value&&o.valueNode){var l=K.create(a.uri,g(a,i));if(e.push({name:xt(o.valueNode),kind:p.Function,location:l}),--s<=0)return r&&r.onResultLimitExceeded&&r.onResultLimitExceeded(n),e}return e}let c=[{node:t,containerName:""}],e=0,u=!1,h=[],f=(e,i)=>{"array"===e.type?e.items.forEach(e=>{e&&c.push({node:e,containerName:i})}):"object"===e.type&&e.properties.forEach(e=>{var t,r,n=e.valueNode;n&&(0<s?(s--,t=K.create(a.uri,g(a,e)),r=i?i+"."+e.keyNode.value:e.keyNode.value,h.push({name:this.getKeyLabel(e),kind:this.getSymbolKind(n.type),location:t,containerName:i}),c.push({node:n,containerName:r})):u=!0)})};for(;e<c.length;){var d=c[e++];f(d.node,d.containerName)}return u&&r&&r.onResultLimitExceeded&&r.onResultLimitExceeded(n),h}findDocumentSymbols2(s,t,r={resultLimit:Number.MAX_VALUE}){t=t.root;if(!t)return[];let o=r.resultLimit||Number.MAX_VALUE,n=s.uri;if(("vscode://defaultsettings/keybindings.json"===n||Re(n.toLowerCase(),"/user/keybindings.json"))&&"array"===t.type){let e=[];for(var i of t.items)if("object"===i.type)for(var a of i.properties)if("key"===a.keyNode.value&&a.valueNode){var l=g(s,i),c=g(s,a.keyNode);if(e.push({name:xt(a.valueNode),kind:p.Function,range:l,selectionRange:c}),--o<=0)return r&&r.onResultLimitExceeded&&r.onResultLimitExceeded(n),e}return e}let e=[],u=[{node:t,result:e}],h=0,f=!1,d=(e,a)=>{"array"===e.type?e.items.forEach((e,t)=>{var r,n;e&&(0<o?(o--,n=r=g(s,e),t={name:String(t),kind:this.getSymbolKind(e.type),range:r,selectionRange:n,children:[]},a.push(t),u.push({result:t.children,node:e})):f=!0)}):"object"===e.type&&e.properties.forEach(e=>{var t,r,n,i=e.valueNode;i&&(0<o?(o--,t=g(s,e),r=g(s,e.keyNode),n=[],e={name:this.getKeyLabel(e),kind:this.getSymbolKind(i.type),range:t,selectionRange:r,children:n,detail:this.getDetail(i)},a.push(e),u.push({result:n,node:i})):f=!0)})};for(;h<u.length;){var m=u[h++];d(m.node,m.result)}return f&&r&&r.onResultLimitExceeded&&r.onResultLimitExceeded(n),e}getSymbolKind(e){switch(e){case"object":return p.Module;case"string":return p.String;case"number":return p.Number;case"array":return p.Array;case"boolean":return p.Boolean;default:return p.Variable}}getKeyLabel(e){let t=e.keyNode.value;return(t=t&&t.replace(/[\n]/g,"↵"))&&t.trim()?t:`"${t}"`}getDetail(e){if(e)return"boolean"===e.type||"number"===e.type||"null"===e.type||"string"===e.type?String(e.value):"array"===e.type?e.children.length?void 0:"[]":"object"!==e.type||e.children.length?void 0:"{}"}findDocumentColors(l,c,u){return this.schemaService.getSchemaForResource(l.uri,c).then(n=>{let i=[];if(n){let e=u&&"number"==typeof u.resultLimit?u.resultLimit:Number.MAX_VALUE,t=c.getMatchingSchemas(n.schema),r={};for(var a of t)if(!a.inverted&&a.schema&&("color"===a.schema.format||"color-hex"===a.schema.format)&&a.node&&"string"===a.node.type){var s=String(a.node.offset);if(!r[s]){var o=function(e){if("#"===e[0])switch(e.length){case 4:return{red:17*h(e.charCodeAt(1))/255,green:17*h(e.charCodeAt(2))/255,blue:17*h(e.charCodeAt(3))/255,alpha:1};case 5:return{red:17*h(e.charCodeAt(1))/255,green:17*h(e.charCodeAt(2))/255,blue:17*h(e.charCodeAt(3))/255,alpha:17*h(e.charCodeAt(4))/255};case 7:return{red:(16*h(e.charCodeAt(1))+h(e.charCodeAt(2)))/255,green:(16*h(e.charCodeAt(3))+h(e.charCodeAt(4)))/255,blue:(16*h(e.charCodeAt(5))+h(e.charCodeAt(6)))/255,alpha:1};case 9:return{red:(16*h(e.charCodeAt(1))+h(e.charCodeAt(2)))/255,green:(16*h(e.charCodeAt(3))+h(e.charCodeAt(4)))/255,blue:(16*h(e.charCodeAt(5))+h(e.charCodeAt(6)))/255,alpha:(16*h(e.charCodeAt(7))+h(e.charCodeAt(8)))/255}}}(fe(a.node));if(o&&(a=g(l,a.node),i.push({color:o,range:a})),r[s]=!0,--e<=0)return u&&u.onResultLimitExceeded&&u.onResultLimitExceeded(l.uri),i}}}return i})}getColorPresentations(e,t,r,n){let i=[],a=Math.round(255*r.red),s=Math.round(255*r.green),o=Math.round(255*r.blue);function l(e){e=e.toString(16);return 2!==e.length?"0"+e:e}let c;return c=1===r.alpha?"#"+l(a)+l(s)+l(o):"#"+l(a)+l(s)+l(o)+l(Math.round(255*r.alpha)),i.push({label:c,textEdit:G.replace(n,JSON.stringify(c))}),i}};function g(e,t){return A.create(e.positionAt(t.offset),e.positionAt(t.offset+t.length))}function xt(e){return fe(e)||ce("<empty>")}var kt,At={schemaAssociations:[],schemas:{"http://json-schema.org/draft-04/schema#":{$schema:"http://json-schema.org/draft-04/schema#",definitions:{schemaArray:{type:"array",minItems:1,items:{$ref:"#"}},positiveInteger:{type:"integer",minimum:0},positiveIntegerDefault0:{allOf:[{$ref:"#/definitions/positiveInteger"},{default:0}]},simpleTypes:{type:"string",enum:["array","boolean","integer","null","number","object","string"]},stringArray:{type:"array",items:{type:"string"},minItems:1,uniqueItems:!0}},type:"object",properties:{id:{type:"string",format:"uri"},$schema:{type:"string",format:"uri"},title:{type:"string"},description:{type:"string"},default:{},multipleOf:{type:"number",minimum:0,exclusiveMinimum:!0},maximum:{type:"number"},exclusiveMaximum:{type:"boolean",default:!1},minimum:{type:"number"},exclusiveMinimum:{type:"boolean",default:!1},maxLength:{allOf:[{$ref:"#/definitions/positiveInteger"}]},minLength:{allOf:[{$ref:"#/definitions/positiveIntegerDefault0"}]},pattern:{type:"string",format:"regex"},additionalItems:{anyOf:[{type:"boolean"},{$ref:"#"}],default:{}},items:{anyOf:[{$ref:"#"},{$ref:"#/definitions/schemaArray"}],default:{}},maxItems:{allOf:[{$ref:"#/definitions/positiveInteger"}]},minItems:{allOf:[{$ref:"#/definitions/positiveIntegerDefault0"}]},uniqueItems:{type:"boolean",default:!1},maxProperties:{allOf:[{$ref:"#/definitions/positiveInteger"}]},minProperties:{allOf:[{$ref:"#/definitions/positiveIntegerDefault0"}]},required:{allOf:[{$ref:"#/definitions/stringArray"}]},additionalProperties:{anyOf:[{type:"boolean"},{$ref:"#"}],default:{}},definitions:{type:"object",additionalProperties:{$ref:"#"},default:{}},properties:{type:"object",additionalProperties:{$ref:"#"},default:{}},patternProperties:{type:"object",additionalProperties:{$ref:"#"},default:{}},dependencies:{type:"object",additionalProperties:{anyOf:[{$ref:"#"},{$ref:"#/definitions/stringArray"}]}},enum:{type:"array",minItems:1,uniqueItems:!0},type:{anyOf:[{$ref:"#/definitions/simpleTypes"},{type:"array",items:{$ref:"#/definitions/simpleTypes"},minItems:1,uniqueItems:!0}]},format:{anyOf:[{type:"string",enum:["date-time","uri","email","hostname","ipv4","ipv6","regex"]},{type:"string"}]},allOf:{allOf:[{$ref:"#/definitions/schemaArray"}]},anyOf:{allOf:[{$ref:"#/definitions/schemaArray"}]},oneOf:{allOf:[{$ref:"#/definitions/schemaArray"}]},not:{allOf:[{$ref:"#"}]}},dependencies:{exclusiveMaximum:["maximum"],exclusiveMinimum:["minimum"]},default:{}},"http://json-schema.org/draft-07/schema#":{definitions:{schemaArray:{type:"array",minItems:1,items:{$ref:"#"}},nonNegativeInteger:{type:"integer",minimum:0},nonNegativeIntegerDefault0:{allOf:[{$ref:"#/definitions/nonNegativeInteger"},{default:0}]},simpleTypes:{enum:["array","boolean","integer","null","number","object","string"]},stringArray:{type:"array",items:{type:"string"},uniqueItems:!0,default:[]}},type:["object","boolean"],properties:{$id:{type:"string",format:"uri-reference"},$schema:{type:"string",format:"uri"},$ref:{type:"string",format:"uri-reference"},$comment:{type:"string"},title:{type:"string"},description:{type:"string"},default:!0,readOnly:{type:"boolean",default:!1},examples:{type:"array",items:!0},multipleOf:{type:"number",exclusiveMinimum:0},maximum:{type:"number"},exclusiveMaximum:{type:"number"},minimum:{type:"number"},exclusiveMinimum:{type:"number"},maxLength:{$ref:"#/definitions/nonNegativeInteger"},minLength:{$ref:"#/definitions/nonNegativeIntegerDefault0"},pattern:{type:"string",format:"regex"},additionalItems:{$ref:"#"},items:{anyOf:[{$ref:"#"},{$ref:"#/definitions/schemaArray"}],default:!0},maxItems:{$ref:"#/definitions/nonNegativeInteger"},minItems:{$ref:"#/definitions/nonNegativeIntegerDefault0"},uniqueItems:{type:"boolean",default:!1},contains:{$ref:"#"},maxProperties:{$ref:"#/definitions/nonNegativeInteger"},minProperties:{$ref:"#/definitions/nonNegativeIntegerDefault0"},required:{$ref:"#/definitions/stringArray"},additionalProperties:{$ref:"#"},definitions:{type:"object",additionalProperties:{$ref:"#"},default:{}},properties:{type:"object",additionalProperties:{$ref:"#"},default:{}},patternProperties:{type:"object",additionalProperties:{$ref:"#"},propertyNames:{format:"regex"},default:{}},dependencies:{type:"object",additionalProperties:{anyOf:[{$ref:"#"},{$ref:"#/definitions/stringArray"}]}},propertyNames:{$ref:"#"},const:!0,enum:{type:"array",items:!0,minItems:1,uniqueItems:!0},type:{anyOf:[{$ref:"#/definitions/simpleTypes"},{type:"array",items:{$ref:"#/definitions/simpleTypes"},minItems:1,uniqueItems:!0}]},format:{type:"string"},contentMediaType:{type:"string"},contentEncoding:{type:"string"},if:{$ref:"#"},then:{$ref:"#"},else:{$ref:"#"},allOf:{$ref:"#/definitions/schemaArray"},anyOf:{$ref:"#/definitions/schemaArray"},oneOf:{$ref:"#/definitions/schemaArray"},not:{$ref:"#"}},default:!0}}},Tt={id:ce("A unique identifier for the schema."),$schema:ce("The schema to verify this document against."),title:ce("A descriptive title of the element."),description:ce("A long description of the element. Used in hover menus and suggestions."),default:ce("A default value. Used by suggestions."),multipleOf:ce("A number that should cleanly divide the current value (i.e. have no remainder)."),maximum:ce("The maximum numerical value, inclusive by default."),exclusiveMaximum:ce("Makes the maximum property exclusive."),minimum:ce("The minimum numerical value, inclusive by default."),exclusiveMinimum:ce("Makes the minimum property exclusive."),maxLength:ce("The maximum length of a string."),minLength:ce("The minimum length of a string."),pattern:ce("A regular expression to match the string against. It is not implicitly anchored."),additionalItems:ce("For arrays, only when items is set as an array. If it is a schema, then this schema validates items after the ones specified by the items array. If it is false, then additional items will cause validation to fail."),items:ce("For arrays. Can either be a schema to validate every element against or an array of schemas to validate each item against in order (the first schema will validate the first element, the second schema will validate the second element, and so on."),maxItems:ce("The maximum number of items that can be inside an array. Inclusive."),minItems:ce("The minimum number of items that can be inside an array. Inclusive."),uniqueItems:ce("If all of the items in the array must be unique. Defaults to false."),maxProperties:ce("The maximum number of properties an object can have. Inclusive."),minProperties:ce("The minimum number of properties an object can have. Inclusive."),required:ce("An array of strings that lists the names of all properties required on this object."),additionalProperties:ce("Either a schema or a boolean. If a schema, then used to validate all properties not matched by 'properties' or 'patternProperties'. If false, then any properties not matched by either will cause this schema to fail."),definitions:ce("Not used for validation. Place subschemas here that you wish to reference inline with $ref."),properties:ce("A map of property names to schemas for each property."),patternProperties:ce("A map of regular expressions on property names to schemas for matching properties."),dependencies:ce("A map of property names to either an array of property names or a schema. An array of property names means the property named in the key depends on the properties in the array being present in the object in order to be valid. If the value is a schema, then the schema is only applied to the object if the property in the key exists on the object."),enum:ce("The set of literal values that are valid."),type:ce("Either a string of one of the basic schema types (number, integer, null, array, object, boolean, string) or an array of strings specifying a subset of those types."),format:ce("Describes the format expected for the value."),allOf:ce("An array of schemas, all of which must match."),anyOf:ce("An array of schemas, where at least one must match."),oneOf:ce("An array of schemas, exactly one of which must match."),not:ce("A schema which must not match."),$id:ce("A unique identifier for the schema."),$ref:ce("Reference a definition hosted on any location."),$comment:ce("Comments from schema authors to readers or maintainers of the schema."),readOnly:ce("Indicates that the value of the instance is managed exclusively by the owning authority."),examples:ce("Sample JSON values associated with a particular schema, for the purpose of illustrating usage."),contains:ce('An array instance is valid against "contains" if at least one of its elements is valid against the given schema.'),propertyNames:ce("If the instance is an object, this keyword validates if every property name in the instance validates against the provided schema."),const:ce("An instance validates successfully against this keyword if its value is equal to the value of the keyword."),contentMediaType:ce("Describes the media type of a string property."),contentEncoding:ce("Describes the content encoding of a string property."),if:ce('The validation outcome of the "if" subschema controls which of the "then" or "else" keywords are evaluated.'),then:ce('The "if" subschema is used for validation when the "if" subschema succeeds.'),else:ce('The "else" subschema is used for validation when the "if" subschema fails.')};for(kt in At.schemas){let t=At.schemas[kt];for(var Ct in t.properties){let e=t.properties[Ct];"boolean"==typeof e&&(e=t.properties[Ct]={});Ct=Tt[Ct];Ct&&(e.description=Ct)}}var wt={470:e=>{function f(e){if("string"!=typeof e)throw new TypeError("Path must be a string. Received "+JSON.stringify(e))}function a(e,t){for(var r,n="",i=0,a=-1,s=0,o=0;o<=e.length;++o){if(o<e.length)r=e.charCodeAt(o);else{if(47===r)break;r=47}if(47===r){if(a!==o-1&&1!==s)if(a!==o-1&&2===s){if(n.length<2||2!==i||46!==n.charCodeAt(n.length-1)||46!==n.charCodeAt(n.length-2))if(2<n.length){var l=n.lastIndexOf("/");if(l!==n.length-1){i=-1===l?(n="",0):(n=n.slice(0,l)).length-1-n.lastIndexOf("/"),a=o,s=0;continue}}else if(2===n.length||1===n.length){n="",a=o,s=i=0;continue}t&&(0<n.length?n+="/..":n="..",i=2)}else 0<n.length?n+="/"+e.slice(a+1,o):n=e.slice(a+1,o),i=o-a-1;a=o,s=0}else 46===r&&-1!==s?++s:s=-1}return n}var d={resolve:function(){for(var e,t="",r=!1,n=arguments.length-1;-1<=n&&!r;n--){var i=0<=n?arguments[n]:e=void 0===e?process.cwd():e;f(i),0!==i.length&&(t=i+"/"+t,r=47===i.charCodeAt(0))}return t=a(t,!r),r?0<t.length?"/"+t:"/":0<t.length?t:"."},normalize:function(e){if(f(e),0===e.length)return".";var t=47===e.charCodeAt(0),r=47===e.charCodeAt(e.length-1);return 0<(e=0!==(e=a(e,!t)).length||t?e:".").length&&r&&(e+="/"),t?"/"+e:e},isAbsolute:function(e){return f(e),0<e.length&&47===e.charCodeAt(0)},join:function(){if(0===arguments.length)return".";for(var e,t=0;t<arguments.length;++t){var r=arguments[t];f(r),0<r.length&&(void 0===e?e=r:e+="/"+r)}return void 0===e?".":d.normalize(e)},relative:function(e,t){if(f(e),f(t),e===t||(e=d.resolve(e))===(t=d.resolve(t)))return"";for(var r=1;r<e.length&&47===e.charCodeAt(r);++r);for(var n=e.length,i=n-r,a=1;a<t.length&&47===t.charCodeAt(a);++a);for(var s=t.length-a,o=i<s?i:s,l=-1,c=0;c<=o;++c){if(c===o){if(o<s){if(47===t.charCodeAt(a+c))return t.slice(a+c+1);if(0===c)return t.slice(a+c)}else o<i&&(47===e.charCodeAt(r+c)?l=c:0===c&&(l=0));break}var u=e.charCodeAt(r+c);if(u!==t.charCodeAt(a+c))break;47===u&&(l=c)}for(var h="",c=r+l+1;c<=n;++c)c!==n&&47!==e.charCodeAt(c)||(0===h.length?h+="..":h+="/..");return 0<h.length?h+t.slice(a+l):(47===t.charCodeAt(a+=l)&&++a,t.slice(a))},_makeLong:function(e){return e},dirname:function(e){if(f(e),0===e.length)return".";for(var t=47===e.charCodeAt(0),r=-1,n=!0,i=e.length-1;1<=i;--i)if(47===e.charCodeAt(i)){if(!n){r=i;break}}else n=!1;return-1===r?t?"/":".":t&&1===r?"//":e.slice(0,r)},basename:function(e,t){if(void 0!==t&&"string"!=typeof t)throw new TypeError('"ext" argument must be a string');f(e);var r=0,n=-1,i=!0;if(void 0!==t&&0<t.length&&t.length<=e.length){if(t.length===e.length&&t===e)return"";for(var a=t.length-1,s=-1,o=e.length-1;0<=o;--o){var l=e.charCodeAt(o);if(47===l){if(!i){r=o+1;break}}else-1===s&&(i=!1,s=o+1),0<=a&&(l===t.charCodeAt(a)?-1==--a&&(n=o):(a=-1,n=s))}return r===n?n=s:-1===n&&(n=e.length),e.slice(r,n)}for(o=e.length-1;0<=o;--o)if(47===e.charCodeAt(o)){if(!i){r=o+1;break}}else-1===n&&(i=!1,n=o+1);return-1===n?"":e.slice(r,n)},extname:function(e){f(e);for(var t=-1,r=0,n=-1,i=!0,a=0,s=e.length-1;0<=s;--s){var o=e.charCodeAt(s);if(47!==o)-1===n&&(i=!1,n=s+1),46===o?-1===t?t=s:1!==a&&(a=1):-1!==t&&(a=-1);else if(!i){r=s+1;break}}return-1===t||-1===n||0===a||1===a&&t===n-1&&t===r+1?"":e.slice(t,n)},format:function(e){if(null===e||"object"!=typeof e)throw new TypeError('The "pathObject" argument must be of type Object. Received type '+typeof e);return t=e.dir||e.root,r=e.base||(e.name||"")+(e.ext||""),t?t===e.root?t+r:t+"/"+r:r;var t,r},parse:function(e){f(e);var t={root:"",dir:"",base:"",ext:"",name:""};if(0===e.length)return t;for(var r,n=47===e.charCodeAt(0),i=n?(t.root="/",1):0,a=-1,s=0,o=-1,l=!0,c=e.length-1,u=0;i<=c;--c)if(47!==(r=e.charCodeAt(c)))-1===o&&(l=!1,o=c+1),46===r?-1===a?a=c:1!==u&&(u=1):-1!==a&&(u=-1);else if(!l){s=c+1;break}return-1===a||-1===o||0===u||1===u&&a===o-1&&a===s+1?-1!==o&&(t.base=t.name=0===s&&n?e.slice(1,o):e.slice(s,o)):(0===s&&n?(t.name=e.slice(1,a),t.base=e.slice(1,o)):(t.name=e.slice(s,a),t.base=e.slice(s,o)),t.ext=e.slice(a,o)),0<s?t.dir=e.slice(0,s-1):n&&(t.dir="/"),t},sep:"/",delimiter:":",win32:null,posix:null};d.posix=d,e.exports=d}},Ot={};function f(e){var t=Ot[e];if(void 0!==t)return t.exports;t=Ot[e]={exports:{}};return wt[e](t,t.exports,f),t.exports}f.d=(e,t)=>{for(var r in t)f.o(t,r)&&!f.o(e,r)&&Object.defineProperty(e,r,{enumerable:!0,get:t[r]})},f.o=(e,t)=>Object.prototype.hasOwnProperty.call(e,t),f.r=e=>{typeof Symbol<"u"&&Symbol.toStringTag&&Object.defineProperty(e,Symbol.toStringTag,{value:"Module"}),Object.defineProperty(e,"__esModule",{value:!0})};var Nt={};{let n,r=(f.r(Nt),f.d(Nt,{URI:()=>mr,Utils:()=>jt}),"object"==typeof process?n="win32"===process.platform:"object"==typeof navigator&&(n=0<=navigator.userAgent.indexOf("Windows")),/^\w[\w\d+.-]*$/),i=/^\//,a=/^\/\//;function Pt(e,t){if(!e.scheme&&t)throw new Error(`[UriError]: Scheme is missing: {scheme: "", authority: "${e.authority}", path: "${e.path}", query: "${e.query}", fragment: "${e.fragment}"}`);if(e.scheme&&!r.test(e.scheme))throw new Error("[UriError]: Scheme contains illegal characters.");if(e.path)if(e.authority){if(!i.test(e.path))throw new Error('[UriError]: If a URI contains an authority component, then the path component must either be empty or begin with a slash ("/") character')}else if(a.test(e.path))throw new Error('[UriError]: If a URI does not contain an authority component, then the path cannot begin with two slash characters ("//")')}let c="/",s=/^(([^:/?#]+?):)?(\/\/([^/?#]*))?([^?#]*)(\?([^#]*))?(#(.*))?/;class mr{static isUri(e){return e instanceof mr||!!e&&"string"==typeof e.authority&&"string"==typeof e.fragment&&"string"==typeof e.path&&"string"==typeof e.query&&"string"==typeof e.scheme&&"string"==typeof e.fsPath&&"function"==typeof e.with&&"function"==typeof e.toString}scheme;authority;path;query;fragment;constructor(e,t,r,n,i,a=!1){"object"==typeof e?(this.scheme=e.scheme||"",this.authority=e.authority||"",this.path=e.path||"",this.query=e.query||"",this.fragment=e.fragment||""):(this.scheme=e||a?e:"file",this.authority=t||"",this.path=function(e,t){switch(e){case"https":case"http":case"file":t?t[0]!==c&&(t=c+t):t=c}return t}(this.scheme,r||""),this.query=n||"",this.fragment=i||"",Pt(this,a))}get fsPath(){return Et(this,!1)}with(e){if(!e)return this;let{scheme:t,authority:r,path:n,query:i,fragment:a}=e;return void 0===t?t=this.scheme:null===t&&(t=""),void 0===r?r=this.authority:null===r&&(r=""),void 0===n?n=this.path:null===n&&(n=""),void 0===i?i=this.query:null===i&&(i=""),void 0===a?a=this.fragment:null===a&&(a=""),t===this.scheme&&r===this.authority&&n===this.path&&i===this.query&&a===this.fragment?this:new pr(t,r,n,i,a)}static parse(e,t=!1){e=s.exec(e);return e?new pr(e[2]||"",Vt(e[4]||""),Vt(e[5]||""),Vt(e[7]||""),Vt(e[9]||""),t):new pr("","","","","")}static file(e){let t="";var r;return(e=n?e.replace(/\\/g,c):e)[0]===c&&e[1]===c&&(e=-1===(r=e.indexOf(c,2))?(t=e.substring(2),c):(t=e.substring(2,r),e.substring(r)||c)),new pr("file",t,e,"","")}static from(e){e=new pr(e.scheme,e.authority,e.path,e.query,e.fragment);return Pt(e,!0),e}toString(e=!1){return Mt(this,e)}toJSON(){return this}static revive(t){if(t){if(t instanceof mr)return t;{let e=new pr(t);return e._formatted=t.external,e._fsPath=t._sep===o?t.fsPath:null,e}}return t}}let o=n?1:void 0;class pr extends mr{_formatted=null;_fsPath=null;get fsPath(){return this._fsPath||(this._fsPath=Et(this,!1)),this._fsPath}toString(e=!1){return e?Mt(this,!0):(this._formatted||(this._formatted=Mt(this,!1)),this._formatted)}toJSON(){let e={$mid:1};return this._fsPath&&(e.fsPath=this._fsPath,e._sep=o),this._formatted&&(e.external=this._formatted),this.path&&(e.path=this.path),this.scheme&&(e.scheme=this.scheme),this.authority&&(e.authority=this.authority),this.query&&(e.query=this.query),this.fragment&&(e.fragment=this.fragment),e}}let l={58:"%3A",47:"%2F",63:"%3F",35:"%23",91:"%5B",93:"%5D",64:"%40",33:"%21",36:"%24",38:"%26",39:"%27",40:"%28",41:"%29",42:"%2A",43:"%2B",44:"%2C",59:"%3B",61:"%3D",32:"%20"};function It(t,r,n){let i,a=-1;for(let e=0;e<t.length;e++){var s=t.charCodeAt(e);97<=s&&s<=122||65<=s&&s<=90||48<=s&&s<=57||45===s||46===s||95===s||126===s||r&&47===s||n&&91===s||n&&93===s||n&&58===s?(-1!==a&&(i+=encodeURIComponent(t.substring(a,e)),a=-1),void 0!==i&&(i+=t.charAt(e))):(void 0===i&&(i=t.substr(0,e)),void 0!==(s=l[s])?(-1!==a&&(i+=encodeURIComponent(t.substring(a,e)),a=-1),i+=s):-1===a&&(a=e))}return-1!==a&&(i+=encodeURIComponent(t.substring(a))),void 0!==i?i:t}function Lt(t){let r;for(let e=0;e<t.length;e++){var n=t.charCodeAt(e);35===n||63===n?(void 0===r&&(r=t.substr(0,e)),r+=l[n]):void 0!==r&&(r+=t[e])}return void 0!==r?r:t}function Et(e,t){let r;return r=e.authority&&1<e.path.length&&"file"===e.scheme?"//"+e.authority+e.path:47===e.path.charCodeAt(0)&&(65<=e.path.charCodeAt(1)&&e.path.charCodeAt(1)<=90||97<=e.path.charCodeAt(1)&&e.path.charCodeAt(1)<=122)&&58===e.path.charCodeAt(2)?t?e.path.substr(1):e.path[1].toLowerCase()+e.path.substr(2):e.path,r=n?r.replace(/\//g,"\\"):r}function Mt(e,t){let r=t?Lt:It,n="",{scheme:i,authority:a,path:s,query:o,fragment:l}=e;if(i&&(n=n+i+":"),!a&&"file"!==i||(n=(n+=c)+c),a){let t=a.indexOf("@");if(-1!==t){let e=a.substr(0,t);a=a.substr(t+1),-1===(t=e.lastIndexOf(":"))?n+=r(e,!1,!1):n=(n=n+r(e.substr(0,t),!1,!1)+":")+r(e.substr(t+1),!1,!0),n+="@"}a=a.toLowerCase(),-1===(t=a.lastIndexOf(":"))?n+=r(a,!1,!0):n=(n+=r(a.substr(0,t),!1,!0))+a.substr(t)}return s&&(3<=s.length&&47===s.charCodeAt(0)&&58===s.charCodeAt(2)?65<=(e=s.charCodeAt(1))&&e<=90&&(s=`/${String.fromCharCode(e+32)}:`+s.substr(3)):2<=s.length&&58===s.charCodeAt(1)&&(65<=(e=s.charCodeAt(0))&&e<=90&&(s=String.fromCharCode(e+32)+":"+s.substr(2))),n+=r(s,!0,!1)),o&&(n=(n+="?")+r(o,!1,!1)),n=l?(n+="#")+(t?l:It(l,!1,!1)):n}let t=/(%[0-9A-Za-z][0-9A-Za-z])+/g;function Vt(e){return e.match(t)?e.replace(t,e=>function e(t){try{return decodeURIComponent(t)}catch{return 3<t.length?t.substr(0,3)+e(t.substr(3)):t}}(e)):e}var jt,d=f(470);let u=d.posix||d;(d=jt=jt||{}).joinPath=function(e,...t){return e.with({path:u.join(e.path,...t)})},d.resolvePath=function(e,...t){let r=e.path,n=!1,i=("/"!==r[0]&&(r="/"+r,n=!0),u.resolve(r,...t));return n&&"/"===i[0]&&!e.authority&&(i=i.substring(1)),e.with({path:i})},d.dirname=function(e){if(0===e.path.length||"/"===e.path)return e;let t=u.dirname(e.path);return 1===t.length&&46===t.charCodeAt(0)&&(t=""),e.with({path:t})},d.basename=function(e){return u.basename(e.path)},d.extname=function(e){return u.extname(e.path)}}var $t=Nt["URI"];var w,Ft,Dt=class{constructor(e,t,r){this.folderUri=t,this.uris=r,this.globWrappers=[];try{for(var n of e){var i="!"!==n[0];0<(n=i?n:n.substring(1)).length&&("/"===n[0]&&(n=n.substring(1)),this.globWrappers.push({regexp:function(e,t){if("string"!=typeof e)throw new TypeError("Expected a string");let n=String(e),i="",a=!!t&&!!t.extended,s=!!t&&!!t.globstar,o=!1,r=t&&"string"==typeof t.flags?t.flags:"",l;for(let r=0,e=n.length;r<e;r++)switch(l=n[r],l){case"/":case"$":case"^":case"+":case".":case"(":case")":case"=":case"!":case"|":i+="\\"+l;break;case"?":if(a){i+=".";break}case"[":case"]":if(a){i+=l;break}case"{":if(a){o=!0,i+="(";break}case"}":if(a){o=!1,i+=")";break}case",":if(o){i+="|";break}i+="\\"+l;break;case"*":let e=n[r-1],t=1;for(;"*"===n[r+1];)t++,r++;var c=n[r+1];s?!(1<t)||"/"!==e&&void 0!==e&&"{"!==e&&","!==e||"/"!==c&&void 0!==c&&","!==c&&"}"!==c?i+="([^/]*)":("/"===c?r++:"/"===e&&i.endsWith("\\/")&&(i=i.substr(0,i.length-2)),i+="((?:[^/]*(?:/|$))*)"):i+=".*";break;default:i+=l}return r&&~r.indexOf("g")||(i="^"+i+"$"),new RegExp(i,r)}("**/"+n,{extended:!0,globstar:!0}),include:i}))}t&&((t=Jt(t)).endsWith("/")||(t+="/"),this.folderUri=t)}catch{this.globWrappers.length=0,this.uris=[]}}matchesPattern(e){if(this.folderUri&&!e.startsWith(this.folderUri))return!1;let t=!1;for(var{regexp:r,include:n}of this.globWrappers)r.test(e)&&(t=n);return t}getURIs(){return this.uris}},Rt=class{constructor(e,t,r){this.service=e,this.uri=t,this.dependencies=new Set,this.anchors=void 0,r&&(this.unresolvedSchema=this.service.promise.resolve(new _t(r)))}getUnresolvedSchema(){return this.unresolvedSchema||(this.unresolvedSchema=this.service.loadSchema(this.uri)),this.unresolvedSchema}getResolvedSchema(){return this.resolvedSchema||(this.resolvedSchema=this.getUnresolvedSchema().then(e=>this.service.resolveSchemaContent(e,this))),this.resolvedSchema}clearSchema(){var e=!!this.unresolvedSchema;return this.resolvedSchema=void 0,this.unresolvedSchema=void 0,this.dependencies.clear(),this.anchors=void 0,e}},_t=class{constructor(e,t=[]){this.schema=e,this.errors=t}},Ut=class{constructor(e,t=[],r=[],n){this.schema=e,this.errors=t,this.warnings=r,this.schemaDraft=n}getSection(e){e=this.getSectionRecursive(e,this.schema);if(e)return ue(e)}getSectionRecursive(e,t){if(!t||"boolean"==typeof t||0===e.length)return t;let r=e.shift();if(t.properties&&(t.properties[r],1))return this.getSectionRecursive(e,t.properties[r]);if(t.patternProperties){for(var n of Object.keys(t.patternProperties))if(_e(n)?.test(r))return this.getSectionRecursive(e,t.patternProperties[n])}else{if("object"==typeof t.additionalProperties)return this.getSectionRecursive(e,t.additionalProperties);if(r.match("[0-9]+"))if(Array.isArray(t.items)){var i=parseInt(r,10);if(!isNaN(i)&&t.items[i])return this.getSectionRecursive(e,t.items[i])}else if(t.items)return this.getSectionRecursive(e,t.items)}}},qt=class{constructor(e,t,r){this.contextService=t,this.requestService=e,this.promiseConstructor=r||Promise,this.callOnDispose=[],this.contributionSchemas={},this.contributionAssociations=[],this.schemasById={},this.filePatternAssociations=[],this.registeredSchemasIds={}}getRegisteredSchemaIds(t){return Object.keys(this.registeredSchemasIds).filter(e=>{e=$t.parse(e).scheme;return"schemaservice"!==e&&(!t||t(e))})}get promise(){return this.promiseConstructor}dispose(){for(;0<this.callOnDispose.length;)this.callOnDispose.pop()()}onResourceChange(e){this.cachedSchemaForResource=void 0;let r=!1,n=[e=S(e)],i=Object.keys(this.schemasById).map(e=>this.schemasById[e]);for(;n.length;){var a=n.pop();for(let t=0;t<i.length;t++){let e=i[t];e&&(e.uri===a||e.dependencies.has(a))&&(e.uri!==a&&n.push(e.uri),e.clearSchema()&&(r=!0),i[t]=void 0)}}return r}setSchemaContributions(e){if(e.schemas){var t,r=e.schemas;for(t in r){var n=S(t);this.contributionSchemas[n]=this.addSchemaHandle(n,r[t])}}var i;if(Array.isArray(e.schemaAssociations))for(i of e.schemaAssociations){var a=i.uris.map(S),a=this.addFilePatternAssociation(i.pattern,i.folderUri,a);this.contributionAssociations.push(a)}}addSchemaHandle(e,t){t=new Rt(this,e,t);return this.schemasById[e]=t}getOrAddSchemaHandle(e,t){return this.schemasById[e]||this.addSchemaHandle(e,t)}addFilePatternAssociation(e,t,r){e=new Dt(e,t,r);return this.filePatternAssociations.push(e),e}registerExternalSchema(e){var t=S(e.uri);return this.registeredSchemasIds[t]=!0,this.cachedSchemaForResource=void 0,e.fileMatch&&e.fileMatch.length&&this.addFilePatternAssociation(e.fileMatch,e.folderUri,[t]),e.schema?this.addSchemaHandle(t,e.schema):this.getOrAddSchemaHandle(t)}clearExternalSchemas(){for(var e in this.schemasById={},this.filePatternAssociations=[],this.registeredSchemasIds={},this.cachedSchemaForResource=void 0,this.contributionSchemas)this.schemasById[e]=this.contributionSchemas[e],this.registeredSchemasIds[e]=!0;for(var t of this.contributionAssociations)this.filePatternAssociations.push(t)}getResolvedSchema(e){let t=S(e),r=this.schemasById[t];return r?r.getResolvedSchema():this.promise.resolve(void 0)}loadSchema(n){var e;return this.requestService?(n.startsWith("http://json-schema.org/")&&(n="https"+n.substring(4)),this.requestService(n).then(e=>{if(!e)return r=ce("Unable to load schema from '{0}': No content.",Kt(n)),new _t({},[r]);let t=[];65279===e.charCodeAt(0)&&(t.push(ce("Problem reading content from '{0}': UTF-8 with BOM detected, only UTF 8 is allowed.",Kt(n))),e=e.trimStart());var r=[],e=Me(e,r);return r.length&&t.push(ce("Unable to parse content from '{0}': Parse error at offset {1}.",Kt(n),r[0].offset)),new _t(e,t)},e=>{let t=e.toString(),r=e.toString().split("Error: ");return Re(t=1<r.length?r[1]:t,".")&&(t=t.substr(0,t.length-1)),new _t({},[ce("Unable to load schema from '{0}': {1}.",Kt(n),t)])})):(e=ce("Unable to load schema from '{0}'. No schema request service available",Kt(n)),this.promise.resolve(new _t({},[e])))}resolveSchemaContent(e,t){let o=e.errors.slice(0),r=e.schema,n=r.$schema?S(r.$schema):void 0;if("http://json-schema.org/draft-03/schema"===n)return this.promise.resolve(new Ut({},[ce("Draft-03 schemas are not supported.")],[],n));let l=new Set,c=this.contextService,a=(e,t)=>{t=decodeURIComponent(t);let r=e;return(t="/"===t[0]?t.substring(1):t).split("/").some(e=>(e=e.replace(/~1/g,"/").replace(/~0/g,"~"),!(r=r[e]))),r},s=(e,t,r)=>(t.anchors||(t.anchors=i(e)),t.anchors.get(r)),u=(e,t)=>{for(var r in t)t.hasOwnProperty(r)&&"id"!==r&&"$id"!==r&&(e[r]=t[r])},h=(e,t,r,n)=>{let i;(i=void 0===n||0===n.length?t:"/"===n.charAt(0)?a(t,n):s(t,r,n))?u(e,i):o.push(ce("$ref '{0}' in '{1}' can not be resolved.",n||"",r.uri))},f=(r,n,i,a)=>{n=S(n=c&&!/^[A-Za-z][A-Za-z0-9+\-.+]*:\/\/.*/.test(n)?c.resolveRelativePath(n,a.uri):n);let s=this.getOrAddSchemaHandle(n);return s.getUnresolvedSchema().then(e=>{var t;return a.dependencies.add(n),e.errors.length&&(t=i?n+"#"+i:n,o.push(ce("Problems loading reference '{0}': {1}",t,e.errors[0]))),h(r,e.schema,s,i),d(r,e.schema,s)})},d=(e,a,s)=>{let o=[];return this.traverseNodes(e,r=>{let n=new Set;for(;r.$ref;){let e=r.$ref,t=e.split("#",2);if(delete r.$ref,0<t[0].length)return void o.push(f(r,t[0],t[1],s));var i;n.has(e)||(i=t[1],h(r,a,s,i),n.add(e))}r.$recursiveRef&&l.add("$recursiveRef"),r.$dynamicRef&&l.add("$dynamicRef")}),this.promise.all(o)},i=e=>{let n=new Map;return this.traverseNodes(e,e=>{let t=e.$id||e.id,r=De(t)&&"#"===t.charAt(0)?t.substring(1):e.$anchor;r&&(n.has(r)?o.push(ce("Duplicate anchor declaration: '{0}'",r)):n.set(r,e)),e.$recursiveAnchor&&l.add("$recursiveAnchor"),e.$dynamicAnchor&&l.add("$dynamicAnchor")}),n};return d(r,r,t).then(e=>{let t=[];return l.size&&t.push(ce("The schema uses meta-schema features ({0}) that are not yet supported by the validator.",Array.from(l.keys()).join(", "))),new Ut(r,o,t,n)})}traverseNodes(e,t){if(!e||"object"!=typeof e)return Promise.resolve(null);let r=new Set,n=[e],i=n.pop();for(;i;){if(!r.has(i)){r.add(i),t(i);{s=void 0;var a=[i.additionalItems,i.additionalProperties,i.not,i.contains,i.propertyNames,i.if,i.then,i.else,i.unevaluatedItems,i.unevaluatedProperties];for(var s of a)se(s)&&n.push(s)}{l=void 0;l=void 0;o=void 0;a=[i.definitions,i.$defs,i.properties,i.patternProperties,i.dependencies,i.dependentSchemas];for(var o of a)if(se(o))for(var l in o){l=o[l];se(l)&&n.push(l)}}{u=void 0;c=void 0;a=[i.anyOf,i.allOf,i.oneOf,i.prefixItems];for(var c of a)if(Array.isArray(c))for(var u of c)se(u)&&n.push(u)}{h=void 0;a=i.items;if(Array.isArray(a))for(var h of a)se(h)&&n.push(h);else se(a)&&n.push(a)}}i=n.pop()}}getSchemaFromProperty(t,e){if("object"===e.root?.type)for(var r of e.root.properties)if("$schema"===r.keyNode.value&&"string"===r.valueNode?.type){let e=r.valueNode.value;return e=this.contextService&&!/^\w[\w\d+.-]*:/.test(e)?this.contextService.resolveRelativePath(e,t):e}}getAssociatedSchemas(e){let t=Object.create(null),r=[],n=Jt(e);for(var i of this.filePatternAssociations)if(i.matchesPattern(n))for(var a of i.getURIs())t[a]||(r.push(a),t[a]=!0);return r}getSchemaURIsForResource(e,t){t=t&&this.getSchemaFromProperty(e,t);return t?[t]:this.getAssociatedSchemas(e)}getSchemaForResource(e,t){if(t){var t=this.getSchemaFromProperty(e,t);if(t)return t=S(t),this.getOrAddSchemaHandle(t).getResolvedSchema()}if(this.cachedSchemaForResource&&this.cachedSchemaForResource.resource===e)return this.cachedSchemaForResource.resolvedSchema;t=this.getAssociatedSchemas(e),t=0<t.length?this.createCombinedSchema(e,t).getResolvedSchema():this.promise.resolve(void 0);return this.cachedSchemaForResource={resource:e,resolvedSchema:t},t}createCombinedSchema(e,t){if(1===t.length)return this.getOrAddSchemaHandle(t[0]);e="schemaservice://combinedSchema/"+encodeURIComponent(e),t={allOf:t.map(e=>({$ref:e}))};return this.addSchemaHandle(e,t)}getMatchingSchemas(e,t,r){var n;return r?(n=r.id||"schemaservice://untitled/matchingSchemas/"+Bt++,this.addSchemaHandle(n,r).getResolvedSchema().then(e=>t.getMatchingSchemas(e.schema).filter(e=>!e.inverted))):this.getSchemaForResource(e.uri,t).then(e=>e?t.getMatchingSchemas(e.schema).filter(e=>!e.inverted):[])}},Bt=0;function S(e){try{return $t.parse(e).toString(!0)}catch{return e}}function Jt(e){try{return $t.parse(e).with({fragment:null,query:null}).toString(!0)}catch{return e}}function Kt(e){try{var t=$t.parse(e);if("file"===t.scheme)return t.fsPath}catch{}return e}function Wt(r,e){let t=[],n=[],i=[],a=-1,s=C(r.getText(),!1),o=s.scan();function l(e){t.push(e),n.push(i.length)}for(;17!==o;){switch(o){case 1:case 3:var c=r.positionAt(s.getTokenOffset()).line,c={startLine:c,endLine:c,kind:1===o?"object":"array"};i.push(c);break;case 2:case 4:c=2===o?"object":"array";if(0<i.length&&i[i.length-1].kind===c){let e=i.pop(),t=r.positionAt(s.getTokenOffset()).line;e&&t>e.startLine+1&&a!==e.startLine&&(e.endLine=t-1,l(e),a=e.startLine)}break;case 13:var u=r.positionAt(s.getTokenOffset()).line,h=r.positionAt(s.getTokenOffset()+s.getTokenLength()).line;1===s.getTokenError()&&u+1<r.lineCount?s.setPosition(r.offsetAt(k.create(u+1,0))):u<h&&(l({startLine:u,endLine:h,kind:W.Comment}),a=u);break;case 12:h=r.getText().substr(s.getTokenOffset(),s.getTokenLength()).match(/^\/\/\s*#(region\b)|(endregion\b)/);if(h){u=r.positionAt(s.getTokenOffset()).line;if(h[1]){var f={startLine:u,endLine:u,kind:W.Region};i.push(f)}else{let t=i.length-1;for(;0<=t&&i[t].kind!==W.Region;)t--;if(0<=t){let e=i[t];i.length=t,u>e.startLine&&a!==e.startLine&&(e.endLine=u,l(e),a=e.startLine)}}}}o=s.scan()}var d,m=e&&e.rangeLimit;if("number"!=typeof m||t.length<=m)return t;e&&e.onRangeLimitExceeded&&e.onRangeLimitExceeded(r.uri);let p=[];for(d of n)d<30&&(p[d]=(p[d]||0)+1);let g=0,y=0;for(let e=0;e<p.length;e++){var v=p[e];if(v){if(v+g>m){y=e;break}g+=v}}let b=[];for(let e=0;e<t.length;e++){var S=n[e];"number"==typeof S&&(S<y||S===y&&g++<m)&&b.push(t[e])}return b}function zt(c,e,u){function h(e,t){return A.create(c.positionAt(e),c.positionAt(t))}let f=C(c.getText(),!0);return e.map(function(e){let t=c.offsetAt(e),r=u.getNodeFromOffset(t,!0),n=[];for(;r;){switch(r.type){case"string":case"object":case"array":var i=r.offset+1,a=r.offset+r.length-1;i<a&&i<=t&&t<=a&&n.push(h(i,a)),n.push(h(r.offset,r.offset+r.length));break;case"number":case"boolean":case"null":case"property":n.push(h(r.offset,r.offset+r.length))}("property"===r.type||r.parent&&"array"===r.parent.type)&&(s=r.offset+r.length,o=5,f.setPosition(s),-1!==(s=f.scan()===o?f.getTokenOffset()+f.getTokenLength():-1)&&n.push(h(r.offset,s))),r=r.parent}var s,o;let l;for(let e=n.length-1;0<=e;e--)l=ke.create(n[e],l);return l=l||ke.create(A.create(e,e))})}function Ht(t,e,r){let n;r&&(i=t.offsetAt(r.start),r=t.offsetAt(r.end)-i,n={offset:i,length:r});var i={tabSize:e?e.tabSize:4,insertSpaces:!0===e?.insertSpaces,insertFinalNewline:!0===e?.insertFinalNewline,eol:`
`,keepLines:!0===e?.keepLines};return R(t.getText(),n,i).map(e=>G.replace(A.create(t.positionAt(e.offset),t.positionAt(e.offset+e.length)),e.content))}(Ft=w=w||{})[Ft.Object=0]="Object",Ft[Ft.Array=1]="Array";var Gt=class{constructor(e,t){this.propertyName=e??"",this.beginningLineNumber=t,this.childrenProperties=[],this.lastProperty=!1,this.noKeyName=!1}addChildProperty(t){if(0<(t.parent=this).childrenProperties.length){let e=0;(e=t.noKeyName?this.childrenProperties.length:function(e,t,r){var n=t.propertyName.toLowerCase(),i=e[0].propertyName.toLowerCase(),a=e[e.length-1].propertyName.toLowerCase();if(n<i)return 0;if(a<n)return e.length;let s=0,o=e.length-1;for(;s<=o;){var l=o+s>>1,c=r(t,e[l]);if(0<c)s=1+l;else{if(!(c<0))return l;o=l-1}}return-s-1}(this.childrenProperties,t,Xt))<0&&(e=-1*e-1),this.childrenProperties.splice(e,0,t)}else this.childrenProperties.push(t);return t}};function Xt(e,t){e=e.propertyName.toLowerCase(),t=t.propertyName.toLowerCase();return e<t?-1:t<e?1:0}function Zt(e,t){var t={...t,keepLines:!1},r=m.applyEdits(e,Ht(e,t,void 0)),r=m.create("test://test.json","json",0,r),r=function(o,e){if(0===e.childrenProperties.length)return o;let l=m.create("test://test.json","json",0,o.getText()),c=[];for(Qt(c,e,e.beginningLineNumber);0<c.length;){let e=c.shift(),a=e.propertyTreeArray,s=e.beginningLineNumber;for(let i=0;i<a.length;i++){let e=a[i],t=A.create(k.create(e.beginningLineNumber,0),k.create(e.endLineNumber+1,0)),r=o.getText(t),n=m.create("test://test.json","json",0,r);!0===e.lastProperty&&i!==a.length-1?(h=e.lineWhereToAddComma-e.beginningLineNumber,u=e.indexWhereToAddComa,h={range:A.create(k.create(h,u),k.create(h,u)),text:","},m.update(n,[h],1)):!1===e.lastProperty&&i===a.length-1&&(u=e.commaIndex,h=e.commaLine-e.beginningLineNumber,f={range:A.create(k.create(h,u),k.create(h,u+1)),text:""},m.update(n,[f],1));var u,h,f=e.endLineNumber-e.beginningLineNumber+1,d={range:A.create(k.create(s,0),k.create(s+f,0)),text:n.getText()};m.update(l,[d],1),Qt(c,e,s),s+=f}}return l}(r,function(t){let e=t.getText(),r=C(e,!1),n=new Gt,i=n,a=n,s=n,o,l=0,c=0,u,h,f=-1,d=-1,m=0,p=0,g=[],y=!1,v=!1;for(;17!==(o=r.scan());){var b,S;if(!0===y&&14!==o&&15!==o&&12!==o&&13!==o&&void 0===a.endLineNumber&&(b=r.getTokenStartLine(),2===h||4===h?s.endLineNumber=b-1:a.endLineNumber=b-1,m=b,y=!1),!0===v&&14!==o&&15!==o&&12!==o&&13!==o&&(m=r.getTokenStartLine(),v=!1),r.getTokenStartLine()!==l){for(let e=l;e<r.getTokenStartLine();e++){var x=t.getText(A.create(k.create(e,0),k.create(e+1,0))).length;c+=x}l=r.getTokenStartLine()}switch(o){case 10:(void 0===u||1===u||5===u&&g[g.length-1]===w.Object)&&(S=new Gt(r.getTokenValue(),m),s=a,a=i.addChildProperty(S));break;case 3:if(void 0===n.beginningLineNumber&&(n.beginningLineNumber=r.getTokenStartLine()),g[g.length-1]===w.Object)i=a;else if(g[g.length-1]===w.Array){let e=new Gt(r.getTokenValue(),m);e.noKeyName=!0,s=a,a=i.addChildProperty(e),i=a}g.push(w.Array),a.type=w.Array,m=r.getTokenStartLine(),m++;break;case 1:if(void 0===n.beginningLineNumber)n.beginningLineNumber=r.getTokenStartLine();else if(g[g.length-1]===w.Array){let e=new Gt(r.getTokenValue(),m);e.noKeyName=!0,s=a,a=i.addChildProperty(e)}a.type=w.Object,g.push(w.Object),i=a,m=r.getTokenStartLine(),m++;break;case 4:p=r.getTokenStartLine(),g.pop(),void 0!==a.endLineNumber||2!==u&&4!==u||(a.endLineNumber=p-1,a.lastProperty=!0,a.lineWhereToAddComma=f,a.indexWhereToAddComa=d,a=(s=a)?a.parent:void 0,i=a),n.endLineNumber=p,m=p+1;break;case 2:p=r.getTokenStartLine(),g.pop(),1!==u&&(void 0===a.endLineNumber&&(a.endLineNumber=p-1,a.lastProperty=!0,a.lineWhereToAddComma=f,a.indexWhereToAddComa=d),a=(s=a)?a.parent:void 0,i=a),n.endLineNumber=r.getTokenStartLine(),m=p+1;break;case 5:p=r.getTokenStartLine(),void 0!==a.endLineNumber||g[g.length-1]!==w.Object&&(g[g.length-1]!==w.Array||2!==u&&4!==u)||(a.endLineNumber=p,a.commaIndex=r.getTokenOffset()-c,a.commaLine=p),2!==u&&4!==u||(a=(s=a)?a.parent:void 0,i=a),m=p+1;break;case 13:5!==u||f!==r.getTokenStartLine()||(g[g.length-1]!==w.Array||2!==h&&4!==h)&&g[g.length-1]!==w.Object||(g[g.length-1]!==w.Array||2!==h&&4!==h)&&g[g.length-1]!==w.Object||(a.endLineNumber=void 0,y=!0),1!==u&&3!==u||f!==r.getTokenStartLine()||(v=!0)}14!==o&&13!==o&&12!==o&&15!==o&&(h=u,u=o,f=r.getTokenStartLine(),d=r.getTokenOffset()+r.getTokenLength()-c)}return n}(r)),t=Ht(r,t,void 0),r=m.applyEdits(r,t);return[G.replace(A.create(k.create(0,0),e.positionAt(e.getText().length)),r)]}function Qt(t,r,n){if(0!==r.childrenProperties.length)if(r.type===w.Object){let e=1/0;for(var i of r.childrenProperties)i.beginningLineNumber<e&&(e=i.beginningLineNumber);var a=e-r.beginningLineNumber;t.push(new er(n+=a,r.childrenProperties))}else r.type===w.Array&&function e(r,n,i){for(var a of n.childrenProperties){if(a.type===w.Object){let e=1/0;for(var s of a.childrenProperties)s.beginningLineNumber<e&&(e=s.beginningLineNumber);let t=e-a.beginningLineNumber;r.push(new er(i+a.beginningLineNumber-n.beginningLineNumber+t,a.childrenProperties))}a.type===w.Array&&e(r,a,i+a.beginningLineNumber-n.beginningLineNumber)}}(t,r,n)}var Yt,er=class{constructor(e,t){this.beginningLineNumber=e,this.propertyTreeArray=t}};function tr(r,n){let i=[];return n.visit(e=>{var t;return"property"===e.type&&"$ref"===e.keyNode.value&&"string"===e.valueNode?.type&&(t=e.valueNode.value,(t=function(e,t){t=function(e){return"#"===e?[]:"#"!==e[0]||"/"!==e[1]?null:e.substring(2).split(/\//).map(rr)}(t);return t?function r(n,i){if(!i)return null;if(0===n.length)return i;let a=n.shift();{if(i&&"object"===i.type){let e=i.properties.find(e=>e.keyNode.value===a);return e?r(n,e.valueNode):null}if(i&&"array"===i.type&&a.match(/^(0|[1-9][0-9]*)$/)){let e=Number.parseInt(a),t=i.items[e];return t?r(n,t):null}}return null}(t,e.root):null}(n,t))&&(t=r.positionAt(t.offset),i.push({target:`${r.uri}#${t.line+1},`+(t.character+1),range:(t=r,e=e.valueNode,A.create(t.positionAt(e.offset+1),t.positionAt(e.offset+e.length-1)))}))),!0}),Promise.resolve(i)}function rr(e){return e.replace(/~1/g,"/").replace(/~0/g,"~")}function nr(e){let t=e.promiseConstructor||Promise,r=new qt(e.schemaRequestService,e.workspaceContext,t),n=(r.setSchemaContributions(At),new mt(r,e.contributions,t,e.clientCapabilities)),i=new pt(r,e.contributions,t),a=new St(r),s=new yt(r,t);return{configure:e=>{r.clearExternalSchemas(),e.schemas?.forEach(r.registerExternalSchema.bind(r)),s.configure(e)},resetSchema:e=>r.onResourceChange(e),doValidation:s.doValidation.bind(s),getLanguageStatus:s.getLanguageStatus.bind(s),parseJSONDocument:e=>dt(e,{collectComments:!0}),newJSONDocument:(e,t)=>{var[e,t=[]]=[e,t];return new ht(e,t,[])},getMatchingSchemas:r.getMatchingSchemas.bind(r),doResolve:n.doResolve.bind(n),doComplete:n.doComplete.bind(n),findDocumentSymbols:a.findDocumentSymbols.bind(a),findDocumentSymbols2:a.findDocumentSymbols2.bind(a),findDocumentColors:a.findDocumentColors.bind(a),getColorPresentations:a.getColorPresentations.bind(a),doHover:i.doHover.bind(i),getFoldingRanges:Wt,getSelectionRanges:zt,findDefinition:()=>Promise.resolve([]),findLinks:tr,format:(e,t,r)=>Ht(e,r,t),sort:(e,t)=>Zt(e,t)}}typeof fetch<"u"&&(Yt=function(e){return fetch(e).then(e=>e.text())});var ir=class{constructor(e,t){this._ctx=e,this._languageSettings=t.languageSettings,this._languageId=t.languageId,this._languageService=nr({workspaceContext:{resolveRelativePath:(r,n)=>{n=n.substr(0,n.lastIndexOf("/")+1);if(function(e){return e.charCodeAt(0)===ar}(r)){let e=$t.parse(n),t=r.split("/");return e.with({path:or(t)}).toString()}{r=[r];let e=$t.parse(n),t=e.path.split("/");for(var i of r)t.push(...i.split("/"));return e.with({path:or(t)}).toString()}}},schemaRequestService:t.enableSchemaRequest?Yt:void 0,clientCapabilities:Be.LATEST}),this._languageService.configure(this._languageSettings)}async doValidation(e){var t,e=this._getTextDocument(e);return e?(t=this._languageService.parseJSONDocument(e),this._languageService.doValidation(e,t,this._languageSettings)):Promise.resolve([])}async doComplete(e,t){e=this._getTextDocument(e);if(!e)return null;var r=this._languageService.parseJSONDocument(e);return this._languageService.doComplete(e,t,r)}async doResolve(e){return this._languageService.doResolve(e)}async doHover(e,t){e=this._getTextDocument(e);if(!e)return null;var r=this._languageService.parseJSONDocument(e);return this._languageService.doHover(e,t,r)}async format(e,t,r){e=this._getTextDocument(e);if(!e)return[];e=this._languageService.format(e,t,r);return Promise.resolve(e)}async resetSchema(e){return Promise.resolve(this._languageService.resetSchema(e))}async findDocumentSymbols(e){e=this._getTextDocument(e);if(!e)return[];var t=this._languageService.parseJSONDocument(e),e=this._languageService.findDocumentSymbols2(e,t);return Promise.resolve(e)}async findDocumentColors(e){e=this._getTextDocument(e);if(!e)return[];var t=this._languageService.parseJSONDocument(e),e=this._languageService.findDocumentColors(e,t);return Promise.resolve(e)}async getColorPresentations(e,t,r){e=this._getTextDocument(e);if(!e)return[];var n=this._languageService.parseJSONDocument(e),e=this._languageService.getColorPresentations(e,n,t,r);return Promise.resolve(e)}async getFoldingRanges(e,t){e=this._getTextDocument(e);if(!e)return[];e=this._languageService.getFoldingRanges(e,t);return Promise.resolve(e)}async getSelectionRanges(e,t){e=this._getTextDocument(e);if(!e)return[];var r=this._languageService.parseJSONDocument(e),e=this._languageService.getSelectionRanges(e,t,r);return Promise.resolve(e)}async parseJSONDocument(e){e=this._getTextDocument(e);if(!e)return null;e=this._languageService.parseJSONDocument(e);return Promise.resolve(e)}async getMatchingSchemas(e){e=this._getTextDocument(e);if(!e)return[];var t=this._languageService.parseJSONDocument(e);return Promise.resolve(this._languageService.getMatchingSchemas(e,t))}_getTextDocument(e){var t;for(t of this._ctx.getMirrorModels())if(t.uri.toString()===e)return m.create(e,this._languageId,t.version,t.getValue());return null}},ar=47,sr=46;function or(e){let t=[];for(var r of e)0===r.length||1===r.length&&r.charCodeAt(0)===sr||(2===r.length&&r.charCodeAt(0)===sr&&r.charCodeAt(1)===sr?t.pop():t.push(r));1<e.length&&0===e[e.length-1].length&&t.push("");let n=t.join("/");return n=0===e[0].length?"/"+n:n}var l=P,lr=u({},"__esModule",{value:!0}),cr=l,ur=void 0,hr=void 0;if(cr&&"object"==typeof cr||"function"==typeof cr)for(let e of O(cr))N.call(lr,e)||e===ur||u(lr,e,{get:()=>cr[e],enumerable:!(hr=x(cr,e))||hr.enumerable});return lr})()});