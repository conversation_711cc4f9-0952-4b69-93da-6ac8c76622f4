using Microsoft.AspNetCore.Mvc;
using Microsoft.AspNetCore.Authorization;
using Elsa.Workflows.Management;
using Elsa.Workflows.Management.Entities;
using Elsa.Workflows.Management.Filters;
using Elsa.Common.Models;
using Harmoni360.Application.Common.Interfaces;
using Microsoft.Extensions.Logging;

namespace Harmoni360.Web.Controllers;

[ApiController]
[Route("api/[controller]")]
[Authorize(Roles = "SuperAdmin,Developer")]
public class WorkflowDebugController : ControllerBase
{
    private readonly IWorkflowDefinitionStore _workflowDefinitionStore;
    private readonly IIncidentWorkflowService _incidentWorkflowService;
    private readonly ILogger<WorkflowDebugController> _logger;

    public WorkflowDebugController(
        IWorkflowDefinitionStore workflowDefinitionStore,
        IIncidentWorkflowService incidentWorkflowService,
        ILogger<WorkflowDebugController> logger)
    {
        _workflowDefinitionStore = workflowDefinitionStore;
        _incidentWorkflowService = incidentWorkflowService;
        _logger = logger;
    }

    [HttpGet("status")]
    public async Task<IActionResult> GetWorkflowStatus()
    {
        try
        {
            // Get all workflow definitions
            var filter = new WorkflowDefinitionFilter();
            var allDefinitions = await _workflowDefinitionStore.FindManyAsync(filter, HttpContext.RequestAborted);

            // Look for incident workflow specifically
            var incidentWorkflow = allDefinitions.FirstOrDefault(w => w.Name == "IncidentProcessWorkflow");

            var result = new
            {
                TotalWorkflowDefinitions = allDefinitions.Count(),
                IncidentWorkflowExists = incidentWorkflow != null,
                IncidentWorkflowDetails = incidentWorkflow != null ? new
                {
                    incidentWorkflow.Id,
                    incidentWorkflow.Name,
                    incidentWorkflow.Version,
                    incidentWorkflow.IsPublished,
                    incidentWorkflow.IsLatest,
                    incidentWorkflow.CreatedAt
                } : null,
                AllWorkflows = allDefinitions.Select(w => new
                {
                    w.Id,
                    w.Name,
                    w.Version,
                    w.IsPublished,
                    w.IsLatest,
                    w.CreatedAt
                }).ToList()
            };

            return Ok(result);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to get workflow status");
            return StatusCode(500, new { error = ex.Message });
        }
    }

    [HttpPost("test-workflow-start")]
    public async Task<IActionResult> TestWorkflowStart()
    {
        try
        {
            _logger.LogInformation("Testing workflow start manually");
            
            var workflowInstanceId = await _incidentWorkflowService.StartIncidentWorkflowAsync(999, HttpContext.RequestAborted);
            
            return Ok(new
            {
                Success = workflowInstanceId != null,
                WorkflowInstanceId = workflowInstanceId,
                Message = workflowInstanceId != null 
                    ? "Workflow started successfully" 
                    : "Failed to start workflow"
            });
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to test workflow start");
            return StatusCode(500, new { error = ex.Message });
        }
    }

    [HttpPost("register-incident-workflow")]
    public async Task<IActionResult> RegisterIncidentWorkflow()
    {
        try
        {
            _logger.LogInformation("Manually registering incident workflow");

            // Create workflow instance
            var workflow = new Harmoni360.Application.Features.WorkflowManagement.IncidentWorkflow.Workflows.IncidentManagementWorkflow();
            var workflowName = "IncidentProcessWorkflow";
            var definitionId = GenerateDeterministicGuid(workflowName);

            // Check if already exists
            var filter = new WorkflowDefinitionFilter
            {
                Name = workflowName,
                VersionOptions = VersionOptions.Latest
            };
            var existing = (await _workflowDefinitionStore.FindManyAsync(filter, HttpContext.RequestAborted)).FirstOrDefault();

            WorkflowDefinition definition;
            if (existing != null)
            {
                definition = existing;
                definition.Version += 1;
                _logger.LogInformation("Updating existing workflow definition");
            }
            else
            {
                definition = new WorkflowDefinition
                {
                    Id = Guid.NewGuid().ToString(),
                    DefinitionId = definitionId,
                    Name = workflowName,
                    Version = 1,
                    IsLatest = true,
                    IsPublished = true,
                    CreatedAt = DateTimeOffset.UtcNow
                };
                _logger.LogInformation("Creating new workflow definition");
            }

            // Create basic workflow structure
            var workflowModel = new
            {
                id = definition.DefinitionId,
                name = workflowName,
                displayName = "Incident Management Workflow",
                description = "Main workflow for incident management",
                version = definition.Version,
                isPublished = true,
                isLatest = true,
                customProperties = new
                {
                    category = "HSE",
                    module = "IncidentManagement",
                    tags = new[] { "incident", "hse", "management" },
                    autoDiscovered = true,
                    registeredAt = DateTimeOffset.UtcNow
                },
                root = new
                {
                    type = "Elsa.Workflows.Activities.Sequence",
                    id = Guid.NewGuid().ToString(),
                    name = "Root",
                    displayName = "Incident Management Workflow",
                    description = "Main workflow for incident management"
                }
            };

            definition.StringData = System.Text.Json.JsonSerializer.Serialize(workflowModel, new System.Text.Json.JsonSerializerOptions
            {
                WriteIndented = true,
                PropertyNamingPolicy = System.Text.Json.JsonNamingPolicy.CamelCase
            });

            await _workflowDefinitionStore.SaveAsync(definition, HttpContext.RequestAborted);

            return Ok(new
            {
                Success = true,
                Message = "Workflow registered successfully",
                WorkflowId = definition.Id,
                DefinitionId = definition.DefinitionId,
                Name = definition.Name,
                Version = definition.Version
            });
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to register incident workflow");
            return StatusCode(500, new { error = ex.Message, stackTrace = ex.StackTrace });
        }
    }

    private string GenerateDeterministicGuid(string input)
    {
        using var md5 = System.Security.Cryptography.MD5.Create();
        var hash = md5.ComputeHash(System.Text.Encoding.UTF8.GetBytes(input));
        return new Guid(hash).ToString();
    }
}