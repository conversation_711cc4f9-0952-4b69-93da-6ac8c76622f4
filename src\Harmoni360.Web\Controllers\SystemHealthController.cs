using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using Microsoft.Extensions.Diagnostics.HealthChecks;
using System.Text.Json;

namespace Harmoni360.Web.Controllers;

[ApiController]
[Route("api/system/[controller]")]
[Authorize]
public class SystemHealthController : ControllerBase
{
    private readonly HealthCheckService _healthCheckService;
    private readonly ILogger<SystemHealthController> _logger;

    public SystemHealthController(
        HealthCheckService healthCheckService,
        ILogger<SystemHealthController> logger)
    {
        _healthCheckService = healthCheckService;
        _logger = logger;
    }

    /// <summary>
    /// Get overall health status of all systems
    /// </summary>
    [HttpGet]
    public async Task<IActionResult> GetHealth()
    {
        try
        {
            var healthReport = await _healthCheckService.CheckHealthAsync();
            
            var response = new
            {
                Status = healthReport.Status.ToString(),
                TotalDuration = healthReport.TotalDuration.TotalMilliseconds,
                Results = healthReport.Entries.Select(entry => new
                {
                    Name = entry.Key,
                    Status = entry.Value.Status.ToString(),
                    Description = entry.Value.Description,
                    Duration = entry.Value.Duration.TotalMilliseconds,
                    Data = entry.Value.Data,
                    Exception = entry.Value.Exception?.Message,
                    Tags = entry.Value.Tags
                }).ToList()
            };

            var statusCode = healthReport.Status switch
            {
                HealthStatus.Healthy => 200,
                HealthStatus.Degraded => 200, // Still operational
                HealthStatus.Unhealthy => 503,
                _ => 503
            };

            return StatusCode(statusCode, response);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Health check failed with exception");
            return StatusCode(503, new
            {
                Status = "Unhealthy",
                Error = "Health check service failed",
                Message = ex.Message
            });
        }
    }

    /// <summary>
    /// Get detailed health status of the workflow system
    /// </summary>
    [HttpGet("workflow")]
    public async Task<IActionResult> GetWorkflowHealth()
    {
        try
        {
            var healthReport = await _healthCheckService.CheckHealthAsync(
                check => check.Name == "workflow-system");

            if (healthReport.Entries.TryGetValue("workflow-system", out var workflowEntry))
            {
                var response = new
                {
                    Status = workflowEntry.Status.ToString(),
                    Description = workflowEntry.Description,
                    Duration = workflowEntry.Duration.TotalMilliseconds,
                    Data = workflowEntry.Data,
                    Exception = workflowEntry.Exception?.Message,
                    Timestamp = DateTime.UtcNow
                };

                var statusCode = workflowEntry.Status switch
                {
                    HealthStatus.Healthy => 200,
                    HealthStatus.Degraded => 200,
                    HealthStatus.Unhealthy => 503,
                    _ => 503
                };

                return StatusCode(statusCode, response);
            }

            return NotFound("Workflow health check not found");
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Workflow health check failed");
            return StatusCode(503, new
            {
                Status = "Unhealthy",
                Error = "Workflow health check failed",
                Message = ex.Message
            });
        }
    }

    /// <summary>
    /// Get health status of Elsa Studio integration
    /// </summary>
    [HttpGet("elsa-studio")]
    public async Task<IActionResult> GetElsaStudioHealth()
    {
        try
        {
            var healthReport = await _healthCheckService.CheckHealthAsync(
                check => check.Name == "elsa-studio");

            if (healthReport.Entries.TryGetValue("elsa-studio", out var elsaEntry))
            {
                var response = new
                {
                    Status = elsaEntry.Status.ToString(),
                    Description = elsaEntry.Description,
                    Duration = elsaEntry.Duration.TotalMilliseconds,
                    Data = elsaEntry.Data,
                    Exception = elsaEntry.Exception?.Message,
                    Timestamp = DateTime.UtcNow
                };

                var statusCode = elsaEntry.Status switch
                {
                    HealthStatus.Healthy => 200,
                    HealthStatus.Degraded => 200,
                    HealthStatus.Unhealthy => 503,
                    _ => 503
                };

                return StatusCode(statusCode, response);
            }

            return NotFound("Elsa Studio health check not found");
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Elsa Studio health check failed");
            return StatusCode(503, new
            {
                Status = "Unhealthy",
                Error = "Elsa Studio health check failed",
                Message = ex.Message
            });
        }
    }

    /// <summary>
    /// Get health status of external integrations
    /// </summary>
    [HttpGet("external-integrations")]
    public async Task<IActionResult> GetExternalIntegrationsHealth()
    {
        try
        {
            var healthReport = await _healthCheckService.CheckHealthAsync(
                check => check.Name == "external-integrations");

            if (healthReport.Entries.TryGetValue("external-integrations", out var externalEntry))
            {
                var response = new
                {
                    Status = externalEntry.Status.ToString(),
                    Description = externalEntry.Description,
                    Duration = externalEntry.Duration.TotalMilliseconds,
                    Data = externalEntry.Data,
                    Exception = externalEntry.Exception?.Message,
                    Timestamp = DateTime.UtcNow
                };

                var statusCode = externalEntry.Status switch
                {
                    HealthStatus.Healthy => 200,
                    HealthStatus.Degraded => 200,
                    HealthStatus.Unhealthy => 503,
                    _ => 503
                };

                return StatusCode(statusCode, response);
            }

            return NotFound("External integrations health check not found");
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "External integrations health check failed");
            return StatusCode(503, new
            {
                Status = "Unhealthy",
                Error = "External integrations health check failed",
                Message = ex.Message
            });
        }
    }

    /// <summary>
    /// Get database health status
    /// </summary>
    [HttpGet("database")]
    public async Task<IActionResult> GetDatabaseHealth()
    {
        try
        {
            var healthReport = await _healthCheckService.CheckHealthAsync(
                check => check.Name == "database");

            if (healthReport.Entries.TryGetValue("database", out var dbEntry))
            {
                var response = new
                {
                    Status = dbEntry.Status.ToString(),
                    Description = dbEntry.Description,
                    Duration = dbEntry.Duration.TotalMilliseconds,
                    Data = dbEntry.Data,
                    Exception = dbEntry.Exception?.Message,
                    Timestamp = DateTime.UtcNow
                };

                var statusCode = dbEntry.Status switch
                {
                    HealthStatus.Healthy => 200,
                    HealthStatus.Degraded => 200,
                    HealthStatus.Unhealthy => 503,
                    _ => 503
                };

                return StatusCode(statusCode, response);
            }

            return NotFound("Database health check not found");
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Database health check failed");
            return StatusCode(503, new
            {
                Status = "Unhealthy",
                Error = "Database health check failed",
                Message = ex.Message
            });
        }
    }

    /// <summary>
    /// Get a lightweight readiness check for load balancers
    /// </summary>
    [HttpGet("ready")]
    [AllowAnonymous]
    public async Task<IActionResult> GetReadiness()
    {
        try
        {
            // Only check critical systems for readiness
            var healthReport = await _healthCheckService.CheckHealthAsync(
                check => check.Name == "self" || check.Name == "database");

            var isReady = healthReport.Status == HealthStatus.Healthy || 
                         healthReport.Status == HealthStatus.Degraded;

            var response = new
            {
                Status = isReady ? "Ready" : "NotReady",
                Timestamp = DateTime.UtcNow,
                Version = "1.0.0"
            };

            return isReady ? Ok(response) : StatusCode(503, response);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Readiness check failed");
            return StatusCode(503, new
            {
                Status = "NotReady",
                Error = "Readiness check failed",
                Message = ex.Message,
                Timestamp = DateTime.UtcNow
            });
        }
    }

    /// <summary>
    /// Get a simple liveness check for container orchestrators
    /// </summary>
    [HttpGet("live")]
    [AllowAnonymous]
    public IActionResult GetLiveness()
    {
        // Simple liveness check - if this endpoint responds, the app is alive
        return Ok(new
        {
            Status = "Alive",
            Timestamp = DateTime.UtcNow,
            ProcessId = Environment.ProcessId,
            MachineName = Environment.MachineName,
            Version = "1.0.0"
        });
    }

    /// <summary>
    /// Get system metrics and performance indicators
    /// </summary>
    [HttpGet("metrics")]
    [Authorize(Roles = "SuperAdmin,HSEManager")]
    public async Task<IActionResult> GetMetrics()
    {
        try
        {
            var healthReport = await _healthCheckService.CheckHealthAsync();
            
            // Collect system metrics
            var process = System.Diagnostics.Process.GetCurrentProcess();
            var gc = GC.GetTotalMemory(false);

            var metrics = new
            {
                Timestamp = DateTime.UtcNow,
                SystemMetrics = new
                {
                    ProcessId = Environment.ProcessId,
                    MachineName = Environment.MachineName,
                    WorkingSet = process.WorkingSet64,
                    PrivateMemorySize = process.PrivateMemorySize64,
                    GCMemory = gc,
                    ThreadCount = process.Threads.Count,
                    HandleCount = process.HandleCount,
                    StartTime = process.StartTime,
                    TotalProcessorTime = process.TotalProcessorTime.TotalMilliseconds,
                    ProcessorCount = Environment.ProcessorCount
                },
                HealthChecks = new
                {
                    OverallStatus = healthReport.Status.ToString(),
                    TotalDuration = healthReport.TotalDuration.TotalMilliseconds,
                    CheckCount = healthReport.Entries.Count,
                    HealthyCount = healthReport.Entries.Count(e => e.Value.Status == HealthStatus.Healthy),
                    DegradedCount = healthReport.Entries.Count(e => e.Value.Status == HealthStatus.Degraded),
                    UnhealthyCount = healthReport.Entries.Count(e => e.Value.Status == HealthStatus.Unhealthy)
                },
                Environment = new
                {
                    EnvironmentName = Environment.GetEnvironmentVariable("ASPNETCORE_ENVIRONMENT"),
                    Version = Environment.Version.ToString(),
                    OSVersion = Environment.OSVersion.ToString(),
                    Is64BitProcess = Environment.Is64BitProcess,
                    CommandLine = Environment.CommandLine
                }
            };

            return Ok(metrics);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to collect system metrics");
            return StatusCode(500, new
            {
                Error = "Failed to collect metrics",
                Message = ex.Message,
                Timestamp = DateTime.UtcNow
            });
        }
    }
}