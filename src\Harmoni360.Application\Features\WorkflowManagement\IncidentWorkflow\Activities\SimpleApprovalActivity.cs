using Elsa.Extensions;
using Elsa.Workflows;
using Elsa.Workflows.Attributes;
using Elsa.Workflows.Models;
using Harmoni360.Application.Features.WorkflowManagement.IncidentWorkflow.Models;
using Microsoft.Extensions.Logging;

namespace Harmoni360.Application.Features.WorkflowManagement.IncidentWorkflow.Activities;

/// <summary>
/// Simple approval activity for incident workflow gates
/// </summary>
[Activity("Incident Management", "Approval Gate", "Handles approval gates in incident workflow")]
public class SimpleApprovalActivity : CodeActivity
{
    [Input(Description = "The incident workflow context")]
    public Input<IncidentWorkflowContext> Context { get; set; } = default!;

    [Input(Description = "From status")]
    public Input<string> FromStatus { get; set; } = default!;

    [Input(Description = "To status")]
    public Input<string> ToStatus { get; set; } = default!;

    [Input(Description = "Required roles for approval")]
    public Input<string> RequiredRoles { get; set; } = default!;

    [Input(Description = "Timeout hours")]
    public Input<int> TimeoutHours { get; set; } = default!;

    [Input(Description = "Gate description")]
    public Input<string> GateDescription { get; set; } = default!;

    protected override async ValueTask ExecuteAsync(ActivityExecutionContext context)
    {
        var logger = context.GetRequiredService<ILogger<SimpleApprovalActivity>>();
        
        var fromStatus = FromStatus.Get(context);
        var toStatus = ToStatus.Get(context);
        var requiredRoles = RequiredRoles.Get(context);
        var timeoutHours = TimeoutHours.Get(context);
        var gateDescription = GateDescription.Get(context);

        logger.LogInformation("APPROVAL GATE: {Description}", gateDescription);
        logger.LogInformation("Required Approvers: {RequiredRoles}", requiredRoles);
        logger.LogInformation("Status Transition: {FromStatus} → {ToStatus}", fromStatus, toStatus);
        logger.LogInformation("Timeout: {TimeoutHours} hours", timeoutHours);

        // In a real implementation, this would:
        // 1. Create a bookmark to wait for approval
        // 2. Send notifications to required roles
        // 3. Wait for approval response
        // 4. Update incident status when approved
        
        // For now, auto-approve to demonstrate the flow
        logger.LogInformation("APPROVAL GRANTED (auto-approved for demonstration)");
        
        await Task.CompletedTask;
    }
}