using Harmoni360.Domain.Entities;
using Harmoni360.Domain.Enums;

namespace Harmoni360.Application.Common.Interfaces;

public interface IWorkflowAuditService
{
    // Workflow execution auditing
    Task LogWorkflowStartedAsync(string workflowInstanceId, string workflowDefinitionId, string initiatedBy, 
        Dictionary<string, object>? inputData = null, CancellationToken cancellationToken = default);

    Task LogWorkflowCompletedAsync(string workflowInstanceId, string completedBy, 
        Dictionary<string, object>? outputData = null, TimeSpan? duration = null, CancellationToken cancellationToken = default);

    Task LogWorkflowFailedAsync(string workflowInstanceId, string errorMessage, Exception? exception = null, 
        string? failedBy = null, CancellationToken cancellationToken = default);

    Task LogWorkflowCancelledAsync(string workflowInstanceId, string cancelledBy, string? reason = null, 
        CancellationToken cancellationToken = default);

    Task LogWorkflowSuspendedAsync(string workflowInstanceId, string suspendedBy, string? reason = null, 
        CancellationToken cancellationToken = default);

    Task LogWorkflowResumedAsync(string workflowInstanceId, string resumedBy, 
        CancellationToken cancellationToken = default);

    // Activity execution auditing
    Task LogActivityStartedAsync(string workflowInstanceId, string activityId, string activityName, 
        string? activityType = null, Dictionary<string, object>? inputData = null, CancellationToken cancellationToken = default);

    Task LogActivityCompletedAsync(string workflowInstanceId, string activityId, string activityName, 
        Dictionary<string, object>? outputData = null, TimeSpan? duration = null, CancellationToken cancellationToken = default);

    Task LogActivityFailedAsync(string workflowInstanceId, string activityId, string activityName, 
        string errorMessage, Exception? exception = null, CancellationToken cancellationToken = default);

    Task LogActivitySkippedAsync(string workflowInstanceId, string activityId, string activityName, 
        string? reason = null, CancellationToken cancellationToken = default);

    // User action auditing
    Task LogUserActionAsync(string workflowInstanceId, string userId, string action, 
        string resourceType, string resourceId, Dictionary<string, object>? actionData = null, 
        CancellationToken cancellationToken = default);

    Task LogApprovalActionAsync(string workflowInstanceId, string approvalId, string approver, 
        bool isApproved, string? comments = null, CancellationToken cancellationToken = default);

    Task LogDataAccessAsync(string workflowInstanceId, string userId, string dataType, 
        string dataId, string accessType, CancellationToken cancellationToken = default);

    // Security auditing
    Task LogSecurityEventAsync(string workflowInstanceId, string eventType, string userId, 
        SecuritySeverity severity, string description, Dictionary<string, object>? eventData = null, 
        CancellationToken cancellationToken = default);

    Task LogPermissionCheckAsync(string workflowInstanceId, string userId, string permission, 
        bool wasGranted, string? resource = null, CancellationToken cancellationToken = default);

    Task LogAuthenticationEventAsync(string workflowInstanceId, string userId, string eventType, 
        bool wasSuccessful, string? details = null, CancellationToken cancellationToken = default);

    // Performance auditing
    Task LogPerformanceMetricAsync(string workflowInstanceId, string metricName, double value, 
        string? unit = null, Dictionary<string, object>? additionalData = null, CancellationToken cancellationToken = default);

    Task LogResourceUsageAsync(string workflowInstanceId, string resourceType, double usage, 
        string unit, DateTime timestamp, CancellationToken cancellationToken = default);

    // Integration auditing
    Task LogExternalIntegrationAsync(string workflowInstanceId, string integrationType, string operation, 
        bool wasSuccessful, string? externalId = null, Dictionary<string, object>? integrationData = null, 
        CancellationToken cancellationToken = default);

    Task LogNotificationSentAsync(string workflowInstanceId, string notificationType, string recipient, 
        bool wasSuccessful, string? messageId = null, CancellationToken cancellationToken = default);

    // Query methods
    Task<IEnumerable<WorkflowExecutionLog>> GetWorkflowAuditLogsAsync(string workflowInstanceId, 
        DateTime? fromDate = null, DateTime? toDate = null, CancellationToken cancellationToken = default);

    Task<IEnumerable<WorkflowExecutionLog>> GetActivityAuditLogsAsync(string workflowInstanceId, 
        string activityId, CancellationToken cancellationToken = default);

    Task<IEnumerable<WorkflowExecutionLog>> GetUserAuditLogsAsync(string userId, 
        DateTime? fromDate = null, DateTime? toDate = null, CancellationToken cancellationToken = default);

    Task<IEnumerable<WorkflowExecutionLog>> GetSecurityAuditLogsAsync(SecuritySeverity? minimumSeverity = null, 
        DateTime? fromDate = null, DateTime? toDate = null, CancellationToken cancellationToken = default);

    Task<Dictionary<string, object>> GetWorkflowPerformanceMetricsAsync(string workflowInstanceId, 
        CancellationToken cancellationToken = default);

    Task<Dictionary<string, object>> GetWorkflowComplianceReportAsync(string workflowDefinitionId, 
        DateTime fromDate, DateTime toDate, CancellationToken cancellationToken = default);

    // Cleanup and maintenance
    Task ArchiveOldLogsAsync(DateTime cutoffDate, CancellationToken cancellationToken = default);
    Task<int> GetAuditLogCountAsync(string? workflowInstanceId = null, CancellationToken cancellationToken = default);
    Task PurgeArchivedLogsAsync(DateTime cutoffDate, CancellationToken cancellationToken = default);
}