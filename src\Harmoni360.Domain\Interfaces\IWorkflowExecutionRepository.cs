using Harmoni360.Domain.Common;
using Harmoni360.Domain.Entities;
using Harmoni360.Domain.Enums;

namespace Harmoni360.Domain.Interfaces;

public interface IWorkflowExecutionRepository : IRepository<WorkflowExecution>
{
    Task<WorkflowExecution?> GetByInstanceIdAsync(string instanceId, CancellationToken cancellationToken = default);
    
    Task<IEnumerable<WorkflowExecution>> GetByWorkflowDefinitionIdAsync(
        string workflowDefinitionId, 
        CancellationToken cancellationToken = default);
    
    Task<IEnumerable<WorkflowExecution>> GetByStatusAsync(
        WorkflowStatus status, 
        CancellationToken cancellationToken = default);
    
    Task<IEnumerable<WorkflowExecution>> GetByCorrelationIdAsync(
        string correlationId, 
        CancellationToken cancellationToken = default);
    
    Task<IEnumerable<WorkflowExecution>> GetByInitiatorAsync(
        string initiatedBy, 
        int pageNumber = 1, 
        int pageSize = 50, 
        CancellationToken cancellationToken = default);

    Task<IEnumerable<WorkflowExecution>> GetActiveWorkflowsAsync(CancellationToken cancellationToken = default);
    
    Task<IEnumerable<WorkflowExecution>> GetCompletedWorkflowsAsync(
        DateTime? startDate = null, 
        DateTime? endDate = null, 
        CancellationToken cancellationToken = default);

    Task<int> GetCountByStatusAsync(WorkflowStatus status, CancellationToken cancellationToken = default);
    
    Task<bool> ExistsAsync(string instanceId, CancellationToken cancellationToken = default);
    
    Task<WorkflowExecution?> GetWithLogsAsync(int id, CancellationToken cancellationToken = default);
    
    Task<WorkflowExecution?> GetWithBookmarksAsync(int id, CancellationToken cancellationToken = default);
    
    Task<WorkflowExecution?> GetFullAsync(int id, CancellationToken cancellationToken = default);
}