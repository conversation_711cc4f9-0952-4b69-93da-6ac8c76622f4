using Elsa.Workflows;
using Elsa.Workflows.Attributes;
using Harmoni360.Application.Common.Interfaces;
using Harmoni360.Application.Features.WorkflowManagement.IncidentWorkflow.Models;
using Harmoni360.Domain.Entities;
using Harmoni360.Domain.Interfaces;
using Microsoft.Extensions.Logging;
using Elsa.Extensions;
using Elsa.Workflows.Models;

namespace Harmoni360.Application.Features.WorkflowManagement.IncidentWorkflow.Activities;

/// <summary>
/// Activity that assigns individual corrective actions to responsible parties
/// </summary>
[Activity("Incident Management", "Assign Corrective Action", "Assigns a corrective action to a responsible party")]
public class AssignCorrectiveActionActivity : IncidentActivityBase
{
    private readonly ICorrectiveActionService _correctiveActionService;
    
    public AssignCorrectiveActionActivity(
        ILogger<AssignCorrectiveActionActivity> logger,
        IIncidentRepository incidentRepository,
        ICurrentUserService currentUserService,
        ICorrectiveActionService correctiveActionService)
        : base(logger, incidentRepository, currentUserService)
    {
        _correctiveActionService = correctiveActionService;
    }
    
    /// <summary>
    /// The corrective action to assign
    /// </summary>
    [Input(
        Description = "The corrective action to assign",
        DisplayName = "Corrective Action"
    )]
    public Input<CorrectiveActionWorkflowModel> Action { get; set; } = default!;
    
    /// <summary>
    /// The incident workflow context
    /// </summary>
    [Input(
        Description = "The incident workflow context",
        DisplayName = "Incident Context"
    )]
    public Input<IncidentWorkflowContext> Context { get; set; } = default!;
    
    protected override async ValueTask ExecuteAsync(ActivityExecutionContext context)
    {
        const string activityName = nameof(AssignCorrectiveActionActivity);
        LogActivity(activityName, "Starting corrective action assignment");
        
        try
        {
            var action = Action.Get(context);
            var workflowContext = Context.Get(context);
            
            // Assign the corrective action
            action.Status = "Assigned";
            await _correctiveActionService.AssignCorrectiveAction(action);
            
            LogActivity(activityName, 
                "Corrective action assigned - ID: {ActionId}, Assigned to: {AssignedTo}",
                action.Id, action.AssignedTo);
        }
        catch (Exception ex)
        {
            LogActivityError(activityName, ex, "Failed to assign corrective action");
        }
    }
}

/// <summary>
/// Activity that monitors the progress of corrective actions
/// </summary>
[Activity("Incident Management", "Monitor Action Progress", "Monitors the progress of corrective action completion")]
public class MonitorActionProgressActivity : IncidentActivityBase
{
    private readonly ICorrectiveActionService _correctiveActionService;
    
    public MonitorActionProgressActivity(
        ILogger<MonitorActionProgressActivity> logger,
        IIncidentRepository incidentRepository,
        ICurrentUserService currentUserService,
        ICorrectiveActionService correctiveActionService)
        : base(logger, incidentRepository, currentUserService)
    {
        _correctiveActionService = correctiveActionService;
    }
    
    /// <summary>
    /// The corrective action to monitor
    /// </summary>
    [Input(
        Description = "The corrective action to monitor",
        DisplayName = "Corrective Action"
    )]
    public Input<CorrectiveActionWorkflowModel> Action { get; set; } = default!;
    
    /// <summary>
    /// The incident workflow context
    /// </summary>
    [Input(
        Description = "The incident workflow context",
        DisplayName = "Incident Context"
    )]
    public Input<IncidentWorkflowContext> Context { get; set; } = default!;
    
    protected override async ValueTask ExecuteAsync(ActivityExecutionContext context)
    {
        const string activityName = nameof(MonitorActionProgressActivity);
        LogActivity(activityName, "Starting action progress monitoring");
        
        try
        {
            var action = Action.Get(context);
            var workflowContext = Context.Get(context);
            
            // Check if this is initial execution or resume
            var isResume = context.GetProperty<bool>("IsResume");
            
            if (!isResume)
            {
                // Initial execution - start monitoring
                context.SetProperty("IsResume", true);
                context.SetProperty("ActionId", action.Id);
                
                // Schedule periodic check
                await ScheduleProgressCheck(context, action);
                return;
            }
            
            // Check action status
            var currentStatus = await _correctiveActionService.GetCorrectiveActionStatus(action.Id);
            
            if (currentStatus?.Status == "Completed")
            {
                LogActivity(activityName, "Corrective action completed - ID: {ActionId}", action.Id);
                return; // Action complete, exit monitoring
            }
            
            if (currentStatus?.DueDate < DateTime.UtcNow && currentStatus?.Status != "Completed")
            {
                // Action is overdue
                LogActivity(activityName, "Corrective action overdue - ID: {ActionId}", action.Id);
                await SendOverdueNotification(workflowContext, action);
            }
            
            // Schedule next check
            await ScheduleProgressCheck(context, action);
        }
        catch (Exception ex)
        {
            LogActivityError(activityName, ex, "Failed to monitor action progress");
        }
    }
    
    private async Task ScheduleProgressCheck(ActivityExecutionContext context, CorrectiveActionWorkflowModel action)
    {
        // Log monitoring action completion
        LogActivity(nameof(MonitorActionProgressActivity), 
            "Action monitoring completed - Action: {ActionDescription}, Status: {Status}", 
            action.Description, action.Status);
    }
    
    private async Task SendOverdueNotification(IncidentWorkflowContext context, CorrectiveActionWorkflowModel action)
    {
        LogActivity(nameof(MonitorActionProgressActivity), 
            "Sending overdue notification for action: {ActionId}", action.Id);
        
        // In a real implementation, this would send notifications
        // For now, just log the notification
    }
}

/// <summary>
/// Activity that verifies all corrective actions are completed
/// </summary>
[Activity("Incident Management", "Verify Completion", "Verifies that all corrective actions are completed")]
public class VerifyCompletionActivity : IncidentActivityBase<bool>
{
    private readonly ICorrectiveActionService _correctiveActionService;
    
    public VerifyCompletionActivity(
        ILogger<VerifyCompletionActivity> logger,
        IIncidentRepository incidentRepository,
        ICurrentUserService currentUserService,
        ICorrectiveActionService correctiveActionService)
        : base(logger, incidentRepository, currentUserService)
    {
        _correctiveActionService = correctiveActionService;
    }
    
    /// <summary>
    /// The incident workflow context
    /// </summary>
    [Input(
        Description = "The incident workflow context",
        DisplayName = "Incident Context"
    )]
    public Input<IncidentWorkflowContext> Context { get; set; } = default!;
    
    protected override async ValueTask ExecuteAsync(ActivityExecutionContext context)
    {
        const string activityName = nameof(VerifyCompletionActivity);
        LogActivity(activityName, "Starting completion verification");
        
        try
        {
            var workflowContext = Context.Get(context);
            
            // Check completion status of all actions
            var allComplete = true;
            var completedCount = 0;
            
            foreach (var action in workflowContext.CorrectiveActions)
            {
                var currentStatus = await _correctiveActionService.GetCorrectiveActionStatus(action.Id);
                if (currentStatus?.Status == "Completed")
                {
                    completedCount++;
                }
                else
                {
                    allComplete = false;
                }
            }
            
            LogActivity(activityName, 
                "Completion verification - {CompletedCount}/{TotalCount} actions completed",
                completedCount, workflowContext.CorrectiveActions.Count);
            
            Result.Set(context, allComplete);
        }
        catch (Exception ex)
        {
            LogActivityError(activityName, ex, "Failed to verify completion");
            Result.Set(context, false);
        }
    }
}


/// <summary>
/// Activity that generates the final incident report
/// </summary>
[Activity("Incident Management", "Generate Final Report", "Generates the final incident report")]
public class GenerateFinalReportActivity : IncidentActivityBase
{
    private readonly IReportService _reportService;
    
    public GenerateFinalReportActivity(
        ILogger<GenerateFinalReportActivity> logger,
        IIncidentRepository incidentRepository,
        ICurrentUserService currentUserService,
        IReportService reportService)
        : base(logger, incidentRepository, currentUserService)
    {
        _reportService = reportService;
    }
    
    /// <summary>
    /// The incident workflow context
    /// </summary>
    [Input(
        Description = "The incident workflow context",
        DisplayName = "Incident Context"
    )]
    public Input<IncidentWorkflowContext> Context { get; set; } = default!;
    
    protected override async ValueTask ExecuteAsync(ActivityExecutionContext context)
    {
        const string activityName = nameof(GenerateFinalReportActivity);
        LogActivity(activityName, "Starting final report generation");
        
        try
        {
            var workflowContext = Context.Get(context);
            
            // Generate final report
            var reportData = CreateReportData(workflowContext);
            var reportId = await _reportService.GenerateIncidentReport(reportData);
            
            LogActivity(activityName, 
                "Final report generated - Report ID: {ReportId}, Incident: {IncidentNumber}",
                reportId, workflowContext.IncidentNumber);
        }
        catch (Exception ex)
        {
            LogActivityError(activityName, ex, "Failed to generate final report");
        }
    }
    
    private IncidentReportData CreateReportData(IncidentWorkflowContext context)
    {
        return new IncidentReportData
        {
            IncidentId = context.IncidentId,
            IncidentNumber = context.IncidentNumber,
            IncidentType = context.IncidentType,
            Severity = context.Severity,
            Description = context.Description,
            Location = context.Location,
            ReportedAt = context.ReportedAt,
            ReportedBy = context.ReportedBy,
            InvestigationData = context.InvestigationData,
            CorrectiveActions = context.CorrectiveActions,
            Status = context.Status
        };
    }
}

/// <summary>
/// Represents incident report data
/// </summary>
public class IncidentReportData
{
    public string IncidentId { get; set; } = string.Empty;
    public string IncidentNumber { get; set; } = string.Empty;
    public string IncidentType { get; set; } = string.Empty;
    public string Severity { get; set; } = string.Empty;
    public string Description { get; set; } = string.Empty;
    public string Location { get; set; } = string.Empty;
    public DateTime ReportedAt { get; set; }
    public string ReportedBy { get; set; } = string.Empty;
    public InvestigationWorkflowModel? InvestigationData { get; set; }
    public List<CorrectiveActionWorkflowModel> CorrectiveActions { get; set; } = new();
    public string Status { get; set; } = string.Empty;
}

/// <summary>
/// Interface for report service operations
/// </summary>
public interface IReportService
{
    Task<string> GenerateIncidentReport(IncidentReportData reportData);
}