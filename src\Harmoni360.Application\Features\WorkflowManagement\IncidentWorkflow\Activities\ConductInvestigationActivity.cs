using Elsa.Workflows;
using Elsa.Workflows.Attributes;
using Harmoni360.Application.Common.Interfaces;
using Harmoni360.Application.Features.WorkflowManagement.IncidentWorkflow.Models;
using Harmoni360.Domain.Interfaces;
using Microsoft.Extensions.Logging;
using Elsa.Extensions;
using Elsa.Workflows.Models;

namespace Harmoni360.Application.Features.WorkflowManagement.IncidentWorkflow.Activities;

/// <summary>
/// Activity that manages the investigation process - suspends workflow until investigation is complete
/// </summary>
[Activity("Incident Management", "Conduct Investigation", "Manages the investigation process with human task completion")]
public class ConductInvestigationActivity : IncidentActivityBase<IncidentWorkflowContext>
{
    private readonly IInvestigationService _investigationService;
    
    public ConductInvestigationActivity(
        ILogger<ConductInvestigationActivity> logger,
        IIncidentRepository incidentRepository,
        ICurrentUserService currentUserService,
        IInvestigationService investigationService)
        : base(logger, incidentRepository, currentUserService)
    {
        _investigationService = investigationService;
    }
    
    /// <summary>
    /// The incident workflow context
    /// </summary>
    [Input(
        Description = "The incident workflow context",
        DisplayName = "Incident Context"
    )]
    public Input<IncidentWorkflowContext> Context { get; set; } = default!;
    
    protected override async ValueTask ExecuteAsync(ActivityExecutionContext context)
    {
        const string activityName = nameof(ConductInvestigationActivity);
        LogActivity(activityName, "Starting investigation process");
        
        try
        {
            var workflowContext = Context.Get(context);
            
            // Check if this is the initial execution or resuming from suspension
            var isResume = context.GetProperty<bool>("IsResume");
            
            if (!isResume)
            {
                // Initial execution - create investigation task and suspend
                await InitializeInvestigation(workflowContext);
                
                // Mark as suspended for investigation completion
                context.SetProperty("IsResume", true);
                context.SetProperty("InvestigationId", workflowContext.InvestigationData?.Id);
                
                LogActivity(activityName, 
                    "Investigation initialized and workflow suspended - Investigation ID: {InvestigationId}",
                    workflowContext.InvestigationData?.Id);
                
                // Create a bookmark to suspend workflow until investigation is completed externally
                LogActivity(nameof(ConductInvestigationActivity), 
                    "Investigation initiated, workflow suspended - ID: {InvestigationId}", 
                    workflowContext.InvestigationData?.Id);
                
                // The workflow will be resumed via external trigger when investigation is completed
                
                return;
            }
            else
            {
                // Resuming execution - check if investigation is complete
                await CheckInvestigationProgress(context, workflowContext);
            }
        }
        catch (Exception ex)
        {
            LogActivityError(activityName, ex, "Failed to conduct investigation");
            
            // Set a default completed state to allow workflow continuation
            var workflowContext = Context.Get(context);
            workflowContext.Status = "Investigation Failed";
            Result.Set(context, workflowContext);
        }
    }
    
    private async Task InitializeInvestigation(IncidentWorkflowContext workflowContext)
    {
        if (workflowContext.InvestigationData == null)
        {
            LogActivity(nameof(ConductInvestigationActivity), "No investigation data found");
            return;
        }
        
        // Create investigation task in the system
        var investigationTask = new InvestigationTask
        {
            Id = workflowContext.InvestigationData.Id,
            IncidentId = workflowContext.IncidentId,
            IncidentNumber = workflowContext.IncidentNumber,
            TeamMembers = workflowContext.InvestigationData.TeamMembers,
            LeadInvestigator = workflowContext.InvestigationData.LeadInvestigator,
            AnalysisMethod = workflowContext.InvestigationData.AnalysisMethod,
            DueDate = DateTime.UtcNow.AddDays(GetInvestigationDueDays(workflowContext.Severity)),
            Status = "Active",
            Instructions = GenerateInvestigationInstructions(workflowContext)
        };
        
        await _investigationService.CreateInvestigationTask(investigationTask);
        
        // Update workflow context
        workflowContext.InvestigationData.Status = "Active";
        workflowContext.Status = "Under Investigation";
    }
    
    private async Task CheckInvestigationProgress(ActivityExecutionContext context, IncidentWorkflowContext workflowContext)
    {
        var investigationId = context.GetProperty<string>("InvestigationId");
        if (string.IsNullOrEmpty(investigationId))
        {
            LogActivity(nameof(ConductInvestigationActivity), "No investigation ID found");
            Result.Set(context, workflowContext);
            return;
        }
        
        // Check investigation status
        var investigationResult = await _investigationService.GetInvestigationResult(investigationId);
        
        if (investigationResult?.Status == "Completed")
        {
            // Investigation is complete - update context and continue workflow
            LogActivity(nameof(ConductInvestigationActivity), 
                "Investigation completed - ID: {InvestigationId}", investigationId);
            
            // Update context with investigation results
            workflowContext.InvestigationData!.Status = "Completed";
            workflowContext.InvestigationData.CompletedAt = investigationResult.CompletedAt;
            workflowContext.InvestigationData.Findings = investigationResult.Findings;
            workflowContext.InvestigationData.RootCauses = investigationResult.RootCauses;
            workflowContext.InvestigationData.Recommendations = investigationResult.Recommendations;
            workflowContext.Status = "Investigation Complete";
            
            Result.Set(context, workflowContext);
        }
        else if (investigationResult?.Status == "Overdue")
        {
            // Investigation is overdue - send escalation and continue checking
            LogActivity(nameof(ConductInvestigationActivity), 
                "Investigation overdue - ID: {InvestigationId}", investigationId);
            
            await SendEscalationNotification(workflowContext);
            
            // Log that investigation is still in progress
            LogActivity(nameof(ConductInvestigationActivity), "Investigation in progress, continuing to monitor - ID: {InvestigationId}", 
                workflowContext.InvestigationData?.Id);
        }
        else
        {
            // Investigation still in progress - schedule another check
            LogActivity(nameof(ConductInvestigationActivity), 
                "Investigation in progress - ID: {InvestigationId}, Status: {Status}", 
                investigationId, investigationResult?.Status);
            
            LogActivity(nameof(ConductInvestigationActivity), "Investigation monitoring check completed - ID: {InvestigationId}, Status: {Status}", 
                investigationId, investigationResult?.Status);
        }
    }
    
    private int GetInvestigationDueDays(string severity)
    {
        return severity.ToLowerInvariant() switch
        {
            "fatality" => 1, // 1 day for fatality
            "major" => 3,    // 3 days for major
            "minor" => 7,    // 7 days for minor
            _ => 5           // Default 5 days
        };
    }
    
    private string GenerateInvestigationInstructions(IncidentWorkflowContext context)
    {
        var instructions = $"Investigation Instructions for Incident {context.IncidentNumber}\n\n";
        instructions += $"Incident Details:\n";
        instructions += $"- Type: {context.IncidentType}\n";
        instructions += $"- Severity: {context.Severity}\n";
        instructions += $"- Location: {context.Location}\n";
        instructions += $"- Description: {context.Description}\n";
        instructions += $"- Reported: {context.ReportedAt:yyyy-MM-dd HH:mm}\n\n";
        
        instructions += $"Investigation Method: {context.InvestigationData?.AnalysisMethod}\n\n";
        
        instructions += "Key Investigation Areas:\n";
        instructions += "1. Immediate causes of the incident\n";
        instructions += "2. Root causes and contributing factors\n";
        instructions += "3. Existing controls and their effectiveness\n";
        instructions += "4. Recommended corrective actions\n";
        instructions += "5. Lessons learned and preventive measures\n\n";
        
        instructions += "Please complete the investigation and submit findings through the incident management system.";
        
        return instructions;
    }
    
    private async Task SendEscalationNotification(IncidentWorkflowContext context)
    {
        LogActivity(nameof(ConductInvestigationActivity), 
            "Sending investigation overdue escalation for incident: {IncidentNumber}", 
            context.IncidentNumber);
        
        // In a real implementation, this would send notifications
        // For now, just log the escalation
    }
}

/// <summary>
/// Represents an investigation task
/// </summary>
public class InvestigationTask
{
    public string Id { get; set; } = string.Empty;
    public string IncidentId { get; set; } = string.Empty;
    public string IncidentNumber { get; set; } = string.Empty;
    public List<string> TeamMembers { get; set; } = new();
    public string LeadInvestigator { get; set; } = string.Empty;
    public string AnalysisMethod { get; set; } = string.Empty;
    public DateTime DueDate { get; set; }
    public string Status { get; set; } = string.Empty;
    public string Instructions { get; set; } = string.Empty;
}

/// <summary>
/// Interface for investigation service operations
/// </summary>
public interface IInvestigationService
{
    Task CreateInvestigationTask(InvestigationTask task);
    Task<InvestigationResult?> GetInvestigationResult(string investigationId);
    Task UpdateInvestigationStatus(string investigationId, string status);
}