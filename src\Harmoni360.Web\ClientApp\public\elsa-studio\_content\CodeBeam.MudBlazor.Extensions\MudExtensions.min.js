function auto_size(n){n.style.height="5px";n.style.height=n.scrollHeight+4+"px"}function getcss(n,t){const i=document.querySelector(n);return i.style.getPropertyValue(t)}function setcss(n,t,i){const r=document.querySelectorAll(n);for(let n=0;n<r.length;n++)r[n].style.setProperty(t,i)}class MudScrollManagerExtended{scrollToMiddle(n,t){let i=document.getElementById(n),r=document.getElementById(t);i.scrollTop=r.offsetTop-i.offsetHeight+i.offsetHeight/2+r.offsetHeight/2}}window.mudScrollManagerExtended=new MudScrollManagerExtended;window.mudTeleport={teleport:(n,t)=>{const i=document.querySelector(t);return i?(i.appendChild(n),"ok"):"not found"},removeFromDOM:n=>{n&&n.__internalId!==null&&n.remove()}};class MudSignaturePadManager{constructor(){this.pads=[]}addPad(n,t,i){const r=new MudSignaturePad(n,t,i);r.init();this.pads.push(r)}togglePadEraser(n){const t=this.getPad(n);t&&t.toggleEraser()}disposePad(n){const t=this.getPad(n);t&&t.dispose()}clearPad(n){const t=this.getPad(n);t&&t.clear(!0)}downloadPadImage(n){const t=this.getPad(n);t&&t.download()}getBase64(n){const t=this.getPad(n);if(t)return t.getBase64()}updatePadOptions(n,t){const i=this.getPad(n);i&&i.setOptions(t)}updatePadImage(n,t){const i=this.getPad(n);if(i){if(t.startsWith("data:image/png;base64,")){i.updateImage(t);return}i.updateImage(`data:image/png;base64,${t}`)}}getPad(n){const t=this.pads.findIndex(t=>t.canvas.id===n.id);return t>=0?this.pads[t]:null}}class MudSignaturePad{constructor(n,t,i){this.canvas=t;this.options=i;this.isMouseDown=!1;this.isErasing=!1;this.memCanvas=document.createElement("canvas");this.points=[];this.dotnetRef=n}get ctx(){return this.canvas.getContext("2d")}get memCtx(){return this.memCanvas.getContext("2d")}getBase64(){return this.canvas.toDataURL()}init(){this.setCanvasSize();this.setOptions(this.options);this.canvas.addEventListener("mousedown",n=>this.startDrawing(n));this.canvas.addEventListener("mousemove",n=>this.drawLine(n));this.canvas.addEventListener("mouseup",()=>this.stopDrawing());this.canvas.addEventListener("mouseout",()=>this.stopDrawing());this.canvas.addEventListener("touchstart",n=>this.startDrawing(n));this.canvas.addEventListener("touchend",()=>this.stopDrawing());this.canvas.addEventListener("touchmove",n=>this.drawLine(n));this.setPencilCursor()}download(){const n=document.createElement("a");n.download="signature.png";n.href=this.canvas.toDataURL();n.click();n.remove()}updateImage(n){this.clear(!0);const t=new Image,i=this.ctx,r=this.memCtx;t.onload=function(){i.drawImage(t,0,0);r.drawImage(t,0,0);t.remove()};t.src=n}setCanvasSize(){const t=this.canvas.parentElement,n=t.getBoundingClientRect();this.canvas.width=n.width;this.canvas.height=n.height;this.memCanvas.height=n.height;this.memCanvas.width=n.width}dispose(){this.canvas.removeEventListener("mousedown");this.canvas.removeEventListener("mousemove");this.canvas.removeEventListener("mouseup");this.canvas.removeEventListener("mouseout");this.canvas.removeEventListener("touchstart");this.canvas.removeEventListener("touchend");this.canvas.removeEventListener("touchmove")}clear(n){n===!0&&this.memCtx.clearRect(0,0,this.canvas.width,this.canvas.height);this.ctx.clearRect(0,0,this.canvas.width,this.canvas.height)}stopDrawing(){this.isMouseDown=!1;this.memCtx.clearRect(0,0,this.memCanvas.width,this.memCanvas.height);this.memCtx.drawImage(this.canvas,0,0);this.points=[]}startDrawing(n){this.isMouseDown=!0;this.points.push({x:n.offsetX,y:n.offsetY})}setOptions(n){this.ctx.lineWidth=n.lineWidth;this.ctx.lineJoin=n.lineJoin;this.ctx.lineCap=n.lineCap;this.ctx.strokeStyle=n.strokeStyle;this.options=n}toggleEraser(){if(this.isErasing=!this.isErasing,this.isErasing){this.setEraserCursor();return}this.setPencilCursor()}setPencilCursor(){this.canvas.setAttribute("style","cursor:url('_content/CodeBeam.MudBlazor.Extensions/pencil.cur'), auto;")}setEraserCursor(){this.canvas.setAttribute("style","cursor:url('_content/CodeBeam.MudBlazor.Extensions/eraser.cur'), auto;")}drawLine(n){this.isMouseDown&&(this.isErasing===!1?(this.clear(),this.ctx.drawImage(this.memCanvas,0,0),this.points.push({x:n.offsetX,y:n.offsetY}),this.drawPoints(this.ctx,this.points)):this.ctx.clearRect(n.offsetX,n.offsetY,23,23))}drawPoints(n,t){if(!(t.length<6)){if(t.length<6){const i=t[0];n.beginPath();n.arc(i.x,i.y,n.lineWidth/2,0,Math.PI*2,!0);n.closePath();n.fill();this.pushUpdateToBlazorComponent();return}n.beginPath();n.moveTo(t[0].x,t[0].y);let i;for(let r=1;r<t.length-2;r++){const u=(t[r].x+t[r+1].x)/2,f=(t[r].y+t[r+1].y)/2;n.quadraticCurveTo(t[r].x,t[r].y,u,f);i=r}n.quadraticCurveTo(t[i].x,t[i].y,t[i+1].x,t[i+1].y);n.stroke();this.pushUpdateToBlazorComponent()}}pushUpdateToBlazorComponent(){this.dotnetRef.invokeMethodAsync("SignatureDataChangedAsync")}}window.mudSignaturePad=new MudSignaturePadManager;