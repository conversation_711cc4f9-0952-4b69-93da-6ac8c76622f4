(()=>{"use strict";(()=>{window.registerBlazorCustomElement=function(t,s){customElements.define(t,class extends e{static get observedAttributes(){return e.getObservedAttributes(s)}constructor(){super(s)}})};class e extends HTMLElement{static getObservedAttributes(e){return e.map((e=>t(e.name)))}constructor(e){super(),this._parameterValues={},this._hasPendingSetParameters=!0,this._isDisposed=!1,this.renderIntoElement=this,this._attributeMappings={},e.forEach((e=>{const s=t(e.name);this._attributeMappings[s]=e})),this._addRootComponentPromise=Promise.resolve().then((()=>(this._hasPendingSetParameters=!1,Blazor.rootComponents.add(this.renderIntoElement,this.localName,this._parameterValues))));for(const[e,t]of Object.entries(this._attributeMappings)){const a=t.name;Object.defineProperty(this,s(a),{get:()=>this._parameterValues[a],set:t=>{this.hasAttribute(e)&&this.setAttribute(e,t),this._parameterValues[a]=t,this._supplyUpdatedParameters()}})}}connectedCallback(){if(this._isDisposed)throw new Error(`Cannot connect component ${this.localName} to the document after it has been disposed.`);clearTimeout(this._disposalTimeoutHandle)}disconnectedCallback(){this._disposalTimeoutHandle=setTimeout((async()=>{this._isDisposed=!0,(await this._addRootComponentPromise).dispose()}),1e3)}attributeChangedCallback(t,s,a){const r=this._attributeMappings[t];r&&(this._parameterValues[r.name]=e.parseAttributeValue(a,r.type,r.name),this._supplyUpdatedParameters())}async _supplyUpdatedParameters(){if(!this._hasPendingSetParameters){this._hasPendingSetParameters=!0;const e=await this._addRootComponentPromise;if(!this._isDisposed){const t=e.setParameters(this._parameterValues);this._hasPendingSetParameters=!1,await t}}}static parseAttributeValue(t,s,a){switch(s){case"string":return t;case"boolean":switch(t){case"true":case"True":return!0;case"false":case"False":return!1;default:throw new Error(`Invalid boolean value '${t}' for parameter '${a}'`)}case"number":const r=Number(t);if(Number.isNaN(r))throw new Error(`Invalid number value '${t}' for parameter '${a}'`);return r;case"boolean?":return t?e.parseAttributeValue(t,"boolean",a):null;case"number?":return t?e.parseAttributeValue(t,"number",a):null;case"object":throw new Error(`The parameter '${a}' accepts a complex-typed object so it cannot be set using an attribute. Try setting it as a element property instead.`);default:throw new Error(`Unknown type '${s}' for parameter '${a}'`)}}}function t(e){return s(e).replace(/([A-Z])/g,"-$1").toLowerCase()}function s(e){return e[0].toLowerCase()+e.substring(1)}})()})();