{"version": 3, "file": "clipboard.entry.js", "mappings": ";;;;;;;;;;;;AAAO,MAAM,QAAQ,GAAG,KAAK,WAAW,IAAI;IACxC,MAAM,SAAS,CAAC,SAAS;SACpB,SAAS,CAAC,IAAI,CAAC;SACf,KAAK,CAAC,UAAU,KAAK;QAClB,KAAK,CAAC,KAAK,CAAC,CAAC;IACjB,CAAC,CAAC,CAAC;AACX,CAAC;;;;;;;;;;;;;;;;ACN2B;;;;;;;SCA5B;SACA;;SAEA;SACA;SACA;SACA;SACA;SACA;SACA;SACA;SACA;SACA;SACA;SACA;SACA;;SAEA;SACA;;SAEA;SACA;SACA;;;;;UCtBA;UACA;UACA;UACA;UACA,yCAAyC,wCAAwC;UACjF;UACA;UACA;;;;;UCPA;;;;;UCAA;UACA;UACA;UACA,uDAAuD,iBAAiB;UACxE;UACA,gDAAgD,aAAa;UAC7D;;;;;;;;;;;;;;;ACN6B", "sources": ["webpack://elsa-studio-dom-interop/./src/clipboard/copy-text.ts", "webpack://elsa-studio-dom-interop/./src/clipboard/index.ts", "webpack://elsa-studio-dom-interop/webpack/bootstrap", "webpack://elsa-studio-dom-interop/webpack/runtime/define property getters", "webpack://elsa-studio-dom-interop/webpack/runtime/hasOwnProperty shorthand", "webpack://elsa-studio-dom-interop/webpack/runtime/make namespace object", "webpack://elsa-studio-dom-interop/./src/clipboard.ts"], "sourcesContent": ["export const copyText = async function (text) {\n    await navigator.clipboard\n        .writeText(text)\n        .catch(function (error) {\n            alert(error);\n        });\n}", "export * from './copy-text';", "// The module cache\nvar __webpack_module_cache__ = {};\n\n// The require function\nfunction __webpack_require__(moduleId) {\n\t// Check if module is in cache\n\tvar cachedModule = __webpack_module_cache__[moduleId];\n\tif (cachedModule !== undefined) {\n\t\treturn cachedModule.exports;\n\t}\n\t// Create a new module (and put it into the cache)\n\tvar module = __webpack_module_cache__[moduleId] = {\n\t\t// no module.id needed\n\t\t// no module.loaded needed\n\t\texports: {}\n\t};\n\n\t// Execute the module function\n\t__webpack_modules__[moduleId](module, module.exports, __webpack_require__);\n\n\t// Return the exports of the module\n\treturn module.exports;\n}\n\n", "// define getter functions for harmony exports\n__webpack_require__.d = (exports, definition) => {\n\tfor(var key in definition) {\n\t\tif(__webpack_require__.o(definition, key) && !__webpack_require__.o(exports, key)) {\n\t\t\tObject.defineProperty(exports, key, { enumerable: true, get: definition[key] });\n\t\t}\n\t}\n};", "__webpack_require__.o = (obj, prop) => (Object.prototype.hasOwnProperty.call(obj, prop))", "// define __esModule on exports\n__webpack_require__.r = (exports) => {\n\tif(typeof Symbol !== 'undefined' && Symbol.toStringTag) {\n\t\tObject.defineProperty(exports, Symbol.toStringTag, { value: 'Module' });\n\t}\n\tObject.defineProperty(exports, '__esModule', { value: true });\n};", "export * from \"./clipboard/\";"], "names": [], "sourceRoot": ""}