using Elsa.Workflows;
using Elsa.Workflows.Attributes;
using Harmoni360.Application.Common.Interfaces;
using Harmoni360.Application.Features.WorkflowManagement.IncidentWorkflow.Models;
using Harmoni360.Domain.Interfaces;
using Microsoft.Extensions.Logging;
using System.ComponentModel;
using System.Text.Json.Serialization;
using Elsa.Extensions;
using Elsa.Workflows.Models;

namespace Harmoni360.Application.Features.WorkflowManagement.IncidentWorkflow.Activities;

/// <summary>
/// Activity that sends email notifications for incident workflow events
/// </summary>
[Activity("Incident Management", "Send Email Notification", "Sends email notifications to relevant stakeholders")]
public class SendEmailNotificationActivity : IncidentActivityBase
{
    private readonly INotificationService _notificationService;
    private readonly IUserManagementService _userService;
    
    public SendEmailNotificationActivity(
        ILogger<SendEmailNotificationActivity> logger,
        IIncidentRepository incidentRepository,
        ICurrentUserService currentUserService,
        INotificationService notificationService,
        IUserManagementService userService)
        : base(logger, incidentRepository, currentUserService)
    {
        _notificationService = notificationService;
        _userService = userService;
    }
    
    /// <summary>
    /// The incident workflow context
    /// </summary>
    [Input(
        Description = "The incident workflow context",
        DisplayName = "Incident Context"
    )]
    public Input<IncidentWorkflowContext> Context { get; set; } = default!;
    
    /// <summary>
    /// The notification template to use
    /// </summary>
    [Input(
        Description = "Email template name (e.g., 'incident-alert', 'investigation-assigned')",
        DisplayName = "Template Name"
    )]
    public Input<string> TemplateName { get; set; } = default!;
    
    /// <summary>
    /// The recipient type (management, team, department, etc.)
    /// </summary>
    [Input(
        Description = "Recipient type: 'management', 'investigation-team', 'department', 'all-hse'",
        DisplayName = "Recipient Type"
    )]
    public Input<string> RecipientType { get; set; } = default!;
    
    protected override async ValueTask ExecuteAsync(ActivityExecutionContext context)
    {
        const string activityName = nameof(SendEmailNotificationActivity);
        LogActivity(activityName, "Starting email notification");
        
        try
        {
            var workflowContext = Context.Get(context);
            var templateName = TemplateName.Get(context);
            var recipientType = RecipientType.Get(context);
            
            // Get recipients based on type
            var recipients = await GetRecipients(workflowContext, recipientType);
            
            if (!recipients.Any())
            {
                LogActivity(activityName, "No recipients found for type: {RecipientType}", recipientType);
                return;
            }
            
            // Prepare notification data
            var notificationData = CreateNotificationData(workflowContext);
            
            // Send notifications
            foreach (var recipient in recipients)
            {
                try
                {
                    await _notificationService.SendNotificationAsync(new Harmoni360.Application.Common.Interfaces.NotificationRequest
                    {
                        UserId = recipient.Id,
                        Email = recipient.Email,
                        TemplateName = templateName,
                        Subject = GenerateSubject(workflowContext, templateName),
                        Data = notificationData
                    });
                    
                    LogActivity(activityName, "Email sent to: {Email}", recipient.Email);
                }
                catch (Exception ex)
                {
                    LogActivityError(activityName, ex, "Failed to send email to: {Email}", recipient.Email);
                }
            }
            
            LogActivity(activityName, 
                "Email notification completed - Template: {Template}, Recipients: {Count}",
                templateName, recipients.Count);
        }
        catch (Exception ex)
        {
            LogActivityError(activityName, ex, "Failed to send email notifications");
        }
    }
    
    private async Task<List<UserInfo>> GetRecipients(IncidentWorkflowContext context, string recipientType)
    {
        var recipients = new List<UserInfo>();
        
        switch (recipientType.ToLowerInvariant())
        {
            case "management":
                recipients.AddRange(await _userService.GetUsersByRole("HSE_Manager"));
                recipients.AddRange(await _userService.GetUsersByRole("Executive"));
                
                // Add department heads for major incidents
                if (context.Severity == "Major" || context.Severity == "Fatality")
                {
                    recipients.AddRange(await _userService.GetUsersByRole("Department_Head"));
                }
                break;
                
            case "investigation-team":
                if (context.InvestigationTeam?.Any() == true)
                {
                    foreach (var userId in context.InvestigationTeam)
                    {
                        var user = await GetUserById(userId);
                        if (user != null)
                        {
                            recipients.Add(user);
                        }
                    }
                }
                break;
                
            case "department":
                var department = GetDepartmentFromLocation(context.Location);
                recipients.AddRange(await _userService.GetUsersByDepartment(department));
                break;
                
            case "all-hse":
                recipients.AddRange(await _userService.GetUsersByRole("HSE_Manager"));
                recipients.AddRange(await _userService.GetUsersByRole("HSE_Officer"));
                break;
                
            case "reporter":
                var reporter = await GetUserById(context.ReportedBy);
                if (reporter != null)
                {
                    recipients.Add(reporter);
                }
                break;
                
            default:
                LogActivity(nameof(SendEmailNotificationActivity), 
                    "Unknown recipient type: {RecipientType}", recipientType);
                break;
        }
        
        // Remove duplicates
        return recipients.GroupBy(r => r.Id).Select(g => g.First()).ToList();
    }
    
    private Dictionary<string, object> CreateNotificationData(IncidentWorkflowContext context)
    {
        return new Dictionary<string, object>
        {
            ["IncidentNumber"] = context.IncidentNumber,
            ["IncidentType"] = context.IncidentType,
            ["Severity"] = context.Severity,
            ["Description"] = context.Description,
            ["Location"] = context.Location,
            ["ReportedAt"] = context.ReportedAt.ToString("yyyy-MM-dd HH:mm:ss"),
            ["Status"] = context.Status,
            ["ReportedBy"] = context.ReportedBy,
            ["WorkflowUrl"] = $"/incidents/{context.IncidentId}",
            ["ElsaStudioUrl"] = $"/elsa-studio/workflow-instances/{context.IncidentId}"
        };
    }
    
    private string GenerateSubject(IncidentWorkflowContext context, string templateName)
    {
        return templateName.ToLowerInvariant() switch
        {
            "incident-alert" => $"[{context.Severity.ToUpper()}] Incident Alert - {context.IncidentNumber}",
            "investigation-assigned" => $"Investigation Assigned - {context.IncidentNumber}",
            "investigation-complete" => $"Investigation Complete - {context.IncidentNumber}",
            "corrective-action-assigned" => $"Corrective Action Assigned - {context.IncidentNumber}",
            "corrective-action-due" => $"Corrective Action Due - {context.IncidentNumber}",
            "incident-closed" => $"Incident Closed - {context.IncidentNumber}",
            _ => $"Incident Update - {context.IncidentNumber}"
        };
    }
    
    private string GetDepartmentFromLocation(string location)
    {
        return location.ToLowerInvariant() switch
        {
            var l when l.Contains("warehouse") => "Operations",
            var l when l.Contains("office") => "Administration",
            var l when l.Contains("lab") => "Research",
            var l when l.Contains("workshop") => "Maintenance",
            _ => "General"
        };
    }
    
    private async Task<UserInfo?> GetUserById(string userId)
    {
        try
        {
            // In a real implementation, this would query the user service
            // For now, return a mock user
            return new UserInfo
            {
                Id = userId,
                Name = $"User {userId}",
                Email = $"user.{userId}@company.com",
                Department = "HSE"
            };
        }
        catch (Exception ex)
        {
            LogActivityError(nameof(SendEmailNotificationActivity), ex, 
                "Failed to get user: {UserId}", userId);
            return null;
        }
    }
}