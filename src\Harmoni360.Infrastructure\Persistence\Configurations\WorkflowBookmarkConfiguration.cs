using Harmoni360.Domain.Entities;
using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;

namespace Harmoni360.Infrastructure.Persistence.Configurations;

public class WorkflowBookmarkConfiguration : IEntityTypeConfiguration<WorkflowBookmark>
{
    public void Configure(EntityTypeBuilder<WorkflowBookmark> builder)
    {
        builder.ToTable("WorkflowBookmarks");

        builder.HasKey(e => e.Id);

        builder.Property(e => e.Id)
            .ValueGeneratedOnAdd();

        builder.Property(e => e.WorkflowExecutionId)
            .IsRequired();

        builder.Property(e => e.ActivityId)
            .HasMaxLength(100)
            .IsRequired();

        builder.Property(e => e.ActivityName)
            .HasMaxLength(500)
            .IsRequired();

        builder.Property(e => e.ActivityType)
            .HasMaxLength(100)
            .IsRequired();

        builder.Property(e => e.BookmarkName)
            .HasMaxLength(500)
            .IsRequired();

        builder.Property(e => e.Stimulus)
            .HasMaxLength(1000)
            .IsRequired(false);

        builder.Property(e => e.Payload)
            .HasColumnType("text")
            .IsRequired(false);

        builder.Property(e => e.Metadata)
            .HasColumnType("text")
            .IsRequired(false);

        builder.Property(e => e.CreatedAt)
            .IsRequired();

        builder.Property(e => e.ResumedAt)
            .IsRequired(false);

        builder.Property(e => e.ResumedBy)
            .HasMaxLength(100)
            .IsRequired(false);

        builder.Property(e => e.IsActive)
            .IsRequired()
            .HasDefaultValue(true);

        builder.Property(e => e.CallbackUrl)
            .HasMaxLength(500)
            .IsRequired(false);

        builder.Property(e => e.Tag)
            .HasMaxLength(1000)
            .IsRequired(false);

        builder.Property(e => e.Priority)
            .IsRequired(false)
            .HasDefaultValue(0);

        // Indexes
        builder.HasIndex(e => e.WorkflowExecutionId)
            .HasDatabaseName("IX_WorkflowBookmarks_WorkflowExecutionId");

        builder.HasIndex(e => e.ActivityId)
            .HasDatabaseName("IX_WorkflowBookmarks_ActivityId");

        builder.HasIndex(e => e.BookmarkName)
            .HasDatabaseName("IX_WorkflowBookmarks_BookmarkName");

        builder.HasIndex(e => e.IsActive)
            .HasDatabaseName("IX_WorkflowBookmarks_IsActive");

        builder.HasIndex(e => e.CreatedAt)
            .HasDatabaseName("IX_WorkflowBookmarks_CreatedAt");

        builder.HasIndex(e => new { e.WorkflowExecutionId, e.IsActive })
            .HasDatabaseName("IX_WorkflowBookmarks_WorkflowExecutionId_IsActive");

        builder.HasIndex(e => new { e.ActivityId, e.BookmarkName })
            .HasDatabaseName("IX_WorkflowBookmarks_ActivityId_BookmarkName");

        // Relationships
        builder.HasOne(e => e.WorkflowExecution)
            .WithMany(w => w.Bookmarks)
            .HasForeignKey(e => e.WorkflowExecutionId)
            .OnDelete(DeleteBehavior.Cascade);
    }
}