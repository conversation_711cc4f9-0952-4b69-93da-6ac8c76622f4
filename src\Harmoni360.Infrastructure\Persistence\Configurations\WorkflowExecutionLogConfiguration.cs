using Harmoni360.Domain.Entities;
using Harmoni360.Domain.Enums;
using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;

namespace Harmoni360.Infrastructure.Persistence.Configurations;

public class WorkflowExecutionLogConfiguration : IEntityTypeConfiguration<WorkflowExecutionLog>
{
    public void Configure(EntityTypeBuilder<WorkflowExecutionLog> builder)
    {
        builder.ToTable("WorkflowExecutionLogs");

        builder.HasKey(e => e.Id);

        builder.Property(e => e.Id)
            .ValueGeneratedOnAdd();

        builder.Property(e => e.WorkflowExecutionId)
            .IsRequired();

        builder.Property(e => e.ActivityId)
            .HasMaxLength(100)
            .IsRequired();

        builder.Property(e => e.ActivityName)
            .HasMaxLength(500)
            .IsRequired();

        builder.Property(e => e.ActivityType)
            .HasMaxLength(100)
            .IsRequired();

        builder.Property(e => e.EventType)
            .HasConversion<int>()
            .IsRequired();

        builder.Property(e => e.Timestamp)
            .IsRequired();

        builder.Property(e => e.Message)
            .HasMaxLength(2000)
            .IsRequired(false);

        builder.Property(e => e.ActivityInput)
            .HasColumnType("text")
            .IsRequired(false);

        builder.Property(e => e.ActivityOutput)
            .HasColumnType("text")
            .IsRequired(false);

        builder.Property(e => e.Exception)
            .HasColumnType("text")
            .IsRequired(false);

        builder.Property(e => e.Source)
            .HasMaxLength(500)
            .IsRequired(false);

        builder.Property(e => e.Sequence)
            .IsRequired(false);

        builder.Property(e => e.ExecutedBy)
            .HasMaxLength(100)
            .IsRequired(false);

        builder.Property(e => e.Duration)
            .IsRequired(false);

        builder.Property(e => e.PayloadType)
            .HasMaxLength(500)
            .IsRequired(false);

        builder.Property(e => e.Payload)
            .HasColumnType("text")
            .IsRequired(false);

        builder.Property(e => e.Properties)
            .HasColumnType("text")
            .IsRequired(false);

        // Indexes
        builder.HasIndex(e => e.WorkflowExecutionId)
            .HasDatabaseName("IX_WorkflowExecutionLogs_WorkflowExecutionId");

        builder.HasIndex(e => e.ActivityId)
            .HasDatabaseName("IX_WorkflowExecutionLogs_ActivityId");

        builder.HasIndex(e => e.EventType)
            .HasDatabaseName("IX_WorkflowExecutionLogs_EventType");

        builder.HasIndex(e => e.Timestamp)
            .HasDatabaseName("IX_WorkflowExecutionLogs_Timestamp");

        builder.HasIndex(e => new { e.WorkflowExecutionId, e.Timestamp })
            .HasDatabaseName("IX_WorkflowExecutionLogs_WorkflowExecutionId_Timestamp");

        builder.HasIndex(e => new { e.ActivityId, e.EventType })
            .HasDatabaseName("IX_WorkflowExecutionLogs_ActivityId_EventType");

        // Relationships
        builder.HasOne(e => e.WorkflowExecution)
            .WithMany(w => w.ExecutionLogs)
            .HasForeignKey(e => e.WorkflowExecutionId)
            .OnDelete(DeleteBehavior.Cascade);
    }
}