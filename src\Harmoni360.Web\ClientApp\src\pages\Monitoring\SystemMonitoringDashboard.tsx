import React, { useState, useEffect } from 'react';
import {
  Box,
  Grid,
  Paper,
  Typography,
  Card,
  CardContent,
  LinearProgress,
  Chip,
  IconButton,
  Tooltip,
  Alert,
  Tab,
  Tabs,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  CircularProgress,
  useTheme,
  alpha,
} from '@mui/material';
import {
  Refresh as RefreshIcon,
  CheckCircle as HealthyIcon,
  Warning as DegradedIcon,
  Error as UnhealthyIcon,
  Speed as SpeedIcon,
  Storage as StorageIcon,
  Cloud as CloudIcon,
  Memory as MemoryIcon,
  Schedule as ScheduleIcon,
  TrendingUp as TrendingUpIcon,
  TrendingDown as TrendingDownIcon,
} from '@mui/icons-material';
import {
  LineChart,
  Line,
  AreaChart,
  Area,
  BarChart,
  Bar,
  PieChart,
  Pie,
  Cell,
  XAxis,
  YAxis,
  CartesianGrid,
  Tooltip as ChartTooltip,
  Legend,
  ResponsiveContainer,
} from 'recharts';
import { format } from 'date-fns';
import axios from 'axios';

interface HealthCheckResult {
  name: string;
  status: 'Healthy' | 'Degraded' | 'Unhealthy';
  description: string;
  duration: number;
  data: Record<string, any>;
  exception?: string;
}

interface SystemMetrics {
  timestamp: Date;
  systemMetrics: {
    workingSet: number;
    privateMemorySize: number;
    gcMemory: number;
    threadCount: number;
    handleCount: number;
    totalProcessorTime: number;
    processorCount: number;
  };
  healthChecks: {
    overallStatus: string;
    totalDuration: number;
    healthyCount: number;
    degradedCount: number;
    unhealthyCount: number;
  };
}

interface WorkflowMetrics {
  total_executions_24h: number;
  failed_executions_24h: number;
  running_executions: number;
  suspended_executions: number;
  failure_rate_24h_percent: number;
  stuck_workflows_count: number;
  audit_logs_24h: number;
  error_logs_24h: number;
}

const SystemMonitoringDashboard: React.FC = () => {
  const theme = useTheme();
  const [loading, setLoading] = useState(true);
  const [refreshing, setRefreshing] = useState(false);
  const [activeTab, setActiveTab] = useState(0);
  const [autoRefresh, setAutoRefresh] = useState(true);
  
  // Health data
  const [overallHealth, setOverallHealth] = useState<string>('Unknown');
  const [healthChecks, setHealthChecks] = useState<HealthCheckResult[]>([]);
  const [systemMetrics, setSystemMetrics] = useState<SystemMetrics | null>(null);
  const [workflowMetrics, setWorkflowMetrics] = useState<WorkflowMetrics | null>(null);
  
  // Historical data for charts
  const [healthHistory, setHealthHistory] = useState<any[]>([]);
  const [performanceHistory, setPerformanceHistory] = useState<any[]>([]);
  const [workflowTrends, setWorkflowTrends] = useState<any[]>([]);

  const fetchHealthData = async () => {
    try {
      setRefreshing(true);
      
      // Fetch overall health
      const healthResponse = await axios.get('/api/system/systemhealth');
      setOverallHealth(healthResponse.data.status);
      setHealthChecks(healthResponse.data.results);
      
      // Fetch system metrics
      const metricsResponse = await axios.get('/api/system/systemhealth/metrics');
      setSystemMetrics(metricsResponse.data);
      
      // Fetch workflow specific metrics
      const workflowResponse = await axios.get('/api/system/systemhealth/workflow');
      if (workflowResponse.data.data) {
        setWorkflowMetrics(workflowResponse.data.data);
      }
      
      // Update historical data
      updateHistoricalData(healthResponse.data, metricsResponse.data);
      
    } catch (error) {
      console.error('Error fetching health data:', error);
    } finally {
      setLoading(false);
      setRefreshing(false);
    }
  };

  const updateHistoricalData = (health: any, metrics: any) => {
    const timestamp = new Date().toISOString();
    
    // Update health history (keep last 20 points)
    setHealthHistory(prev => {
      const newData = [...prev, {
        timestamp,
        healthy: health.results.filter((r: any) => r.status === 'Healthy').length,
        degraded: health.results.filter((r: any) => r.status === 'Degraded').length,
        unhealthy: health.results.filter((r: any) => r.status === 'Unhealthy').length,
      }].slice(-20);
      return newData;
    });
    
    // Update performance history
    setPerformanceHistory(prev => {
      const newData = [...prev, {
        timestamp,
        memory: metrics.systemMetrics.gcMemory / 1024 / 1024, // Convert to MB
        threads: metrics.systemMetrics.threadCount,
        responseTime: health.totalDuration,
      }].slice(-20);
      return newData;
    });
  };

  useEffect(() => {
    fetchHealthData();
    
    // Auto-refresh every 30 seconds
    const interval = autoRefresh ? setInterval(fetchHealthData, 30000) : null;
    
    return () => {
      if (interval) clearInterval(interval);
    };
  }, [autoRefresh]);

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'Healthy':
        return <HealthyIcon color="success" />;
      case 'Degraded':
        return <DegradedIcon color="warning" />;
      case 'Unhealthy':
        return <UnhealthyIcon color="error" />;
      default:
        return null;
    }
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'Healthy':
        return theme.palette.success.main;
      case 'Degraded':
        return theme.palette.warning.main;
      case 'Unhealthy':
        return theme.palette.error.main;
      default:
        return theme.palette.grey[500];
    }
  };

  const formatBytes = (bytes: number) => {
    const sizes = ['Bytes', 'KB', 'MB', 'GB'];
    if (bytes === 0) return '0 Bytes';
    const i = Math.floor(Math.log(bytes) / Math.log(1024));
    return Math.round(bytes / Math.pow(1024, i) * 100) / 100 + ' ' + sizes[i];
  };

  const renderOverviewTab = () => (
    <Grid container spacing={3}>
      {/* Overall Health Status */}
      <Grid item xs={12} md={4}>
        <Card>
          <CardContent>
            <Box display="flex" alignItems="center" justifyContent="space-between">
              <Typography variant="h6">System Health</Typography>
              {getStatusIcon(overallHealth)}
            </Box>
            <Typography variant="h3" sx={{ mt: 2, color: getStatusColor(overallHealth) }}>
              {overallHealth}
            </Typography>
            <Box sx={{ mt: 2 }}>
              <Grid container spacing={1}>
                {healthChecks.map((check, index) => (
                  <Grid item xs={12} key={index}>
                    <Box display="flex" alignItems="center" justifyContent="space-between">
                      <Typography variant="body2">{check.name}</Typography>
                      <Chip
                        size="small"
                        label={check.status}
                        color={
                          check.status === 'Healthy' ? 'success' :
                          check.status === 'Degraded' ? 'warning' : 'error'
                        }
                      />
                    </Box>
                  </Grid>
                ))}
              </Grid>
            </Box>
          </CardContent>
        </Card>
      </Grid>

      {/* System Metrics */}
      <Grid item xs={12} md={4}>
        <Card>
          <CardContent>
            <Typography variant="h6">System Resources</Typography>
            {systemMetrics && (
              <Box sx={{ mt: 2 }}>
                <Box sx={{ mb: 2 }}>
                  <Typography variant="body2" color="textSecondary">
                    Memory Usage
                  </Typography>
                  <Typography variant="h4">
                    {formatBytes(systemMetrics.systemMetrics.gcMemory)}
                  </Typography>
                  <LinearProgress
                    variant="determinate"
                    value={(systemMetrics.systemMetrics.gcMemory / (4 * 1024 * 1024 * 1024)) * 100}
                    sx={{ mt: 1 }}
                  />
                </Box>
                <Box sx={{ mb: 2 }}>
                  <Typography variant="body2" color="textSecondary">
                    Thread Count
                  </Typography>
                  <Typography variant="h4">
                    {systemMetrics.systemMetrics.threadCount}
                  </Typography>
                </Box>
                <Box>
                  <Typography variant="body2" color="textSecondary">
                    CPU Cores
                  </Typography>
                  <Typography variant="h4">
                    {systemMetrics.systemMetrics.processorCount}
                  </Typography>
                </Box>
              </Box>
            )}
          </CardContent>
        </Card>
      </Grid>

      {/* Workflow Metrics */}
      <Grid item xs={12} md={4}>
        <Card>
          <CardContent>
            <Typography variant="h6">Workflow Statistics (24h)</Typography>
            {workflowMetrics && (
              <Box sx={{ mt: 2 }}>
                <Box sx={{ mb: 2 }}>
                  <Typography variant="body2" color="textSecondary">
                    Total Executions
                  </Typography>
                  <Typography variant="h4">
                    {workflowMetrics.total_executions_24h}
                  </Typography>
                </Box>
                <Box sx={{ mb: 2 }}>
                  <Typography variant="body2" color="textSecondary">
                    Failure Rate
                  </Typography>
                  <Box display="flex" alignItems="baseline">
                    <Typography variant="h4" color={
                      workflowMetrics.failure_rate_24h_percent > 10 ? 'error' : 'textPrimary'
                    }>
                      {workflowMetrics.failure_rate_24h_percent.toFixed(1)}%
                    </Typography>
                    {workflowMetrics.failure_rate_24h_percent > 10 && (
                      <UnhealthyIcon color="error" sx={{ ml: 1 }} />
                    )}
                  </Box>
                </Box>
                <Box>
                  <Typography variant="body2" color="textSecondary">
                    Currently Running
                  </Typography>
                  <Typography variant="h4">
                    {workflowMetrics.running_executions}
                  </Typography>
                </Box>
              </Box>
            )}
          </CardContent>
        </Card>
      </Grid>

      {/* Health Trend Chart */}
      <Grid item xs={12} md={6}>
        <Paper sx={{ p: 2 }}>
          <Typography variant="h6" gutterBottom>
            Health Status Trend
          </Typography>
          <ResponsiveContainer width="100%" height={300}>
            <AreaChart data={healthHistory}>
              <CartesianGrid strokeDasharray="3 3" />
              <XAxis 
                dataKey="timestamp" 
                tickFormatter={(value) => format(new Date(value), 'HH:mm')}
              />
              <YAxis />
              <ChartTooltip />
              <Legend />
              <Area
                type="monotone"
                dataKey="healthy"
                stackId="1"
                stroke={theme.palette.success.main}
                fill={theme.palette.success.main}
              />
              <Area
                type="monotone"
                dataKey="degraded"
                stackId="1"
                stroke={theme.palette.warning.main}
                fill={theme.palette.warning.main}
              />
              <Area
                type="monotone"
                dataKey="unhealthy"
                stackId="1"
                stroke={theme.palette.error.main}
                fill={theme.palette.error.main}
              />
            </AreaChart>
          </ResponsiveContainer>
        </Paper>
      </Grid>

      {/* Performance Chart */}
      <Grid item xs={12} md={6}>
        <Paper sx={{ p: 2 }}>
          <Typography variant="h6" gutterBottom>
            System Performance
          </Typography>
          <ResponsiveContainer width="100%" height={300}>
            <LineChart data={performanceHistory}>
              <CartesianGrid strokeDasharray="3 3" />
              <XAxis 
                dataKey="timestamp" 
                tickFormatter={(value) => format(new Date(value), 'HH:mm')}
              />
              <YAxis yAxisId="left" />
              <YAxis yAxisId="right" orientation="right" />
              <ChartTooltip />
              <Legend />
              <Line
                yAxisId="left"
                type="monotone"
                dataKey="memory"
                stroke={theme.palette.primary.main}
                name="Memory (MB)"
              />
              <Line
                yAxisId="left"
                type="monotone"
                dataKey="threads"
                stroke={theme.palette.secondary.main}
                name="Threads"
              />
              <Line
                yAxisId="right"
                type="monotone"
                dataKey="responseTime"
                stroke={theme.palette.warning.main}
                name="Response Time (ms)"
              />
            </LineChart>
          </ResponsiveContainer>
        </Paper>
      </Grid>
    </Grid>
  );

  const renderDetailsTab = () => (
    <Grid container spacing={3}>
      <Grid item xs={12}>
        <TableContainer component={Paper}>
          <Table>
            <TableHead>
              <TableRow>
                <TableCell>Component</TableCell>
                <TableCell>Status</TableCell>
                <TableCell>Response Time</TableCell>
                <TableCell>Description</TableCell>
                <TableCell>Details</TableCell>
              </TableRow>
            </TableHead>
            <TableBody>
              {healthChecks.map((check, index) => (
                <TableRow key={index}>
                  <TableCell>{check.name}</TableCell>
                  <TableCell>
                    <Chip
                      size="small"
                      label={check.status}
                      color={
                        check.status === 'Healthy' ? 'success' :
                        check.status === 'Degraded' ? 'warning' : 'error'
                      }
                    />
                  </TableCell>
                  <TableCell>{check.duration.toFixed(2)} ms</TableCell>
                  <TableCell>{check.description}</TableCell>
                  <TableCell>
                    {check.exception ? (
                      <Tooltip title={check.exception}>
                        <UnhealthyIcon color="error" />
                      </Tooltip>
                    ) : (
                      <Typography variant="body2" color="textSecondary">
                        {JSON.stringify(check.data).substring(0, 50)}...
                      </Typography>
                    )}
                  </TableCell>
                </TableRow>
              ))}
            </TableBody>
          </Table>
        </TableContainer>
      </Grid>

      {/* Detailed Workflow Metrics */}
      {workflowMetrics && (
        <Grid item xs={12}>
          <Paper sx={{ p: 2 }}>
            <Typography variant="h6" gutterBottom>
              Workflow System Details
            </Typography>
            <Grid container spacing={2}>
              <Grid item xs={12} md={3}>
                <Box sx={{ textAlign: 'center' }}>
                  <Typography variant="body2" color="textSecondary">
                    Stuck Workflows
                  </Typography>
                  <Typography variant="h4" color={
                    workflowMetrics.stuck_workflows_count > 0 ? 'error' : 'textPrimary'
                  }>
                    {workflowMetrics.stuck_workflows_count}
                  </Typography>
                </Box>
              </Grid>
              <Grid item xs={12} md={3}>
                <Box sx={{ textAlign: 'center' }}>
                  <Typography variant="body2" color="textSecondary">
                    Suspended Workflows
                  </Typography>
                  <Typography variant="h4">
                    {workflowMetrics.suspended_executions}
                  </Typography>
                </Box>
              </Grid>
              <Grid item xs={12} md={3}>
                <Box sx={{ textAlign: 'center' }}>
                  <Typography variant="body2" color="textSecondary">
                    Audit Logs (24h)
                  </Typography>
                  <Typography variant="h4">
                    {workflowMetrics.audit_logs_24h}
                  </Typography>
                </Box>
              </Grid>
              <Grid item xs={12} md={3}>
                <Box sx={{ textAlign: 'center' }}>
                  <Typography variant="body2" color="textSecondary">
                    Error Logs (24h)
                  </Typography>
                  <Typography variant="h4" color={
                    workflowMetrics.error_logs_24h > 100 ? 'error' : 'textPrimary'
                  }>
                    {workflowMetrics.error_logs_24h}
                  </Typography>
                </Box>
              </Grid>
            </Grid>
          </Paper>
        </Grid>
      )}
    </Grid>
  );

  if (loading) {
    return (
      <Box display="flex" justifyContent="center" alignItems="center" minHeight={400}>
        <CircularProgress />
      </Box>
    );
  }

  return (
    <Box>
      <Box sx={{ mb: 3, display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
        <Typography variant="h4">System Monitoring Dashboard</Typography>
        <Box>
          <Tooltip title={autoRefresh ? "Auto-refresh enabled" : "Auto-refresh disabled"}>
            <IconButton onClick={() => setAutoRefresh(!autoRefresh)}>
              <ScheduleIcon color={autoRefresh ? "primary" : "disabled"} />
            </IconButton>
          </Tooltip>
          <Tooltip title="Refresh now">
            <IconButton onClick={fetchHealthData} disabled={refreshing}>
              <RefreshIcon />
            </IconButton>
          </Tooltip>
        </Box>
      </Box>

      {overallHealth === 'Unhealthy' && (
        <Alert severity="error" sx={{ mb: 2 }}>
          System health check failed. One or more critical components are not functioning properly.
        </Alert>
      )}

      {overallHealth === 'Degraded' && (
        <Alert severity="warning" sx={{ mb: 2 }}>
          System is operational but some components are experiencing issues.
        </Alert>
      )}

      <Box sx={{ borderBottom: 1, borderColor: 'divider', mb: 3 }}>
        <Tabs value={activeTab} onChange={(e, newValue) => setActiveTab(newValue)}>
          <Tab label="Overview" />
          <Tab label="Detailed Status" />
        </Tabs>
      </Box>

      {activeTab === 0 && renderOverviewTab()}
      {activeTab === 1 && renderDetailsTab()}
    </Box>
  );
};

export default SystemMonitoringDashboard;