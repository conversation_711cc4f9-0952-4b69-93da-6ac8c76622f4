using System.Text.Json.Serialization;

namespace Harmoni360.Application.Features.WorkflowManagement.IncidentWorkflow.Models;

/// <summary>
/// Represents the workflow context for incident management processes
/// </summary>
public class IncidentWorkflowContext
{
    [JsonPropertyName("incidentId")]
    public string IncidentId { get; set; } = string.Empty;
    
    [JsonPropertyName("incidentNumber")]
    public string IncidentNumber { get; set; } = string.Empty;
    
    [JsonPropertyName("incidentType")]
    public string IncidentType { get; set; } = string.Empty; // Accident, NearMiss
    
    [JsonPropertyName("categoryCode")]
    public string CategoryCode { get; set; } = string.Empty; // STF, EQUIP, CHEM, etc.
    
    [JsonPropertyName("categoryName")]
    public string CategoryName { get; set; } = string.Empty;
    
    [JsonPropertyName("severity")]
    public string Severity { get; set; } = string.Empty; // Major, Minor, Fatality
    
    [JsonPropertyName("status")]
    public string Status { get; set; } = string.Empty;
    
    [JsonPropertyName("reportedBy")]
    public string ReportedBy { get; set; } = string.Empty;
    
    [JsonPropertyName("reportedAt")]
    public DateTime ReportedAt { get; set; }
    
    [JsonPropertyName("description")]
    public string Description { get; set; } = string.Empty;
    
    [JsonPropertyName("location")]
    public string Location { get; set; } = string.Empty;
    
    [JsonPropertyName("investigationTeam")]
    public List<string> InvestigationTeam { get; set; } = new();
    
    [JsonPropertyName("correctiveActions")]
    public List<CorrectiveActionWorkflowModel> CorrectiveActions { get; set; } = new();
    
    [JsonPropertyName("attachments")]
    public List<AttachmentWorkflowModel> Attachments { get; set; } = new();
    
    [JsonPropertyName("investigationData")]
    public InvestigationWorkflowModel? InvestigationData { get; set; }
    
    [JsonPropertyName("investigation")]
    public InvestigationWorkflowModel? Investigation => InvestigationData;
    
    [JsonPropertyName("analysisResults")]
    public AnalysisResults? AnalysisResults { get; set; }
    
    [JsonPropertyName("externalTicketId")]
    public string? ExternalTicketId { get; set; }
    
    [JsonPropertyName("externalTicketUrl")]
    public string? ExternalTicketUrl { get; set; }
    
    [JsonPropertyName("investigationScheduledDate")]
    public DateTime? InvestigationScheduledDate { get; set; }
    
    [JsonPropertyName("investigationStatus")]
    public string InvestigationStatus { get; set; } = string.Empty;
    
    [JsonPropertyName("lastUpdated")]
    public DateTime LastUpdated { get; set; } = DateTime.UtcNow;
    
    [JsonPropertyName("closedAt")]
    public DateTime? ClosedAt { get; set; }
}

/// <summary>
/// Represents an incident report submitted to the workflow
/// </summary>
public class IncidentReportModel
{
    [JsonPropertyName("reporterId")]
    public string ReporterId { get; set; } = string.Empty;
    
    [JsonPropertyName("type")]
    public string Type { get; set; } = string.Empty;
    
    [JsonPropertyName("description")]
    public string Description { get; set; } = string.Empty;
    
    [JsonPropertyName("location")]
    public string Location { get; set; } = string.Empty;
    
    [JsonPropertyName("incidentDateTime")]
    public DateTime IncidentDateTime { get; set; }
    
    [JsonPropertyName("perceivedSeverity")]
    public string PerceivedSeverity { get; set; } = string.Empty;
    
    [JsonPropertyName("affectedPersonnel")]
    public List<string> AffectedPersonnel { get; set; } = new();
    
    [JsonPropertyName("witnessStatements")]
    public List<string> WitnessStatements { get; set; } = new();
    
    [JsonPropertyName("attachments")]
    public List<AttachmentWorkflowModel> Attachments { get; set; } = new();
}

/// <summary>
/// Represents investigation data within the workflow
/// </summary>
public class InvestigationWorkflowModel
{
    [JsonPropertyName("id")]
    public string Id { get; set; } = string.Empty;
    
    [JsonPropertyName("teamMembers")]
    public List<string> TeamMembers { get; set; } = new();
    
    [JsonPropertyName("leadInvestigator")]
    public string LeadInvestigator { get; set; } = string.Empty;
    
    [JsonPropertyName("analysisMethod")]
    public string AnalysisMethod { get; set; } = string.Empty; // HFACS or ICAM
    
    [JsonPropertyName("startedAt")]
    public DateTime StartedAt { get; set; }
    
    [JsonPropertyName("completedAt")]
    public DateTime? CompletedAt { get; set; }
    
    [JsonPropertyName("findings")]
    public List<FindingWorkflowModel> Findings { get; set; } = new();
    
    [JsonPropertyName("rootCauses")]
    public List<RootCauseWorkflowModel> RootCauses { get; set; } = new();
    
    [JsonPropertyName("rootCausesText")]
    public string RootCausesText { get; set; } = string.Empty;
    
    [JsonPropertyName("contributingFactors")]
    public string ContributingFactors { get; set; } = string.Empty;
    
    [JsonPropertyName("recommendations")]
    public string Recommendations { get; set; } = string.Empty;
    
    [JsonPropertyName("status")]
    public string Status { get; set; } = "InProgress";
}

/// <summary>
/// Represents a corrective action within the workflow
/// </summary>
public class CorrectiveActionWorkflowModel
{
    [JsonPropertyName("id")]
    public string Id { get; set; } = string.Empty;
    
    [JsonPropertyName("description")]
    public string Description { get; set; } = string.Empty;
    
    [JsonPropertyName("assignedTo")]
    public string AssignedTo { get; set; } = string.Empty;
    
    [JsonPropertyName("assignedDepartment")]
    public string AssignedDepartment { get; set; } = string.Empty;
    
    [JsonPropertyName("dueDate")]
    public DateTime DueDate { get; set; }
    
    [JsonPropertyName("status")]
    public string Status { get; set; } = "Assigned";
    
    [JsonPropertyName("priority")]
    public string Priority { get; set; } = "Medium";
    
    [JsonPropertyName("completedAt")]
    public DateTime? CompletedAt { get; set; }
    
    [JsonPropertyName("completionEvidence")]
    public string CompletionEvidence { get; set; } = string.Empty;
    
    [JsonPropertyName("verifiedBy")]
    public string VerifiedBy { get; set; } = string.Empty;
    
    [JsonPropertyName("verifiedAt")]
    public DateTime? VerifiedAt { get; set; }
}

/// <summary>
/// Represents an attachment in the workflow
/// </summary>
public class AttachmentWorkflowModel
{
    [JsonPropertyName("fileName")]
    public string FileName { get; set; } = string.Empty;
    
    [JsonPropertyName("fileSize")]
    public long FileSize { get; set; }
    
    [JsonPropertyName("contentType")]
    public string ContentType { get; set; } = string.Empty;
    
    [JsonPropertyName("storagePath")]
    public string StoragePath { get; set; } = string.Empty;
    
    [JsonPropertyName("uploadedBy")]
    public string UploadedBy { get; set; } = string.Empty;
    
    [JsonPropertyName("uploadedAt")]
    public DateTime UploadedAt { get; set; }
}

/// <summary>
/// Represents a finding from an investigation
/// </summary>
public class FindingWorkflowModel
{
    [JsonPropertyName("id")]
    public string Id { get; set; } = string.Empty;
    
    [JsonPropertyName("description")]
    public string Description { get; set; } = string.Empty;
    
    [JsonPropertyName("category")]
    public string Category { get; set; } = string.Empty;
    
    [JsonPropertyName("severity")]
    public string Severity { get; set; } = string.Empty;
    
    [JsonPropertyName("evidence")]
    public List<AttachmentWorkflowModel> Evidence { get; set; } = new();
}

/// <summary>
/// Represents a root cause identified during investigation
/// </summary>
public class RootCauseWorkflowModel
{
    [JsonPropertyName("id")]
    public string Id { get; set; } = string.Empty;
    
    [JsonPropertyName("description")]
    public string Description { get; set; } = string.Empty;
    
    [JsonPropertyName("category")]
    public string Category { get; set; } = string.Empty; // Human Error, Equipment Failure, Process Gap, etc.
    
    [JsonPropertyName("contributingFactors")]
    public List<string> ContributingFactors { get; set; } = new();
}

/// <summary>
/// Represents the result of an investigation task
/// </summary>
public class InvestigationResult
{
    [JsonPropertyName("id")]
    public string Id { get; set; } = string.Empty;
    
    [JsonPropertyName("status")]
    public string Status { get; set; } = string.Empty; // InProgress, Completed
    
    [JsonPropertyName("completedAt")]
    public DateTime? CompletedAt { get; set; }
    
    [JsonPropertyName("findings")]
    public List<FindingWorkflowModel> Findings { get; set; } = new();
    
    [JsonPropertyName("rootCauses")]
    public List<RootCauseWorkflowModel> RootCauses { get; set; } = new();
    
    [JsonPropertyName("recommendations")]
    public string Recommendations { get; set; } = string.Empty;
    
    [JsonPropertyName("isComplete")]
    public bool IsComplete => Status == "Completed";
}

/// <summary>
/// Email notification data for workflow notifications
/// </summary>
public class WorkflowNotificationModel
{
    [JsonPropertyName("recipients")]
    public List<string> Recipients { get; set; } = new();
    
    [JsonPropertyName("template")]
    public string Template { get; set; } = string.Empty;
    
    [JsonPropertyName("subject")]
    public string Subject { get; set; } = string.Empty;
    
    [JsonPropertyName("data")]
    public Dictionary<string, object> Data { get; set; } = new();
}

/// <summary>
/// Calendar event data for workflow scheduling
/// </summary>
public class WorkflowCalendarEvent
{
    [JsonPropertyName("title")]
    public string Title { get; set; } = string.Empty;
    
    [JsonPropertyName("description")]
    public string Description { get; set; } = string.Empty;
    
    [JsonPropertyName("start")]
    public DateTime Start { get; set; }
    
    [JsonPropertyName("end")]
    public DateTime End { get; set; }
    
    [JsonPropertyName("attendees")]
    public List<string> Attendees { get; set; } = new();
    
    [JsonPropertyName("location")]
    public string Location { get; set; } = string.Empty;
    
    [JsonPropertyName("sendInvitations")]
    public bool SendInvitations { get; set; } = true;
}

/// <summary>
/// Analysis results model
/// </summary>
public class AnalysisResults
{
    public string Method { get; set; } = string.Empty;
    
    // HFACS specific
    public List<string> UnsafeActs { get; set; } = new();
    public List<string> Preconditions { get; set; } = new();
    public List<string> UnsafeSupervision { get; set; } = new();
    public List<string> OrganizationalInfluences { get; set; } = new();
    
    // ICAM specific
    public List<string> OrganizationalFactors { get; set; } = new();
    public List<string> TaskEnvironmentalConditions { get; set; } = new();
    public List<string> IndividualTeamActions { get; set; } = new();
    public List<string> AbsentFailedDefenses { get; set; } = new();
    
    // Common
    public List<string> RootCauses { get; set; } = new();
    public List<string> ContributingFactors { get; set; } = new();
}