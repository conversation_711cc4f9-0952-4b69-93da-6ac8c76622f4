using Elsa.Extensions;
using Elsa.Workflows;
using Elsa.Workflows.Attributes;
using Elsa.Workflows.Models;
using Harmoni360.Application.Common.Interfaces;
using Harmoni360.Application.Features.WorkflowManagement.Common;
using Harmoni360.Application.Features.WorkflowManagement.IncidentWorkflow.Models;
using Harmoni360.Domain.Entities;
using Harmoni360.Domain.Enums;
using Harmoni360.Domain.Interfaces;
using Microsoft.Extensions.Logging;

namespace Harmoni360.Application.Features.WorkflowManagement.IncidentWorkflow.Activities;

/// <summary>
/// Activity that handles approval gates for incident status transitions
/// </summary>
[Activity("Incident Management", "Approval Gate", "Handles role-based approval for incident status transitions")]
public class ApprovalActivity : IncidentActivityBase
{
    private readonly IApprovalService _approvalService;
    private readonly INotificationService _notificationService;
    
    public ApprovalActivity(
        ILogger<ApprovalActivity> logger,
        IIncidentRepository incidentRepository,
        ICurrentUserService currentUserService,
        IApprovalService approvalService,
        INotificationService notificationService)
        : base(logger, incidentRepository, currentUserService)
    {
        _approvalService = approvalService;
        _notificationService = notificationService;
    }
    
    /// <summary>
    /// The workflow context containing incident information
    /// </summary>
    [Input(
        Description = "The incident workflow context",
        DisplayName = "Incident Context"
    )]
    public Input<IncidentWorkflowContext> Context { get; set; } = default!;
    
    /// <summary>
    /// The status to transition from
    /// </summary>
    [Input(
        Description = "The current incident status",
        DisplayName = "From Status"
    )]
    public Input<string> FromStatus { get; set; } = default!;
    
    /// <summary>
    /// The status to transition to
    /// </summary>
    [Input(
        Description = "The target incident status",
        DisplayName = "To Status"
    )]
    public Input<string> ToStatus { get; set; } = default!;
    
    /// <summary>
    /// Required roles for this approval
    /// </summary>
    [Input(
        Description = "Comma-separated list of roles that can approve this transition",
        DisplayName = "Required Roles"
    )]
    public Input<string> RequiredRoles { get; set; } = default!;
    
    /// <summary>
    /// Approval timeout in hours
    /// </summary>
    [Input(
        Description = "Hours to wait for approval before escalation (default: 24)",
        DisplayName = "Timeout Hours"
    )]
    public Input<int> TimeoutHours { get; set; } = new(24);
    
    /// <summary>
    /// Output indicating if the approval was granted
    /// </summary>
    [Output(
        Description = "Whether the approval was granted",
        DisplayName = "Is Approved"
    )]
    public Output<bool> IsApproved { get; set; } = default!;
    
    /// <summary>
    /// Output with the approver information
    /// </summary>
    [Output(
        Description = "Information about who approved",
        DisplayName = "Approver Info"
    )]
    public Output<ApprovalInfo> ApproverInfo { get; set; } = default!;
    
    protected override async ValueTask ExecuteAsync(ActivityExecutionContext context)
    {
        const string activityName = nameof(ApprovalActivity);
        LogActivity(activityName, "Starting approval gate");
        
        try
        {
            var workflowContext = Context.Get(context);
            var fromStatusStr = FromStatus.Get(context);
            var toStatusStr = ToStatus.Get(context);
            var requiredRolesStr = RequiredRoles.Get(context);
            var timeoutHours = TimeoutHours.Get(context);
            
            // Parse incident ID
            if (!int.TryParse(workflowContext.IncidentId, out var incidentId))
            {
                LogActivityError(activityName, "Invalid incident ID: {IncidentId}", workflowContext.IncidentId);
                IsApproved.Set(context, false);
                return;
            }
            
            // Get incident from database
            var incident = await IncidentRepository.GetByIdAsync(incidentId);
            if (incident == null)
            {
                LogActivityError(activityName, "Incident not found: {IncidentId}", incidentId);
                IsApproved.Set(context, false);
                return;
            }
            
            // Map status strings to enums
            var fromStatus = MapStringToIncidentStatus(fromStatusStr);
            var toStatus = MapStringToIncidentStatus(toStatusStr);
            
            // Parse required roles
            var requiredRolesList = requiredRolesStr
                .Split(',', StringSplitOptions.RemoveEmptyEntries)
                .Select(r => r.Trim())
                .ToList();
            
            // Create approval request bookmark
            var bookmarkName = $"ApprovalRequest_{incidentId}_{fromStatus}_{toStatus}_{Guid.NewGuid()}";
            
            // Send approval notifications
            await SendApprovalNotifications(incident, fromStatus, toStatus, requiredRolesList, bookmarkName);
            
            // Create bookmark and wait for approval
            context.CreateBookmark(new CreateBookmarkArgs
            {
                BookmarkName = bookmarkName,
                IncludeActivityInstanceId = false,
                AutoBurn = false,
                Callback = OnApprovalReceived
            });
            
            LogActivity(activityName, 
                "Waiting for approval for incident {IncidentId} transition from {FromStatus} to {ToStatus}", 
                incidentId, fromStatus, toStatus);
        }
        catch (Exception ex)
        {
            LogActivityError(activityName, ex, "Failed to setup approval gate");
            IsApproved.Set(context, false);
        }
    }
    
    private async ValueTask OnApprovalReceived(ActivityExecutionContext context)
    {
        const string activityName = nameof(ApprovalActivity);
        
        try
        {
            // Get approval data from bookmark payload
            var approvalData = context.GetWorkflowInput<ApprovalData>();
            
            if (approvalData == null)
            {
                LogActivityError(activityName, "No approval data received");
                IsApproved.Set(context, false);
                return;
            }
            
            // Set outputs
            IsApproved.Set(context, approvalData.IsApproved);
            ApproverInfo.Set(context, new ApprovalInfo
            {
                ApproverUserId = approvalData.ApproverUserId,
                ApproverName = approvalData.ApproverName,
                ApproverRole = approvalData.ApproverRole,
                ApprovedAt = approvalData.ApprovedAt,
                Comments = approvalData.Comments
            });
            
            // Update incident status if approved
            if (approvalData.IsApproved)
            {
                var workflowContext = Context.Get(context);
                var toStatusStr = ToStatus.Get(context);
                
                if (int.TryParse(workflowContext.IncidentId, out var incidentId))
                {
                    var incident = await IncidentRepository.GetByIdAsync(incidentId);
                    if (incident != null)
                    {
                        var toStatus = MapStringToIncidentStatus(toStatusStr);
                        incident.UpdateStatus(toStatus, 
                            $"Approved by {approvalData.ApproverName} ({approvalData.ApproverRole}) - {approvalData.Comments}");
                        await IncidentRepository.UpdateAsync(incident);
                        
                        // Update workflow context
                        workflowContext.Status = toStatusStr;
                        workflowContext.LastUpdated = DateTime.UtcNow;
                    }
                }
                
                LogActivity(activityName, "Approval granted by {ApproverName}", approvalData.ApproverName);
            }
            else
            {
                LogActivity(activityName, "Approval denied by {ApproverName}", approvalData.ApproverName);
            }
            
            // Complete the activity
            await context.CompleteActivityAsync();
        }
        catch (Exception ex)
        {
            LogActivityError(activityName, ex, "Failed to process approval");
            IsApproved.Set(context, false);
        }
    }
    
    private async Task SendApprovalNotifications(
        Incident incident, 
        IncidentStatus fromStatus, 
        IncidentStatus toStatus, 
        List<string> requiredRoles,
        string bookmarkName)
    {
        var title = $"Approval Required: {incident.Title}";
        var message = $@"
Your approval is required for the following incident status transition:

Incident Details:
- ID: {incident.Id}
- Number: {incident.IncidentNumber ?? "TBD"}
- Type: {incident.Type}
- Severity: {incident.Severity}
- Current Status: {fromStatus}
- Requested Status: {toStatus}

Description: {incident.Description}

To approve or reject this request, please use the approval interface or contact the HSE team.

Approval Reference: {bookmarkName}
";
        
        // Send notifications to all required roles
        foreach (var role in requiredRoles)
        {
            await _notificationService.NotifyRoleAsync(
                role,
                title,
                message,
                cancellationToken: default);
        }
    }
    
    private static IncidentStatus MapStringToIncidentStatus(string status)
    {
        return status.ToLowerInvariant() switch
        {
            "open" => IncidentStatus.Open,
            "reported" => IncidentStatus.Reported,
            "in progress" => IncidentStatus.InProgress,
            "under investigation" => IncidentStatus.UnderInvestigation,
            "awaiting action" => IncidentStatus.AwaitingAction,
            "resolved" => IncidentStatus.Resolved,
            "closed" => IncidentStatus.Closed,
            "pending review" => IncidentStatus.PendingReview,
            "approved" => IncidentStatus.Approved,
            "under review" => IncidentStatus.UnderReview,
            // Legacy/alternative mappings
            "processing" => IncidentStatus.InProgress,
            "investigating" => IncidentStatus.UnderInvestigation,
            "analyzing" => IncidentStatus.UnderInvestigation,
            "planning controls" => IncidentStatus.AwaitingAction,
            "actions assigned" => IncidentStatus.AwaitingAction,
            "verifying" => IncidentStatus.UnderReview,
            _ => IncidentStatus.Reported
        };
    }
}

/// <summary>
/// Approval data passed through bookmark
/// </summary>
public class ApprovalData
{
    public bool IsApproved { get; set; }
    public string ApproverUserId { get; set; } = string.Empty;
    public string ApproverName { get; set; } = string.Empty;
    public string ApproverRole { get; set; } = string.Empty;
    public DateTime ApprovedAt { get; set; }
    public string? Comments { get; set; }
}

/// <summary>
/// Approval information output
/// </summary>
public class ApprovalInfo
{
    public string ApproverUserId { get; set; } = string.Empty;
    public string ApproverName { get; set; } = string.Empty;
    public string ApproverRole { get; set; } = string.Empty;
    public DateTime ApprovedAt { get; set; }
    public string? Comments { get; set; }
}