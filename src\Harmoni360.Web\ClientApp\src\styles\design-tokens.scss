// Enhanced Color Palette - Design Tokens
// WCAG 2.1 AA Compliant Color System for HarmoniHSE360
// Created: June 2025

// Primary Brand Colors (Maintained)
:root {
  // Teal Primary Scale
  --harmoni-teal-50: #E6F7F9;
  --harmoni-teal-100: #B3E8ED;
  --harmoni-teal-200: #80D8E1;
  --harmoni-teal-300: #4DC9D5;
  --harmoni-teal-400: #26B3C0;
  --harmoni-teal-500: #0097A7;    // Primary brand color
  --harmoni-teal-600: #008A9A;
  --harmoni-teal-700: #007A8A;
  --harmoni-teal-800: #006B7A;
  --harmoni-teal-900: #005A6A;

  // Blue Secondary Scale
  --harmoni-blue-50: #E6F0F5;
  --harmoni-blue-100: #B3D4E1;
  --harmoni-blue-200: #80B8CD;
  --harmoni-blue-300: #4D9CB9;
  --harmoni-blue-400: #2685A8;
  --harmoni-blue-500: #004D6E;    // Secondary brand color
  --harmoni-blue-600: #004565;
  --harmoni-blue-700: #003D5C;
  --harmoni-blue-800: #003553;
  --harmoni-blue-900: #002D4A;

  // Risk Level Colors - Light Mode (WCAG AA Compliant)
  --risk-critical-light: #DC3545;     // Contrast: 5.25:1 on white
  --risk-critical-dark: #FF6B7A;      // Contrast: 4.52:1 on #1A202C
  --risk-critical-bg-light: #F8D7DA;
  --risk-critical-bg-dark: #2D1B1F;

  --risk-high-light: #FD7E14;         // Contrast: 4.89:1 on white
  --risk-high-dark: #FF9F47;          // Contrast: 4.51:1 on #1A202C
  --risk-high-bg-light: #FFF3CD;
  --risk-high-bg-dark: #2D2419;

  --risk-medium-light: #F9A825;       // Contrast: 4.67:1 on white
  --risk-medium-dark: #FFD93D;        // Contrast: 4.52:1 on #1A202C
  --risk-medium-bg-light: #FFF8E1;
  --risk-medium-bg-dark: #2D2A1A;

  --risk-low-light: #28A745;          // Contrast: 4.54:1 on white
  --risk-low-dark: #68D391;           // Contrast: 4.51:1 on #1A202C
  --risk-low-bg-light: #D4EDDA;
  --risk-low-bg-dark: #1B2D20;

  --risk-none-light: #6C757D;         // Contrast: 4.54:1 on white
  --risk-none-dark: #A0AEC0;          // Contrast: 4.52:1 on #1A202C
  --risk-none-bg-light: #F8F9FA;
  --risk-none-bg-dark: #2D3748;

  // Status Workflow Colors
  --status-draft-light: #17A2B8;      // Contrast: 4.51:1 on white
  --status-draft-dark: #63B3ED;       // Contrast: 4.52:1 on #1A202C
  --status-draft-bg-light: #D1ECF1;
  --status-draft-bg-dark: #1A2332;

  --status-progress-light: #F9A825;   // Same as medium risk
  --status-progress-dark: #FFD93D;
  --status-progress-bg-light: #FFF8E1;
  --status-progress-bg-dark: #2D2A1A;

  --status-review-light: #6F42C1;     // Contrast: 4.56:1 on white
  --status-review-dark: #B794F6;      // Contrast: 4.51:1 on #1A202C
  --status-review-bg-light: #E2D9F3;
  --status-review-bg-dark: #252040;

  --status-complete-light: #28A745;   // Same as low risk
  --status-complete-dark: #68D391;
  --status-complete-bg-light: #D4EDDA;
  --status-complete-bg-dark: #1B2D20;

  --status-overdue-light: #DC3545;    // Same as critical risk
  --status-overdue-dark: #FF6B7A;
  --status-overdue-bg-light: #F8D7DA;
  --status-overdue-bg-dark: #2D1B1F;

  --status-cancelled-light: #6C757D;  // Same as no risk
  --status-cancelled-dark: #A0AEC0;
  --status-cancelled-bg-light: #F8F9FA;
  --status-cancelled-bg-dark: #2D3748;

  // Neutral Colors - Light Mode
  --text-primary-light: #1A202C;      // Primary text - high contrast
  --text-secondary-light: #4A5568;    // Secondary text
  --text-muted-light: #718096;        // Muted text
  --text-disabled-light: #A0AEC0;     // Disabled text

  --bg-primary-light: #FFFFFF;        // Primary background
  --bg-secondary-light: #F7FAFC;      // Secondary background
  --bg-tertiary-light: #EDF2F7;       // Tertiary background
  --bg-overlay-light: rgba(0,0,0,0.5); // Modal overlays

  --border-light: #E2E8F0;            // Default borders
  --border-muted-light: #CBD5E0;      // Muted borders
  --border-strong-light: #A0AEC0;     // Strong borders

  // Neutral Colors - Dark Mode
  --text-primary-dark: #F7FAFC;       // Primary text - high contrast
  --text-secondary-dark: #E2E8F0;     // Secondary text
  --text-muted-dark: #A0AEC0;         // Muted text
  --text-disabled-dark: #718096;      // Disabled text

  --bg-primary-dark: #1A202C;         // Primary background
  --bg-secondary-dark: #2D3748;       // Secondary background
  --bg-tertiary-dark: #4A5568;        // Tertiary background
  --bg-overlay-dark: rgba(0,0,0,0.7);  // Modal overlays

  --border-dark: #4A5568;             // Default borders
  --border-muted-dark: #718096;       // Muted borders
  --border-strong-dark: #A0AEC0;      // Strong borders

  // Emergency and Alert Colors
  --emergency-bg-light: #DC3545;
  --emergency-text-light: #FFFFFF;    // Contrast: 5.25:1
  --emergency-bg-dark: #FF6B7A;
  --emergency-text-dark: #1A202C;     // Contrast: 4.52:1

  --success-bg-light: #28A745;
  --success-text-light: #FFFFFF;      // Contrast: 4.54:1
  --success-bg-dark: #68D391;
  --success-text-dark: #1A202C;       // Contrast: 4.51:1

  --info-bg-light: #17A2B8;
  --info-text-light: #FFFFFF;         // Contrast: 4.51:1
  --info-bg-dark: #63B3ED;
  --info-text-dark: #1A202C;          // Contrast: 4.52:1

  --warning-bg-light: #F9A825;
  --warning-text-light: #1A202C;      // Contrast: 4.67:1
  --warning-bg-dark: #FFD93D;
  --warning-text-dark: #1A202C;       // Contrast: 4.52:1
}

// Pattern-Based Indicators for Color-Blind Accessibility
.pattern-critical {
  background-image: repeating-linear-gradient(
    45deg,
    transparent,
    transparent 2px,
    rgba(220, 53, 69, 0.1) 2px,
    rgba(220, 53, 69, 0.1) 4px
  );
}

.pattern-warning {
  background-image: repeating-linear-gradient(
    90deg,
    transparent,
    transparent 3px,
    rgba(249, 168, 37, 0.1) 3px,
    rgba(249, 168, 37, 0.1) 6px
  );
}

.pattern-success {
  background-image: repeating-linear-gradient(
    135deg,
    transparent,
    transparent 2px,
    rgba(40, 167, 69, 0.1) 2px,
    rgba(40, 167, 69, 0.1) 4px
  );
}

// Icon-Based Status Indicators
.status-critical::before { content: "⚠️"; }
.status-high::before { content: "🔶"; }
.status-medium::before { content: "⚡"; }
.status-low::before { content: "✅"; }
.status-info::before { content: "ℹ️"; }

// High Contrast Mode Support
@media (prefers-contrast: high) {
  :root {
    // Enhanced contrast ratios for high contrast mode
    --text-primary-light: #000000;
    --bg-primary-light: #FFFFFF;
    --border-light: #000000;
  }
  
  [data-theme="dark"] {
    --text-primary-dark: #FFFFFF;
    --bg-primary-dark: #000000;
    --border-dark: #FFFFFF;
  }
  
  // Ensure critical elements have maximum contrast
  .btn-primary {
    background-color: #000000 !important;
    color: #FFFFFF !important;
    border: 2px solid #FFFFFF !important;
  }
  
  .alert-danger {
    background-color: #000000 !important;
    color: #FFFFFF !important;
    border: 3px solid #FFFFFF !important;
  }
}

// Reduced Motion Support
@media (prefers-reduced-motion: reduce) {
  * {
    animation-duration: 0.01ms !important;
    animation-iteration-count: 1 !important;
    transition-duration: 0.01ms !important;
    scroll-behavior: auto !important;
  }
}

// Print-Friendly Colors
@media print {
  // Ensure critical information is visible in black and white
  .risk-critical {
    background-color: #000000 !important;
    color: #FFFFFF !important;
    border: 2px solid #000000 !important;
  }

  .risk-high {
    background-color: #666666 !important;
    color: #FFFFFF !important;
    border: 2px solid #666666 !important;
  }

  .risk-medium {
    background-color: #CCCCCC !important;
    color: #000000 !important;
    border: 2px solid #000000 !important;
  }

  .risk-low {
    background-color: #FFFFFF !important;
    color: #000000 !important;
    border: 2px solid #000000 !important;
  }
}