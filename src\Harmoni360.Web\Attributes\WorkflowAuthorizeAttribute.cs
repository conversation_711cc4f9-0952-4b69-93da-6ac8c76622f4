using Harmoni360.Application.Common.Interfaces;
using Harmoni360.Domain.Authorization;
using Harmoni360.Domain.Enums;
using Harmoni360.Domain.Exceptions;
using Microsoft.AspNetCore.Mvc;
using Microsoft.AspNetCore.Mvc.Filters;
using Microsoft.EntityFrameworkCore;
using System.Security.Claims;

namespace Harmoni360.Web.Attributes;

/// <summary>
/// Authorization attribute for workflow-specific operations
/// </summary>
[AttributeUsage(AttributeTargets.Class | AttributeTargets.Method, AllowMultiple = true)]
public class WorkflowAuthorizeAttribute : Attribute, IAsyncAuthorizationFilter
{
    public WorkflowPermission Permission { get; }
    public string? Resource { get; set; }
    public bool RequireOwnership { get; set; }

    public WorkflowAuthorizeAttribute(WorkflowPermission permission)
    {
        Permission = permission;
    }

    public async Task OnAuthorizationAsync(AuthorizationFilterContext context)
    {
        // Check if user is authenticated
        if (!context.HttpContext.User.Identity?.IsAuthenticated == true)
        {
            context.Result = new UnauthorizedResult();
            return;
        }

        var currentUserService = context.HttpContext.RequestServices.GetRequiredService<ICurrentUserService>();
        var auditService = context.HttpContext.RequestServices.GetRequiredService<IWorkflowAuditService>();

        try
        {
            var userId = currentUserService.UserId.ToString();
            var userEmail = currentUserService.Email;

            if (string.IsNullOrEmpty(userId))
            {
                context.Result = new UnauthorizedResult();
                return;
            }

            // Get user role from claims
            var roleClaim = context.HttpContext.User.FindFirst(ClaimTypes.Role)?.Value;
            if (!Enum.TryParse<RoleType>(roleClaim, out var userRole))
            {
                context.Result = new ForbidResult();
                return;
            }

            // Check if user has the required permission
            var hasPermission = WorkflowPermissionMap.HasPermission(userRole, Permission);

            // Extract workflow instance ID from route or query if available
            var workflowInstanceId = ExtractWorkflowInstanceId(context);

            // Log the permission check
            if (!string.IsNullOrEmpty(workflowInstanceId))
            {
                await auditService.LogPermissionCheckAsync(
                    workflowInstanceId,
                    userId,
                    Permission.ToString(),
                    hasPermission,
                    Resource);
            }

            if (!hasPermission)
            {
                // Log security event for permission denial
                if (!string.IsNullOrEmpty(workflowInstanceId))
                {
                    await auditService.LogSecurityEventAsync(
                        workflowInstanceId,
                        "PermissionDenied",
                        userId,
                        SecuritySeverity.Medium,
                        $"User {userEmail} ({userRole}) was denied {Permission} permission on {Resource ?? "workflow"}");
                }

                throw new WorkflowAuthorizationException(
                    workflowInstanceId ?? string.Empty,
                    userId,
                    Permission.ToString(),
                    Resource);
            }

            // Additional ownership check if required
            if (RequireOwnership && !string.IsNullOrEmpty(workflowInstanceId))
            {
                var isOwner = await CheckOwnershipAsync(context, workflowInstanceId, userId);
                if (!isOwner)
                {
                    await auditService.LogSecurityEventAsync(
                        workflowInstanceId,
                        "OwnershipViolation",
                        userId,
                        SecuritySeverity.High,
                        $"User {userEmail} attempted to access workflow owned by another user");

                    throw new WorkflowAuthorizationException(
                        workflowInstanceId,
                        userId,
                        $"{Permission} (ownership required)",
                        Resource);
                }
            }

            // Log successful authorization
            if (!string.IsNullOrEmpty(workflowInstanceId))
            {
                await auditService.LogSecurityEventAsync(
                    workflowInstanceId,
                    "AuthorizationGranted",
                    userId,
                    SecuritySeverity.Low,
                    $"User {userEmail} ({userRole}) granted {Permission} permission on {Resource ?? "workflow"}");
            }
        }
        catch (WorkflowAuthorizationException)
        {
            // Re-throw workflow authorization exceptions to be handled by middleware
            throw;
        }
        catch (Exception ex)
        {
            // Log unexpected authorization errors
            var logger = context.HttpContext.RequestServices.GetRequiredService<ILogger<WorkflowAuthorizeAttribute>>();
            logger.LogError(ex, "Unexpected error during workflow authorization check");
            
            context.Result = new StatusCodeResult(500);
        }
    }

    /// <summary>
    /// Extracts workflow instance ID from route parameters or query string
    /// </summary>
    private string? ExtractWorkflowInstanceId(AuthorizationFilterContext context)
    {
        // Try route values first
        if (context.RouteData.Values.TryGetValue("workflowInstanceId", out var routeValue))
        {
            return routeValue?.ToString();
        }

        if (context.RouteData.Values.TryGetValue("instanceId", out var instanceValue))
        {
            return instanceValue?.ToString();
        }

        // Try query string
        if (context.HttpContext.Request.Query.TryGetValue("workflowInstanceId", out var queryValue))
        {
            return queryValue.FirstOrDefault();
        }

        if (context.HttpContext.Request.Query.TryGetValue("instanceId", out var instanceQuery))
        {
            return instanceQuery.FirstOrDefault();
        }

        // Try to extract from request body for POST requests
        if (context.HttpContext.Request.Method == "POST" && 
            context.HttpContext.Request.ContentType?.Contains("application/json") == true)
        {
            // This would require reading the request body, which is complex in a filter
            // Consider using a custom model binder or action filter for this case
        }

        return null;
    }

    /// <summary>
    /// Checks if the current user owns the workflow instance
    /// </summary>
    private async Task<bool> CheckOwnershipAsync(AuthorizationFilterContext context, string workflowInstanceId, string userId)
    {
        try
        {
            var dbContext = context.HttpContext.RequestServices.GetRequiredService<IApplicationDbContext>();
            
            var workflowExecution = await dbContext.WorkflowExecutions
                .FirstOrDefaultAsync(w => w.InstanceId == workflowInstanceId);

            if (workflowExecution == null)
            {
                return false; // Workflow not found, deny access
            }

            // Check if user is the owner or creator
            return workflowExecution.CreatedBy == userId || workflowExecution.InitiatedBy == userId;
        }
        catch (Exception ex)
        {
            var logger = context.HttpContext.RequestServices.GetRequiredService<ILogger<WorkflowAuthorizeAttribute>>();
            logger.LogError(ex, "Error checking workflow ownership for user {UserId} and workflow {WorkflowInstanceId}", 
                userId, workflowInstanceId);
            
            return false; // Err on the side of caution
        }
    }
}

/// <summary>
/// Convenience attributes for common workflow permissions
/// </summary>
public class WorkflowViewAttribute : WorkflowAuthorizeAttribute
{
    public WorkflowViewAttribute() : base(WorkflowPermission.ViewWorkflowDefinitions) { }
}

public class WorkflowExecuteAttribute : WorkflowAuthorizeAttribute
{
    public WorkflowExecuteAttribute() : base(WorkflowPermission.ExecuteWorkflows) { }
}

public class WorkflowCreateAttribute : WorkflowAuthorizeAttribute
{
    public WorkflowCreateAttribute() : base(WorkflowPermission.CreateWorkflowDefinitions) { }
}

public class WorkflowEditAttribute : WorkflowAuthorizeAttribute
{
    public WorkflowEditAttribute() : base(WorkflowPermission.EditWorkflowDefinitions) { }
}

public class WorkflowDeleteAttribute : WorkflowAuthorizeAttribute
{
    public WorkflowDeleteAttribute() : base(WorkflowPermission.DeleteWorkflowDefinitions) { }
}

public class WorkflowManageAttribute : WorkflowAuthorizeAttribute
{
    public WorkflowManageAttribute() : base(WorkflowPermission.ManageWorkflowSettings) { }
}

public class WorkflowDebugAttribute : WorkflowAuthorizeAttribute
{
    public WorkflowDebugAttribute() : base(WorkflowPermission.DebugWorkflowsInStudio) { }
}

/// <summary>
/// Attribute for operations that require workflow ownership
/// </summary>
public class WorkflowOwnerOnlyAttribute : WorkflowAuthorizeAttribute
{
    public WorkflowOwnerOnlyAttribute(WorkflowPermission permission) : base(permission)
    {
        RequireOwnership = true;
    }
}