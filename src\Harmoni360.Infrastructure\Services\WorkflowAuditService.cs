using Harmoni360.Application.Common.Interfaces;
using Harmoni360.Domain.Entities;
using Harmoni360.Domain.Enums;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Logging;

namespace Harmoni360.Infrastructure.Services;

// Simplified WorkflowAuditService implementation matching the actual WorkflowExecutionLog entity
public class WorkflowAuditService : IWorkflowAuditService
{
    private readonly IApplicationDbContext _context;
    private readonly ICurrentUserService _currentUserService;
    private readonly ILogger<WorkflowAuditService> _logger;

    public WorkflowAuditService(
        IApplicationDbContext context,
        ICurrentUserService currentUserService,
        ILogger<WorkflowAuditService> logger)
    {
        _context = context;
        _currentUserService = currentUserService;
        _logger = logger;
    }

    public async Task LogWorkflowStartedAsync(string workflowInstanceId, string workflowDefinitionId, 
        string initiatedBy, Dictionary<string, object>? inputData = null, CancellationToken cancellationToken = default)
    {
        await LogWorkflowEventAsync(workflowInstanceId, WorkflowLogEventType.WorkflowStarted, 
            $"Workflow '{workflowDefinitionId}' started by {initiatedBy}", initiatedBy, inputData, cancellationToken);
        
        _logger.LogInformation("Workflow {WorkflowDefinitionId} instance {WorkflowInstanceId} started by {InitiatedBy}",
            workflowDefinitionId, workflowInstanceId, initiatedBy);
    }

    public async Task LogWorkflowCompletedAsync(string workflowInstanceId, string completedBy, 
        Dictionary<string, object>? outputData = null, TimeSpan? duration = null, CancellationToken cancellationToken = default)
    {
        await LogWorkflowEventAsync(workflowInstanceId, WorkflowLogEventType.WorkflowCompleted, 
            $"Workflow completed by {completedBy}" + (duration.HasValue ? $" in {duration.Value.TotalSeconds:F2} seconds" : ""), 
            completedBy, outputData, cancellationToken, duration);
        
        _logger.LogInformation("Workflow instance {WorkflowInstanceId} completed by {CompletedBy} in {Duration}ms",
            workflowInstanceId, completedBy, duration?.TotalMilliseconds ?? 0);
    }

    public async Task LogWorkflowFailedAsync(string workflowInstanceId, string errorMessage, 
        Exception? exception = null, string? failedBy = null, CancellationToken cancellationToken = default)
    {
        var eventData = new Dictionary<string, object>
        {
            ["ErrorMessage"] = errorMessage
        };

        if (exception != null)
        {
            eventData["ExceptionType"] = exception.GetType().Name;
            eventData["StackTrace"] = exception.StackTrace ?? "";
        }

        await LogWorkflowEventAsync(workflowInstanceId, WorkflowLogEventType.WorkflowFaulted,
            $"Workflow failed: {errorMessage}", failedBy, eventData, cancellationToken, exception: exception?.ToString());

        _logger.LogError(exception, "Workflow instance {WorkflowInstanceId} failed: {ErrorMessage}",
            workflowInstanceId, errorMessage);
    }

    public async Task LogWorkflowCancelledAsync(string workflowInstanceId, string cancelledBy, 
        string? reason = null, CancellationToken cancellationToken = default)
    {
        var eventData = !string.IsNullOrEmpty(reason) ? new Dictionary<string, object> { ["Reason"] = reason } : null;
        
        await LogWorkflowEventAsync(workflowInstanceId, WorkflowLogEventType.WorkflowCancelled,
            $"Workflow cancelled by {cancelledBy}" + (!string.IsNullOrEmpty(reason) ? $": {reason}" : ""), 
            cancelledBy, eventData, cancellationToken);

        _logger.LogWarning("Workflow instance {WorkflowInstanceId} cancelled by {CancelledBy}: {Reason}",
            workflowInstanceId, cancelledBy, reason);
    }

    public async Task LogWorkflowSuspendedAsync(string workflowInstanceId, string suspendedBy, 
        string? reason = null, CancellationToken cancellationToken = default)
    {
        var eventData = !string.IsNullOrEmpty(reason) ? new Dictionary<string, object> { ["Reason"] = reason } : null;
        
        await LogWorkflowEventAsync(workflowInstanceId, WorkflowLogEventType.WorkflowSuspended,
            $"Workflow suspended by {suspendedBy}" + (!string.IsNullOrEmpty(reason) ? $": {reason}" : ""), 
            suspendedBy, eventData, cancellationToken);

        _logger.LogInformation("Workflow instance {WorkflowInstanceId} suspended by {SuspendedBy}: {Reason}",
            workflowInstanceId, suspendedBy, reason);
    }

    public async Task LogWorkflowResumedAsync(string workflowInstanceId, string resumedBy, CancellationToken cancellationToken = default)
    {
        await LogWorkflowEventAsync(workflowInstanceId, WorkflowLogEventType.WorkflowResumed,
            $"Workflow resumed by {resumedBy}", resumedBy, null, cancellationToken);

        _logger.LogInformation("Workflow instance {WorkflowInstanceId} resumed by {ResumedBy}",
            workflowInstanceId, resumedBy);
    }

    public async Task LogActivityStartedAsync(string workflowInstanceId, string activityId, string activityName, 
        string? activityType = null, Dictionary<string, object>? inputData = null, CancellationToken cancellationToken = default)
    {
        await LogActivityEventAsync(workflowInstanceId, activityId, activityName, activityType ?? "Activity", 
            WorkflowLogEventType.ActivityStarted, $"Activity '{activityName}' started", 
            null, inputData, cancellationToken);
    }

    public async Task LogActivityCompletedAsync(string workflowInstanceId, string activityId, string activityName, 
        Dictionary<string, object>? outputData = null, TimeSpan? duration = null, CancellationToken cancellationToken = default)
    {
        await LogActivityEventAsync(workflowInstanceId, activityId, activityName, "Activity", 
            WorkflowLogEventType.ActivityCompleted, 
            $"Activity '{activityName}' completed" + (duration.HasValue ? $" in {duration.Value.TotalMilliseconds:F2}ms" : ""), 
            null, outputData, cancellationToken, duration);
    }

    public async Task LogActivityFailedAsync(string workflowInstanceId, string activityId, string activityName, 
        string errorMessage, Exception? exception = null, CancellationToken cancellationToken = default)
    {
        var eventData = new Dictionary<string, object>
        {
            ["ErrorMessage"] = errorMessage
        };

        if (exception != null)
        {
            eventData["ExceptionType"] = exception.GetType().Name;
            eventData["StackTrace"] = exception.StackTrace ?? "";
        }

        await LogActivityEventAsync(workflowInstanceId, activityId, activityName, "Activity", 
            WorkflowLogEventType.ActivityFaulted, $"Activity '{activityName}' failed: {errorMessage}", 
            null, eventData, cancellationToken, exception: exception?.ToString());

        _logger.LogError(exception, "Activity {ActivityName} failed in workflow {WorkflowInstanceId}: {ErrorMessage}",
            activityName, workflowInstanceId, errorMessage);
    }

    public async Task LogActivitySkippedAsync(string workflowInstanceId, string activityId, string activityName, 
        string? reason = null, CancellationToken cancellationToken = default)
    {
        var eventData = !string.IsNullOrEmpty(reason) ? new Dictionary<string, object> { ["Reason"] = reason } : null;
        
        await LogActivityEventAsync(workflowInstanceId, activityId, activityName, "Activity", 
            WorkflowLogEventType.ActivityCompleted, // Using ActivityCompleted for skipped activities 
            $"Activity '{activityName}' skipped" + (!string.IsNullOrEmpty(reason) ? $": {reason}" : ""), 
            null, eventData, cancellationToken);
    }

    // Simplified implementations for other methods
    public async Task LogUserActionAsync(string workflowInstanceId, string userId, string action, 
        string resourceType, string resourceId, Dictionary<string, object>? actionData = null, CancellationToken cancellationToken = default)
    {
        var eventData = new Dictionary<string, object>
        {
            ["Action"] = action,
            ["ResourceType"] = resourceType,
            ["ResourceId"] = resourceId
        };

        if (actionData != null)
        {
            foreach (var kvp in actionData)
                eventData[kvp.Key] = kvp.Value;
        }

        await LogWorkflowEventAsync(workflowInstanceId, WorkflowLogEventType.CustomEvent,
            $"User {userId} performed {action} on {resourceType} {resourceId}", userId, eventData, cancellationToken);
    }

    public async Task LogApprovalActionAsync(string workflowInstanceId, string approvalId, string approver, 
        bool isApproved, string? comments = null, CancellationToken cancellationToken = default)
    {
        var eventData = new Dictionary<string, object>
        {
            ["ApprovalId"] = approvalId,
            ["IsApproved"] = isApproved,
            ["Approver"] = approver
        };

        if (!string.IsNullOrEmpty(comments))
            eventData["Comments"] = comments;

        await LogWorkflowEventAsync(workflowInstanceId, WorkflowLogEventType.CustomEvent,
            $"Approval {approvalId} {(isApproved ? "approved" : "rejected")} by {approver}", approver, eventData, cancellationToken);
    }

    // Stub implementations for other interface methods to get build working
    public Task LogDataAccessAsync(string workflowInstanceId, string userId, string dataType, string dataId, string accessType, CancellationToken cancellationToken = default) => Task.CompletedTask;
    public Task LogSecurityEventAsync(string workflowInstanceId, string eventType, string userId, SecuritySeverity severity, string description, Dictionary<string, object>? eventData = null, CancellationToken cancellationToken = default) => Task.CompletedTask;
    public Task LogPermissionCheckAsync(string workflowInstanceId, string userId, string permission, bool wasGranted, string? resource = null, CancellationToken cancellationToken = default) => Task.CompletedTask;
    public Task LogAuthenticationEventAsync(string workflowInstanceId, string userId, string eventType, bool wasSuccessful, string? details = null, CancellationToken cancellationToken = default) => Task.CompletedTask;
    public Task LogPerformanceMetricAsync(string workflowInstanceId, string metricName, double value, string? unit = null, Dictionary<string, object>? additionalData = null, CancellationToken cancellationToken = default) => Task.CompletedTask;
    public Task LogResourceUsageAsync(string workflowInstanceId, string resourceType, double usage, string unit, DateTime timestamp, CancellationToken cancellationToken = default) => Task.CompletedTask;
    public Task LogExternalIntegrationAsync(string workflowInstanceId, string integrationType, string operation, bool wasSuccessful, string? externalId = null, Dictionary<string, object>? integrationData = null, CancellationToken cancellationToken = default) => Task.CompletedTask;
    public Task LogNotificationSentAsync(string workflowInstanceId, string notificationType, string recipient, bool wasSuccessful, string? messageId = null, CancellationToken cancellationToken = default) => Task.CompletedTask;
    
    // Query methods
    public async Task<IEnumerable<WorkflowExecutionLog>> GetWorkflowAuditLogsAsync(string workflowInstanceId, DateTime? fromDate = null, DateTime? toDate = null, CancellationToken cancellationToken = default)
    {
        var query = _context.WorkflowExecutionLogs
            .Where(l => l.WorkflowExecutionId.ToString() == workflowInstanceId); // Simplified for now

        if (fromDate.HasValue)
            query = query.Where(l => l.Timestamp >= fromDate.Value);

        if (toDate.HasValue)
            query = query.Where(l => l.Timestamp <= toDate.Value);

        return await query.OrderByDescending(l => l.Timestamp).ToListAsync(cancellationToken);
    }

    public async Task<IEnumerable<WorkflowExecutionLog>> GetActivityAuditLogsAsync(string workflowInstanceId, string activityId, CancellationToken cancellationToken = default)
    {
        return await _context.WorkflowExecutionLogs
            .Where(l => l.WorkflowExecutionId.ToString() == workflowInstanceId && l.ActivityId == activityId)
            .OrderByDescending(l => l.Timestamp)
            .ToListAsync(cancellationToken);
    }

    public async Task<IEnumerable<WorkflowExecutionLog>> GetUserAuditLogsAsync(string userId, DateTime? fromDate = null, DateTime? toDate = null, CancellationToken cancellationToken = default)
    {
        var query = _context.WorkflowExecutionLogs
            .Where(l => l.ExecutedBy == userId);

        if (fromDate.HasValue)
            query = query.Where(l => l.Timestamp >= fromDate.Value);

        if (toDate.HasValue)
            query = query.Where(l => l.Timestamp <= toDate.Value);

        return await query.OrderByDescending(l => l.Timestamp).ToListAsync(cancellationToken);
    }

    // Stub implementations for other query methods
    public Task<IEnumerable<WorkflowExecutionLog>> GetSecurityAuditLogsAsync(SecuritySeverity? minimumSeverity = null, DateTime? fromDate = null, DateTime? toDate = null, CancellationToken cancellationToken = default) => Task.FromResult(Enumerable.Empty<WorkflowExecutionLog>());
    public Task<Dictionary<string, object>> GetWorkflowPerformanceMetricsAsync(string workflowInstanceId, CancellationToken cancellationToken = default) => Task.FromResult(new Dictionary<string, object>());
    public Task<Dictionary<string, object>> GetWorkflowComplianceReportAsync(string workflowDefinitionId, DateTime fromDate, DateTime toDate, CancellationToken cancellationToken = default) => Task.FromResult(new Dictionary<string, object>());
    public Task ArchiveOldLogsAsync(DateTime cutoffDate, CancellationToken cancellationToken = default) => Task.CompletedTask;
    public Task<int> GetAuditLogCountAsync(string? workflowInstanceId = null, CancellationToken cancellationToken = default) => Task.FromResult(0);
    public Task PurgeArchivedLogsAsync(DateTime cutoffDate, CancellationToken cancellationToken = default) => Task.CompletedTask;

    // Private helper methods
    private async Task LogWorkflowEventAsync(string workflowInstanceId, WorkflowLogEventType eventType, 
        string message, string? executedBy = null, Dictionary<string, object>? eventData = null, 
        CancellationToken cancellationToken = default, TimeSpan? duration = null, string? exception = null)
    {
        try
        {
            var logEntry = new WorkflowExecutionLog
            {
                WorkflowExecutionId = 0, // Will be set when we have the proper WorkflowExecution entity
                ActivityId = $"workflow-{eventType}",
                ActivityName = $"Workflow {eventType}",
                ActivityType = "System",
                EventType = eventType,
                Message = message,
                ExecutedBy = executedBy ?? _currentUserService.UserIdAsString,
                Payload = eventData != null ? System.Text.Json.JsonSerializer.Serialize(eventData) : null,
                Duration = duration,
                Exception = exception,
                Timestamp = DateTime.UtcNow,
                Source = "WorkflowAuditService"
            };

            _context.WorkflowExecutionLogs.Add(logEntry);
            await _context.SaveChangesAsync(cancellationToken);
        }
        catch (Exception ex) when (ex.Message.Contains("does not exist") || 
                                   ex.Message.Contains("WorkflowExecutionLogs") ||
                                   ex.Message.Contains("relation") && ex.Message.Contains("not found"))
        {
            // Workflow tables not yet created - log warning but don't fail
            _logger.LogWarning("Workflow audit log skipped - workflow tables not yet created: {EventType} for workflow {WorkflowId}", 
                eventType, workflowInstanceId);
        }
        catch (Exception ex)
        {
            // Log other database errors but don't throw to avoid breaking workflow execution
            _logger.LogError(ex, "Failed to log workflow audit event: {EventType} for workflow {WorkflowId}", 
                eventType, workflowInstanceId);
        }
    }

    private async Task LogActivityEventAsync(string workflowInstanceId, string activityId, string activityName, 
        string activityType, WorkflowLogEventType eventType, string message, string? executedBy = null, 
        Dictionary<string, object>? eventData = null, CancellationToken cancellationToken = default, 
        TimeSpan? duration = null, string? exception = null)
    {
        try
        {
            var logEntry = new WorkflowExecutionLog
            {
                WorkflowExecutionId = 0, // Will be set when we have the proper WorkflowExecution entity
                ActivityId = activityId,
                ActivityName = activityName,
                ActivityType = activityType,
                EventType = eventType,
                Message = message,
                ExecutedBy = executedBy ?? _currentUserService.UserIdAsString,
                ActivityInput = eventData != null ? System.Text.Json.JsonSerializer.Serialize(eventData) : null,
                Duration = duration,
                Exception = exception,
                Timestamp = DateTime.UtcNow,
                Source = "WorkflowAuditService"
            };

            _context.WorkflowExecutionLogs.Add(logEntry);
            await _context.SaveChangesAsync(cancellationToken);
        }
        catch (Exception ex) when (ex.Message.Contains("does not exist") || 
                                   ex.Message.Contains("WorkflowExecutionLogs") ||
                                   ex.Message.Contains("relation") && ex.Message.Contains("not found"))
        {
            // Workflow tables not yet created - log warning but don't fail
            _logger.LogWarning("Workflow audit log skipped - workflow tables not yet created: {EventType} for {ActivityId}", 
                eventType, activityId);
        }
        catch (Exception ex)
        {
            // Log other database errors but don't throw to avoid breaking workflow execution
            _logger.LogError(ex, "Failed to log workflow audit event: {EventType} for {ActivityId}", 
                eventType, activityId);
        }
    }
}