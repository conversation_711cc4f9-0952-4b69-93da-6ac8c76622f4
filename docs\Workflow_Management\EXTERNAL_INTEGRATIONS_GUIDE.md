# External System Integrations Guide

This guide explains how to configure and use external system integrations in the Harmoni360 workflow system.

## Overview

The workflow system supports integration with three main types of external systems:

1. **Email Notifications** - SMTP email delivery with template support
2. **Calendar Services** - Calendar event creation for meetings and deadlines
3. **Ticketing Systems** - External ticket creation and management

All integrations are designed with **graceful degradation** - if external services are unavailable, the system continues to function and logs the intended actions.

## Email Integration

### Configuration

#### Development (SMTP4Dev)

```json
{
  "Email": {
    "SmtpHost": "localhost",
    "SmtpPort": "25",
    "EnableSsl": false,
    "FromAddress": "<EMAIL>",
    "FromName": "Harmoni360 Development",
    "Username": "",
    "Password": ""
  }
}
```

#### Production (SendGrid/Other SMTP)

```json
{
  "Email": {
    "SmtpHost": "smtp.sendgrid.net",
    "SmtpPort": "587",
    "EnableSsl": true,
    "FromAddress": "<EMAIL>",
    "FromName": "Harmoni360 HSE Management",
    "Username": "apikey",
    "Password": "[Your API Key]"
  }
}
```

### Features

- **Multi-channel notifications** (Email, SMS, WhatsApp, Push)
- **Template-based emails** with variable substitution
- **HTML and plain text** email support
- **Priority levels** (Critical, High, Normal, Low)
- **Attachment support**
- **Graceful degradation** - logs emails when SMTP is unavailable

### Built-in Email Templates

- `incident_created` - New incident notifications
- `incident_assigned` - Assignment notifications
- `investigation_started` - Investigation kick-off
- `incident_escalated` - Escalation alerts (marked as urgent)
- `incident_completed` - Completion notifications

### Usage in Workflows

```csharp
// In workflow activities
await _notificationService.SendNotificationAsync(new NotificationRequest
{
    Email = recipient.Email,
    TemplateName = "incident_created",
    Subject = "New HSE Incident Created",
    TemplateData = new Dictionary<string, string>
    {
        ["IncidentNumber"] = "HSE-2024-001",
        ["Title"] = "Safety Equipment Issue",
        ["Severity"] = "High",
        ["Location"] = "Factory Floor A"
    }
});
```

## Calendar Integration

### Current Implementation

The calendar service is currently implemented as a **mock service** for development and testing. It provides:

- **Business day calculations** (skips weekends)
- **Meeting type support** (Investigation, Review, Follow-up)
- **Attendee management**
- **Event scheduling** with appropriate durations

### Configuration (Future)

```json
{
  "Calendar": {
    "Provider": "outlook|google|exchange",
    "ClientId": "",
    "ClientSecret": "",
    "TenantId": "",
    "DefaultTimeZone": "UTC"
  }
}
```

### Usage in Workflows

```csharp
// Create calendar event for investigation meeting
var calendarEvent = new WorkflowCalendarEvent
{
    Title = $"Investigation Meeting - {incident.Title}",
    EventType = CalendarEventType.InvestigationMeeting,
    IncidentId = workflowContext.IncidentId,
    Attendees = investigationTeam.Select(u => u.Email).ToList()
};

var eventId = await _calendarService.CreateEventAsync(calendarEvent);
```

## Ticketing System Integration

### Supported Systems

1. **Jira** - Full integration with custom fields and labels
2. **ServiceNow** - Incident management integration
3. **Linear** - Modern project management integration
4. **Generic REST API** - Customizable for any system

### Configuration

#### Jira Integration

```json
{
  "Ticketing": {
    "Provider": "jira",
    "BaseUrl": "https://yourcompany.atlassian.net",
    "Username": "<EMAIL>",
    "Password": "[API Token]",
    "Jira": {
      "ProjectKey": "HSE",
      "IssueType": "Bug"
    }
  }
}
```

#### ServiceNow Integration

```json
{
  "Ticketing": {
    "Provider": "servicenow",
    "BaseUrl": "https://yourinstance.service-now.com",
    "Username": "integration-user",
    "Password": "[Password or Token]"
  }
}
```

#### Linear Integration

```json
{
  "Ticketing": {
    "Provider": "linear",
    "BaseUrl": "https://api.linear.app",
    "ApiKey": "[Your API Key]",
    "Linear": {
      "TeamId": "[Your Team ID]"
    }
  }
}
```

### Features

- **Multi-provider support** with provider-specific optimizations
- **Automatic ticket creation** from incident data
- **Custom fields** for Harmoni360 integration metadata
- **Priority mapping** between systems
- **Label/tag support** for categorization
- **Graceful degradation** - creates local tickets when service unavailable
- **Ticket status synchronization**

### Ticket Creation

Tickets are automatically created with:

- **Structured title**: "HSE Incident: [Incident Title]"
- **Detailed description** including all incident information
- **Custom fields** for integration tracking
- **Labels** based on incident severity and location
- **Proper priority mapping**
- **Assignee integration**

### Usage in Workflows

```csharp
// Create external ticket
var ticketRequest = new CreateTicketRequest
{
    Title = $"HSE Incident: {incident.Title}",
    Description = incident.Description,
    Priority = TicketPriority.High,
    Type = TicketType.Incident,
    ReporterId = incident.ReporterEmail,
    Labels = new[] { "hse", "incident", "urgent" }
};

var ticketId = await _ticketingService.CreateTicketAsync(ticketRequest);
```

## Security Considerations

### Authentication

- **API Keys**: Store in secure configuration (Azure Key Vault, AWS Secrets Manager)
- **OAuth Tokens**: Use proper token refresh mechanisms
- **Basic Auth**: Only for development/testing environments

### Configuration Security

```json
// Use environment variables or secure configuration
{
  "Email": {
    "Password": "${EMAIL_PASSWORD}",
  },
  "Ticketing": {
    "ApiKey": "${TICKETING_API_KEY}"
  }
}
```

### Network Security

- **HTTPS Only**: All external integrations use encrypted connections
- **Firewall Rules**: Configure appropriate outbound access
- **Rate Limiting**: Built-in protection against API abuse

## Troubleshooting

### Email Issues

1. **SMTP Connection Failed**
   - Check SMTP host and port configuration
   - Verify SSL/TLS settings
   - Test with tools like `telnet smtp.server.com 587`

2. **Authentication Failed**
   - Verify username and password/API key
   - Check if 2FA requires app-specific passwords

3. **Emails Not Delivered**
   - Check spam folders
   - Verify sender domain reputation
   - Review SMTP logs in application logs

### Ticketing Issues

1. **Service Unavailable**
   - Check network connectivity to ticketing system
   - Verify API endpoints and authentication
   - Review rate limiting settings

2. **Permission Errors**
   - Verify API user has ticket creation permissions
   - Check project/workspace access rights
   - Review custom field permissions

3. **Invalid Requests**
   - Check required fields for your ticketing system
   - Verify field mappings and data types
   - Review API documentation for changes

## Monitoring and Logging

### Application Logs

All integration activities are logged with appropriate levels:

- **Information**: Successful operations
- **Warning**: Fallback operations due to service unavailability
- **Error**: Failed operations with exception details

### Metrics to Monitor

1. **Email Delivery Rate**: Percentage of successful email deliveries
2. **Ticket Creation Success**: Rate of successful ticket creations
3. **API Response Times**: Performance of external service calls
4. **Error Rates**: Frequency of integration failures

## Development and Testing

### Local Development Setup

1. **SMTP4Dev**: Use for email testing without sending real emails
2. **Mock Services**: Use built-in mock implementations
3. **Integration Testing**: Test with sandbox/development instances

### Configuration Files

- `appsettings.Development.json` - Local development settings
- `appsettings.Production.json` - Production settings (empty for security)
- Use environment variables or secure configuration services

## Future Enhancements

### Planned Features

1. **Calendar Integration**: Full Outlook/Google Calendar integration
2. **Webhook Support**: Inbound webhooks from external systems
3. **Bidirectional Sync**: Two-way data synchronization
4. **Custom Templates**: User-configurable email and ticket templates
5. **Integration Dashboard**: Monitoring and management UI

### Extension Points

The integration architecture is designed for extensibility:

- **New Providers**: Easy to add new ticketing/calendar providers
- **Custom Fields**: Configurable field mappings
- **Workflow Hooks**: Custom integration points in workflows
- **Event Handling**: React to external system events

## Support

For integration support:

1. Check application logs for detailed error messages
2. Verify configuration settings
3. Test connectivity to external services
4. Review this documentation for troubleshooting steps
5. Contact system administrators for API access issues