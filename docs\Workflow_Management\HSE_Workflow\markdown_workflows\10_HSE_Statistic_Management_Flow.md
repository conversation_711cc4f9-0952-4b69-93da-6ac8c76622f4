# HSE Business Work Flow - HSE Statistic Management Flow

## Overview
This workflow describes the process for collecting, analyzing, and reporting HSE statistics, including incident rates, hazard data, and performance metrics for management decision-making and compliance reporting.

## Data Collection Sources

### Primary Data Sources:
- **Incident reports** - from the incident management system
- **Inspection reports** - from routine and ad-hoc inspections
- **Additional HSE data** - from various HSE management systems

## Statistical Calculations and Metrics

### 1. Total Recordable Incident Frequency Rate (TRIFR)
**Formula:**
TRIFR = (Total number of incident cases / Total man-hours) × 1,000,000

**Definition:**
TRIFR is calculated from incident reports using the following formula to measure the frequency of recordable incidents per million man-hours worked.

### 2. Total Recordable Severity Frequency Rate (TRSFR)
**Formula:**
TRSFR = (Total number of lost time injury cases / Total man-hours) × 1,000,000

**Definition:**
TRSFR is calculated from incident reports using the following formula to measure the frequency of severe incidents resulting in lost time.

### 3. Lost Time Incident Frequency Rate (LTIFR)
**Formula:**
LTIFR = (Total number of lost days that caused partial or full school operation shutdown / Total man-hours) × 1,000,000

**Definition:**
LTIFR is calculated from incident reports using the following formula to measure incidents that result in operational impact.

### 4. Hazard Statistics
**Data Source:**
The total number of hazards is gathered from inspection reports, along with:
- Status of each hazard case
- Number of hazards reported on a monthly basis
- Types of hazards
- Non-conformance criteria
- Responsible actions
- Top ten unsafe conditions

## Data Processing Workflow

### Step 1: Data Collection
**Source:** Data collection from Incident report and Inspection Report

**Process:**
- Systematic gathering of data from multiple HSE systems
- Data validation and quality checks
- Data standardization and formatting

### Step 2: Statistical Analysis and Categorization
**Process:**
TRIFR, TRSFR, and LTIFR are broken down into three categories:
- **Study-related activities**
- **Work-related activities** 
- **Sport/physical activities**

Analysis includes combination of study and work-related activities, and are recorded based on the past five years up to the current year-to-date.

### Step 3: Hazard Data Analysis
**Process:**
Comprehensive analysis of hazard data including:
- Hazard identification trends
- Risk level categorization
- Monthly reporting patterns
- Corrective action effectiveness
- Top safety concerns identification

### Step 4: Statistical Chart/Diagram Creation
**Process:**
- Create the statistical chart/diagram
- Visual representation of trends and patterns
- Comparative analysis over time periods
- Performance indicator dashboards

### Step 5: Dashboard Publication
**Process:**
- Post it to the dashboard view
- Real-time data visualization
- Interactive reporting features
- User-specific access controls

### Step 6: Information Sharing
**Process:**
- Copy/share into BSJ Staff Portal (Google Site) under HSE Section
- Stakeholder communication
- Public reporting (where appropriate)
- Regulatory reporting compliance

## Statistical Categories and Breakdown

### Activity-Based Classification:
1. **Study-related activities:**
   - Classroom incidents
   - Laboratory accidents
   - Research-related injuries
   - Academic field work incidents

2. **Work-related activities:**
   - Workplace injuries
   - Occupational incidents
   - Maintenance-related accidents
   - Contractor incidents

3. **Sport/physical activities:**
   - Sports injuries
   - Physical education incidents
   - Recreational activity accidents
   - Exercise-related injuries

### Time-Based Analysis:
- Current year-to-date performance
- Five-year historical trends
- Monthly trending analysis
- Seasonal pattern identification

## Hazard Management Statistics

### Hazard Status Tracking:
- Open hazards
- Closed hazards
- Overdue corrective actions
- Risk level distribution

### Monthly Hazard Reporting:
- New hazards identified
- Hazards resolved
- Risk assessment updates
- Trend analysis

### Top Ten Unsafe Conditions:
- Ranking by frequency
- Risk level assessment
- Corrective action status
- Prevention effectiveness

## Key Performance Indicators (KPIs)

### Safety Performance Metrics:
- TRIFR trends and targets
- TRSFR performance indicators
- LTIFR benchmarking
- Zero incident goals

### Hazard Management Metrics:
- Hazard identification rates
- Corrective action completion rates
- Risk reduction effectiveness
- Prevention program success

## Reporting and Communication

### Dashboard Features:
- Real-time data updates
- Interactive visualizations
- Drill-down capabilities
- Export functionality

### Stakeholder Reports:
- Management summary reports
- Regulatory compliance reports
- Department-specific reports
- Public safety communications

### Communication Channels:
- BSJ Staff Portal integration
- Email notifications
- Management briefings
- Safety committee presentations

## Key Stakeholders
- HSE Management team
- Senior leadership and executives
- Department heads and supervisors
- Safety committee members
- Regulatory authorities
- Insurance companies
- External auditors
- Staff and students (via portal)

## Data Quality Assurance

### Validation Procedures:
- Data accuracy verification
- Consistency checks
- Completeness assessments
- Timeliness monitoring

### Audit Trail:
- Data source tracking
- Calculation verification
- Report generation logs
- Access and modification records

## Deliverables
- Statistical analysis reports
- Performance trend charts and graphs
- Dashboard visualizations
- Regulatory compliance reports
- Management summary presentations
- Public safety communications
- Benchmark comparison reports
- Improvement recommendation reports

## Continuous Improvement

### Performance Review:
- Regular metric effectiveness assessment
- Benchmark comparison analysis
- Industry best practice evaluation
- Stakeholder feedback integration

### System Enhancement:
- Data collection optimization
- Reporting process improvement
- Dashboard functionality updates
- Analytics capability expansion

## Compliance and Regulatory Requirements

### Reporting Obligations:
- Regulatory authority submissions
- Industry standard compliance
- Insurance reporting requirements
- Audit documentation

### Data Retention:
- Historical data preservation
- Regulatory retention periods
- Archive management
- Retrieval procedures