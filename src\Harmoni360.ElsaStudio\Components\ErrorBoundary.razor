@using Microsoft.AspNetCore.Components.Web
@inherits ErrorBoundaryBase

<CascadingValue Value="this">
    @if (CurrentException is null)
    {
        @ChildContent
    }
    else
    {
        <div class="error-boundary">
            <div class="error-content">
                <h3>⚠️ Application Error</h3>
                <p>Sorry, there was an error in the application.</p>
                @if (IsNullabilityError(CurrentException))
                {
                    <p><em>This appears to be a WebAssembly compatibility issue that we're working to resolve.</em></p>
                    <button class="btn btn-warning" @onclick="Recover">Try to Continue</button>
                }
                else
                {
                    <details>
                        <summary>Error details</summary>
                        <pre>@CurrentException</pre>
                    </details>
                    <button class="btn btn-danger" @onclick="Recover">Reset</button>
                }
            </div>
        </div>
    }
</CascadingValue>

<style>
    .error-boundary {
        display: flex;
        justify-content: center;
        align-items: center;
        min-height: 300px;
        padding: 20px;
    }
    
    .error-content {
        text-align: center;
        background: #f8f9fa;
        border: 1px solid #dee2e6;
        border-radius: 8px;
        padding: 30px;
        max-width: 600px;
    }
    
    .btn {
        padding: 8px 16px;
        margin: 10px;
        border: none;
        border-radius: 4px;
        cursor: pointer;
    }
    
    .btn-warning {
        background-color: #ffc107;
        color: #212529;
    }
    
    .btn-danger {
        background-color: #dc3545;
        color: white;
    }
    
    details {
        margin: 15px 0;
        text-align: left;
    }
    
    pre {
        background: #f1f3f4;
        padding: 10px;
        border-radius: 4px;
        overflow-x: auto;
        font-size: 12px;
    }
</style>

@code {
    protected override Task OnErrorAsync(Exception exception)
    {
        // Log the error to console for debugging
        Console.WriteLine($"ErrorBoundary caught exception: {exception}");
        
        // If it's a nullability context error, just log it and continue
        if (IsNullabilityError(exception))
        {
            Console.WriteLine("This is a known WebAssembly compatibility issue");
        }
        
        return Task.CompletedTask;
    }

    private bool IsNullabilityError(Exception exception)
    {
        return exception.Message.Contains("NullabilityInfoContext_NotSupported") ||
               exception.InnerException?.Message.Contains("NullabilityInfoContext_NotSupported") == true;
    }
}