using Microsoft.AspNetCore.Mvc;
using Microsoft.Extensions.Logging;

namespace Harmoni360.Web.Controllers;

/// <summary>
/// Base controller providing common functionality for API controllers
/// </summary>
[ApiController]
[Route("api/[controller]")]
public abstract class BaseApiController : ControllerBase
{
    protected readonly ILogger Logger;

    protected BaseApiController(ILogger logger)
    {
        Logger = logger;
    }

    /// <summary>
    /// Executes an action with standard error handling and logging
    /// </summary>
    protected async Task<IActionResult> ExecuteActionAsync<T>(
        Func<Task<T>> action,
        string actionDescription,
        object? logParameters = null)
    {
        try
        {
            var result = await action();
            Logger.LogInformation("{ActionDescription} completed successfully {@Parameters}", 
                actionDescription, logParameters);
            return Ok(result);
        }
        catch (ArgumentException ex)
        {
            Logger.LogWarning(ex, "{ActionDescription} failed with invalid arguments {@Parameters}", 
                actionDescription, logParameters);
            return BadRequest(new { error = ex.Message });
        }
        catch (UnauthorizedAccessException ex)
        {
            Logger.LogWarning(ex, "{ActionDescription} failed - unauthorized access {@Parameters}", 
                actionDescription, logParameters);
            return Unauthorized(new { error = "Access denied" });
        }
        catch (KeyNotFoundException ex)
        {
            Logger.LogWarning(ex, "{ActionDescription} failed - resource not found {@Parameters}", 
                actionDescription, logParameters);
            return NotFound(new { error = ex.Message });
        }
        catch (InvalidOperationException ex)
        {
            Logger.LogError(ex, "{ActionDescription} failed with invalid operation {@Parameters}", 
                actionDescription, logParameters);
            return Conflict(new { error = ex.Message });
        }
        catch (Exception ex)
        {
            Logger.LogError(ex, "{ActionDescription} failed with unexpected error {@Parameters}", 
                actionDescription, logParameters);
            return StatusCode(500, new { 
                error = "An internal server error occurred", 
                details = ex.Message 
            });
        }
    }

    /// <summary>
    /// Executes an action that returns void with standard error handling
    /// </summary>
    protected async Task<IActionResult> ExecuteActionAsync(
        Func<Task> action,
        string actionDescription,
        object? logParameters = null)
    {
        try
        {
            await action();
            Logger.LogInformation("{ActionDescription} completed successfully {@Parameters}", 
                actionDescription, logParameters);
            return Ok();
        }
        catch (ArgumentException ex)
        {
            Logger.LogWarning(ex, "{ActionDescription} failed with invalid arguments {@Parameters}", 
                actionDescription, logParameters);
            return BadRequest(new { error = ex.Message });
        }
        catch (UnauthorizedAccessException ex)
        {
            Logger.LogWarning(ex, "{ActionDescription} failed - unauthorized access {@Parameters}", 
                actionDescription, logParameters);
            return Unauthorized(new { error = "Access denied" });
        }
        catch (KeyNotFoundException ex)
        {
            Logger.LogWarning(ex, "{ActionDescription} failed - resource not found {@Parameters}", 
                actionDescription, logParameters);
            return NotFound(new { error = ex.Message });
        }
        catch (InvalidOperationException ex)
        {
            Logger.LogError(ex, "{ActionDescription} failed with invalid operation {@Parameters}", 
                actionDescription, logParameters);
            return Conflict(new { error = ex.Message });
        }
        catch (Exception ex)
        {
            Logger.LogError(ex, "{ActionDescription} failed with unexpected error {@Parameters}", 
                actionDescription, logParameters);
            return StatusCode(500, new { 
                error = "An internal server error occurred", 
                details = ex.Message 
            });
        }
    }

    /// <summary>
    /// Executes a synchronous action with standard error handling
    /// </summary>
    protected IActionResult ExecuteAction<T>(
        Func<T> action,
        string actionDescription,
        object? logParameters = null)
    {
        try
        {
            var result = action();
            Logger.LogInformation("{ActionDescription} completed successfully {@Parameters}", 
                actionDescription, logParameters);
            return Ok(result);
        }
        catch (ArgumentException ex)
        {
            Logger.LogWarning(ex, "{ActionDescription} failed with invalid arguments {@Parameters}", 
                actionDescription, logParameters);
            return BadRequest(new { error = ex.Message });
        }
        catch (UnauthorizedAccessException ex)
        {
            Logger.LogWarning(ex, "{ActionDescription} failed - unauthorized access {@Parameters}", 
                actionDescription, logParameters);
            return Unauthorized(new { error = "Access denied" });
        }
        catch (KeyNotFoundException ex)
        {
            Logger.LogWarning(ex, "{ActionDescription} failed - resource not found {@Parameters}", 
                actionDescription, logParameters);
            return NotFound(new { error = ex.Message });
        }
        catch (InvalidOperationException ex)
        {
            Logger.LogError(ex, "{ActionDescription} failed with invalid operation {@Parameters}", 
                actionDescription, logParameters);
            return Conflict(new { error = ex.Message });
        }
        catch (Exception ex)
        {
            Logger.LogError(ex, "{ActionDescription} failed with unexpected error {@Parameters}", 
                actionDescription, logParameters);
            return StatusCode(500, new { 
                error = "An internal server error occurred", 
                details = ex.Message 
            });
        }
    }
}