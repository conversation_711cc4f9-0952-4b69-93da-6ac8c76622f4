using Elsa.Workflows.Runtime;
using Elsa.Workflows.Runtime.Parameters;
using Harmoni360.Application.Common.Interfaces;
using Harmoni360.Domain.Interfaces;
using Harmoni360.Domain.Entities;
using Harmoni360.Application.Features.WorkflowManagement.IncidentWorkflow.Models;
using Microsoft.Extensions.Logging;

namespace Harmoni360.Infrastructure.Services;

/// <summary>
/// Service for managing incident workflows
/// </summary>
public class IncidentWorkflowService : IIncidentWorkflowService
{
    private readonly IWorkflowRuntime _workflowRuntime;
    private readonly ILogger<IncidentWorkflowService> _logger;
    private readonly IIncidentRepository _incidentRepository;
    private readonly ICurrentUserService _currentUserService;

    public IncidentWorkflowService(
        IWorkflowRuntime workflowRuntime,
        ILogger<IncidentWorkflowService> logger,
        IIncidentRepository incidentRepository,
        ICurrentUserService currentUserService)
    {
        _workflowRuntime = workflowRuntime;
        _logger = logger;
        _incidentRepository = incidentRepository;
        _currentUserService = currentUserService;
    }

    public async Task<string?> StartIncidentWorkflowAsync(int incidentId, CancellationToken cancellationToken = default)
    {
        try
        {
            _logger.LogInformation("Starting incident workflow for incident {IncidentId}", incidentId);
            
            // Get incident data with category
            var incident = await _incidentRepository.GetByIdWithCategoryAsync(incidentId);
            if (incident == null)
            {
                _logger.LogError("Incident {IncidentId} not found", incidentId);
                return null;
            }
            
            // Create workflow context with incident data
            var workflowContext = new IncidentWorkflowContext
            {
                IncidentId = incident.Id.ToString(),
                IncidentType = MapIncidentType(incident.Type),
                CategoryCode = incident.Category?.Code ?? "UNKNOWN",
                CategoryName = incident.Category?.Name ?? "Unknown Category",
                Severity = MapSeverity(incident.Severity),
                Status = "Reported",
                ReportedBy = incident.ReporterId?.ToString() ?? "Unknown",
                ReportedAt = incident.CreatedAt,
                Description = incident.Description,
                Location = incident.Location,
                Attachments = new List<AttachmentWorkflowModel>() // Will be populated when attachments are implemented
            };

            // Start workflow with incident context data
            var workflowResult = await _workflowRuntime.StartWorkflowAsync(
                GenerateDeterministicGuid("IncidentManagementWorkflow"),
                new StartWorkflowRuntimeParams
                {
                    Input = new Dictionary<string, object>
                    {
                        { "IncidentContext", workflowContext },
                        { "IncidentId", incident.Id }
                    },
                    CorrelationId = incident.Id.ToString()
                });
            
            if (workflowResult.Incidents.Any())
            {
                _logger.LogWarning("Workflow started with issues for incident {IncidentId}: {Issues}", 
                    incidentId, string.Join(", ", workflowResult.Incidents.Select(i => i.Exception?.Message)));
                return workflowResult.WorkflowInstanceId; // Still return the ID even with warnings
            }
            else
            {
                _logger.LogInformation("Successfully started workflow for incident {IncidentId}, WorkflowInstanceId: {WorkflowInstanceId}", 
                    incidentId, workflowResult.WorkflowInstanceId);
                return workflowResult.WorkflowInstanceId;
            }
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to start workflow for incident {IncidentId}", incidentId);
            return null;
        }
    }
    
    private string MapIncidentType(IncidentType type)
    {
        return type switch
        {
            IncidentType.Accident => "Accident",
            IncidentType.NearMiss => "NearMiss",
            IncidentType.EnvironmentalIncident => "EnvironmentalIncident",
            IncidentType.SecurityIncident => "SecurityIncident",
            IncidentType.Other => "Other",
            _ => "Unknown"
        };
    }
    
    private string MapSeverity(IncidentSeverity severity)
    {
        return severity switch
        {
            IncidentSeverity.Minor => "Minor",
            IncidentSeverity.Major => "Major",
            IncidentSeverity.Critical => "Fatal",
            _ => "Minor"
        };
    }
    
    private static string GenerateDeterministicGuid(string input)
    {
        using var md5 = System.Security.Cryptography.MD5.Create();
        var hash = md5.ComputeHash(System.Text.Encoding.UTF8.GetBytes(input));
        return new Guid(hash).ToString();
    }
}