import React, { useState, useEffect } from 'react';
import {
  Box,
  Grid,
  Paper,
  Typography,
  Card,
  CardContent,
  Chip,
  IconButton,
  Tooltip,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  TablePagination,
  TextField,
  InputAdornment,
  Select,
  MenuItem,
  FormControl,
  InputLabel,
  Button,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  LinearProgress,
  useTheme,
} from '@mui/material';
import {
  Search as SearchIcon,
  Refresh as RefreshIcon,
  PlayArrow as RunningIcon,
  Pause as SuspendedIcon,
  CheckCircle as CompletedIcon,
  Error as FaultedIcon,
  Cancel as CancelledIcon,
  Timeline as TimelineIcon,
  Assessment as AssessmentIcon,
  FilterList as FilterIcon,
} from '@mui/icons-material';
import {
  LineChart,
  Line,
  AreaChart,
  Area,
  BarChart,
  Bar,
  PieChart,
  Pie,
  Cell,
  XAxis,
  YAxis,
  CartesianGrid,
  Tooltip as ChartTooltip,
  Legend,
  ResponsiveContainer,
} from 'recharts';
import { format, subDays, startOfDay, endOfDay } from 'date-fns';
import axios from 'axios';

interface WorkflowExecution {
  id: number;
  workflowInstanceId: string;
  workflowDefinitionId: string;
  workflowName: string;
  status: string;
  initiatedBy: string;
  startedAt: string;
  completedAt?: string;
  duration?: number;
  errorMessage?: string;
}

interface WorkflowMetrics {
  totalExecutions: number;
  runningExecutions: number;
  completedExecutions: number;
  failedExecutions: number;
  suspendedExecutions: number;
  cancelledExecutions: number;
  averageDuration: number;
  successRate: number;
}

interface WorkflowLog {
  id: number;
  workflowInstanceId: string;
  logType: string;
  eventType: string;
  message: string;
  severity: string;
  timestamp: string;
  activityName?: string;
  duration?: number;
}

const WorkflowMonitoringDashboard: React.FC = () => {
  const theme = useTheme();
  const [loading, setLoading] = useState(true);
  const [refreshing, setRefreshing] = useState(false);
  
  // State for workflow data
  const [executions, setExecutions] = useState<WorkflowExecution[]>([]);
  const [metrics, setMetrics] = useState<WorkflowMetrics | null>(null);
  const [selectedExecution, setSelectedExecution] = useState<WorkflowExecution | null>(null);
  const [executionLogs, setExecutionLogs] = useState<WorkflowLog[]>([]);
  
  // Filtering and pagination
  const [page, setPage] = useState(0);
  const [rowsPerPage, setRowsPerPage] = useState(10);
  const [searchTerm, setSearchTerm] = useState('');
  const [statusFilter, setStatusFilter] = useState<string>('all');
  const [dateRange, setDateRange] = useState({
    start: startOfDay(subDays(new Date(), 7)),
    end: endOfDay(new Date()),
  });
  
  // Chart data
  const [executionTrends, setExecutionTrends] = useState<any[]>([]);
  const [statusDistribution, setStatusDistribution] = useState<any[]>([]);
  const [performanceMetrics, setPerformanceMetrics] = useState<any[]>([]);

  const fetchWorkflowData = async () => {
    try {
      setRefreshing(true);
      
      // Fetch workflow executions
      const executionsResponse = await axios.get('/api/workflow/executions', {
        params: {
          page: page + 1,
          pageSize: rowsPerPage,
          searchTerm,
          status: statusFilter !== 'all' ? statusFilter : undefined,
          fromDate: dateRange.start.toISOString(),
          toDate: dateRange.end.toISOString(),
        },
      });
      
      setExecutions(executionsResponse.data.items || []);
      
      // Fetch workflow metrics
      const metricsResponse = await axios.get('/api/workflow/metrics/performance');
      setMetrics(metricsResponse.data);
      
      // Update chart data
      updateChartData(executionsResponse.data.items);
      
    } catch (error) {
      console.error('Error fetching workflow data:', error);
    } finally {
      setLoading(false);
      setRefreshing(false);
    }
  };

  const fetchExecutionLogs = async (workflowInstanceId: string) => {
    try {
      const response = await axios.get(`/api/workflow/audit/${workflowInstanceId}`);
      setExecutionLogs(response.data);
    } catch (error) {
      console.error('Error fetching execution logs:', error);
    }
  };

  const updateChartData = (executionData: WorkflowExecution[]) => {
    // Calculate status distribution
    const statusCounts = executionData.reduce((acc: any, exec) => {
      acc[exec.status] = (acc[exec.status] || 0) + 1;
      return acc;
    }, {});
    
    setStatusDistribution(
      Object.entries(statusCounts).map(([status, count]) => ({
        name: status,
        value: count as number,
        color: getStatusColor(status),
      }))
    );
    
    // Calculate execution trends (last 7 days)
    const trends: any[] = [];
    for (let i = 6; i >= 0; i--) {
      const date = subDays(new Date(), i);
      const dayExecutions = executionData.filter(exec => {
        const execDate = new Date(exec.startedAt);
        return execDate.toDateString() === date.toDateString();
      });
      
      trends.push({
        date: format(date, 'MMM dd'),
        total: dayExecutions.length,
        completed: dayExecutions.filter(e => e.status === 'Finished').length,
        failed: dayExecutions.filter(e => e.status === 'Faulted').length,
      });
    }
    setExecutionTrends(trends);
  };

  useEffect(() => {
    fetchWorkflowData();
  }, [page, rowsPerPage, statusFilter, dateRange]);

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'Running':
        return <RunningIcon color="primary" />;
      case 'Finished':
        return <CompletedIcon color="success" />;
      case 'Suspended':
        return <SuspendedIcon color="warning" />;
      case 'Faulted':
        return <FaultedIcon color="error" />;
      case 'Cancelled':
        return <CancelledIcon color="disabled" />;
      default:
        return null;
    }
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'Running':
        return theme.palette.primary.main;
      case 'Finished':
        return theme.palette.success.main;
      case 'Suspended':
        return theme.palette.warning.main;
      case 'Faulted':
        return theme.palette.error.main;
      case 'Cancelled':
        return theme.palette.grey[500];
      default:
        return theme.palette.grey[400];
    }
  };

  const getSeverityColor = (severity: string) => {
    switch (severity) {
      case 'Critical':
        return 'error';
      case 'Error':
        return 'error';
      case 'Warning':
        return 'warning';
      case 'Information':
        return 'info';
      case 'Debug':
        return 'default';
      default:
        return 'default';
    }
  };

  const formatDuration = (milliseconds?: number) => {
    if (!milliseconds) return '-';
    const seconds = Math.floor(milliseconds / 1000);
    const minutes = Math.floor(seconds / 60);
    const hours = Math.floor(minutes / 60);
    
    if (hours > 0) {
      return `${hours}h ${minutes % 60}m`;
    } else if (minutes > 0) {
      return `${minutes}m ${seconds % 60}s`;
    } else {
      return `${seconds}s`;
    }
  };

  const handleExecutionClick = async (execution: WorkflowExecution) => {
    setSelectedExecution(execution);
    await fetchExecutionLogs(execution.workflowInstanceId);
  };

  const renderMetricsCards = () => (
    <Grid container spacing={3}>
      <Grid item xs={12} sm={6} md={3}>
        <Card>
          <CardContent>
            <Typography color="textSecondary" gutterBottom>
              Total Executions
            </Typography>
            <Typography variant="h4">
              {metrics?.totalExecutions || 0}
            </Typography>
          </CardContent>
        </Card>
      </Grid>
      <Grid item xs={12} sm={6} md={3}>
        <Card>
          <CardContent>
            <Typography color="textSecondary" gutterBottom>
              Success Rate
            </Typography>
            <Typography variant="h4" color={
              (metrics?.successRate || 0) > 90 ? 'success.main' : 'warning.main'
            }>
              {(metrics?.successRate || 0).toFixed(1)}%
            </Typography>
          </CardContent>
        </Card>
      </Grid>
      <Grid item xs={12} sm={6} md={3}>
        <Card>
          <CardContent>
            <Typography color="textSecondary" gutterBottom>
              Currently Running
            </Typography>
            <Typography variant="h4" color="primary">
              {metrics?.runningExecutions || 0}
            </Typography>
          </CardContent>
        </Card>
      </Grid>
      <Grid item xs={12} sm={6} md={3}>
        <Card>
          <CardContent>
            <Typography color="textSecondary" gutterBottom>
              Avg Duration
            </Typography>
            <Typography variant="h4">
              {formatDuration(metrics?.averageDuration)}
            </Typography>
          </CardContent>
        </Card>
      </Grid>
    </Grid>
  );

  const renderCharts = () => (
    <Grid container spacing={3} sx={{ mt: 2 }}>
      {/* Execution Trends */}
      <Grid item xs={12} md={8}>
        <Paper sx={{ p: 2 }}>
          <Typography variant="h6" gutterBottom>
            Execution Trends (Last 7 Days)
          </Typography>
          <ResponsiveContainer width="100%" height={300}>
            <LineChart data={executionTrends}>
              <CartesianGrid strokeDasharray="3 3" />
              <XAxis dataKey="date" />
              <YAxis />
              <ChartTooltip />
              <Legend />
              <Line 
                type="monotone" 
                dataKey="total" 
                stroke={theme.palette.primary.main} 
                name="Total"
              />
              <Line 
                type="monotone" 
                dataKey="completed" 
                stroke={theme.palette.success.main} 
                name="Completed"
              />
              <Line 
                type="monotone" 
                dataKey="failed" 
                stroke={theme.palette.error.main} 
                name="Failed"
              />
            </LineChart>
          </ResponsiveContainer>
        </Paper>
      </Grid>

      {/* Status Distribution */}
      <Grid item xs={12} md={4}>
        <Paper sx={{ p: 2 }}>
          <Typography variant="h6" gutterBottom>
            Status Distribution
          </Typography>
          <ResponsiveContainer width="100%" height={300}>
            <PieChart>
              <Pie
                data={statusDistribution}
                cx="50%"
                cy="50%"
                labelLine={false}
                label={(entry) => `${entry.name}: ${entry.value}`}
                outerRadius={80}
                fill="#8884d8"
                dataKey="value"
              >
                {statusDistribution.map((entry, index) => (
                  <Cell key={`cell-${index}`} fill={entry.color} />
                ))}
              </Pie>
              <ChartTooltip />
            </PieChart>
          </ResponsiveContainer>
        </Paper>
      </Grid>
    </Grid>
  );

  const renderExecutionsTable = () => (
    <Paper sx={{ mt: 3 }}>
      <Box sx={{ p: 2, display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
        <Typography variant="h6">Workflow Executions</Typography>
        <Box sx={{ display: 'flex', gap: 2 }}>
          <TextField
            size="small"
            placeholder="Search workflows..."
            value={searchTerm}
            onChange={(e) => setSearchTerm(e.target.value)}
            InputProps={{
              startAdornment: (
                <InputAdornment position="start">
                  <SearchIcon />
                </InputAdornment>
              ),
            }}
          />
          <FormControl size="small" sx={{ minWidth: 120 }}>
            <InputLabel>Status</InputLabel>
            <Select
              value={statusFilter}
              onChange={(e) => setStatusFilter(e.target.value)}
              label="Status"
            >
              <MenuItem value="all">All</MenuItem>
              <MenuItem value="Running">Running</MenuItem>
              <MenuItem value="Finished">Completed</MenuItem>
              <MenuItem value="Faulted">Failed</MenuItem>
              <MenuItem value="Suspended">Suspended</MenuItem>
              <MenuItem value="Cancelled">Cancelled</MenuItem>
            </Select>
          </FormControl>
          <IconButton onClick={fetchWorkflowData} disabled={refreshing}>
            <RefreshIcon />
          </IconButton>
        </Box>
      </Box>
      
      <TableContainer>
        <Table>
          <TableHead>
            <TableRow>
              <TableCell>Workflow Name</TableCell>
              <TableCell>Instance ID</TableCell>
              <TableCell>Status</TableCell>
              <TableCell>Started By</TableCell>
              <TableCell>Started At</TableCell>
              <TableCell>Duration</TableCell>
              <TableCell>Actions</TableCell>
            </TableRow>
          </TableHead>
          <TableBody>
            {executions.map((execution) => (
              <TableRow 
                key={execution.id}
                hover
                onClick={() => handleExecutionClick(execution)}
                sx={{ cursor: 'pointer' }}
              >
                <TableCell>{execution.workflowName}</TableCell>
                <TableCell>
                  <Typography variant="body2" sx={{ fontFamily: 'monospace' }}>
                    {execution.workflowInstanceId.substring(0, 8)}...
                  </Typography>
                </TableCell>
                <TableCell>
                  <Chip
                    icon={getStatusIcon(execution.status)}
                    label={execution.status}
                    size="small"
                    color={
                      execution.status === 'Finished' ? 'success' :
                      execution.status === 'Faulted' ? 'error' :
                      execution.status === 'Running' ? 'primary' :
                      'default'
                    }
                  />
                </TableCell>
                <TableCell>{execution.initiatedBy}</TableCell>
                <TableCell>{format(new Date(execution.startedAt), 'MMM dd, HH:mm')}</TableCell>
                <TableCell>{formatDuration(execution.duration)}</TableCell>
                <TableCell>
                  <IconButton size="small" onClick={(e) => {
                    e.stopPropagation();
                    handleExecutionClick(execution);
                  }}>
                    <TimelineIcon />
                  </IconButton>
                </TableCell>
              </TableRow>
            ))}
          </TableBody>
        </Table>
      </TableContainer>
      
      <TablePagination
        component="div"
        count={100} // This should come from API
        page={page}
        onPageChange={(e, newPage) => setPage(newPage)}
        rowsPerPage={rowsPerPage}
        onRowsPerPageChange={(e) => {
          setRowsPerPage(parseInt(e.target.value, 10));
          setPage(0);
        }}
      />
    </Paper>
  );

  const renderExecutionDetailsDialog = () => (
    <Dialog 
      open={!!selectedExecution} 
      onClose={() => setSelectedExecution(null)}
      maxWidth="lg"
      fullWidth
    >
      <DialogTitle>
        Workflow Execution Details - {selectedExecution?.workflowName}
      </DialogTitle>
      <DialogContent>
        <Box sx={{ mb: 2 }}>
          <Grid container spacing={2}>
            <Grid item xs={6}>
              <Typography variant="body2" color="textSecondary">Instance ID</Typography>
              <Typography variant="body1" sx={{ fontFamily: 'monospace' }}>
                {selectedExecution?.workflowInstanceId}
              </Typography>
            </Grid>
            <Grid item xs={6}>
              <Typography variant="body2" color="textSecondary">Status</Typography>
              <Chip
                icon={getStatusIcon(selectedExecution?.status || '')}
                label={selectedExecution?.status}
                color={
                  selectedExecution?.status === 'Finished' ? 'success' :
                  selectedExecution?.status === 'Faulted' ? 'error' :
                  'default'
                }
              />
            </Grid>
            <Grid item xs={6}>
              <Typography variant="body2" color="textSecondary">Started At</Typography>
              <Typography variant="body1">
                {selectedExecution && format(new Date(selectedExecution.startedAt), 'PPpp')}
              </Typography>
            </Grid>
            <Grid item xs={6}>
              <Typography variant="body2" color="textSecondary">Duration</Typography>
              <Typography variant="body1">
                {formatDuration(selectedExecution?.duration)}
              </Typography>
            </Grid>
          </Grid>
        </Box>

        <Typography variant="h6" sx={{ mt: 3, mb: 2 }}>Execution Logs</Typography>
        <TableContainer component={Paper} sx={{ maxHeight: 400 }}>
          <Table stickyHeader size="small">
            <TableHead>
              <TableRow>
                <TableCell>Timestamp</TableCell>
                <TableCell>Type</TableCell>
                <TableCell>Activity</TableCell>
                <TableCell>Message</TableCell>
                <TableCell>Severity</TableCell>
              </TableRow>
            </TableHead>
            <TableBody>
              {executionLogs.map((log) => (
                <TableRow key={log.id}>
                  <TableCell>{format(new Date(log.timestamp), 'HH:mm:ss.SSS')}</TableCell>
                  <TableCell>{log.logType}</TableCell>
                  <TableCell>{log.activityName || '-'}</TableCell>
                  <TableCell>{log.message}</TableCell>
                  <TableCell>
                    <Chip
                      label={log.severity}
                      size="small"
                      color={getSeverityColor(log.severity) as any}
                    />
                  </TableCell>
                </TableRow>
              ))}
            </TableBody>
          </Table>
        </TableContainer>
      </DialogContent>
      <DialogActions>
        <Button onClick={() => setSelectedExecution(null)}>Close</Button>
      </DialogActions>
    </Dialog>
  );

  if (loading) {
    return (
      <Box sx={{ width: '100%' }}>
        <LinearProgress />
      </Box>
    );
  }

  return (
    <Box>
      <Typography variant="h4" sx={{ mb: 3 }}>
        Workflow Monitoring Dashboard
      </Typography>

      {renderMetricsCards()}
      {renderCharts()}
      {renderExecutionsTable()}
      {renderExecutionDetailsDialog()}
    </Box>
  );
};

export default WorkflowMonitoringDashboard;