<!DOCTYPE html>
<html>

<head>
    <meta charset="utf-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=no" />
    <title>Elsa Studio</title>
    <base href="/elsa-studio/" />
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css?family=Roboto:300,400,500,700&display=swap" rel="stylesheet" />
    <link href="https://fonts.googleapis.com/css2?family=Ubuntu:wght@300;400;500;700&display=swap" rel="stylesheet">
    <link href="https://fonts.googleapis.com/css2?family=Montserrat:wght@400;500;600;700&display=swap" rel="stylesheet">
    <link href="https://fonts.googleapis.com/css2?family=Grandstander:wght@100&display=swap" rel="stylesheet">
    <link href="_content/MudBlazor/MudBlazor.min.css" rel="stylesheet" />
    <link href="_content/CodeBeam.MudBlazor.Extensions/MudExtensions.min.css" rel="stylesheet" />
    <link href="_content/Radzen.Blazor/css/material-base.css" rel="stylesheet">
    <link href="_content/Elsa.Studio.Shell/css/shell.css" rel="stylesheet">
    <link href="Harmoni360.ElsaStudio.styles.css" rel="stylesheet">
    <link rel="icon" type="image/png" href="favicon.png" />
</head>

<body>
    <div id="app">
        <div class="loading-splash mud-container mud-container-maxwidth-false">
            <h5 class="mud-typography mud-typography-h5 mud-primary-text my-6">Loading...</h5>
        </div>
    </div>

    <div id="blazor-error-ui">
        An unhandled error has occurred.
        <a href="" class="reload">Reload</a>
        <a class="dismiss">🗙</a>
    </div>
    
    <!-- Initialize MudBlazor stubs before Blazor starts -->
    <script>
        // MudBlazor JavaScript stubs to prevent errors during initialization
        window.mudElementRef = {
            getBoundingClientRect: function() {
                return { width: 0, height: 0, top: 0, left: 0, right: 0, bottom: 0, x: 0, y: 0 };
            },
            saveFocus: function() { return Promise.resolve(); },
            addOnBlurEvent: function() { return Promise.resolve(); },
            restoreFocus: function() { return Promise.resolve(); },
            focus: function() { return Promise.resolve(); },
            select: function() { return Promise.resolve(); },
            selectRange: function() { return Promise.resolve(); }
        };
        
        window.mudKeyInterceptor = {
            connect: function() { return Promise.resolve(); },
            disconnect: function() { return Promise.resolve(); },
            updateKey: function() { return Promise.resolve(); }
        };
        
        window.mudScrollManager = {
            lockScroll: function() { return Promise.resolve(); },
            unlockScroll: function() { return Promise.resolve(); }
        };
        
        window.mudWindow = {
            copyToClipboard: function(text) {
                try {
                    return navigator.clipboard.writeText(text);
                } catch {
                    return Promise.resolve();
                }
            },
            getBoundingClientRect: function(selector) {
                try {
                    const element = document.querySelector(selector);
                    return element ? element.getBoundingClientRect() : { width: 0, height: 0, top: 0, left: 0, right: 0, bottom: 0, x: 0, y: 0 };
                } catch {
                    return { width: 0, height: 0, top: 0, left: 0, right: 0, bottom: 0, x: 0, y: 0 };
                }
            },
            open: function() { return Promise.resolve(); }
        };
        
        window.mudDragAndDrop = {
            initDropZone: function() { return Promise.resolve(); },
            dispose: function() { return Promise.resolve(); }
        };
        
        window.mudResizeObserver = {
            connect: function() { return Promise.resolve(); },
            disconnect: function() { return Promise.resolve(); },
            observe: function() { return Promise.resolve(); },
            unobserve: function() { return Promise.resolve(); }
        };
        
        window.mudEventProjections = {
            subscribe: function() { return Promise.resolve(); },
            unsubscribe: function() { return Promise.resolve(); }
        };
        
        window.mudPopover = {
            initialize: function() { return Promise.resolve(); },
            dispose: function() { return Promise.resolve(); },
            connect: function() { return Promise.resolve(); }
        };
        
        window.mudMenu = {
            initialize: function() { return Promise.resolve(); },
            dispose: function() { return Promise.resolve(); }
        };
        
        window.mudTooltip = {
            initialize: function() { return Promise.resolve(); },
            dispose: function() { return Promise.resolve(); }
        };
        
        window.mudUtilities = {
            getScrollOffset: function() { return { x: 0, y: 0 }; },
            getViewportSize: function() { return { width: window.innerWidth, height: window.innerHeight }; }
        };
    </script>
    
    <script src="_content/BlazorMonaco/jsInterop.js"></script>
    <script src="_content/BlazorMonaco/lib/monaco-editor/min/vs/loader.js"></script>
    <script src="_content/BlazorMonaco/lib/monaco-editor/min/vs/editor/editor.main.js"></script>
    <script src="_content/MudBlazor/MudBlazor.min.js"></script>
    <script src="_content/CodeBeam.MudBlazor.Extensions/MudExtensions.min.js"></script>
    <script src="_content/Radzen.Blazor/Radzen.Blazor.js"></script>
    <script src="_framework/blazor.webassembly.js"></script>
</body>

</html>
