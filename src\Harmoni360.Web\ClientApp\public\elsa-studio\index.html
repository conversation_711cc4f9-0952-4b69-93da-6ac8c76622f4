<!DOCTYPE html>
<html lang="en">

<head>
    <meta charset="utf-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>Elsa Studio</title>
    <base href="/elsa-studio/" />
    <link href="css/bootstrap/bootstrap.min.css" rel="stylesheet" />
    <link href="css/app.css" rel="stylesheet" />
    <link rel="icon" type="image/png" href="favicon.png" />
    <link href="Harmoni360.ElsaStudio.styles.css" rel="stylesheet" />
    
    <!-- MudBlazor CSS -->
    <link href="_content/8.5.0/staticwebassets/MudBlazor.min.css" rel="stylesheet" />
    
    <style>
        .loading-progress {
            width: 100px;
            height: 100px;
            position: relative;
            margin: 100px auto;
            display: block;
        }
        
        .loading-progress circle {
            fill: none;
            stroke: #e0e0e0;
            stroke-width: 6;
        }
        
        .loading-progress circle:last-child {
            stroke: #1976d2;
            stroke-dasharray: calc(3.141 * 80);
            stroke-dashoffset: calc(3.141 * 80);
            animation: progress 1s ease-in-out forwards;
        }
        
        @keyframes progress {
            to {
                stroke-dashoffset: 0;
            }
        }
        
        .loading-progress-text {
            position: absolute;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            text-align: center;
            font-weight: bold;
            font-size: 14px;
        }
    </style>
</head>

<body>
    <div id="app">
        <svg class="loading-progress">
            <circle r="40%" cx="50%" cy="50%" />
            <circle r="40%" cx="50%" cy="50%" />
        </svg>
        <div class="loading-progress-text"></div>
    </div>

    <div id="blazor-error-ui">
        An unhandled error has occurred.
        <a href="" class="reload">Reload</a>
        <a class="dismiss">🗙</a>
    </div>
    
    <!-- MudBlazor JS -->
    <script src="_content/8.5.0/staticwebassets/MudBlazor.min.js"></script>
    
    <script src="_framework/blazor.webassembly.js"></script>
</body>

</html>
