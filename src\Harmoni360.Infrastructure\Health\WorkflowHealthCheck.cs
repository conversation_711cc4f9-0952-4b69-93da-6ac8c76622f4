using Harmoni360.Application.Common.Interfaces;
using Harmoni360.Infrastructure.Persistence;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Diagnostics.HealthChecks;
using Microsoft.Extensions.Logging;

namespace Harmoni360.Infrastructure.Health;

public class WorkflowHealthCheck : IHealthCheck
{
    private readonly ApplicationDbContext _context;
    private readonly ITicketingService _ticketingService;
    private readonly INotificationService _notificationService;
    private readonly ILogger<WorkflowHealthCheck> _logger;

    public WorkflowHealthCheck(
        ApplicationDbContext context,
        ITicketingService ticketingService,
        INotificationService notificationService,
        ILogger<WorkflowHealthCheck> logger)
    {
        _context = context;
        _ticketingService = ticketingService;
        _notificationService = notificationService;
        _logger = logger;
    }

    public async Task<HealthCheckResult> CheckHealthAsync(
        HealthCheckContext context, 
        CancellationToken cancellationToken = default)
    {
        try
        {
            var healthData = new Dictionary<string, object>();
            var issues = new List<string>();
            var overallHealthy = true;

            // Check database connectivity
            var dbHealthy = await CheckDatabaseHealth(healthData, cancellationToken);
            if (!dbHealthy)
            {
                issues.Add("Database connection failed");
                overallHealthy = false;
            }

            // Check workflow execution metrics
            var workflowHealthy = await CheckWorkflowMetrics(healthData, cancellationToken);
            if (!workflowHealthy)
            {
                issues.Add("Workflow execution metrics indicate issues");
                overallHealthy = false;
            }

            // Check external service connectivity (non-blocking)
            await CheckExternalServices(healthData, cancellationToken);

            // Check for stuck/long-running workflows
            var stuckWorkflowsHealthy = await CheckForStuckWorkflows(healthData, cancellationToken);
            if (!stuckWorkflowsHealthy)
            {
                issues.Add("Found stuck or long-running workflows");
                overallHealthy = false;
            }

            // Check audit log health
            var auditHealthy = await CheckAuditLogHealth(healthData, cancellationToken);
            if (!auditHealthy)
            {
                issues.Add("Audit logging issues detected");
                overallHealthy = false;
            }

            var status = overallHealthy ? HealthStatus.Healthy : HealthStatus.Unhealthy;
            var description = overallHealthy 
                ? "Workflow system is healthy" 
                : $"Workflow system has issues: {string.Join(", ", issues)}";

            return new HealthCheckResult(status, description, data: healthData);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Workflow health check failed with exception");
            return HealthCheckResult.Unhealthy(
                "Workflow health check failed", 
                ex, 
                new Dictionary<string, object> { ["error"] = ex.Message });
        }
    }

    private async Task<bool> CheckDatabaseHealth(
        Dictionary<string, object> healthData, 
        CancellationToken cancellationToken)
    {
        try
        {
            var startTime = DateTime.UtcNow;
            
            // Simple connectivity test
            await _context.Database.ExecuteSqlRawAsync("SELECT 1", cancellationToken);
            
            var responseTime = (DateTime.UtcNow - startTime).TotalMilliseconds;
            healthData["database_response_time_ms"] = responseTime;
            healthData["database_status"] = "healthy";

            // Check if response time is concerning
            if (responseTime > 5000) // 5 seconds
            {
                healthData["database_warning"] = "Slow response time detected";
                _logger.LogWarning("Database response time is slow: {ResponseTime}ms", responseTime);
            }

            return true;
        }
        catch (Exception ex)
        {
            healthData["database_status"] = "unhealthy";
            healthData["database_error"] = ex.Message;
            _logger.LogError(ex, "Database health check failed");
            return false;
        }
    }

    private async Task<bool> CheckWorkflowMetrics(
        Dictionary<string, object> healthData, 
        CancellationToken cancellationToken)
    {
        try
        {
            var now = DateTime.UtcNow;
            var last24Hours = now.AddDays(-1);

            // Get workflow execution counts
            var totalExecutions = await _context.WorkflowExecutions
                .Where(e => e.CreatedAt >= last24Hours)
                .CountAsync(cancellationToken);

            var failedExecutions = await _context.WorkflowExecutions
                .Where(e => e.CreatedAt >= last24Hours && 
                           e.Status == Domain.Enums.WorkflowStatus.Faulted)
                .CountAsync(cancellationToken);

            var runningExecutions = await _context.WorkflowExecutions
                .Where(e => e.Status == Domain.Enums.WorkflowStatus.Running)
                .CountAsync(cancellationToken);

            var suspendedExecutions = await _context.WorkflowExecutions
                .Where(e => e.Status == Domain.Enums.WorkflowStatus.Suspended)
                .CountAsync(cancellationToken);

            healthData["total_executions_24h"] = totalExecutions;
            healthData["failed_executions_24h"] = failedExecutions;
            healthData["running_executions"] = runningExecutions;
            healthData["suspended_executions"] = suspendedExecutions;

            // Calculate failure rate
            var failureRate = totalExecutions > 0 ? (double)failedExecutions / totalExecutions * 100 : 0;
            healthData["failure_rate_24h_percent"] = Math.Round(failureRate, 2);

            // Check if failure rate is concerning
            var healthy = failureRate < 10; // Less than 10% failure rate
            if (!healthy)
            {
                _logger.LogWarning("High workflow failure rate detected: {FailureRate}%", failureRate);
            }

            return healthy;
        }
        catch (Exception ex)
        {
            // Check if this is a missing table error
            if (ex.Message.Contains("does not exist") || 
                ex.Message.Contains("WorkflowExecutions") ||
                ex.Message.Contains("WorkflowExecutionLogs") ||
                ex.Message.Contains("relation") && ex.Message.Contains("not found"))
            {
                healthData["workflow_metrics_status"] = "not_configured";
                healthData["workflow_metrics_message"] = "Workflow tables not yet created - workflow features not available";
                _logger.LogInformation("Workflow tables not found - workflow features not configured yet");
                return true; // Don't fail health check for missing workflow feature
            }
            
            healthData["workflow_metrics_error"] = ex.Message;
            _logger.LogError(ex, "Failed to check workflow metrics");
            return false;
        }
    }

    private async Task CheckExternalServices(
        Dictionary<string, object> healthData, 
        CancellationToken cancellationToken)
    {
        // Check ticketing service (non-blocking)
        try
        {
            var ticketingHealthy = await CheckTicketingServiceHealth(cancellationToken);
            healthData["ticketing_service_status"] = ticketingHealthy ? "healthy" : "degraded";
        }
        catch (Exception ex)
        {
            healthData["ticketing_service_status"] = "unhealthy";
            healthData["ticketing_service_error"] = ex.Message;
            _logger.LogWarning(ex, "Ticketing service health check failed");
        }

        // Check notification service (non-blocking)
        try
        {
            var notificationHealthy = await CheckNotificationServiceHealth(cancellationToken);
            healthData["notification_service_status"] = notificationHealthy ? "healthy" : "degraded";
        }
        catch (Exception ex)
        {
            healthData["notification_service_status"] = "unhealthy";
            healthData["notification_service_error"] = ex.Message;
            _logger.LogWarning(ex, "Notification service health check failed");
        }
    }

    private async Task<bool> CheckTicketingServiceHealth(CancellationToken cancellationToken)
    {
        try
        {
            // Create a minimal test request to check service availability
            // Create a test ticket request for health check
            var testRequest = new CreateTicketRequest
            {
                Title = "Health Check Test",
                Description = "Automated health check - please ignore",
                Priority = TicketPriority.Low,
                Type = TicketType.Task,
                ReporterId = "system",
                ReporterEmail = "<EMAIL>"
            };

            using var timeoutCts = CancellationTokenSource.CreateLinkedTokenSource(cancellationToken);
            timeoutCts.CancelAfter(TimeSpan.FromSeconds(10));

            // This is a test call - in production, you might want to use a dedicated health check endpoint
            var ticketId = await _ticketingService.CreateTicketAsync(testRequest, timeoutCts.Token);
            
            // Clean up test ticket if created successfully
            if (!string.IsNullOrEmpty(ticketId))
            {
                // In a real implementation, you might want to delete the test ticket
                // or mark it as a health check ticket
                _logger.LogDebug("Ticketing service health check created test ticket: {TicketId}", ticketId);
            }

            return !string.IsNullOrEmpty(ticketId);
        }
        catch (OperationCanceledException)
        {
            _logger.LogWarning("Ticketing service health check timed out");
            return false;
        }
        catch (Exception ex)
        {
            _logger.LogWarning(ex, "Ticketing service health check failed");
            return false;
        }
    }

    private async Task<bool> CheckNotificationServiceHealth(CancellationToken cancellationToken)
    {
        try
        {
            // Check if we can get notification status (this should be a lightweight operation)
            var status = await _notificationService.GetNotificationStatusAsync(cancellationToken);
            
            return status != null;
        }
        catch (Exception ex)
        {
            _logger.LogWarning(ex, "Notification service health check failed");
            return false;
        }
    }

    private async Task<bool> CheckForStuckWorkflows(
        Dictionary<string, object> healthData, 
        CancellationToken cancellationToken)
    {
        try
        {
            var now = DateTime.UtcNow;
            var stuckThreshold = now.AddHours(-4); // Workflows running for more than 4 hours

            var stuckWorkflows = await _context.WorkflowExecutions
                .Where(e => e.Status == Domain.Enums.WorkflowStatus.Running &&
                           e.StartedAt < stuckThreshold)
                .CountAsync(cancellationToken);

            var longSuspendedWorkflows = await _context.WorkflowExecutions
                .Where(e => e.Status == Domain.Enums.WorkflowStatus.Suspended &&
                           e.StartedAt < now.AddDays(-1)) // Suspended for more than 1 day
                .CountAsync(cancellationToken);

            healthData["stuck_workflows_count"] = stuckWorkflows;
            healthData["long_suspended_workflows_count"] = longSuspendedWorkflows;

            var healthy = stuckWorkflows == 0 && longSuspendedWorkflows < 5;
            
            if (!healthy)
            {
                _logger.LogWarning("Found {StuckCount} stuck workflows and {SuspendedCount} long-suspended workflows", 
                    stuckWorkflows, longSuspendedWorkflows);
            }

            return healthy;
        }
        catch (Exception ex)
        {
            // Check if this is a missing table error
            if (ex.Message.Contains("does not exist") || 
                ex.Message.Contains("WorkflowExecutions") ||
                ex.Message.Contains("WorkflowExecutionLogs") ||
                ex.Message.Contains("relation") && ex.Message.Contains("not found"))
            {
                healthData["stuck_workflows_status"] = "not_configured";
                healthData["stuck_workflows_message"] = "Workflow tables not yet created - workflow features not available";
                _logger.LogInformation("Workflow tables not found - stuck workflow check skipped");
                return true; // Don't fail health check for missing workflow feature
            }
            
            healthData["stuck_workflows_error"] = ex.Message;
            _logger.LogError(ex, "Failed to check for stuck workflows");
            return false;
        }
    }

    private async Task<bool> CheckAuditLogHealth(
        Dictionary<string, object> healthData, 
        CancellationToken cancellationToken)
    {
        try
        {
            var now = DateTime.UtcNow;
            var last24Hours = now.AddDays(-1);

            // Check recent audit log activity
            var recentLogCount = await _context.WorkflowExecutionLogs
                .Where(l => l.Timestamp >= last24Hours)
                .CountAsync(cancellationToken);

            var errorLogCount = await _context.WorkflowExecutionLogs
                .Where(l => l.Timestamp >= last24Hours &&
                           (l.EventType == Domain.Enums.WorkflowLogEventType.WorkflowFaulted ||
                            l.EventType == Domain.Enums.WorkflowLogEventType.ActivityFaulted))
                .CountAsync(cancellationToken);

            healthData["audit_logs_24h"] = recentLogCount;
            healthData["error_logs_24h"] = errorLogCount;

            // Check if audit logging is working (should have some activity if workflows are running)
            var workflowsInLast24h = await _context.WorkflowExecutions
                .Where(e => e.CreatedAt >= last24Hours)
                .CountAsync(cancellationToken);

            var healthy = true;
            
            // If there were workflows but no audit logs, something might be wrong
            if (workflowsInLast24h > 0 && recentLogCount == 0)
            {
                healthData["audit_warning"] = "No audit logs found despite recent workflow activity";
                _logger.LogWarning("No audit logs found in last 24 hours despite {WorkflowCount} workflows", workflowsInLast24h);
                healthy = false;
            }

            // Too many errors might indicate a problem
            if (errorLogCount > recentLogCount * 0.2) // More than 20% error logs
            {
                healthData["audit_warning"] = "High error log percentage detected";
                _logger.LogWarning("High error log percentage: {ErrorCount}/{TotalCount}", errorLogCount, recentLogCount);
                healthy = false;
            }

            return healthy;
        }
        catch (Exception ex)
        {
            // Check if this is a missing table error
            if (ex.Message.Contains("does not exist") || 
                ex.Message.Contains("WorkflowExecutions") ||
                ex.Message.Contains("WorkflowExecutionLogs") ||
                ex.Message.Contains("relation") && ex.Message.Contains("not found"))
            {
                healthData["audit_log_status"] = "not_configured";
                healthData["audit_log_message"] = "Workflow tables not yet created - workflow features not available";
                _logger.LogInformation("Workflow tables not found - audit log check skipped");
                return true; // Don't fail health check for missing workflow feature
            }
            
            healthData["audit_log_error"] = ex.Message;
            _logger.LogError(ex, "Failed to check audit log health");
            return false;
        }
    }
}