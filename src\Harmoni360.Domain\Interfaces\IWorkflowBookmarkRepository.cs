using Harmoni360.Domain.Common;
using Harmoni360.Domain.Entities;

namespace Harmoni360.Domain.Interfaces;

public interface IWorkflowBookmarkRepository : IRepository<WorkflowBookmark>
{
    Task<IEnumerable<WorkflowBookmark>> GetByWorkflowExecutionIdAsync(
        int workflowExecutionId, 
        CancellationToken cancellationToken = default);
    
    Task<IEnumerable<WorkflowBookmark>> GetActiveBookmarksAsync(
        CancellationToken cancellationToken = default);
    
    Task<IEnumerable<WorkflowBookmark>> GetByActivityIdAsync(
        string activityId, 
        CancellationToken cancellationToken = default);
    
    Task<IEnumerable<WorkflowBookmark>> GetByBookmarkNameAsync(
        string bookmarkName, 
        CancellationToken cancellationToken = default);
    
    Task<WorkflowBookmark?> GetActiveBookmarkAsync(
        int workflowExecutionId, 
        string activityId, 
        string bookmarkName, 
        CancellationToken cancellationToken = default);
    
    Task<IEnumerable<WorkflowBookmark>> GetExpiredBookmarksAsync(
        DateTime cutoffDate, 
        CancellationToken cancellationToken = default);
    
    Task<int> GetActiveBookmarkCountAsync(
        int? workflowExecutionId = null, 
        CancellationToken cancellationToken = default);
    
    Task ResumeBookmarkAsync(
        int bookmarkId, 
        string resumedBy, 
        CancellationToken cancellationToken = default);
    
    Task ResumeBookmarksAsync(
        IEnumerable<int> bookmarkIds, 
        string resumedBy, 
        CancellationToken cancellationToken = default);
    
    Task<IEnumerable<WorkflowBookmark>> GetBookmarksByTagAsync(
        string tag, 
        CancellationToken cancellationToken = default);
}