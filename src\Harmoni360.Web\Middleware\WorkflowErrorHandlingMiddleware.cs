using Harmoni360.Application.Common.Interfaces;
using Harmoni360.Domain.Exceptions;
using Microsoft.AspNetCore.Mvc;
using System.Net;
using System.Text.Json;

namespace Harmoni360.Web.Middleware;

public class WorkflowErrorHandlingMiddleware
{
    private readonly RequestDelegate _next;
    private readonly ILogger<WorkflowErrorHandlingMiddleware> _logger;

    public WorkflowErrorHandlingMiddleware(RequestDelegate next, ILogger<WorkflowErrorHandlingMiddleware> logger)
    {
        _next = next;
        _logger = logger;
    }

    public async Task InvokeAsync(HttpContext context, IWorkflowAuditService auditService)
    {
        try
        {
            await _next(context);
        }
        catch (Exception ex)
        {
            await HandleWorkflowExceptionAsync(context, ex, auditService);
        }
    }

    private async Task HandleWorkflowExceptionAsync(HttpContext context, Exception exception, IWorkflowAuditService auditService)
    {
        var response = context.Response;
        response.ContentType = "application/json";

        var problemDetails = new ProblemDetails();
        var statusCode = HttpStatusCode.InternalServerError;

        // Log the exception and create audit entry if it's a workflow exception
        if (exception is WorkflowException workflowException)
        {
            try
            {
                await auditService.LogWorkflowFailedAsync(
                    workflowException.WorkflowInstanceId,
                    exception.Message,
                    exception,
                    context.User?.Identity?.Name);
            }
            catch (Exception auditEx)
            {
                _logger.LogError(auditEx, "Failed to log workflow exception to audit service");
            }
        }

        switch (exception)
        {
            case WorkflowDefinitionNotFoundException:
            case WorkflowInstanceNotFoundException:
                statusCode = HttpStatusCode.NotFound;
                problemDetails.Title = "Resource Not Found";
                problemDetails.Detail = exception.Message;
                _logger.LogWarning(exception, "Workflow resource not found: {Message}", exception.Message);
                break;

            case WorkflowValidationException validationEx:
                statusCode = HttpStatusCode.BadRequest;
                problemDetails.Title = "Workflow Validation Failed";
                problemDetails.Detail = exception.Message;
                problemDetails.Extensions["validationErrors"] = validationEx.ValidationErrors.ToArray();
                _logger.LogWarning(exception, "Workflow validation failed: {ValidationErrors}", 
                    string.Join(", ", validationEx.ValidationErrors));
                break;

            case WorkflowAuthorizationException authEx:
                statusCode = HttpStatusCode.Forbidden;
                problemDetails.Title = "Workflow Access Denied";
                problemDetails.Detail = exception.Message;
                problemDetails.Extensions["userId"] = authEx.UserId;
                problemDetails.Extensions["requiredPermission"] = authEx.RequiredPermission;
                problemDetails.Extensions["resource"] = authEx.Resource;
                _logger.LogWarning(exception, "Workflow authorization failed for user {UserId}: {Permission}", 
                    authEx.UserId, authEx.RequiredPermission);
                break;

            case WorkflowInvalidStateException stateEx:
                statusCode = HttpStatusCode.Conflict;
                problemDetails.Title = "Workflow Invalid State";
                problemDetails.Detail = exception.Message;
                problemDetails.Extensions["currentState"] = stateEx.CurrentState;
                problemDetails.Extensions["attemptedOperation"] = stateEx.AttemptedOperation;
                problemDetails.Extensions["validStates"] = stateEx.ValidStates.ToArray();
                _logger.LogWarning(exception, "Workflow state conflict: {CurrentState} -> {Operation}", 
                    stateEx.CurrentState, stateEx.AttemptedOperation);
                break;

            case WorkflowExecutionTimeoutException timeoutEx:
                statusCode = HttpStatusCode.RequestTimeout;
                problemDetails.Title = "Workflow Execution Timeout";
                problemDetails.Detail = exception.Message;
                problemDetails.Extensions["timeoutSeconds"] = timeoutEx.Timeout.TotalSeconds;
                problemDetails.Extensions["elapsedSeconds"] = timeoutEx.ElapsedTime.TotalSeconds;
                _logger.LogWarning(exception, "Workflow execution timeout: {Elapsed}s > {Timeout}s", 
                    timeoutEx.ElapsedTime.TotalSeconds, timeoutEx.Timeout.TotalSeconds);
                break;

            case WorkflowActivityException activityEx:
                statusCode = HttpStatusCode.UnprocessableEntity;
                problemDetails.Title = "Workflow Activity Failed";
                problemDetails.Detail = exception.Message;
                problemDetails.Extensions["activityId"] = activityEx.ActivityId;
                problemDetails.Extensions["activityName"] = activityEx.ActivityName;
                problemDetails.Extensions["activityType"] = activityEx.ActivityType;
                _logger.LogError(exception, "Workflow activity failed: {ActivityName} ({ActivityId})", 
                    activityEx.ActivityName, activityEx.ActivityId);
                break;

            case WorkflowExternalIntegrationException integrationEx:
                statusCode = HttpStatusCode.BadGateway;
                problemDetails.Title = "External Integration Failed";
                problemDetails.Detail = exception.Message;
                problemDetails.Extensions["integrationType"] = integrationEx.IntegrationType;
                problemDetails.Extensions["operation"] = integrationEx.Operation;
                problemDetails.Extensions["externalErrorCode"] = integrationEx.ExternalErrorCode;
                _logger.LogError(exception, "Workflow external integration failed: {IntegrationType} - {Operation}", 
                    integrationEx.IntegrationType, integrationEx.Operation);
                break;

            case WorkflowDataCorruptionException dataEx:
                statusCode = HttpStatusCode.InternalServerError;
                problemDetails.Title = "Workflow Data Corruption";
                problemDetails.Detail = exception.Message;
                problemDetails.Extensions["dataType"] = dataEx.DataType;
                problemDetails.Extensions["dataId"] = dataEx.DataId;
                _logger.LogError(exception, "Workflow data corruption detected: {DataType} - {DataId}", 
                    dataEx.DataType, dataEx.DataId);
                break;

            case WorkflowDependencyException dependencyEx:
                statusCode = HttpStatusCode.ServiceUnavailable;
                problemDetails.Title = "Workflow Dependency Unavailable";
                problemDetails.Detail = exception.Message;
                problemDetails.Extensions["dependencyType"] = dependencyEx.DependencyType;
                problemDetails.Extensions["dependencyName"] = dependencyEx.DependencyName;
                _logger.LogError(exception, "Workflow dependency unavailable: {DependencyType} - {DependencyName}", 
                    dependencyEx.DependencyType, dependencyEx.DependencyName);
                break;

            case WorkflowConfigurationException configEx:
                statusCode = HttpStatusCode.InternalServerError;
                problemDetails.Title = "Workflow Configuration Error";
                problemDetails.Detail = exception.Message;
                problemDetails.Extensions["configurationSection"] = configEx.ConfigurationSection;
                _logger.LogError(exception, "Workflow configuration error: {ConfigurationSection}", 
                    configEx.ConfigurationSection);
                break;

            case WorkflowCancellationException cancellationEx:
                statusCode = HttpStatusCode.RequestTimeout;
                problemDetails.Title = "Workflow Cancelled";
                problemDetails.Detail = exception.Message;
                problemDetails.Extensions["cancelledBy"] = cancellationEx.CancelledBy;
                problemDetails.Extensions["reason"] = cancellationEx.CancellationReason;
                _logger.LogInformation("Workflow cancelled by {CancelledBy}: {Reason}", 
                    cancellationEx.CancelledBy, cancellationEx.CancellationReason);
                break;

            case WorkflowResourceLimitException resourceEx:
                statusCode = HttpStatusCode.TooManyRequests;
                problemDetails.Title = "Workflow Resource Limit Exceeded";
                problemDetails.Detail = exception.Message;
                problemDetails.Extensions["resourceType"] = resourceEx.ResourceType;
                problemDetails.Extensions["currentUsage"] = resourceEx.CurrentUsage;
                problemDetails.Extensions["limit"] = resourceEx.Limit;
                problemDetails.Extensions["unit"] = resourceEx.Unit;
                _logger.LogWarning(exception, "Workflow resource limit exceeded: {ResourceType} {Usage}/{Limit} {Unit}", 
                    resourceEx.ResourceType, resourceEx.CurrentUsage, resourceEx.Limit, resourceEx.Unit);
                break;

            case WorkflowException genericWorkflowEx:
                // Generic workflow exception
                statusCode = HttpStatusCode.InternalServerError;
                problemDetails.Title = "Workflow Error";
                problemDetails.Detail = exception.Message;
                problemDetails.Extensions["workflowInstanceId"] = genericWorkflowEx.WorkflowInstanceId;
                problemDetails.Extensions["workflowDefinitionId"] = genericWorkflowEx.WorkflowDefinitionId;
                _logger.LogError(exception, "Generic workflow error in {WorkflowInstanceId}: {Message}", 
                    genericWorkflowEx.WorkflowInstanceId, exception.Message);
                break;

            default:
                // Non-workflow exceptions
                statusCode = HttpStatusCode.InternalServerError;
                problemDetails.Title = "Internal Server Error";
                problemDetails.Detail = "An unexpected error occurred while processing the request.";
                _logger.LogError(exception, "Unhandled exception: {Message}", exception.Message);
                break;
        }

        // Set common problem details properties
        response.StatusCode = (int)statusCode;
        problemDetails.Status = (int)statusCode;
        problemDetails.Instance = context.Request.Path;

        // Add correlation ID if available
        if (context.Items.TryGetValue("CorrelationId", out var correlationId))
        {
            problemDetails.Extensions["correlationId"] = correlationId;
        }

        // Add timestamp
        problemDetails.Extensions["timestamp"] = DateTime.UtcNow;

        // Add additional context in development
        if (context.RequestServices.GetRequiredService<IWebHostEnvironment>().IsDevelopment())
        {
            problemDetails.Extensions["stackTrace"] = exception.StackTrace;
        }

        var json = JsonSerializer.Serialize(problemDetails, new JsonSerializerOptions
        {
            PropertyNamingPolicy = JsonNamingPolicy.CamelCase,
            WriteIndented = true
        });

        await response.WriteAsync(json);
    }
}