# Incident Management Workflow Test Scenarios

## Document Overview
This document provides comprehensive test scenarios for validating the Incident Management workflow implementation in Harmoni360. Each scenario includes prerequisites, test steps, expected results, and validation criteria.

## Table of Contents
1. [Test Environment Setup](#test-environment-setup)
2. [Positive Test Scenarios](#positive-test-scenarios)
3. [Negative Test Scenarios](#negative-test-scenarios)
4. [Edge Case Scenarios](#edge-case-scenarios)
5. [Performance Test Scenarios](#performance-test-scenarios)
6. [Integration Test Scenarios](#integration-test-scenarios)

---

## Test Environment Setup

### Prerequisites
1. **System Requirements**
   - Harmoni360 application running locally
   - Elsa Studio accessible at `http://localhost:5173/elsa-studio/`
   - Database migrations applied
   - Test users created with appropriate roles

2. **Test Users**
   | Username | Role | Permissions |
   |----------|------|-------------|
   | <EMAIL> | SuperAdmin | All permissions |
   | <EMAIL> | HSEManager | Incident management, approval |
   | <EMAIL> | Investigator | Incident investigation |
   | <EMAIL> | Employee | Incident reporting only |
   | <EMAIL> | ReadOnly | View incidents only |

3. **Test Data**
   - Pre-configured incident categories
   - Sample locations and departments
   - Mock notification services (Email, SMS, WhatsApp)

---

## Positive Test Scenarios

### P01: Basic Incident Creation and Auto-Assignment

**Objective**: Verify that incidents are created and automatically assigned based on severity

**Test Steps**:
1. Login as `<EMAIL>`
2. Navigate to Incident Reporting
3. Create incident with:
   - Title: "Slip and fall in warehouse"
   - Category: "Injury"
   - Severity: "Minor"
   - Location: "Warehouse A"
4. Submit incident

**Expected Results**:
- ✅ Incident created with unique ID
- ✅ Status set to "Open"
- ✅ Workflow instance created
- ✅ Auto-assigned to available HSE team member
- ✅ Email notification sent to assignee
- ✅ Incident visible in Elsa Studio workflow instances

**Validation Queries**:
```sql
-- Check incident creation
SELECT * FROM "Incidents" WHERE Title LIKE '%Slip and fall%' ORDER BY CreatedDate DESC;

-- Check workflow execution
SELECT * FROM "WorkflowExecutions" WHERE IncidentId = {incident_id};

-- Check notifications
SELECT * FROM "NotificationLogs" WHERE RelatedEntityId = {incident_id};
```

### P02: Critical Incident Escalation

**Objective**: Verify escalation workflow for critical severity incidents

**Test Steps**:
1. Login as `<EMAIL>`
2. Create incident with:
   - Title: "Chemical spill in production area"
   - Category: "Environmental"
   - Severity: "Critical"
   - Immediate action required: Yes
3. Submit and wait 15 minutes

**Expected Results**:
- ✅ Immediate notification to HSE Manager
- ✅ SMS alert sent to emergency response team
- ✅ Escalation to Department Head after 15 minutes
- ✅ WhatsApp notification to management group
- ✅ Incident flagged as "High Priority"
- ✅ Workflow shows escalation path in Elsa Studio

### P03: Investigation Assignment and Completion

**Objective**: Verify investigation workflow execution

**Test Steps**:
1. Login as `<EMAIL>`
2. Select an open incident
3. Assign investigation to `<EMAIL>`
4. Login as `<EMAIL>`
5. Complete investigation with:
   - Root cause: "Inadequate safety procedures"
   - Corrective actions: "Update safety protocols"
   - Evidence: Upload test document
6. Submit investigation

**Expected Results**:
- ✅ Investigation task created
- ✅ Notification sent to investigator
- ✅ Investigation form accessible
- ✅ Evidence uploaded successfully
- ✅ Status updated to "Under Investigation"
- ✅ Completion triggers review workflow

### P04: Manager Approval Workflow

**Objective**: Verify approval process for incident resolution

**Test Steps**:
1. Login as `<EMAIL>`
2. Navigate to pending approvals
3. Review investigation findings
4. Add comments: "Approved with recommendations"
5. Approve and close incident

**Expected Results**:
- ✅ Approval task visible in dashboard
- ✅ Investigation details accessible
- ✅ Comments saved to incident history
- ✅ Status changed to "Resolved"
- ✅ Closure notifications sent
- ✅ Workflow instance marked as completed

### P05: Recurring Incident Pattern Detection

**Objective**: Verify pattern detection for similar incidents

**Test Steps**:
1. Create 3 incidents with similar characteristics:
   - Same location: "Warehouse A"
   - Same category: "Slip/Fall"
   - Within 30 days
2. Submit third incident

**Expected Results**:
- ✅ Pattern detection triggered
- ✅ Alert sent to HSE Manager
- ✅ Recommendation for preventive action
- ✅ Incidents linked in system
- ✅ Special workflow branch activated

---

## Negative Test Scenarios

### N01: Unauthorized Incident Creation

**Objective**: Verify access control for incident creation

**Test Steps**:
1. Login as `<EMAIL>`
2. Attempt to access incident creation form
3. Try direct API call to create incident

**Expected Results**:
- ❌ Access denied message displayed
- ❌ No incident created
- ❌ Security event logged
- ❌ No workflow triggered

### N02: Invalid Severity Escalation

**Objective**: Verify system handles invalid severity changes

**Test Steps**:
1. Create incident with severity "Minor"
2. Attempt to change severity to invalid value via API
3. Attempt to bypass workflow escalation

**Expected Results**:
- ❌ Invalid severity rejected
- ❌ Original workflow continues
- ❌ Error logged in system
- ❌ No false escalations

### N03: Assignment to Inactive User

**Objective**: Verify handling of assignments to unavailable users

**Test Steps**:
1. Deactivate user account for investigator
2. Attempt to assign investigation to inactive user
3. Check workflow behavior

**Expected Results**:
- ❌ Assignment rejected
- ✅ Alternative assignee suggested
- ✅ Workflow continues without interruption
- ✅ Admin notified of issue

### N04: Workflow Timeout Handling

**Objective**: Verify timeout handling for stalled workflows

**Test Steps**:
1. Create incident requiring investigation
2. Assign but do not complete within SLA (48 hours)
3. Monitor workflow behavior

**Expected Results**:
- ✅ Timeout warning at 24 hours
- ✅ Escalation at 48 hours
- ✅ Reassignment option provided
- ✅ SLA breach recorded
- ✅ Workflow remains active

### N05: Concurrent Update Conflict

**Objective**: Verify handling of concurrent updates

**Test Steps**:
1. Open same incident in two browser sessions
2. User A: Start editing incident details
3. User B: Submit changes to same incident
4. User A: Attempt to save changes

**Expected Results**:
- ❌ User A save rejected
- ✅ Conflict message displayed
- ✅ Option to refresh and retry
- ✅ No data corruption
- ✅ Workflow state consistent

---

## Edge Case Scenarios

### E01: Mass Incident Reporting

**Objective**: Verify system behavior under high load

**Test Steps**:
1. Simulate 50 incidents reported within 5 minutes
2. Various severities and categories
3. Monitor workflow engine

**Expected Results**:
- ✅ All incidents created successfully
- ✅ Workflows queued appropriately
- ✅ No system degradation
- ✅ Notifications batched efficiently
- ✅ Priority incidents processed first

### E02: Network Interruption Recovery

**Objective**: Verify workflow resilience to network issues

**Test Steps**:
1. Start incident creation
2. Simulate network disconnection
3. Restore connection
4. Verify workflow state

**Expected Results**:
- ✅ Partial data saved
- ✅ Workflow suspended gracefully
- ✅ Resume from last checkpoint
- ✅ No duplicate workflows
- ✅ User notified of recovery

### E03: External Service Failure

**Objective**: Verify handling of external service failures

**Test Steps**:
1. Disable email service
2. Create critical incident
3. Monitor notification handling

**Expected Results**:
- ✅ Primary notification fails gracefully
- ✅ Fallback to SMS/WhatsApp
- ✅ Retry queue activated
- ✅ Admin alerted to service issue
- ✅ Workflow continues

---

## Performance Test Scenarios

### PT01: Workflow Execution Speed

**Objective**: Measure workflow performance metrics

**Test Criteria**:
- Incident creation to assignment: < 2 seconds
- Notification delivery: < 5 seconds
- Status updates: < 1 second
- Workflow state persistence: < 500ms

**Measurement Points**:
```javascript
// Performance monitoring points
- WorkflowInstanceCreated
- ActivityExecutionStarted
- ActivityExecutionCompleted
- WorkflowInstanceCompleted
```

### PT02: Concurrent Workflow Capacity

**Objective**: Verify system capacity limits

**Test Steps**:
1. Execute 100 concurrent workflows
2. Monitor resource usage
3. Check completion rates

**Success Criteria**:
- ✅ 100% completion rate
- ✅ CPU usage < 80%
- ✅ Memory usage stable
- ✅ No deadlocks detected

---

## Integration Test Scenarios

### I01: Email Integration

**Test Steps**:
1. Configure SMTP settings
2. Trigger incident notification
3. Verify email delivery

**Expected Results**:
- ✅ Email formatted correctly
- ✅ Links functional
- ✅ Attachments included
- ✅ Delivery confirmed

### I02: Elsa Studio Visibility

**Test Steps**:
1. Create incident via UI
2. Navigate to Elsa Studio
3. Check workflow instances

**Expected Results**:
- ✅ Workflow visible in definitions
- ✅ Instance shows current state
- ✅ Activity history available
- ✅ Variables inspectable

### I03: API Integration

**Test Steps**:
1. Create incident via API
2. Update via API
3. Query workflow status

**Expected Results**:
- ✅ RESTful endpoints functional
- ✅ Proper authentication required
- ✅ Workflow triggered correctly
- ✅ Status accurately reported

---

## Test Execution Checklist

### Pre-Test Validation
- [ ] Database migrations completed
- [ ] Test users created
- [ ] Elsa Studio accessible
- [ ] Email service configured
- [ ] Test data seeded

### Test Execution Order
1. [ ] Environment Setup (30 min)
2. [ ] Positive Scenarios (2 hours)
3. [ ] Negative Scenarios (1.5 hours)
4. [ ] Edge Cases (1 hour)
5. [ ] Performance Tests (1 hour)
6. [ ] Integration Tests (1.5 hours)

### Post-Test Activities
- [ ] Export test results
- [ ] Document issues found
- [ ] Clear test data
- [ ] Generate test report

---

## Workflow Visibility Troubleshooting

If workflows are not visible in Elsa Studio:

1. **Check Workflow Registration**
   ```csharp
   // Ensure workflows are registered in DI
   services.AddWorkflow<IncidentWorkflow>();
   ```

2. **Verify Workflow Deployment**
   ```bash
   # Check if workflows are published
   dotnet run --project Harmoni360.Web
   # Look for: "Workflow 'IncidentWorkflow' registered"
   ```

3. **Elsa Studio Connection**
   - Verify API endpoint: `/elsa/api`
   - Check authentication token
   - Confirm CORS settings

4. **Database Verification**
   ```sql
   -- Check workflow definitions
   SELECT * FROM "WorkflowDefinitions";
   
   -- Check workflow instances
   SELECT * FROM "WorkflowInstances";
   ```

---

## Expected Test Results Summary

### Success Metrics
- **Functional Coverage**: 100% of workflow paths tested
- **Pass Rate**: > 95% for positive scenarios
- **Performance**: All operations < 5 seconds
- **Reliability**: No workflow corruption or data loss
- **Security**: All access controls enforced

### Known Limitations
- Email delivery depends on SMTP configuration
- SMS/WhatsApp require external service setup
- Performance may vary based on hardware

---

## Test Report Template

```
Test Execution Date: ___________
Tester Name: ___________
Environment: ___________

Summary:
- Total Scenarios: 25
- Passed: ___
- Failed: ___
- Blocked: ___

Critical Issues:
1. ___________
2. ___________

Recommendations:
1. ___________
2. ___________

Sign-off: ___________
```