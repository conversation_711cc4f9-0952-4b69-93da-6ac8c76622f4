using Microsoft.EntityFrameworkCore;
using Elsa.EntityFrameworkCore.Modules.Management;
using Elsa.EntityFrameworkCore.Modules.Runtime;

namespace Harmoni360.Infrastructure.Extensions;

/// <summary>
/// Extensions to configure <PERSON> to use the public schema
/// </summary>
public static class ElsaSchemaExtensions
{
    /// <summary>
    /// Configures Elsa Management DbContext to use the public schema
    /// </summary>
    public static void ConfigureElsaManagementSchema(this ModelBuilder modelBuilder)
    {
        // Set default schema to public for all Elsa management entities
        foreach (var entityType in modelBuilder.Model.GetEntityTypes())
        {
            if (entityType.ClrType.Namespace?.StartsWith("Elsa.") == true)
            {
                var tableName = entityType.GetTableName();
                if (!string.IsNullOrEmpty(tableName))
                {
                    entityType.SetSchema("public");
                }
            }
        }
    }

    /// <summary>
    /// Configures Elsa Runtime DbContext to use the public schema
    /// </summary>
    public static void ConfigureElsaRuntimeSchema(this ModelBuilder modelBuilder)
    {
        // Set default schema to public for all Elsa runtime entities
        foreach (var entityType in modelBuilder.Model.GetEntityTypes())
        {
            if (entityType.ClrType.Namespace?.StartsWith("Elsa.") == true)
            {
                var tableName = entityType.GetTableName();
                if (!string.IsNullOrEmpty(tableName))
                {
                    entityType.SetSchema("public");
                }
            }
        }
    }
}