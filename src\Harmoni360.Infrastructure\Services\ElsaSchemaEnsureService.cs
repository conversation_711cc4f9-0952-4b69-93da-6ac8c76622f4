using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Hosting;
using Microsoft.Extensions.Logging;
using Elsa.EntityFrameworkCore.Modules.Management;
using Elsa.EntityFrameworkCore.Modules.Runtime;

namespace Harmoni360.Infrastructure.Services;

/// <summary>
/// Background service that ensures Elsa tables are created in the public schema
/// </summary>
public class ElsaSchemaEnsureService : BackgroundService
{
    private readonly IServiceProvider _serviceProvider;
    private readonly ILogger<ElsaSchemaEnsureService> _logger;

    public ElsaSchemaEnsureService(
        IServiceProvider serviceProvider,
        ILogger<ElsaSchemaEnsureService> logger)
    {
        _serviceProvider = serviceProvider;
        _logger = logger;
    }

    protected override async Task ExecuteAsync(CancellationToken stoppingToken)
    {
        try
        {
            // Small delay to ensure other services are initialized
            await Task.Delay(2000, stoppingToken);

            using var scope = _serviceProvider.CreateScope();
            
            _logger.LogInformation("Ensuring Elsa tables are created in public schema...");

            // Get Elsa Management DbContext
            var managementContext = scope.ServiceProvider.GetRequiredService<ManagementElsaDbContext>();
            
            // Ensure the database is created and migrated
            await managementContext.Database.EnsureCreatedAsync(stoppingToken);
            _logger.LogInformation("Elsa Management database ensured");

            // Get Elsa Runtime DbContext
            var runtimeContext = scope.ServiceProvider.GetRequiredService<RuntimeElsaDbContext>();
            
            // Ensure the database is created and migrated
            await runtimeContext.Database.EnsureCreatedAsync(stoppingToken);
            _logger.LogInformation("Elsa Runtime database ensured");

            // Check what tables were created
            await LogElsaTables(managementContext, "Management");
            await LogElsaTables(runtimeContext, "Runtime");

            _logger.LogInformation("Elsa schema setup completed successfully");
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to ensure Elsa schema setup");
        }
    }

    private async Task LogElsaTables(DbContext context, string contextType)
    {
        try
        {
            var sql = @"
                SELECT table_name, table_schema 
                FROM information_schema.tables 
                WHERE table_type = 'BASE TABLE' 
                AND (table_name LIKE '%Workflow%' 
                     OR table_name LIKE '%Bookmark%' 
                     OR table_name LIKE '%Activity%'
                     OR table_name LIKE '%Trigger%'
                     OR table_name LIKE '%Elsa%'
                     OR table_name LIKE '%KeyValue%')
                ORDER BY table_schema, table_name";

            var result = await context.Database.SqlQueryRaw<TableInfo>(sql).ToListAsync();
            
            _logger.LogInformation("Elsa {ContextType} tables found: {Count}", contextType, result.Count);
            foreach (var table in result)
            {
                _logger.LogInformation("Table: {Schema}.{TableName}", table.table_schema, table.table_name);
            }
        }
        catch (Exception ex)
        {
            _logger.LogWarning(ex, "Failed to query Elsa {ContextType} tables", contextType);
        }
    }

    public class TableInfo
    {
        public string table_name { get; set; } = "";
        public string table_schema { get; set; } = "";
    }
}