import React, { useEffect, useState } from 'react';
import ElsaStudioGuard from '../../components/auth/ElsaStudioGuard';
import { useAuth } from '../../hooks/useAuth';

const WorkflowManagement: React.FC = () => {
  const { token } = useAuth();
  const [isReady, setIsReady] = useState(false);
  const [error, setError] = useState<string | null>(null);

  const ensureAuthToken = async () => {
    console.log('🔄 WorkflowManagement component mounted');
    console.log('🎫 Token from useAuth:', token ? token.substring(0, 20) + '...' : 'null');
    
    // Try to get token from useAuth hook first
    let tokenToUse = token;
    
    // If no token from Redux, try to get from localStorage directly
    if (!tokenToUse) {
      tokenToUse = localStorage.getItem('harmoni360_token');
      console.log('🔍 Fallback to localStorage token:', tokenToUse ? tokenToUse.substring(0, 20) + '...' : 'null');
    }
    
    if (!tokenToUse) {
      console.log('❌ No token available from any source');
      setError('Authentication token not found. Please log in again.');
      return;
    }

    try {
      console.log('🔐 Setting authentication token for Elsa Studio access');
      
      // Set JWT token in multiple places for Elsa Studio to find
      localStorage.setItem('harmoni360_token', tokenToUse);
      sessionStorage.setItem('harmoni360_token', tokenToUse);
      
      // Set cookie
      const isHttps = window.location.protocol === 'https:';
      const secureFlag = isHttps ? '; secure' : '';
      document.cookie = `harmoni360_token=${tokenToUse}; path=/${secureFlag}; samesite=strict; max-age=28800`;

      console.log('  ✅ Authentication tokens set successfully');
      setIsReady(true);
    } catch (err) {
      setError(`Error setting up authentication: ${err instanceof Error ? err.message : 'Unknown error'}`);
    }
  };

  useEffect(() => {
    ensureAuthToken();
  }, [token]);

  const openElsaStudio = (path: string = '') => {
    const elsaStudioUrl = `${window.location.origin}/elsa-studio/${path}`;
    window.open(elsaStudioUrl, '_blank');
  };

  return (
    <ElsaStudioGuard>
      <div className="container-fluid p-4">
        <div className="row">
          <div className="col-12">
            <div className="d-flex justify-content-between align-items-center mb-4">
              <div>
                <h1 className="mb-2">Workflow Management</h1>
                <p className="text-muted mb-0">Manage and monitor HSE workflows using Elsa Studio</p>
              </div>
            </div>

            {error ? (
              <div className="alert alert-danger" role="alert">
                <h5 className="alert-heading">Authentication Error</h5>
                <p>{error}</p>
                <button 
                  className="btn btn-primary"
                  onClick={() => window.location.href = '/dashboard'}
                >
                  Return to Dashboard
                </button>
              </div>
            ) : !isReady ? (
              <div className="text-center py-5">
                <div className="spinner-border text-primary mb-3" role="status">
                  <span className="visually-hidden">Loading...</span>
                </div>
                <p>Preparing Elsa Studio access...</p>
              </div>
            ) : (
              <div className="row g-4">
                {/* Quick Access Cards */}
                <div className="col-md-4">
                  <div className="card h-100">
                    <div className="card-body">
                      <div className="d-flex align-items-center mb-3">
                        <div className="bg-primary bg-opacity-10 p-2 rounded me-3">
                          <i className="bi bi-diagram-3 text-primary fs-4"></i>
                        </div>
                        <h5 className="card-title mb-0">Workflow Definitions</h5>
                      </div>
                      <p className="card-text">View and manage workflow definitions including the HSE Incident Management workflow.</p>
                      <button 
                        className="btn btn-primary"
                        onClick={() => openElsaStudio('workflows/definitions')}
                      >
                        <i className="bi bi-box-arrow-up-right me-2"></i>
                        Open Definitions
                      </button>
                    </div>
                  </div>
                </div>

                <div className="col-md-4">
                  <div className="card h-100">
                    <div className="card-body">
                      <div className="d-flex align-items-center mb-3">
                        <div className="bg-success bg-opacity-10 p-2 rounded me-3">
                          <i className="bi bi-play-circle text-success fs-4"></i>
                        </div>
                        <h5 className="card-title mb-0">Running Instances</h5>
                      </div>
                      <p className="card-text">Monitor running workflow instances and view detailed execution logs and progress.</p>
                      <button 
                        className="btn btn-success"
                        onClick={() => openElsaStudio('workflows/instances')}
                      >
                        <i className="bi bi-box-arrow-up-right me-2"></i>
                        View Instances
                      </button>
                    </div>
                  </div>
                </div>

                <div className="col-md-4">
                  <div className="card h-100">
                    <div className="card-body">
                      <div className="d-flex align-items-center mb-3">
                        <div className="bg-info bg-opacity-10 p-2 rounded me-3">
                          <i className="bi bi-speedometer2 text-info fs-4"></i>
                        </div>
                        <h5 className="card-title mb-0">Dashboard</h5>
                      </div>
                      <p className="card-text">Access the main Elsa Studio dashboard for an overview of all workflow activity.</p>
                      <button 
                        className="btn btn-info"
                        onClick={() => openElsaStudio('')}
                      >
                        <i className="bi bi-box-arrow-up-right me-2"></i>
                        Open Dashboard
                      </button>
                    </div>
                  </div>
                </div>

                {/* Features Overview */}
                <div className="col-12">
                  <div className="card">
                    <div className="card-header">
                      <h5 className="mb-0">How to View Workflow Execution Paths</h5>
                    </div>
                    <div className="card-body">
                      <div className="row">
                        <div className="col-md-6">
                          <h6>📊 View Running Instances</h6>
                          <ol className="ps-3">
                            <li>Click "View Instances" above</li>
                            <li>Find your workflow instance in the list</li>
                            <li>Click on the instance ID to view details</li>
                            <li>See real-time execution progress and logs</li>
                          </ol>
                        </div>
                        <div className="col-md-6">
                          <h6>🔍 Monitor Execution Path</h6>
                          <ul className="ps-3">
                            <li><strong>Activity Timeline:</strong> See each step with timestamps</li>
                            <li><strong>Status Tracking:</strong> Running, Completed, Failed states</li>
                            <li><strong>Error Details:</strong> View failure reasons and stack traces</li>
                            <li><strong>Custom Properties:</strong> Track incident IDs and metadata</li>
                          </ul>
                        </div>
                      </div>
                      
                      <div className="alert alert-info mt-3">
                        <i className="bi bi-info-circle me-2"></i>
                        <strong>Enhanced Workflow Tracking:</strong> Our HSE Incident Management workflow includes detailed metadata and named activities (like "ReceiveIncidentReport", "GenerateIncidentNumber") making it easy to track progress through the 12-step incident management process.
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            )}
          </div>
        </div>
      </div>
    </ElsaStudioGuard>
  );
};

export default WorkflowManagement;