using Harmoni360.Domain.Entities;
using Harmoni360.Domain.Enums;
using Harmoni360.Domain.Interfaces;
using Harmoni360.Infrastructure.Persistence;
using Harmoni360.Infrastructure.Persistence.Repositories;
using Microsoft.EntityFrameworkCore;

namespace Harmoni360.Infrastructure.Repositories;

public class WorkflowExecutionLogRepository : Repository<WorkflowExecutionLog>, IWorkflowExecutionLogRepository
{
    public WorkflowExecutionLogRepository(ApplicationDbContext context) : base(context)
    {
    }

    public async Task<IEnumerable<WorkflowExecutionLog>> GetByWorkflowExecutionIdAsync(
        int workflowExecutionId, 
        CancellationToken cancellationToken = default)
    {
        return await _context.WorkflowExecutionLogs
            .Where(l => l.WorkflowExecutionId == workflowExecutionId)
            .OrderBy(l => l.Timestamp)
            .ToListAsync(cancellationToken);
    }

    public async Task<IEnumerable<WorkflowExecutionLog>> GetByActivityIdAsync(
        string activityId, 
        CancellationToken cancellationToken = default)
    {
        return await _context.WorkflowExecutionLogs
            .Where(l => l.ActivityId == activityId)
            .OrderByDescending(l => l.Timestamp)
            .ToListAsync(cancellationToken);
    }

    public async Task<IEnumerable<WorkflowExecutionLog>> GetByEventTypeAsync(
        WorkflowLogEventType eventType, 
        CancellationToken cancellationToken = default)
    {
        return await _context.WorkflowExecutionLogs
            .Where(l => l.EventType == eventType)
            .OrderByDescending(l => l.Timestamp)
            .ToListAsync(cancellationToken);
    }

    public async Task<IEnumerable<WorkflowExecutionLog>> GetByDateRangeAsync(
        DateTime startDate, 
        DateTime endDate, 
        CancellationToken cancellationToken = default)
    {
        return await _context.WorkflowExecutionLogs
            .Where(l => l.Timestamp >= startDate && l.Timestamp <= endDate)
            .OrderBy(l => l.Timestamp)
            .ToListAsync(cancellationToken);
    }

    public async Task<IEnumerable<WorkflowExecutionLog>> GetLogsByWorkflowAndActivityAsync(
        int workflowExecutionId, 
        string activityId, 
        CancellationToken cancellationToken = default)
    {
        return await _context.WorkflowExecutionLogs
            .Where(l => l.WorkflowExecutionId == workflowExecutionId && l.ActivityId == activityId)
            .OrderBy(l => l.Timestamp)
            .ToListAsync(cancellationToken);
    }

    public async Task<WorkflowExecutionLog?> GetLatestLogAsync(
        int workflowExecutionId, 
        CancellationToken cancellationToken = default)
    {
        return await _context.WorkflowExecutionLogs
            .Where(l => l.WorkflowExecutionId == workflowExecutionId)
            .OrderByDescending(l => l.Timestamp)
            .FirstOrDefaultAsync(cancellationToken);
    }

    public async Task<IEnumerable<WorkflowExecutionLog>> GetErrorLogsAsync(
        int? workflowExecutionId = null, 
        CancellationToken cancellationToken = default)
    {
        var query = _context.WorkflowExecutionLogs
            .Where(l => l.EventType == WorkflowLogEventType.WorkflowFaulted || 
                       l.EventType == WorkflowLogEventType.ActivityFaulted ||
                       !string.IsNullOrEmpty(l.Exception));

        if (workflowExecutionId.HasValue)
        {
            query = query.Where(l => l.WorkflowExecutionId == workflowExecutionId.Value);
        }

        return await query
            .OrderByDescending(l => l.Timestamp)
            .ToListAsync(cancellationToken);
    }

    public async Task<int> GetLogCountAsync(
        int workflowExecutionId, 
        CancellationToken cancellationToken = default)
    {
        return await _context.WorkflowExecutionLogs
            .CountAsync(l => l.WorkflowExecutionId == workflowExecutionId, cancellationToken);
    }

    public async Task DeleteOldLogsAsync(
        DateTime cutoffDate, 
        CancellationToken cancellationToken = default)
    {
        var oldLogs = await _context.WorkflowExecutionLogs
            .Where(l => l.Timestamp < cutoffDate)
            .ToListAsync(cancellationToken);

        _context.WorkflowExecutionLogs.RemoveRange(oldLogs);
        await _context.SaveChangesAsync(cancellationToken);
    }
}