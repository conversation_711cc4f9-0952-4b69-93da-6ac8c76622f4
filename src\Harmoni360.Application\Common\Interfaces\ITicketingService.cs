using Harmoni360.Domain.Common;

namespace Harmoni360.Application.Common.Interfaces;

public interface ITicketingService
{
    Task<string> CreateTicketAsync(CreateTicketRequest request, CancellationToken cancellationToken = default);
    Task UpdateTicketAsync(string ticketId, UpdateTicketRequest request, CancellationToken cancellationToken = default);
    Task CloseTicketAsync(string ticketId, string reason, CancellationToken cancellationToken = default);
    Task<TicketDetails?> GetTicketAsync(string ticketId, CancellationToken cancellationToken = default);
    Task AddCommentAsync(string ticketId, string comment, string author, CancellationToken cancellationToken = default);
    Task AssignTicketAsync(string ticketId, string assigneeId, CancellationToken cancellationToken = default);
    Task<bool> IsTicketingServiceAvailableAsync(CancellationToken cancellationToken = default);
}

public class CreateTicketRequest
{
    public string Title { get; set; } = string.Empty;
    public string Description { get; set; } = string.Empty;
    public TicketPriority Priority { get; set; } = TicketPriority.Medium;
    public TicketType Type { get; set; } = TicketType.Incident;
    public string ReporterId { get; set; } = string.Empty;
    public string ReporterEmail { get; set; } = string.Empty;
    public string? AssigneeId { get; set; }
    public Dictionary<string, string> CustomFields { get; set; } = new();
    public List<TicketAttachment> Attachments { get; set; } = new();
    public List<string> Labels { get; set; } = new();
}

public class UpdateTicketRequest
{
    public string? Title { get; set; }
    public string? Description { get; set; }
    public TicketPriority? Priority { get; set; }
    public TicketStatus? Status { get; set; }
    public string? AssigneeId { get; set; }
    public Dictionary<string, string> CustomFields { get; set; } = new();
    public List<string> Labels { get; set; } = new();
    public string? Resolution { get; set; }
}

public class TicketDetails
{
    public string Id { get; set; } = string.Empty;
    public string Title { get; set; } = string.Empty;
    public string Description { get; set; } = string.Empty;
    public TicketStatus Status { get; set; }
    public TicketPriority Priority { get; set; }
    public TicketType Type { get; set; }
    public string ReporterId { get; set; } = string.Empty;
    public string ReporterEmail { get; set; } = string.Empty;
    public string? AssigneeId { get; set; }
    public string? AssigneeName { get; set; }
    public DateTime CreatedAt { get; set; }
    public DateTime? UpdatedAt { get; set; }
    public DateTime? ResolvedAt { get; set; }
    public string? Resolution { get; set; }
    public List<TicketComment> Comments { get; set; } = new();
    public List<string> Labels { get; set; } = new();
    public Dictionary<string, string> CustomFields { get; set; } = new();
    public string ExternalUrl { get; set; } = string.Empty;
}

public class TicketComment
{
    public string Id { get; set; } = string.Empty;
    public string Author { get; set; } = string.Empty;
    public string Content { get; set; } = string.Empty;
    public DateTime CreatedAt { get; set; }
    public bool IsInternal { get; set; } = false;
}

public class TicketAttachment
{
    public string FileName { get; set; } = string.Empty;
    public byte[] Content { get; set; } = Array.Empty<byte>();
    public string ContentType { get; set; } = string.Empty;
    public string Description { get; set; } = string.Empty;
}

public enum TicketPriority
{
    Critical = 1,
    High = 2,
    Medium = 3,
    Low = 4
}

public enum TicketStatus
{
    New = 1,
    Open = 2,
    InProgress = 3,
    PendingReview = 4,
    Resolved = 5,
    Closed = 6,
    Cancelled = 7
}

public enum TicketType
{
    Incident = 1,
    ServiceRequest = 2,
    Problem = 3,
    Change = 4,
    Task = 5
}