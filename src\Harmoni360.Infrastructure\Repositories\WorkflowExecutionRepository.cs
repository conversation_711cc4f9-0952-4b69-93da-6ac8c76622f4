using Harmoni360.Domain.Entities;
using Harmoni360.Domain.Enums;
using Harmoni360.Domain.Interfaces;
using Harmoni360.Infrastructure.Persistence;
using Harmoni360.Infrastructure.Persistence.Repositories;
using Microsoft.EntityFrameworkCore;

namespace Harmoni360.Infrastructure.Repositories;

public class WorkflowExecutionRepository : Repository<WorkflowExecution>, IWorkflowExecutionRepository
{
    public WorkflowExecutionRepository(ApplicationDbContext context) : base(context)
    {
    }

    public async Task<WorkflowExecution?> GetByInstanceIdAsync(string instanceId, CancellationToken cancellationToken = default)
    {
        return await _context.WorkflowExecutions
            .FirstOrDefaultAsync(w => w.InstanceId == instanceId, cancellationToken);
    }

    public async Task<IEnumerable<WorkflowExecution>> GetByWorkflowDefinitionIdAsync(
        string workflowDefinitionId, 
        CancellationToken cancellationToken = default)
    {
        return await _context.WorkflowExecutions
            .Where(w => w.WorkflowDefinitionId == workflowDefinitionId)
            .OrderByDescending(w => w.StartedAt)
            .ToListAsync(cancellationToken);
    }

    public async Task<IEnumerable<WorkflowExecution>> GetByStatusAsync(
        WorkflowStatus status, 
        CancellationToken cancellationToken = default)
    {
        return await _context.WorkflowExecutions
            .Where(w => w.Status == status)
            .OrderByDescending(w => w.StartedAt)
            .ToListAsync(cancellationToken);
    }

    public async Task<IEnumerable<WorkflowExecution>> GetByCorrelationIdAsync(
        string correlationId, 
        CancellationToken cancellationToken = default)
    {
        return await _context.WorkflowExecutions
            .Where(w => w.CorrelationId == correlationId)
            .OrderByDescending(w => w.StartedAt)
            .ToListAsync(cancellationToken);
    }

    public async Task<IEnumerable<WorkflowExecution>> GetByInitiatorAsync(
        string initiatedBy, 
        int pageNumber = 1, 
        int pageSize = 50, 
        CancellationToken cancellationToken = default)
    {
        return await _context.WorkflowExecutions
            .Where(w => w.InitiatedBy == initiatedBy)
            .OrderByDescending(w => w.StartedAt)
            .Skip((pageNumber - 1) * pageSize)
            .Take(pageSize)
            .ToListAsync(cancellationToken);
    }

    public async Task<IEnumerable<WorkflowExecution>> GetActiveWorkflowsAsync(CancellationToken cancellationToken = default)
    {
        return await _context.WorkflowExecutions
            .Where(w => w.Status == WorkflowStatus.Running || 
                       w.Status == WorkflowStatus.Suspended ||
                       w.Status == WorkflowStatus.Idle)
            .OrderByDescending(w => w.StartedAt)
            .ToListAsync(cancellationToken);
    }

    public async Task<IEnumerable<WorkflowExecution>> GetCompletedWorkflowsAsync(
        DateTime? startDate = null, 
        DateTime? endDate = null, 
        CancellationToken cancellationToken = default)
    {
        var query = _context.WorkflowExecutions
            .Where(w => w.Status == WorkflowStatus.Finished || 
                       w.Status == WorkflowStatus.Cancelled ||
                       w.Status == WorkflowStatus.Faulted);

        if (startDate.HasValue)
        {
            query = query.Where(w => w.FinishedAt >= startDate.Value);
        }

        if (endDate.HasValue)
        {
            query = query.Where(w => w.FinishedAt <= endDate.Value);
        }

        return await query
            .OrderByDescending(w => w.FinishedAt ?? w.StartedAt)
            .ToListAsync(cancellationToken);
    }

    public async Task<int> GetCountByStatusAsync(WorkflowStatus status, CancellationToken cancellationToken = default)
    {
        return await _context.WorkflowExecutions
            .CountAsync(w => w.Status == status, cancellationToken);
    }

    public async Task<bool> ExistsAsync(string instanceId, CancellationToken cancellationToken = default)
    {
        return await _context.WorkflowExecutions
            .AnyAsync(w => w.InstanceId == instanceId, cancellationToken);
    }

    public async Task<WorkflowExecution?> GetWithLogsAsync(int id, CancellationToken cancellationToken = default)
    {
        return await _context.WorkflowExecutions
            .Include(w => w.ExecutionLogs.OrderBy(l => l.Timestamp))
            .FirstOrDefaultAsync(w => w.Id == id, cancellationToken);
    }

    public async Task<WorkflowExecution?> GetWithBookmarksAsync(int id, CancellationToken cancellationToken = default)
    {
        return await _context.WorkflowExecutions
            .Include(w => w.Bookmarks.Where(b => b.IsActive))
            .FirstOrDefaultAsync(w => w.Id == id, cancellationToken);
    }

    public async Task<WorkflowExecution?> GetFullAsync(int id, CancellationToken cancellationToken = default)
    {
        return await _context.WorkflowExecutions
            .Include(w => w.ExecutionLogs.OrderBy(l => l.Timestamp))
            .Include(w => w.Bookmarks)
            .Include(w => w.ExecutionContexts)
            .FirstOrDefaultAsync(w => w.Id == id, cancellationToken);
    }
}