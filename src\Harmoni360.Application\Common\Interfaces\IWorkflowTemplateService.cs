namespace Harmoni360.Application.Common.Interfaces;

/// <summary>
/// Service for managing workflow templates and user customizations
/// Implements the template pattern to allow safe customization of code-based workflows
/// </summary>
public interface IWorkflowTemplateService
{
    /// <summary>
    /// Creates or updates template and user versions of a workflow
    /// </summary>
    /// <param name="workflowType">The code-based workflow type</param>
    /// <param name="cancellationToken">Cancellation token</param>
    /// <returns>Template creation result</returns>
    Task<WorkflowTemplateResult> CreateOrUpdateTemplateAsync(Type workflowType, CancellationToken cancellationToken = default);

    /// <summary>
    /// Checks if a workflow has user customizations
    /// </summary>
    /// <param name="workflowName">Name of the workflow</param>
    /// <param name="cancellationToken">Cancellation token</param>
    /// <returns>True if user has customized the workflow</returns>
    Task<bool> HasUserCustomizationsAsync(string workflowName, CancellationToken cancellationToken = default);

    /// <summary>
    /// Preserves user customizations during template updates
    /// </summary>
    /// <param name="workflowName">Name of the workflow</param>
    /// <param name="newTemplateVersion">New template version</param>
    /// <param name="cancellationToken">Cancellation token</param>
    /// <returns>Preservation result</returns>
    Task<CustomizationPreservationResult> PreserveUserCustomizationsAsync(
        string workflowName, 
        int newTemplateVersion, 
        CancellationToken cancellationToken = default);

    /// <summary>
    /// Gets notification details for template updates
    /// </summary>
    /// <param name="workflowName">Name of the workflow</param>
    /// <param name="cancellationToken">Cancellation token</param>
    /// <returns>Update notification details</returns>
    Task<TemplateUpdateNotification> GetUpdateNotificationAsync(string workflowName, CancellationToken cancellationToken = default);

    /// <summary>
    /// Resets user workflow to match current template
    /// </summary>
    /// <param name="workflowName">Name of the workflow</param>
    /// <param name="userId">User requesting the reset</param>
    /// <param name="cancellationToken">Cancellation token</param>
    /// <returns>Reset result</returns>
    Task<WorkflowResetResult> ResetToTemplateAsync(string workflowName, string userId, CancellationToken cancellationToken = default);
}

/// <summary>
/// Result of template creation or update operation
/// </summary>
public class WorkflowTemplateResult
{
    public string WorkflowName { get; set; } = string.Empty;
    public string TemplateId { get; set; } = string.Empty;
    public string UserVersionId { get; set; } = string.Empty;
    public int TemplateVersion { get; set; }
    public int UserVersion { get; set; }
    public bool IsNewTemplate { get; set; }
    public bool UserVersionUpdated { get; set; }
    public bool HasUserCustomizations { get; set; }
    public string[] UpdatedProperties { get; set; } = Array.Empty<string>();
}

/// <summary>
/// Result of customization preservation operation
/// </summary>
public class CustomizationPreservationResult
{
    public bool CustomizationsPreserved { get; set; }
    public string[] PreservedElements { get; set; } = Array.Empty<string>();
    public string[] ConflictingElements { get; set; } = Array.Empty<string>();
    public bool RequiresManualReview { get; set; }
    public string? ReviewNotes { get; set; }
}

/// <summary>
/// Template update notification details
/// </summary>
public class TemplateUpdateNotification
{
    public string WorkflowName { get; set; } = string.Empty;
    public int CurrentTemplateVersion { get; set; }
    public int NewTemplateVersion { get; set; }
    public bool HasUserCustomizations { get; set; }
    public string[] ChangedFeatures { get; set; } = Array.Empty<string>();
    public DateTime LastTemplateUpdate { get; set; }
    public string UpdateSummary { get; set; } = string.Empty;
    public bool RequiresUserAction { get; set; }
}

/// <summary>
/// Result of workflow reset operation
/// </summary>
public class WorkflowResetResult
{
    public bool Success { get; set; }
    public string WorkflowName { get; set; } = string.Empty;
    public int NewVersion { get; set; }
    public string[] LostCustomizations { get; set; } = Array.Empty<string>();
    public string ResetBy { get; set; } = string.Empty;
    public DateTime ResetAt { get; set; }
}