using Harmoni360.Domain.Common;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;
using System.Text.Json;

namespace Harmoni360.Domain.Entities;

public class WorkflowExecutionContext : BaseEntity, IAuditableEntity
{

    [Required]
    public int WorkflowExecutionId { get; set; }

    [Required]
    [MaxLength(100)]
    public string ActivityId { get; set; } = string.Empty;

    [Required]
    [MaxLength(500)]
    public string ActivityName { get; set; } = string.Empty;

    [Required]
    [MaxLength(100)]
    public string ActivityType { get; set; } = string.Empty;

    [Required]
    [MaxLength(500)]
    public string PropertyName { get; set; } = string.Empty;

    public string? PropertyValue { get; set; }

    [MaxLength(200)]
    public string? PropertyType { get; set; }

    [Required]
    public DateTime UpdatedAt { get; set; }

    [MaxLength(100)]
    public string? UpdatedBy { get; set; }

    // Audit properties
    public DateTime CreatedAt { get; private set; }
    public string CreatedBy { get; private set; } = string.Empty;
    public DateTime? LastModifiedAt { get; private set; }
    public string? LastModifiedBy { get; private set; }

    // Navigation properties
    [ForeignKey(nameof(WorkflowExecutionId))]
    public virtual WorkflowExecution WorkflowExecution { get; set; } = null!;

    // Helper methods
    public T? GetValue<T>()
    {
        if (string.IsNullOrEmpty(PropertyValue))
            return default;

        try
        {
            if (typeof(T) == typeof(string))
                return (T)(object)PropertyValue;

            return JsonSerializer.Deserialize<T>(PropertyValue);
        }
        catch
        {
            return default;
        }
    }

    public void SetValue<T>(T? value)
    {
        if (value == null)
        {
            PropertyValue = null;
            PropertyType = null;
            return;
        }

        PropertyType = typeof(T).FullName;

        if (typeof(T) == typeof(string))
        {
            PropertyValue = value.ToString();
        }
        else
        {
            PropertyValue = JsonSerializer.Serialize(value);
        }

        UpdatedAt = DateTime.UtcNow;
    }
}