namespace Harmoni360.Domain.Exceptions;

/// <summary>
/// Base exception for all workflow-related errors
/// </summary>
public abstract class WorkflowException : Exception
{
    public string WorkflowInstanceId { get; }
    public string? WorkflowDefinitionId { get; }

    protected WorkflowException(string message, string workflowInstanceId, string? workflowDefinitionId = null) 
        : base(message)
    {
        WorkflowInstanceId = workflowInstanceId;
        WorkflowDefinitionId = workflowDefinitionId;
    }

    protected WorkflowException(string message, Exception innerException, string workflowInstanceId, string? workflowDefinitionId = null) 
        : base(message, innerException)
    {
        WorkflowInstanceId = workflowInstanceId;
        WorkflowDefinitionId = workflowDefinitionId;
    }
}

/// <summary>
/// Thrown when a workflow definition cannot be found
/// </summary>
public class WorkflowDefinitionNotFoundException : WorkflowException
{
    public WorkflowDefinitionNotFoundException(string workflowDefinitionId)
        : base($"Workflow definition '{workflowDefinitionId}' was not found.", string.Empty, workflowDefinitionId)
    {
    }
}

/// <summary>
/// Thrown when a workflow instance cannot be found
/// </summary>
public class WorkflowInstanceNotFoundException : WorkflowException
{
    public WorkflowInstanceNotFoundException(string workflowInstanceId)
        : base($"Workflow instance '{workflowInstanceId}' was not found.", workflowInstanceId)
    {
    }
}

/// <summary>
/// Thrown when a workflow execution fails due to validation errors
/// </summary>
public class WorkflowValidationException : WorkflowException
{
    public IEnumerable<string> ValidationErrors { get; }

    public WorkflowValidationException(string workflowInstanceId, IEnumerable<string> validationErrors)
        : base($"Workflow validation failed: {string.Join(", ", validationErrors)}", workflowInstanceId)
    {
        ValidationErrors = validationErrors;
    }

    public WorkflowValidationException(string workflowInstanceId, string validationError)
        : this(workflowInstanceId, new[] { validationError })
    {
    }
}

/// <summary>
/// Thrown when a workflow activity fails to execute
/// </summary>
public class WorkflowActivityException : WorkflowException
{
    public string ActivityId { get; }
    public string ActivityName { get; }
    public string? ActivityType { get; }

    public WorkflowActivityException(string workflowInstanceId, string activityId, string activityName, string message, string? activityType = null)
        : base($"Activity '{activityName}' ({activityId}) failed: {message}", workflowInstanceId)
    {
        ActivityId = activityId;
        ActivityName = activityName;
        ActivityType = activityType;
    }

    public WorkflowActivityException(string workflowInstanceId, string activityId, string activityName, string message, Exception innerException, string? activityType = null)
        : base($"Activity '{activityName}' ({activityId}) failed: {message}", innerException, workflowInstanceId)
    {
        ActivityId = activityId;
        ActivityName = activityName;
        ActivityType = activityType;
    }
}

/// <summary>
/// Thrown when workflow execution is unauthorized
/// </summary>
public class WorkflowAuthorizationException : WorkflowException
{
    public string UserId { get; }
    public string RequiredPermission { get; }
    public string? Resource { get; }

    public WorkflowAuthorizationException(string workflowInstanceId, string userId, string requiredPermission, string? resource = null)
        : base($"User '{userId}' is not authorized to perform '{requiredPermission}' on workflow {workflowInstanceId}" + 
               (resource != null ? $" for resource '{resource}'" : ""), workflowInstanceId)
    {
        UserId = userId;
        RequiredPermission = requiredPermission;
        Resource = resource;
    }
}

/// <summary>
/// Thrown when a workflow operation is attempted on a workflow in an invalid state
/// </summary>
public class WorkflowInvalidStateException : WorkflowException
{
    public string CurrentState { get; }
    public string AttemptedOperation { get; }
    public IEnumerable<string> ValidStates { get; }

    public WorkflowInvalidStateException(string workflowInstanceId, string currentState, string attemptedOperation, IEnumerable<string> validStates)
        : base($"Cannot perform '{attemptedOperation}' on workflow in state '{currentState}'. Valid states are: {string.Join(", ", validStates)}", workflowInstanceId)
    {
        CurrentState = currentState;
        AttemptedOperation = attemptedOperation;
        ValidStates = validStates;
    }
}

/// <summary>
/// Thrown when a workflow execution times out
/// </summary>
public class WorkflowExecutionTimeoutException : WorkflowException
{
    public TimeSpan Timeout { get; }
    public TimeSpan ElapsedTime { get; }

    public WorkflowExecutionTimeoutException(string workflowInstanceId, TimeSpan timeout, TimeSpan elapsedTime)
        : base($"Workflow execution timed out after {elapsedTime.TotalSeconds:F2} seconds (timeout: {timeout.TotalSeconds:F2} seconds)", workflowInstanceId)
    {
        Timeout = timeout;
        ElapsedTime = elapsedTime;
    }
}

/// <summary>
/// Thrown when an external integration required by the workflow fails
/// </summary>
public class WorkflowExternalIntegrationException : WorkflowException
{
    public string IntegrationType { get; }
    public string Operation { get; }
    public string? ExternalErrorCode { get; }

    public WorkflowExternalIntegrationException(string workflowInstanceId, string integrationType, string operation, string message, string? externalErrorCode = null)
        : base($"External integration '{integrationType}' failed during '{operation}': {message}", workflowInstanceId)
    {
        IntegrationType = integrationType;
        Operation = operation;
        ExternalErrorCode = externalErrorCode;
    }

    public WorkflowExternalIntegrationException(string workflowInstanceId, string integrationType, string operation, string message, Exception innerException, string? externalErrorCode = null)
        : base($"External integration '{integrationType}' failed during '{operation}': {message}", innerException, workflowInstanceId)
    {
        IntegrationType = integrationType;
        Operation = operation;
        ExternalErrorCode = externalErrorCode;
    }
}

/// <summary>
/// Thrown when workflow data is corrupted or inconsistent
/// </summary>
public class WorkflowDataCorruptionException : WorkflowException
{
    public string DataType { get; }
    public string? DataId { get; }

    public WorkflowDataCorruptionException(string workflowInstanceId, string dataType, string message, string? dataId = null)
        : base($"Workflow data corruption detected in {dataType}{(dataId != null ? $" ({dataId})" : "")}: {message}", workflowInstanceId)
    {
        DataType = dataType;
        DataId = dataId;
    }
}

/// <summary>
/// Thrown when a workflow dependency is not available
/// </summary>
public class WorkflowDependencyException : WorkflowException
{
    public string DependencyType { get; }
    public string DependencyName { get; }

    public WorkflowDependencyException(string workflowInstanceId, string dependencyType, string dependencyName, string message)
        : base($"Workflow dependency '{dependencyName}' of type '{dependencyType}' is not available: {message}", workflowInstanceId)
    {
        DependencyType = dependencyType;
        DependencyName = dependencyName;
    }

    public WorkflowDependencyException(string workflowInstanceId, string dependencyType, string dependencyName, string message, Exception innerException)
        : base($"Workflow dependency '{dependencyName}' of type '{dependencyType}' is not available: {message}", innerException, workflowInstanceId)
    {
        DependencyType = dependencyType;
        DependencyName = dependencyName;
    }
}

/// <summary>
/// Thrown when a workflow configuration is invalid
/// </summary>
public class WorkflowConfigurationException : WorkflowException
{
    public string ConfigurationSection { get; }

    public WorkflowConfigurationException(string configurationSection, string message)
        : base($"Invalid workflow configuration in section '{configurationSection}': {message}", string.Empty)
    {
        ConfigurationSection = configurationSection;
    }

    public WorkflowConfigurationException(string workflowInstanceId, string configurationSection, string message)
        : base($"Invalid workflow configuration in section '{configurationSection}': {message}", workflowInstanceId)
    {
        ConfigurationSection = configurationSection;
    }
}

/// <summary>
/// Thrown when a workflow execution is cancelled by user request
/// </summary>
public class WorkflowCancellationException : WorkflowException
{
    public string CancelledBy { get; }
    public string? CancellationReason { get; }

    public WorkflowCancellationException(string workflowInstanceId, string cancelledBy, string? cancellationReason = null)
        : base($"Workflow was cancelled by {cancelledBy}" + (cancellationReason != null ? $": {cancellationReason}" : ""), workflowInstanceId)
    {
        CancelledBy = cancelledBy;
        CancellationReason = cancellationReason;
    }
}

/// <summary>
/// Thrown when a workflow resource limit is exceeded
/// </summary>
public class WorkflowResourceLimitException : WorkflowException
{
    public string ResourceType { get; }
    public double CurrentUsage { get; }
    public double Limit { get; }
    public string Unit { get; }

    public WorkflowResourceLimitException(string workflowInstanceId, string resourceType, double currentUsage, double limit, string unit)
        : base($"Workflow resource limit exceeded for {resourceType}: {currentUsage} {unit} (limit: {limit} {unit})", workflowInstanceId)
    {
        ResourceType = resourceType;
        CurrentUsage = currentUsage;
        Limit = limit;
        Unit = unit;
    }
}