using Harmoni360.Domain.Entities;
using System.Text.RegularExpressions;

namespace Harmoni360.Application.Common.Services;

/// <summary>
/// Common service for parsing incident content from various sources (email, WhatsApp, etc.)
/// </summary>
public class IncidentContentParser
{
    public static string ExtractTitle(string subject, string? fallback = null)
    {
        // Remove common prefixes and clean up
        var title = Regex.Replace(subject, @"^(Re:|Fwd?:|FWD:|INCIDENT:|URGENT:)\s*", "", RegexOptions.IgnoreCase);
        
        if (string.IsNullOrWhiteSpace(title))
            return fallback ?? "Incident Report";
            
        return title.Trim();
    }

    public static IncidentSeverity DetectSeverity(string content)
    {
        var upperContent = content.ToUpperInvariant();

        if (ContainsAny(upperContent, "CRITICAL", "EMERGENCY", "FATAL", "DEATH", "!!!"))
            return IncidentSeverity.Critical;

        if (ContainsAny(upperContent, "SERIOUS", "SEVERE", "MAJOR", "INJURY", "HURT", "BLOOD"))
            return IncidentSeverity.Serious;

        if (ContainsAny(upperContent, "MODERATE", "MEDIUM", "CONCERN"))
            return IncidentSeverity.Moderate;

        return IncidentSeverity.Minor;
    }

    public static DateTime? ExtractDate(string content, DateTime fallbackDate)
    {
        var datePatterns = new[]
        {
            @"(?:occurred|happened|date|incident date)[:\s]*(\d{1,2}[/-]\d{1,2}[/-]\d{2,4})",
            @"(\d{1,2}[/-]\d{1,2}[/-]\d{2,4})",
            @"(?:on|at)\s+(\w+ \d{1,2},? \d{4})",
            // Casual date patterns
            @"(?:today|earlier|now|just happened)",
            @"(?:yesterday)",
            @"(?:this morning|this afternoon|this evening)"
        };

        var now = DateTime.UtcNow;

        foreach (var pattern in datePatterns)
        {
            var match = Regex.Match(content, pattern, RegexOptions.IgnoreCase);
            if (match.Success)
            {
                var matchValue = match.Value.ToLowerInvariant();

                // Handle relative dates
                if (matchValue.Contains("today") || matchValue.Contains("now") || matchValue.Contains("just"))
                    return now;

                if (matchValue.Contains("yesterday"))
                    return now.AddDays(-1);

                if (matchValue.Contains("morning"))
                    return now.Date.AddHours(9);

                if (matchValue.Contains("afternoon"))
                    return now.Date.AddHours(14);

                if (matchValue.Contains("evening"))
                    return now.Date.AddHours(19);

                // Try to parse actual dates
                if (DateTime.TryParse(match.Groups[1].Value, out var date))
                {
                    return date;
                }
            }
        }

        return null;
    }

    public static string? ExtractLocation(string content)
    {
        var locationPatterns = new[]
        {
            @"(?:location|place|site|building|room)[:\s]*([^\r\n]+)",
            @"(?:at|in)\s+([A-Z][a-zA-Z\s]+(?:Building|Room|Floor|Lab|Office))",
            @"(?:building|room|floor|lab|office)\s*[:#]?\s*([^\r\n]+)"
        };

        foreach (var pattern in locationPatterns)
        {
            var match = Regex.Match(content, pattern, RegexOptions.IgnoreCase);
            if (match.Success)
            {
                return match.Groups[1].Value.Trim();
            }
        }

        return null;
    }

    public static string? ExtractWitnesses(string content)
    {
        var witnessPatterns = new[]
        {
            @"(?:witness|witnesses|saw|observed|people present)[:\s]*([^\r\n]+)",
            @"(?:present|witnessed by|with me|others)[:\s]*([^\r\n]+)"
        };

        foreach (var pattern in witnessPatterns)
        {
            var match = Regex.Match(content, pattern, RegexOptions.IgnoreCase);
            if (match.Success)
            {
                return match.Groups[1].Value.Trim();
            }
        }

        return null;
    }

    public static string? ExtractImmediateActions(string content)
    {
        var actionPatterns = new[]
        {
            @"(?:immediate actions?|first aid|response|action taken)[:\s]*([^\r\n]+)",
            @"(?:did|action|response|help|called|contacted|notified)[:\s]*([^\r\n]+)"
        };

        foreach (var pattern in actionPatterns)
        {
            var match = Regex.Match(content, pattern, RegexOptions.IgnoreCase);
            if (match.Success)
            {
                return match.Groups[1].Value.Trim();
            }
        }

        return null;
    }

    public static string CleanEmailBody(string body)
    {
        if (string.IsNullOrWhiteSpace(body))
            return "No description provided.";

        // Remove email signatures and quoted text
        var lines = body.Split('\n');
        var cleanLines = new List<string>();
        bool inQuotedSection = false;

        foreach (var line in lines)
        {
            var trimmedLine = line.Trim();

            // Skip quoted text
            if (trimmedLine.StartsWith(">") || (trimmedLine.StartsWith("On ") && trimmedLine.Contains(" wrote:")))
            {
                inQuotedSection = true;
                continue;
            }

            if (inQuotedSection && string.IsNullOrEmpty(trimmedLine))
                continue;

            inQuotedSection = false;

            // Skip signature separators
            if (trimmedLine == "--" || trimmedLine.StartsWith("---"))
                break;

            cleanLines.Add(line);
        }

        return string.Join('\n', cleanLines).Trim();
    }

    private static bool ContainsAny(string content, params string[] keywords)
    {
        return keywords.Any(keyword => content.Contains(keyword));
    }
}