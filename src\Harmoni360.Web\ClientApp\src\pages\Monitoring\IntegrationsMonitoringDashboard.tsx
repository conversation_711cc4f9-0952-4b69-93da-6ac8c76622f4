import React, { useState, useEffect } from 'react';
import {
  Box,
  Grid,
  Paper,
  Typography,
  Card,
  CardContent,
  Chip,
  IconButton,
  Tooltip,
  LinearProgress,
  Alert,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  useTheme,
  CircularProgress,
  Button,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
} from '@mui/material';
import {
  Refresh as RefreshIcon,
  CheckCircle as HealthyIcon,
  Warning as DegradedIcon,
  Error as ErrorIcon,
  Email as EmailIcon,
  BugReport as TicketIcon,
  Schedule as ScheduleIcon,
  Speed as SpeedIcon,
  CloudOff as OfflineIcon,
  Sync as SyncIcon,
  TrendingUp as TrendingUpIcon,
  TrendingDown as TrendingDownIcon,
} from '@mui/icons-material';
import {
  LineChart,
  Line,
  BarChart,
  Bar,
  PieChart,
  Pie,
  Cell,
  XAxis,
  YAxis,
  CartesianGrid,
  Tooltip as ChartTooltip,
  Legend,
  ResponsiveContainer,
} from 'recharts';
import { format, subHours } from 'date-fns';
import axios from 'axios';

interface IntegrationStatus {
  name: string;
  provider: string;
  status: 'Healthy' | 'Degraded' | 'Unhealthy';
  responseTime: number;
  lastChecked: string;
  configuration: {
    provider: string;
    gracefulDegradation: boolean;
    fallbackProvider?: string;
  };
  metrics?: {
    successRate: number;
    totalRequests: number;
    failedRequests: number;
    averageResponseTime: number;
  };
}

interface IntegrationMetrics {
  ticketing: {
    totalTicketsCreated: number;
    failedTicketCreations: number;
    averageResponseTime: number;
    providerDistribution: { provider: string; count: number }[];
  };
  notifications: {
    totalEmailsSent: number;
    failedEmails: number;
    averageDeliveryTime: number;
    queueSize: number;
  };
}

const IntegrationsMonitoringDashboard: React.FC = () => {
  const theme = useTheme();
  const [loading, setLoading] = useState(true);
  const [refreshing, setRefreshing] = useState(false);
  const [integrations, setIntegrations] = useState<IntegrationStatus[]>([]);
  const [metrics, setMetrics] = useState<IntegrationMetrics | null>(null);
  const [selectedIntegration, setSelectedIntegration] = useState<IntegrationStatus | null>(null);
  const [performanceHistory, setPerformanceHistory] = useState<any[]>([]);
  const [testResults, setTestResults] = useState<any>(null);

  const fetchIntegrationData = async () => {
    try {
      setRefreshing(true);
      
      // Fetch integration health status
      const healthResponse = await axios.get('/api/system/systemhealth/external-integrations');
      
      // Parse health data to extract individual service statuses
      const healthData = healthResponse.data.data || {};
      const integrationStatuses: IntegrationStatus[] = [];
      
      // Ticketing Service Status
      if (healthData.ticketing_status) {
        integrationStatuses.push({
          name: 'Ticketing Service',
          provider: healthData.ticketing_provider || 'Unknown',
          status: healthData.ticketing_status === 'healthy' ? 'Healthy' : 
                 healthData.ticketing_status === 'degraded' ? 'Degraded' : 'Unhealthy',
          responseTime: healthData.ticketing_response_time_ms || 0,
          lastChecked: new Date().toISOString(),
          configuration: {
            provider: healthData.ticketing_provider || 'Unknown',
            gracefulDegradation: healthData.ticketing_graceful_degradation_enabled || false,
            fallbackProvider: healthData.ticketing_fallback_provider,
          },
        });
      }
      
      // Notification Service Status
      if (healthData.notification_status) {
        integrationStatuses.push({
          name: 'Email Notification Service',
          provider: 'SMTP',
          status: healthData.notification_status === 'healthy' ? 'Healthy' : 
                 healthData.notification_status === 'degraded' ? 'Degraded' : 'Unhealthy',
          responseTime: healthData.notification_response_time_ms || 0,
          lastChecked: new Date().toISOString(),
          configuration: {
            provider: 'SMTP',
            gracefulDegradation: true,
          },
        });
      }
      
      setIntegrations(integrationStatuses);
      
      // Simulate metrics data (in real implementation, this would come from API)
      setMetrics({
        ticketing: {
          totalTicketsCreated: 245,
          failedTicketCreations: 12,
          averageResponseTime: 850,
          providerDistribution: [
            { provider: 'JIRA', count: 180 },
            { provider: 'Linear', count: 45 },
            { provider: 'ServiceNow', count: 20 },
          ],
        },
        notifications: {
          totalEmailsSent: 1523,
          failedEmails: 23,
          averageDeliveryTime: 320,
          queueSize: 5,
        },
      });
      
      // Update performance history
      updatePerformanceHistory(integrationStatuses);
      
    } catch (error) {
      console.error('Error fetching integration data:', error);
    } finally {
      setLoading(false);
      setRefreshing(false);
    }
  };

  const updatePerformanceHistory = (statuses: IntegrationStatus[]) => {
    setPerformanceHistory(prev => {
      const newPoint = {
        timestamp: format(new Date(), 'HH:mm'),
        ticketing: statuses.find(s => s.name === 'Ticketing Service')?.responseTime || 0,
        notifications: statuses.find(s => s.name === 'Email Notification Service')?.responseTime || 0,
      };
      return [...prev, newPoint].slice(-20);
    });
  };

  const testIntegration = async (integration: IntegrationStatus) => {
    try {
      setTestResults({ testing: true });
      
      // Simulate test API call
      await new Promise(resolve => setTimeout(resolve, 2000));
      
      setTestResults({
        success: Math.random() > 0.2,
        responseTime: Math.random() * 1000 + 200,
        message: 'Test completed successfully',
        timestamp: new Date().toISOString(),
      });
    } catch (error) {
      setTestResults({
        success: false,
        message: 'Test failed: ' + (error as Error).message,
        timestamp: new Date().toISOString(),
      });
    }
  };

  useEffect(() => {
    fetchIntegrationData();
    const interval = setInterval(fetchIntegrationData, 60000); // Refresh every minute
    return () => clearInterval(interval);
  }, []);

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'Healthy':
        return <HealthyIcon color="success" />;
      case 'Degraded':
        return <DegradedIcon color="warning" />;
      case 'Unhealthy':
        return <ErrorIcon color="error" />;
      default:
        return <OfflineIcon color="disabled" />;
    }
  };

  const getIntegrationIcon = (name: string) => {
    if (name.includes('Ticketing')) return <TicketIcon />;
    if (name.includes('Email')) return <EmailIcon />;
    return <CloudOff />;
  };

  const renderIntegrationCards = () => (
    <Grid container spacing={3}>
      {integrations.map((integration, index) => (
        <Grid item xs={12} md={6} key={index}>
          <Card 
            sx={{ 
              cursor: 'pointer',
              '&:hover': { boxShadow: theme.shadows[4] }
            }}
            onClick={() => setSelectedIntegration(integration)}
          >
            <CardContent>
              <Box display="flex" justifyContent="space-between" alignItems="center">
                <Box display="flex" alignItems="center" gap={1}>
                  {getIntegrationIcon(integration.name)}
                  <Typography variant="h6">{integration.name}</Typography>
                </Box>
                {getStatusIcon(integration.status)}
              </Box>
              
              <Box sx={{ mt: 2 }}>
                <Grid container spacing={2}>
                  <Grid item xs={6}>
                    <Typography variant="body2" color="textSecondary">
                      Provider
                    </Typography>
                    <Typography variant="body1">
                      {integration.provider}
                    </Typography>
                  </Grid>
                  <Grid item xs={6}>
                    <Typography variant="body2" color="textSecondary">
                      Response Time
                    </Typography>
                    <Typography variant="body1">
                      {integration.responseTime.toFixed(0)} ms
                    </Typography>
                  </Grid>
                </Grid>
                
                <Box sx={{ mt: 2 }}>
                  <LinearProgress
                    variant="determinate"
                    value={Math.max(0, Math.min(100, 100 - (integration.responseTime / 50)))}
                    color={
                      integration.status === 'Healthy' ? 'success' :
                      integration.status === 'Degraded' ? 'warning' : 'error'
                    }
                  />
                </Box>
                
                {integration.configuration.gracefulDegradation && (
                  <Chip
                    size="small"
                    label="Graceful Degradation Enabled"
                    color="info"
                    sx={{ mt: 1 }}
                  />
                )}
              </Box>
            </CardContent>
          </Card>
        </Grid>
      ))}
    </Grid>
  );

  const renderMetricsSection = () => (
    <Grid container spacing={3} sx={{ mt: 2 }}>
      {/* Ticketing Metrics */}
      <Grid item xs={12} md={6}>
        <Paper sx={{ p: 2 }}>
          <Typography variant="h6" gutterBottom>
            Ticketing Service Metrics (24h)
          </Typography>
          {metrics && (
            <Grid container spacing={2}>
              <Grid item xs={6}>
                <Box textAlign="center">
                  <Typography variant="h4" color="primary">
                    {metrics.ticketing.totalTicketsCreated}
                  </Typography>
                  <Typography variant="body2" color="textSecondary">
                    Tickets Created
                  </Typography>
                </Box>
              </Grid>
              <Grid item xs={6}>
                <Box textAlign="center">
                  <Typography variant="h4" color={
                    metrics.ticketing.failedTicketCreations > 10 ? 'error' : 'textPrimary'
                  }>
                    {((metrics.ticketing.totalTicketsCreated - metrics.ticketing.failedTicketCreations) / 
                      metrics.ticketing.totalTicketsCreated * 100).toFixed(1)}%
                  </Typography>
                  <Typography variant="body2" color="textSecondary">
                    Success Rate
                  </Typography>
                </Box>
              </Grid>
            </Grid>
          )}
          
          {metrics && (
            <Box sx={{ mt: 3 }}>
              <Typography variant="subtitle2" gutterBottom>
                Provider Distribution
              </Typography>
              <ResponsiveContainer width="100%" height={200}>
                <PieChart>
                  <Pie
                    data={metrics.ticketing.providerDistribution}
                    cx="50%"
                    cy="50%"
                    labelLine={false}
                    label={(entry) => `${entry.provider}: ${entry.count}`}
                    outerRadius={80}
                    fill="#8884d8"
                    dataKey="count"
                  >
                    {metrics.ticketing.providerDistribution.map((entry, index) => (
                      <Cell key={`cell-${index}`} fill={theme.palette.primary.main} />
                    ))}
                  </Pie>
                  <ChartTooltip />
                </PieChart>
              </ResponsiveContainer>
            </Box>
          )}
        </Paper>
      </Grid>

      {/* Email Metrics */}
      <Grid item xs={12} md={6}>
        <Paper sx={{ p: 2 }}>
          <Typography variant="h6" gutterBottom>
            Email Service Metrics (24h)
          </Typography>
          {metrics && (
            <Grid container spacing={2}>
              <Grid item xs={6}>
                <Box textAlign="center">
                  <Typography variant="h4" color="primary">
                    {metrics.notifications.totalEmailsSent}
                  </Typography>
                  <Typography variant="body2" color="textSecondary">
                    Emails Sent
                  </Typography>
                </Box>
              </Grid>
              <Grid item xs={6}>
                <Box textAlign="center">
                  <Typography variant="h4" color={
                    metrics.notifications.queueSize > 50 ? 'warning' : 'textPrimary'
                  }>
                    {metrics.notifications.queueSize}
                  </Typography>
                  <Typography variant="body2" color="textSecondary">
                    Queue Size
                  </Typography>
                </Box>
              </Grid>
              <Grid item xs={12}>
                <Box sx={{ mt: 2 }}>
                  <Typography variant="body2" color="textSecondary">
                    Average Delivery Time
                  </Typography>
                  <Box display="flex" alignItems="center" gap={1}>
                    <Typography variant="h5">
                      {metrics.notifications.averageDeliveryTime} ms
                    </Typography>
                    <SpeedIcon color="action" />
                  </Box>
                </Box>
              </Grid>
            </Grid>
          )}
        </Paper>
      </Grid>

      {/* Performance History */}
      <Grid item xs={12}>
        <Paper sx={{ p: 2 }}>
          <Typography variant="h6" gutterBottom>
            Response Time History
          </Typography>
          <ResponsiveContainer width="100%" height={300}>
            <LineChart data={performanceHistory}>
              <CartesianGrid strokeDasharray="3 3" />
              <XAxis dataKey="timestamp" />
              <YAxis />
              <ChartTooltip />
              <Legend />
              <Line 
                type="monotone" 
                dataKey="ticketing" 
                stroke={theme.palette.primary.main} 
                name="Ticketing Service"
              />
              <Line 
                type="monotone" 
                dataKey="notifications" 
                stroke={theme.palette.secondary.main} 
                name="Email Service"
              />
            </LineChart>
          </ResponsiveContainer>
        </Paper>
      </Grid>
    </Grid>
  );

  const renderIntegrationDetailsDialog = () => (
    <Dialog
      open={!!selectedIntegration}
      onClose={() => {
        setSelectedIntegration(null);
        setTestResults(null);
      }}
      maxWidth="md"
      fullWidth
    >
      <DialogTitle>
        {selectedIntegration?.name} Details
      </DialogTitle>
      <DialogContent>
        <Grid container spacing={2}>
          <Grid item xs={6}>
            <Typography variant="body2" color="textSecondary">Provider</Typography>
            <Typography variant="body1">{selectedIntegration?.provider}</Typography>
          </Grid>
          <Grid item xs={6}>
            <Typography variant="body2" color="textSecondary">Status</Typography>
            <Chip
              icon={getStatusIcon(selectedIntegration?.status || '')}
              label={selectedIntegration?.status}
              color={
                selectedIntegration?.status === 'Healthy' ? 'success' :
                selectedIntegration?.status === 'Degraded' ? 'warning' : 'error'
              }
            />
          </Grid>
          <Grid item xs={6}>
            <Typography variant="body2" color="textSecondary">Response Time</Typography>
            <Typography variant="body1">{selectedIntegration?.responseTime.toFixed(0)} ms</Typography>
          </Grid>
          <Grid item xs={6}>
            <Typography variant="body2" color="textSecondary">Last Checked</Typography>
            <Typography variant="body1">
              {selectedIntegration && format(new Date(selectedIntegration.lastChecked), 'PPpp')}
            </Typography>
          </Grid>
          
          {selectedIntegration?.configuration.gracefulDegradation && (
            <>
              <Grid item xs={6}>
                <Typography variant="body2" color="textSecondary">Graceful Degradation</Typography>
                <Chip label="Enabled" color="success" size="small" />
              </Grid>
              {selectedIntegration.configuration.fallbackProvider && (
                <Grid item xs={6}>
                  <Typography variant="body2" color="textSecondary">Fallback Provider</Typography>
                  <Typography variant="body1">{selectedIntegration.configuration.fallbackProvider}</Typography>
                </Grid>
              )}
            </>
          )}
        </Grid>

        {testResults && (
          <Box sx={{ mt: 3 }}>
            <Alert severity={testResults.success ? 'success' : 'error'}>
              {testResults.testing ? (
                <Box display="flex" alignItems="center" gap={1}>
                  <CircularProgress size={20} />
                  Testing integration...
                </Box>
              ) : (
                <>
                  <Typography variant="subtitle2">
                    Test {testResults.success ? 'Successful' : 'Failed'}
                  </Typography>
                  <Typography variant="body2">
                    {testResults.message}
                  </Typography>
                  {testResults.responseTime && (
                    <Typography variant="body2">
                      Response time: {testResults.responseTime.toFixed(0)} ms
                    </Typography>
                  )}
                </>
              )}
            </Alert>
          </Box>
        )}
      </DialogContent>
      <DialogActions>
        <Button 
          onClick={() => testIntegration(selectedIntegration!)}
          disabled={testResults?.testing}
        >
          Test Connection
        </Button>
        <Button onClick={() => {
          setSelectedIntegration(null);
          setTestResults(null);
        }}>
          Close
        </Button>
      </DialogActions>
    </Dialog>
  );

  if (loading) {
    return (
      <Box display="flex" justifyContent="center" alignItems="center" minHeight={400}>
        <CircularProgress />
      </Box>
    );
  }

  return (
    <Box>
      <Box sx={{ mb: 3, display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
        <Typography variant="h4">External Integrations Monitoring</Typography>
        <Tooltip title="Refresh">
          <IconButton onClick={fetchIntegrationData} disabled={refreshing}>
            <RefreshIcon />
          </IconButton>
        </Tooltip>
      </Box>

      {integrations.some(i => i.status === 'Unhealthy') && (
        <Alert severity="error" sx={{ mb: 2 }}>
          One or more external integrations are experiencing issues. Graceful degradation may be active.
        </Alert>
      )}

      {renderIntegrationCards()}
      {renderMetricsSection()}
      {renderIntegrationDetailsDialog()}
    </Box>
  );
};

export default IntegrationsMonitoringDashboard;