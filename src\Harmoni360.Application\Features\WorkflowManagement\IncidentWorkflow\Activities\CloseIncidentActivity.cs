using Elsa.Workflows;
using Elsa.Workflows.Attributes;
using Harmoni360.Application.Common.Interfaces;
using Harmoni360.Application.Features.WorkflowManagement.Common;
using Harmoni360.Application.Features.WorkflowManagement.IncidentWorkflow.Models;
using Harmoni360.Domain.Entities;
using Harmoni360.Domain.Interfaces;
using Microsoft.Extensions.Logging;
using System.ComponentModel;
using Elsa.Extensions;
using Elsa.Workflows.Models;

namespace Harmoni360.Application.Features.WorkflowManagement.IncidentWorkflow.Activities;

/// <summary>
/// Activity that closes the incident and sends final report to stakeholders
/// </summary>
[Activity("Incident Management", "Close Incident", "Closes the incident and sends final report to all stakeholders")]
public class CloseIncidentActivity : IncidentActivityBase
{
    private readonly INotificationService _notificationService;
    
    public CloseIncidentActivity(
        ILogger<CloseIncidentActivity> logger,
        IIncidentRepository incidentRepository,
        ICurrentUserService currentUserService,
        INotificationService notificationService)
        : base(logger, incidentRepository, currentUserService)
    {
        _notificationService = notificationService;
    }
    
    /// <summary>
    /// The workflow context containing incident information
    /// </summary>
    [Input(
        Description = "The incident workflow context",
        DisplayName = "Incident Context"
    )]
    public Input<IncidentWorkflowContext> Context { get; set; } = default!;
    
    protected override async ValueTask ExecuteAsync(ActivityExecutionContext context)
    {
        const string activityName = nameof(CloseIncidentActivity);
        LogActivity(activityName, "Starting incident closure");
        
        try
        {
            var workflowContext = Context.Get(context);
            
            // Parse incident ID
            if (!int.TryParse(workflowContext.IncidentId, out var incidentId))
            {
                LogActivityError(activityName, "Invalid incident ID: {IncidentId}", workflowContext.IncidentId);
                return;
            }
            
            // Get incident from database
            var incident = await IncidentRepository.GetByIdAsync(incidentId);
            if (incident == null)
            {
                LogActivityError(activityName, "Incident not found: {IncidentId}", incidentId);
                return;
            }
            
            // Update incident status to closed
            incident.UpdateStatus(IncidentStatus.Closed, "Incident closed by workflow - all investigation and corrective actions completed");
            
            // Set closure date
            var closureDate = DateTime.UtcNow;
            
            // Save changes
            await IncidentRepository.UpdateAsync(incident);
            
            // Generate final incident report
            var finalReport = GenerateFinalReport(incident, workflowContext, closureDate);
            
            // Send final report to all stakeholders
            await SendFinalReportToStakeholders(incident, finalReport);
            
            // Update workflow context
            workflowContext.Status = "Closed";
            workflowContext.ClosedAt = closureDate;
            workflowContext.LastUpdated = closureDate;
            
            LogActivity(activityName, "Successfully closed incident {IncidentId} and sent final report", incidentId);
        }
        catch (Exception ex)
        {
            LogActivityError(activityName, ex, "Failed to close incident");
        }
    }
    
    private string GenerateFinalReport(Incident incident, IncidentWorkflowContext context, DateTime closureDate)
    {
        return $@"
HSE INCIDENT FINAL REPORT
========================

INCIDENT INFORMATION:
- Incident ID: {incident.Id}
- Incident Number: {incident.IncidentNumber ?? "N/A"}
- Type: {incident.Type}
- Severity: {incident.Severity}
- Status: {incident.Status}
- Location: {incident.Location}

TIMELINE:
- Reported: {incident.IncidentDate:yyyy-MM-dd HH:mm}
- Investigation Scheduled: {context.InvestigationScheduledDate?.ToString("yyyy-MM-dd HH:mm") ?? "N/A"}
- Closed: {closureDate:yyyy-MM-dd HH:mm}
- Total Resolution Time: {(closureDate - incident.IncidentDate).TotalHours:F1} hours

INVESTIGATION SUMMARY:
The incident has been thoroughly investigated following HSE standards using 
HFACS (Human Factor Analysis Classification System) or ICAM (Incident Causative Analysis Method).

ROOT CAUSE ANALYSIS:
- Primary causes identified and documented
- Contributing factors analyzed
- Organizational factors assessed

CONTROL MEASURES IMPLEMENTED:
- Elimination measures: Applied where possible
- Substitution measures: Implemented as required
- Engineering controls: Installed and verified
- Administrative controls: Updated procedures and training
- Personal protective equipment: Enhanced requirements

VERIFICATION AND CLOSURE:
- All corrective actions have been completed and verified
- Control measures effectiveness has been validated
- Documentation has been updated
- Lessons learned have been captured

STAKEHOLDER ACKNOWLEDGMENT:
This incident is now formally closed. All investigation findings, corrective actions,
and preventive measures have been completed satisfactorily.

Report Generated: {closureDate:yyyy-MM-dd HH:mm:ss} UTC
Generated By: HSE Incident Management Workflow System
";
    }
    
    private async Task SendFinalReportToStakeholders(Incident incident, string finalReport)
    {
        var title = $"HSE Incident Closed - Final Report: {incident.Title}";
        
        // Send to management
        await _notificationService.NotifyRoleAsync(
            "HSEManager",
            title,
            finalReport,
            cancellationToken: default);
        
        await _notificationService.NotifyRoleAsync(
            "SuperAdmin",
            title,
            finalReport,
            cancellationToken: default);
        
        // Send to department heads if available
        await _notificationService.NotifyRoleAsync(
            "DepartmentHead",
            title,
            finalReport,
            cancellationToken: default);
        
        // If there's a specific reporter, notify them as well
        if (!string.IsNullOrEmpty(incident.ReporterEmail))
        {
            try
            {
                await _notificationService.NotifyUserAsync(
                    incident.ReporterEmail,
                    title,
                    $"Thank you for reporting this incident. Please find the final report below:\n\n{finalReport}",
                    cancellationToken: default);
            }
            catch (Exception ex)
            {
                Logger.LogWarning(ex, "Could not notify original reporter {ReporterEmail} about incident closure", 
                    incident.ReporterEmail);
            }
        }
    }
}