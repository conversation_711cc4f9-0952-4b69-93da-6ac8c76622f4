using Elsa.Workflows;
using Elsa.Workflows.Attributes;
using Harmoni360.Application.Common.Interfaces;
using Harmoni360.Application.Features.WorkflowManagement.IncidentWorkflow.Models;
using Harmoni360.Domain.Entities;
using Harmoni360.Domain.Interfaces;
using Microsoft.Extensions.Logging;
using System.ComponentModel;
using Elsa.Extensions;
using Elsa.Workflows.Models;

namespace Harmoni360.Application.Features.WorkflowManagement.IncidentWorkflow.Activities;

/// <summary>
/// Activity that receives and processes an incident report to start the workflow
/// </summary>
[Activity("Incident Management", "Receive Incident Report", "Receives and validates an incident report to start the workflow")]
public class ReceiveIncidentReportActivity : IncidentActivityBase<IncidentWorkflowContext>
{
    
    /// <summary>
    /// The incident report data from the trigger
    /// </summary>
    [Input(
        Description = "The incident report data submitted by the user",
        DisplayName = "Incident Report"
    )]
    public Input<IncidentReportModel> IncidentReport { get; set; } = default!;
    
    protected override async ValueTask ExecuteAsync(ActivityExecutionContext context)
    {
        const string activityName = nameof(ReceiveIncidentReportActivity);
        LogActivity(context, activityName, "Starting incident report processing");
        
        try
        {
            var report = IncidentReport.Get(context);
            
            // Validate the incident report
            var validationResult = await ValidateIncidentReport(report);
            if (!validationResult.IsValid)
            {
                LogActivity(context, activityName, "Incident report validation failed: {Errors}", 
                    string.Join(", ", validationResult.Errors));
                
                // Set context to fault state with validation errors
                context.SetProperty("ValidationErrors", validationResult.Errors);
                Result.Set(context, new IncidentWorkflowContext());
                return;
            }
            
            // Create incident in database
            var incident = await CreateIncidentFromReport(context, report);
            
            // Create workflow context
            var workflowContext = new IncidentWorkflowContext
            {
                IncidentId = incident.Id.ToString(),
                IncidentType = report.Type,
                Severity = DetermineSeverity(report),
                Status = "Reported",
                ReportedBy = report.ReporterId,
                ReportedAt = DateTime.UtcNow,
                Description = report.Description,
                Location = report.Location,
                Attachments = report.Attachments
            };
            
            LogActivity(context, activityName, "Incident report processed successfully. Incident ID: {IncidentId}", 
                workflowContext.IncidentId);
            
            // Set the output
            Result.Set(context, workflowContext);
        }
        catch (Exception ex)
        {
            LogActivityError(context, activityName, ex, "Failed to process incident report");
            
            // Return empty context on error
            Result.Set(context, new IncidentWorkflowContext());
        }
    }
    
    private async Task<ValidationResult> ValidateIncidentReport(IncidentReportModel report)
    {
        var errors = new List<string>();
        
        if (string.IsNullOrWhiteSpace(report.ReporterId))
            errors.Add("Reporter ID is required");
            
        if (string.IsNullOrWhiteSpace(report.Description))
            errors.Add("Incident description is required");
            
        if (string.IsNullOrWhiteSpace(report.Location))
            errors.Add("Incident location is required");
            
        if (string.IsNullOrWhiteSpace(report.Type))
            errors.Add("Incident type is required");
        else if (!IsValidIncidentType(report.Type))
            errors.Add("Invalid incident type");
        
        // Additional validation can be added here
        
        return new ValidationResult 
        { 
            IsValid = !errors.Any(), 
            Errors = errors 
        };
    }
    
    private static bool IsValidIncidentType(string type)
    {
        var validTypes = new[] { "Accident", "NearMiss" };
        return validTypes.Contains(type, StringComparer.OrdinalIgnoreCase);
    }
    
    private static string DetermineSeverity(IncidentReportModel report)
    {
        // Basic severity determination logic
        // This could be enhanced with more sophisticated rules
        if (!string.IsNullOrEmpty(report.PerceivedSeverity))
        {
            return report.PerceivedSeverity;
        }
        
        // Default to Minor for NearMiss, Medium for Accident
        return report.Type.Equals("NearMiss", StringComparison.OrdinalIgnoreCase) 
            ? "Minor" 
            : "Major";
    }
    
    private async Task<Incident> CreateIncidentFromReport(ActivityExecutionContext context, IncidentReportModel report)
    {
        var incident = Incident.Create(
            title: $"{report.Type} at {report.Location}",
            description: report.Description,
            type: MapIncidentType(report.Type),
            severity: MapIncidentSeverity(DetermineSeverity(report)),
            incidentDate: report.IncidentDateTime,
            location: report.Location,
            reporterName: report.ReporterId, // In real implementation, get actual name
            reporterEmail: report.ReporterId, // Assuming ReporterId is email for now
            reporterDepartment: "Unknown" // In real implementation, get from user service
        );
        
        // Save to database
        var incidentRepository = GetIncidentRepository(context);
        await incidentRepository.AddAsync(incident);
        
        return incident;
    }
    
    private static Domain.Entities.IncidentType MapIncidentType(string type)
    {
        return type.ToLowerInvariant() switch
        {
            "accident" => Domain.Entities.IncidentType.Accident,
            "nearmiss" => Domain.Entities.IncidentType.NearMiss,
            _ => Domain.Entities.IncidentType.Accident
        };
    }
    
    private static Domain.Entities.IncidentSeverity MapIncidentSeverity(string severity)
    {
        return severity.ToLowerInvariant() switch
        {
            "minor" => Domain.Entities.IncidentSeverity.Minor,
            "major" => Domain.Entities.IncidentSeverity.Major,
            "fatality" => Domain.Entities.IncidentSeverity.Fatal,
            _ => Domain.Entities.IncidentSeverity.Major
        };
    }
}

/// <summary>
/// Represents the result of incident report validation
/// </summary>
public class ValidationResult
{
    public bool IsValid { get; set; }
    public List<string> Errors { get; set; } = new();
}