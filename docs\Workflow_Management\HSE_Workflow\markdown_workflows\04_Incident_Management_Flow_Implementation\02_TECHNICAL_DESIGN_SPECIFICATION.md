# Incident Management Workflow - Technical Design Specification

## 1. System Architecture

### 1.1 High-Level Architecture

```mermaid
graph TB
    subgraph "Presentation Layer"
        A[Elsa Studio UI]
        B[Harmoni360 Web Portal]
        C[Mobile App]
    end
    
    subgraph "API Gateway"
        D[API Gateway/Load Balancer]
    end
    
    subgraph "Application Layer"
        E[Elsa Workflow Engine]
        F[Incident Management Service]
        G[Notification Service]
        H[Integration Service]
    end
    
    subgraph "Data Layer"
        I[(SQL Server - Workflows)]
        J[(SQL Server - Incidents)]
        K[(Redis Cache)]
    end
    
    subgraph "External Systems"
        L[Email Server]
        M[Calendar System]
        N[Ticketing System]
        O[Harmoni360 Auth]
    end
    
    A --> D
    B --> D
    C --> D
    D --> E
    D --> F
    E --> F
    E --> G
    E --> H
    F --> J
    E --> I
    E --> K
    G --> L
    H --> M
    H --> N
    D --> O
```

### 1.2 Component Architecture

```yaml
Components:
  WorkflowEngine:
    - Elsa.Core
    - Elsa.Persistence.SqlServer
    - Elsa.Caching.Redis
    - Custom Activities Library
    
  IncidentService:
    - Incident API
    - Business Logic Layer
    - Data Access Layer
    - Domain Models
    
  NotificationService:
    - Email Provider
    - SMS Provider (future)
    - Push Notifications (future)
    - Template Engine
    
  IntegrationService:
    - Calendar Adapter
    - Ticketing Adapter
    - Document Storage Adapter
    - External API Clients
```

## 2. Workflow Design

### 2.1 Workflow Definition

```csharp
public class IncidentManagementWorkflow : IWorkflow
{
    public void Build(IWorkflowBuilder builder)
    {
        builder
            .WithDisplayName("Incident Management Workflow")
            .WithDescription("Manages HSE incidents from reporting to closure")
            .WithVersion(1)
            .Variable<IncidentData>("IncidentData")
            .Variable<string>("IncidentNumber")
            .Variable<string>("Status")
            .Variable<List<string>>("InvestigationTeam")
            .Variable<List<CorrectiveAction>>("CorrectiveActions")
            .StartWith<ReceiveIncidentReport>()
                .WithName("ReceiveReport")
                .Output(data => data.IncidentData, step => step.Output)
            .Then<GenerateIncidentNumber>()
                .WithName("GenerateNumber")
                .Input(step => step.IncidentType, data => data.IncidentData.Type)
                .Output(data => data.IncidentNumber, step => step.Number)
            .Then<ClassifyIncident>()
                .WithName("ClassifySeverity")
                .Input(step => step.Incident, data => data.IncidentData)
                .Output(data => data.IncidentData.Severity, step => step.Severity)
            .If(data => data.IncidentData.Severity != "Minor")
                .Do(then => then
                    .Parallel(branches => branches
                        .Add(branch => branch
                            .SendEmail("NotifyManagement")
                                .WithTemplate("IncidentAlert")
                                .WithRecipients(data => GetManagementEmails(data))
                        )
                        .Add(branch => branch
                            .Then<CreateCalendarEvent>()
                                .WithName("ScheduleInvestigation")
                        )
                    )
                    .Then<AssignInvestigator>()
                        .WithName("AssignTeam")
                        .Output(data => data.InvestigationTeam, step => step.Team)
                    .Then<ConductInvestigation>()
                        .WithName("Investigation")
                        .Then<DetermineCorrectiveActions>()
                            .WithName("DefineActions")
                            .Output(data => data.CorrectiveActions, step => step.Actions)
                    .ForEach(data => data.CorrectiveActions)
                        .Do(action => action
                            .Then<AssignCorrectiveAction>()
                                .WithName("AssignAction")
                            .Then<MonitorActionProgress>()
                                .WithName("MonitorProgress")
                        )
                    .Then<VerifyCompletion>()
                        .WithName("VerifyActions")
                )
            .Then<CloseIncident>()
                .WithName("CloseCase")
            .Then<GenerateFinalReport>()
                .WithName("FinalReport");
    }
}
```

### 2.2 Activity Definitions

#### 2.2.1 Receive Incident Report Activity

```csharp
public class ReceiveIncidentReport : Activity<IncidentData>
{
    [ActivityInput(Hint = "The incident report data")]
    public IncidentReportModel Input { get; set; }
    
    [ActivityOutput]
    public IncidentData Output { get; set; }
    
    protected override async ValueTask<IActivityExecutionResult> OnExecuteAsync(
        ActivityExecutionContext context)
    {
        // Validate input
        var validationResult = await ValidateReport(Input);
        if (!validationResult.IsValid)
        {
            return Fault(validationResult.Errors);
        }
        
        // Create incident data
        Output = new IncidentData
        {
            ReportedBy = Input.ReporterId,
            ReportedAt = DateTime.UtcNow,
            Description = Input.Description,
            Location = Input.Location,
            Type = DetermineIncidentType(Input),
            InitialSeverity = Input.PerceivedSeverity,
            Attachments = Input.Attachments
        };
        
        // Store initial report
        await _incidentService.CreateInitialReport(Output);
        
        return Done(Output);
    }
}
```

#### 2.2.2 Generate Incident Number Activity

```csharp
public class GenerateIncidentNumber : Activity<string>
{
    [ActivityInput]
    public string IncidentType { get; set; }
    
    [ActivityOutput]
    public string Number { get; set; }
    
    protected override IActivityExecutionResult OnExecute(ActivityExecutionContext context)
    {
        var currentDate = DateTime.Now;
        var sequenceNumber = _sequenceService.GetNextNumber(IncidentType, currentDate);
        
        Number = IncidentType switch
        {
            "Accident" => $"{sequenceNumber:000}/HSE-Accident/{currentDate:MM}/{currentDate:yyyy}",
            "NearMiss" => $"{sequenceNumber:000}/HSE-Near Miss/{currentDate:MM}/{currentDate:yyyy}",
            _ => $"{sequenceNumber:000}/HSE-Unknown/{currentDate:MM}/{currentDate:yyyy}"
        };
        
        return Done(Number);
    }
}
```

#### 2.2.3 Investigation Activity

```csharp
public class ConductInvestigation : Activity
{
    [ActivityInput]
    public string IncidentId { get; set; }
    
    [ActivityInput]
    public List<string> InvestigationTeam { get; set; }
    
    [ActivityInput]
    public string AnalysisMethod { get; set; } // HFACS or ICAM
    
    protected override async ValueTask<IActivityExecutionResult> OnExecuteAsync(
        ActivityExecutionContext context)
    {
        // Create investigation task
        var investigationId = await _investigationService.CreateInvestigation(
            IncidentId, 
            InvestigationTeam, 
            AnalysisMethod);
        
        // Signal for external completion
        return Suspend();
    }
    
    protected override async ValueTask<IActivityExecutionResult> OnResumeAsync(
        ActivityExecutionContext context)
    {
        var investigationResult = context.GetInput<InvestigationResult>();
        
        // Validate investigation completeness
        if (!investigationResult.IsComplete)
        {
            return Suspend();
        }
        
        // Store investigation results
        await _investigationService.CompleteInvestigation(
            investigationResult.Id, 
            investigationResult);
        
        return Done();
    }
}
```

### 2.3 Workflow Variables

```csharp
public class IncidentData
{
    public string Id { get; set; }
    public string Number { get; set; }
    public string Type { get; set; } // Accident, NearMiss
    public string Severity { get; set; } // Major, Minor, Fatality
    public string Status { get; set; }
    public DateTime ReportedAt { get; set; }
    public string ReportedBy { get; set; }
    public string Description { get; set; }
    public string Location { get; set; }
    public List<string> AffectedPersonnel { get; set; }
    public List<Attachment> Attachments { get; set; }
    public InvestigationData Investigation { get; set; }
    public List<CorrectiveAction> CorrectiveActions { get; set; }
}

public class InvestigationData
{
    public string Id { get; set; }
    public List<string> TeamMembers { get; set; }
    public string LeadInvestigator { get; set; }
    public string AnalysisMethod { get; set; }
    public DateTime StartedAt { get; set; }
    public DateTime? CompletedAt { get; set; }
    public List<Finding> Findings { get; set; }
    public List<RootCause> RootCauses { get; set; }
    public string Recommendations { get; set; }
}

public class CorrectiveAction
{
    public string Id { get; set; }
    public string Description { get; set; }
    public string AssignedTo { get; set; }
    public string AssignedDepartment { get; set; }
    public DateTime DueDate { get; set; }
    public string Status { get; set; }
    public DateTime? CompletedAt { get; set; }
    public string CompletionEvidence { get; set; }
    public string VerifiedBy { get; set; }
}
```

## 3. Integration Specifications

### 3.1 Authentication Integration

```csharp
public class Harmoni360AuthenticationHandler : AuthenticationHandler<Harmoni360AuthOptions>
{
    protected override async Task<AuthenticateResult> HandleAuthenticateAsync()
    {
        // Extract JWT token
        var token = Request.Headers["Authorization"]
            .FirstOrDefault()?.Split(" ").Last();
            
        if (string.IsNullOrEmpty(token))
        {
            return AuthenticateResult.Fail("No token provided");
        }
        
        // Validate with Harmoni360
        var validationResult = await _authService.ValidateToken(token);
        if (!validationResult.IsValid)
        {
            return AuthenticateResult.Fail("Invalid token");
        }
        
        // Create claims principal
        var claims = new[]
        {
            new Claim(ClaimTypes.NameIdentifier, validationResult.UserId),
            new Claim(ClaimTypes.Name, validationResult.Username),
            new Claim(ClaimTypes.Email, validationResult.Email),
            new Claim("Department", validationResult.Department),
        }.Concat(validationResult.Roles.Select(r => new Claim(ClaimTypes.Role, r)));
        
        var identity = new ClaimsIdentity(claims, Scheme.Name);
        var principal = new ClaimsPrincipal(identity);
        var ticket = new AuthenticationTicket(principal, Scheme.Name);
        
        return AuthenticateResult.Success(ticket);
    }
}
```

### 3.2 Email Integration

```csharp
public interface IEmailService
{
    Task SendEmailAsync(EmailMessage message);
    Task SendBulkEmailAsync(List<EmailMessage> messages);
}

public class EmailMessage
{
    public List<string> To { get; set; }
    public List<string> Cc { get; set; }
    public List<string> Bcc { get; set; }
    public string Subject { get; set; }
    public string Body { get; set; }
    public bool IsHtml { get; set; }
    public List<EmailAttachment> Attachments { get; set; }
    public Dictionary<string, string> Headers { get; set; }
}

public class SmtpEmailService : IEmailService
{
    private readonly SmtpSettings _settings;
    private readonly ITemplateEngine _templateEngine;
    
    public async Task SendEmailAsync(EmailMessage message)
    {
        using var client = new SmtpClient(_settings.Host, _settings.Port)
        {
            EnableSsl = _settings.EnableSsl,
            Credentials = new NetworkCredential(_settings.Username, _settings.Password)
        };
        
        var mailMessage = BuildMailMessage(message);
        await client.SendMailAsync(mailMessage);
    }
}
```

### 3.3 Calendar Integration

```csharp
public interface ICalendarService
{
    Task<string> CreateEventAsync(CalendarEvent calendarEvent);
    Task UpdateEventAsync(string eventId, CalendarEvent calendarEvent);
    Task DeleteEventAsync(string eventId);
    Task<List<CalendarEvent>> GetEventsAsync(DateTime start, DateTime end);
}

public class CalendarEvent
{
    public string Title { get; set; }
    public string Description { get; set; }
    public DateTime Start { get; set; }
    public DateTime End { get; set; }
    public List<string> Attendees { get; set; }
    public string Location { get; set; }
    public bool SendInvitations { get; set; }
    public Dictionary<string, string> Metadata { get; set; }
}
```

### 3.4 Ticketing System Integration

```csharp
public interface ITicketingService
{
    Task<string> CreateTicketAsync(Ticket ticket);
    Task UpdateTicketAsync(string ticketId, TicketUpdate update);
    Task<Ticket> GetTicketAsync(string ticketId);
    Task AddCommentAsync(string ticketId, Comment comment);
    Task AttachFileAsync(string ticketId, FileAttachment file);
}

public class Ticket
{
    public string Title { get; set; }
    public string Description { get; set; }
    public string Priority { get; set; }
    public string Category { get; set; }
    public string AssignedTo { get; set; }
    public Dictionary<string, object> CustomFields { get; set; }
}
```

## 4. Security Specifications

### 4.1 Authorization Policies

```csharp
public class WorkflowAuthorizationPolicies
{
    public const string CanReportIncident = "CanReportIncident";
    public const string CanInvestigateIncident = "CanInvestigateIncident";
    public const string CanApproveActions = "CanApproveActions";
    public const string CanViewReports = "CanViewReports";
    
    public static void Configure(AuthorizationOptions options)
    {
        options.AddPolicy(CanReportIncident, policy =>
            policy.RequireAuthenticatedUser());
            
        options.AddPolicy(CanInvestigateIncident, policy =>
            policy.RequireRole("HSE_Officer", "HSE_Manager", "Investigator"));
            
        options.AddPolicy(CanApproveActions, policy =>
            policy.RequireRole("HSE_Manager", "Department_Head"));
            
        options.AddPolicy(CanViewReports, policy =>
            policy.RequireRole("HSE_Officer", "HSE_Manager", "Executive"));
    }
}
```

### 4.2 Data Encryption

```csharp
public class EncryptionService
{
    private readonly byte[] _key;
    private readonly byte[] _iv;
    
    public string Encrypt(string plainText)
    {
        using var aes = Aes.Create();
        aes.Key = _key;
        aes.IV = _iv;
        
        var encryptor = aes.CreateEncryptor(aes.Key, aes.IV);
        using var msEncrypt = new MemoryStream();
        using var csEncrypt = new CryptoStream(msEncrypt, encryptor, CryptoStreamMode.Write);
        using var swEncrypt = new StreamWriter(csEncrypt);
        
        swEncrypt.Write(plainText);
        return Convert.ToBase64String(msEncrypt.ToArray());
    }
    
    public string Decrypt(string cipherText)
    {
        // Decryption implementation
    }
}
```

### 4.3 Audit Logging

```csharp
public class WorkflowAuditLogger
{
    private readonly IAuditRepository _repository;
    
    public async Task LogActivityExecutionAsync(
        string workflowInstanceId,
        string activityId,
        string activityType,
        string userId,
        Dictionary<string, object> data)
    {
        var entry = new AuditEntry
        {
            Id = Guid.NewGuid().ToString(),
            Timestamp = DateTime.UtcNow,
            WorkflowInstanceId = workflowInstanceId,
            ActivityId = activityId,
            ActivityType = activityType,
            UserId = userId,
            Action = "ActivityExecuted",
            Data = JsonSerializer.Serialize(data),
            IpAddress = GetClientIpAddress(),
            UserAgent = GetUserAgent()
        };
        
        await _repository.InsertAsync(entry);
    }
}
```

## 5. Database Schema

### 5.1 Workflow Tables

```sql
-- Elsa Workflow Tables (managed by Elsa)
-- WorkflowDefinitions
-- WorkflowInstances
-- ActivityInstances
-- WorkflowExecutionLogRecords
-- Bookmarks

-- Custom Incident Tables
CREATE TABLE Incidents (
    Id UNIQUEIDENTIFIER PRIMARY KEY DEFAULT NEWID(),
    IncidentNumber NVARCHAR(50) UNIQUE NOT NULL,
    Type NVARCHAR(20) NOT NULL,
    Severity NVARCHAR(20) NOT NULL,
    Status NVARCHAR(50) NOT NULL,
    ReportedAt DATETIME2 NOT NULL,
    ReportedBy NVARCHAR(100) NOT NULL,
    Description NVARCHAR(MAX) NOT NULL,
    Location NVARCHAR(500),
    WorkflowInstanceId NVARCHAR(100),
    CreatedAt DATETIME2 DEFAULT GETUTCDATE(),
    UpdatedAt DATETIME2 DEFAULT GETUTCDATE(),
    INDEX IX_Incidents_Number (IncidentNumber),
    INDEX IX_Incidents_Status (Status),
    INDEX IX_Incidents_ReportedAt (ReportedAt)
);

CREATE TABLE Investigations (
    Id UNIQUEIDENTIFIER PRIMARY KEY DEFAULT NEWID(),
    IncidentId UNIQUEIDENTIFIER NOT NULL,
    LeadInvestigatorId NVARCHAR(100) NOT NULL,
    AnalysisMethod NVARCHAR(20) NOT NULL,
    Status NVARCHAR(50) NOT NULL,
    StartedAt DATETIME2 NOT NULL,
    CompletedAt DATETIME2,
    Findings NVARCHAR(MAX),
    RootCauses NVARCHAR(MAX),
    Recommendations NVARCHAR(MAX),
    FOREIGN KEY (IncidentId) REFERENCES Incidents(Id)
);

CREATE TABLE InvestigationTeamMembers (
    Id UNIQUEIDENTIFIER PRIMARY KEY DEFAULT NEWID(),
    InvestigationId UNIQUEIDENTIFIER NOT NULL,
    UserId NVARCHAR(100) NOT NULL,
    Role NVARCHAR(50),
    AssignedAt DATETIME2 DEFAULT GETUTCDATE(),
    FOREIGN KEY (InvestigationId) REFERENCES Investigations(Id)
);

CREATE TABLE CorrectiveActions (
    Id UNIQUEIDENTIFIER PRIMARY KEY DEFAULT NEWID(),
    IncidentId UNIQUEIDENTIFIER NOT NULL,
    Description NVARCHAR(MAX) NOT NULL,
    AssignedTo NVARCHAR(100) NOT NULL,
    AssignedDepartment NVARCHAR(100),
    DueDate DATETIME2 NOT NULL,
    Status NVARCHAR(50) NOT NULL,
    CompletedAt DATETIME2,
    CompletionEvidence NVARCHAR(MAX),
    VerifiedBy NVARCHAR(100),
    VerifiedAt DATETIME2,
    FOREIGN KEY (IncidentId) REFERENCES Incidents(Id),
    INDEX IX_CorrectiveActions_Status (Status),
    INDEX IX_CorrectiveActions_DueDate (DueDate)
);

CREATE TABLE IncidentAttachments (
    Id UNIQUEIDENTIFIER PRIMARY KEY DEFAULT NEWID(),
    IncidentId UNIQUEIDENTIFIER NOT NULL,
    FileName NVARCHAR(255) NOT NULL,
    FileSize BIGINT NOT NULL,
    ContentType NVARCHAR(100) NOT NULL,
    StoragePath NVARCHAR(500) NOT NULL,
    UploadedBy NVARCHAR(100) NOT NULL,
    UploadedAt DATETIME2 DEFAULT GETUTCDATE(),
    FOREIGN KEY (IncidentId) REFERENCES Incidents(Id)
);

CREATE TABLE WorkflowAuditLog (
    Id UNIQUEIDENTIFIER PRIMARY KEY DEFAULT NEWID(),
    Timestamp DATETIME2 NOT NULL,
    WorkflowInstanceId NVARCHAR(100),
    ActivityId NVARCHAR(100),
    ActivityType NVARCHAR(200),
    UserId NVARCHAR(100),
    Action NVARCHAR(100),
    Data NVARCHAR(MAX),
    IpAddress NVARCHAR(45),
    UserAgent NVARCHAR(500),
    INDEX IX_AuditLog_Timestamp (Timestamp),
    INDEX IX_AuditLog_WorkflowInstance (WorkflowInstanceId),
    INDEX IX_AuditLog_UserId (UserId)
);
```

## 6. API Specifications

### 6.1 Incident Management API

```yaml
openapi: 3.0.0
info:
  title: Incident Management API
  version: 1.0.0
  
paths:
  /api/incidents:
    post:
      summary: Report new incident
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/IncidentReport'
      responses:
        201:
          description: Incident created
          content:
            application/json:
              schema:
                type: object
                properties:
                  incidentId: 
                    type: string
                  incidentNumber:
                    type: string
                  workflowInstanceId:
                    type: string
                    
  /api/incidents/{incidentId}:
    get:
      summary: Get incident details
      parameters:
        - name: incidentId
          in: path
          required: true
          schema:
            type: string
      responses:
        200:
          description: Incident details
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/IncidentDetails'
                
  /api/incidents/{incidentId}/investigate:
    post:
      summary: Complete investigation
      parameters:
        - name: incidentId
          in: path
          required: true
          schema:
            type: string
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/InvestigationResult'
      responses:
        200:
          description: Investigation completed
          
  /api/incidents/{incidentId}/actions/{actionId}/complete:
    post:
      summary: Complete corrective action
      parameters:
        - name: incidentId
          in: path
          required: true
          schema:
            type: string
        - name: actionId
          in: path
          required: true
          schema:
            type: string
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/ActionCompletion'
      responses:
        200:
          description: Action completed
```

### 6.2 Workflow API Extensions

```csharp
[ApiController]
[Route("api/workflow")]
public class WorkflowController : ControllerBase
{
    private readonly IWorkflowLaunchpad _workflowLaunchpad;
    private readonly IWorkflowRegistry _workflowRegistry;
    private readonly IWorkflowInstanceStore _instanceStore;
    
    [HttpPost("incident/start")]
    [Authorize(Policy = WorkflowAuthorizationPolicies.CanReportIncident)]
    public async Task<IActionResult> StartIncidentWorkflow([FromBody] IncidentReport report)
    {
        var input = new
        {
            IncidentReport = report,
            ReporterId = User.FindFirst(ClaimTypes.NameIdentifier)?.Value
        };
        
        var startResult = await _workflowLaunchpad.StartWorkflowAsync(
            "IncidentManagementWorkflow",
            input: input);
        
        return Ok(new
        {
            WorkflowInstanceId = startResult.WorkflowInstance.Id,
            Status = startResult.WorkflowInstance.WorkflowStatus
        });
    }
    
    [HttpGet("incident/{workflowInstanceId}/status")]
    [Authorize(Policy = WorkflowAuthorizationPolicies.CanViewReports)]
    public async Task<IActionResult> GetWorkflowStatus(string workflowInstanceId)
    {
        var instance = await _instanceStore.FindAsync(new WorkflowInstanceIdSpecification(workflowInstanceId));
        
        if (instance == null)
            return NotFound();
            
        return Ok(new
        {
            Status = instance.WorkflowStatus.ToString(),
            CurrentActivity = instance.CurrentActivity?.Type,
            Variables = instance.Variables.ToDictionary(v => v.Key, v => v.Value)
        });
    }
}
```

## 7. Performance Specifications

### 7.1 Performance Requirements

```yaml
Performance Targets:
  Workflow Execution:
    - Workflow trigger latency: < 500ms
    - Activity execution: < 2s (excluding external calls)
    - Concurrent workflows: 1000 active instances
    - Throughput: 100 new incidents/minute
    
  API Response Times:
    - GET requests: < 200ms
    - POST requests: < 500ms
    - Complex queries: < 1s
    
  Database Performance:
    - Query execution: < 100ms
    - Write operations: < 50ms
    - Connection pool size: 100
    
  External Integration:
    - Email sending: < 3s
    - Calendar operations: < 2s
    - Ticket creation: < 5s
```

### 7.2 Caching Strategy

```csharp
public class WorkflowCachingService
{
    private readonly IDistributedCache _cache;
    private readonly CacheOptions _options;
    
    public async Task<T> GetOrCreateAsync<T>(
        string key, 
        Func<Task<T>> factory, 
        TimeSpan? expiration = null)
    {
        var cached = await _cache.GetAsync(key);
        if (cached != null)
        {
            return JsonSerializer.Deserialize<T>(cached);
        }
        
        var value = await factory();
        var serialized = JsonSerializer.SerializeToUtf8Bytes(value);
        
        await _cache.SetAsync(key, serialized, new DistributedCacheEntryOptions
        {
            AbsoluteExpirationRelativeToNow = expiration ?? _options.DefaultExpiration
        });
        
        return value;
    }
}
```

## 8. Monitoring and Logging

### 8.1 Application Insights Integration

```csharp
public class WorkflowTelemetry
{
    private readonly TelemetryClient _telemetryClient;
    
    public void TrackWorkflowStarted(string workflowType, string instanceId)
    {
        _telemetryClient.TrackEvent("WorkflowStarted", new Dictionary<string, string>
        {
            ["WorkflowType"] = workflowType,
            ["InstanceId"] = instanceId,
            ["Timestamp"] = DateTime.UtcNow.ToString("O")
        });
    }
    
    public void TrackActivityExecuted(string activityType, TimeSpan duration, bool success)
    {
        _telemetryClient.TrackDependency("Activity", activityType, 
            DateTime.UtcNow.Subtract(duration), duration, success);
            
        _telemetryClient.TrackMetric($"Activity.{activityType}.Duration", 
            duration.TotalMilliseconds);
    }
}
```

### 8.2 Health Checks

```csharp
public class WorkflowHealthCheck : IHealthCheck
{
    private readonly IWorkflowRegistry _registry;
    private readonly IDbConnection _dbConnection;
    private readonly IConnectionMultiplexer _redis;
    
    public async Task<HealthCheckResult> CheckHealthAsync(
        HealthCheckContext context,
        CancellationToken cancellationToken = default)
    {
        var data = new Dictionary<string, object>();
        
        try
        {
            // Check workflow engine
            var workflowCount = await _registry.ListAsync(cancellationToken);
            data["WorkflowDefinitions"] = workflowCount.Count();
            
            // Check database
            await _dbConnection.ExecuteScalarAsync("SELECT 1");
            data["Database"] = "Connected";
            
            // Check Redis
            var db = _redis.GetDatabase();
            await db.PingAsync();
            data["Redis"] = "Connected";
            
            return HealthCheckResult.Healthy("All systems operational", data);
        }
        catch (Exception ex)
        {
            return HealthCheckResult.Unhealthy("System check failed", ex, data);
        }
    }
}
```

## 9. Error Handling and Recovery

### 9.1 Compensation Activities

```csharp
public class CompensatingTransaction : CompositeActivity
{
    private readonly Stack<IActivity> _compensationStack = new();
    
    protected override async ValueTask<IActivityExecutionResult> OnExecuteAsync(
        ActivityExecutionContext context)
    {
        try
        {
            // Execute main activities
            foreach (var activity in Activities)
            {
                var result = await activity.ExecuteAsync(context);
                if (result.Status == ActivityStatus.Faulted)
                {
                    // Trigger compensation
                    await CompensateAsync(context);
                    return Fault(result.Exception);
                }
                _compensationStack.Push(activity);
            }
            return Done();
        }
        catch (Exception ex)
        {
            await CompensateAsync(context);
            return Fault(ex);
        }
    }
    
    private async Task CompensateAsync(ActivityExecutionContext context)
    {
        while (_compensationStack.Count > 0)
        {
            var activity = _compensationStack.Pop();
            if (activity is ICompensableActivity compensable)
            {
                await compensable.CompensateAsync(context);
            }
        }
    }
}
```

### 9.2 Retry Policies

```csharp
public class RetryActivity : Activity
{
    [ActivityInput]
    public int MaxAttempts { get; set; } = 3;
    
    [ActivityInput]
    public int DelaySeconds { get; set; } = 5;
    
    protected override async ValueTask<IActivityExecutionResult> OnExecuteAsync(
        ActivityExecutionContext context)
    {
        var attemptCount = context.GetVariable<int>("RetryAttempt") ?? 0;
        
        try
        {
            // Execute the actual work
            var result = await ExecuteWorkAsync(context);
            return result;
        }
        catch (Exception ex)
        {
            attemptCount++;
            
            if (attemptCount >= MaxAttempts)
            {
                return Fault($"Failed after {MaxAttempts} attempts: {ex.Message}");
            }
            
            context.SetVariable("RetryAttempt", attemptCount);
            
            // Schedule retry
            return Suspend(TimeSpan.FromSeconds(DelaySeconds * attemptCount));
        }
    }
}
```

## 10. Deployment Configuration

### 10.1 Docker Configuration

```dockerfile
# Dockerfile
FROM mcr.microsoft.com/dotnet/aspnet:8.0 AS base
WORKDIR /app
EXPOSE 80
EXPOSE 443

FROM mcr.microsoft.com/dotnet/sdk:8.0 AS build
WORKDIR /src
COPY ["Harmoni360.Workflow.csproj", "."]
RUN dotnet restore "Harmoni360.Workflow.csproj"
COPY . .
RUN dotnet build "Harmoni360.Workflow.csproj" -c Release -o /app/build

FROM build AS publish
RUN dotnet publish "Harmoni360.Workflow.csproj" -c Release -o /app/publish

FROM base AS final
WORKDIR /app
COPY --from=publish /app/publish .
ENTRYPOINT ["dotnet", "Harmoni360.Workflow.dll"]
```

### 10.2 Kubernetes Deployment

```yaml
apiVersion: apps/v1
kind: Deployment
metadata:
  name: incident-workflow-engine
  namespace: harmoni360
spec:
  replicas: 3
  selector:
    matchLabels:
      app: incident-workflow
  template:
    metadata:
      labels:
        app: incident-workflow
    spec:
      containers:
      - name: workflow-engine
        image: harmoni360/incident-workflow:latest
        ports:
        - containerPort: 80
        env:
        - name: ConnectionStrings__WorkflowDb
          valueFrom:
            secretKeyRef:
              name: db-connection
              key: workflow-db
        - name: Redis__ConnectionString
          valueFrom:
            secretKeyRef:
              name: redis-connection
              key: connection-string
        resources:
          requests:
            memory: "512Mi"
            cpu: "500m"
          limits:
            memory: "1Gi"
            cpu: "1000m"
        livenessProbe:
          httpGet:
            path: /health
            port: 80
          initialDelaySeconds: 30
          periodSeconds: 10
        readinessProbe:
          httpGet:
            path: /health/ready
            port: 80
          initialDelaySeconds: 10
          periodSeconds: 5
```

This technical design specification provides a comprehensive blueprint for implementing the Incident Management workflow using Elsa Studio integrated with Harmoni360 HSSE. The design ensures scalability, security, and maintainability while meeting all functional requirements.