//! Licensed to the .NET Foundation under one or more agreements.
//! The .NET Foundation licenses this file to you under the MIT license.
var e="8.0.17";let t,n;const r="object"==typeof process&&"object"==typeof process.versions&&"string"==typeof process.versions.node,o="function"==typeof importScripts,a="object"==typeof window||o&&!r,s=!a&&!r&&!o;let i,c=null,l=null,u=null,p=!1,d=!0,_=!0,f=!1,m=!1,h=!1;function b(e){i=e.isPThread,p=e.linkerDisableLegacyJsInterop,d=e.linkerWasmEnableSIMD,_=e.linkerWasmEnableEH,f=e.linkerEnableAotProfiler,m=e.linkerEnableBrowserProfiler,l.quit=e.quit_,l.ExitStatus=e.ExitStatus,l.moduleGitHash=e.gitHash}function g(e){if(h)throw new Error("Runtime module already loaded");h=!0,t=e.module,n=e.internal,l=e.runtimeHelpers,u=e.loaderHelpers,c=e.api,Object.assign(l,{gitHash:"77545d6fd5ca79bc08198fd6d8037c14843f14ad",allAssetsInMemory:y(),dotnetReady:y(),afterInstantiateWasm:y(),beforePreInit:y(),afterPreInit:y(),afterPreRun:y(),beforeOnRuntimeInitialized:y(),afterOnRuntimeInitialized:y(),afterPostRun:y(),mono_wasm_exit:()=>{throw new Error("Mono shutdown")},abort:e=>{throw e}}),Object.assign(e.module.config,{}),Object.assign(e.api,{Module:e.module,...e.module}),Object.assign(e.api,{INTERNAL:e.internal})}function y(e,t){return u.createPromiseController(e,t)}function w(e,t){if(e)return;const n="Assert failed: "+("function"==typeof t?t():t),r=new Error(n);l.abort(r)}const S=0,k=0,v=0,E=0,U=0,I=-1,x=0,T=0,j=0;function A(e){return null==e}var R;!function(e){e[e.None=0]="None",e[e.Void=1]="Void",e[e.Discard=2]="Discard",e[e.Boolean=3]="Boolean",e[e.Byte=4]="Byte",e[e.Char=5]="Char",e[e.Int16=6]="Int16",e[e.Int32=7]="Int32",e[e.Int52=8]="Int52",e[e.BigInt64=9]="BigInt64",e[e.Double=10]="Double",e[e.Single=11]="Single",e[e.IntPtr=12]="IntPtr",e[e.JSObject=13]="JSObject",e[e.Object=14]="Object",e[e.String=15]="String",e[e.Exception=16]="Exception",e[e.DateTime=17]="DateTime",e[e.DateTimeOffset=18]="DateTimeOffset",e[e.Nullable=19]="Nullable",e[e.Task=20]="Task",e[e.Array=21]="Array",e[e.ArraySegment=22]="ArraySegment",e[e.Span=23]="Span",e[e.Action=24]="Action",e[e.Function=25]="Function",e[e.JSException=26]="JSException"}(R||(R={}));var L=!1;const $=[],C=32768;let N,D;const B=BigInt("9223372036854775807"),O=BigInt("-9223372036854775808");function M(){N||(N=t._malloc(C),D=N),$.push(D)}function F(e,t,n){if(!Number.isSafeInteger(e))throw new Error(`Assert failed: Value is not an integer: ${e} (${typeof e})`);if(!(e>=t&&e<=n))throw new Error(`Assert failed: Overflow: value ${e} is out of ${t} ${n} range`)}function P(e,t){Ee().fill(0,e,e+t)}function z(e,n){const r=!!n;"number"==typeof n&&F(n,0,1),t.HEAP32[e>>>2]=r?1:0}function V(e,n){F(n,0,255),t.HEAPU8[e]=n}function W(e,n){F(n,0,65535),t.HEAPU16[e>>>1]=n}function H(e,t,n){F(n,0,65535),e[t>>>1]=n}function G(e,n){t.HEAPU32[e>>>2]=n}function q(e,n){F(n,0,4294967295),t.HEAPU32[e>>>2]=n}function J(e,n){F(n,-128,127),t.HEAP8[e]=n}function Y(e,n){F(n,-32768,32767),t.HEAP16[e>>>1]=n}function X(e,n){t.HEAP32[e>>>2]=n}function Z(e,n){F(n,-2147483648,2147483647),t.HEAP32[e>>>2]=n}function K(e){if(0!==e)switch(e){case 1:throw new Error("value was not an integer");case 2:throw new Error("value out of range");default:throw new Error("unknown internal error")}}function Q(e,t){if(!Number.isSafeInteger(t))throw new Error(`Assert failed: Value is not a safe integer: ${t} (${typeof t})`);K(St.mono_wasm_f64_to_i52(e,t))}function ee(e,t){if(!Number.isSafeInteger(t))throw new Error(`Assert failed: Value is not a safe integer: ${t} (${typeof t})`);if(!(t>=0))throw new Error("Assert failed: Can't convert negative Number into UInt64");K(St.mono_wasm_f64_to_u52(e,t))}function te(e,n){if("bigint"!=typeof n)throw new Error(`Assert failed: Value is not an bigint: ${n} (${typeof n})`);if(!(n>=O&&n<=B))throw new Error(`Assert failed: Overflow: value ${n} is out of ${O} ${B} range`);t.HEAP64[e>>>3]=n}function ne(e,n){if("number"!=typeof n)throw new Error(`Assert failed: Value is not a Number: ${n} (${typeof n})`);t.HEAPF32[e>>>2]=n}function re(e,n){if("number"!=typeof n)throw new Error(`Assert failed: Value is not a Number: ${n} (${typeof n})`);t.HEAPF64[e>>>3]=n}function oe(e){return!!t.HEAP32[e>>>2]}function ae(e){return t.HEAPU8[e]}function se(e){return t.HEAPU16[e>>>1]}function ie(e){return t.HEAPU32[e>>>2]}function ce(e,t){return e[t>>>2]}function le(e){return St.mono_wasm_get_i32_unaligned(e)}function ue(e){return St.mono_wasm_get_i32_unaligned(e)>>>0}function pe(e){return t.HEAP8[e]}function de(e){return t.HEAP16[e>>>1]}function _e(e){return t.HEAP32[e>>>2]}function fe(e){const t=St.mono_wasm_i52_to_f64(e,l._i52_error_scratch_buffer);return K(_e(l._i52_error_scratch_buffer)),t}function me(e){const t=St.mono_wasm_u52_to_f64(e,l._i52_error_scratch_buffer);return K(_e(l._i52_error_scratch_buffer)),t}function he(e){return t.HEAP64[e>>>3]}function be(e){return t.HEAPF32[e>>>2]}function ge(e){return t.HEAPF64[e>>>3]}function ye(e){const n=t._malloc(e.length);return new Uint8Array(Ee().buffer,n,e.length).set(e),n}function we(){return t.HEAP8}function Se(){return t.HEAP16}function ke(){return t.HEAP32}function ve(){return t.HEAP64}function Ee(){return t.HEAPU8}function Ue(){return t.HEAPU16}function Ie(){return t.HEAPU32}function xe(){return t.HEAPF32}function Te(){return t.HEAPF64}const je=8192;let Ae=null,Re=null,Le=0;const $e=[],Ce=[];function Ne(e,n){if(e<=0)throw new Error("capacity >= 1");const r=4*(e|=0),o=t._malloc(r);if(o%4!=0)throw new Error("Malloc returned an unaligned offset");return P(o,r),new WasmRootBufferImpl(o,e,!0,n)}function De(e){let t;if(!e)throw new Error("address must be a location in the native heap");return Ce.length>0?(t=Ce.pop(),t._set_address(e)):t=new Fe(e),t}function Be(e=void 0){let t;if($e.length>0)t=$e.pop();else{const e=function(){if(A(Ae)||!Re){Ae=Ne(je,"js roots"),Re=new Int32Array(je),Le=je;for(let e=0;e<je;e++)Re[e]=je-e-1}if(Le<1)throw new Error("Out of scratch root space");const e=Re[Le-1];return Le--,e}();t=new Me(Ae,e)}if(void 0!==e){if("number"!=typeof e)throw new Error("value must be an address in the managed heap");t.set(e)}else t.set(0);return t}function Oe(...e){for(let t=0;t<e.length;t++)A(e[t])||e[t].release()}class WasmRootBufferImpl{constructor(e,t,n,r){const o=4*t;this.__offset=e,this.__offset32=e>>>2,this.__count=t,this.length=t,this.__handle=St.mono_wasm_register_root(e,o,r||"noname"),this.__ownsAllocation=n}_throw_index_out_of_range(){throw new Error("index out of range")}_check_in_range(e){(e>=this.__count||e<0)&&this._throw_index_out_of_range()}get_address(e){return this._check_in_range(e),this.__offset+4*e}get_address_32(e){return this._check_in_range(e),this.__offset32+e}get(e){this._check_in_range(e);const t=this.get_address_32(e);return Ie()[t]}set(e,t){const n=this.get_address(e);return St.mono_wasm_write_managed_pointer_unsafe(n,t),t}copy_value_from_address(e,t){const n=this.get_address(e);St.mono_wasm_copy_managed_pointer(n,t)}_unsafe_get(e){return Ie()[this.__offset32+e]}_unsafe_set(e,t){const n=this.__offset+e;St.mono_wasm_write_managed_pointer_unsafe(n,t)}clear(){this.__offset&&P(this.__offset,4*this.__count)}release(){this.__offset&&this.__ownsAllocation&&(St.mono_wasm_deregister_root(this.__offset),P(this.__offset,4*this.__count),t._free(this.__offset)),this.__handle=this.__offset=this.__count=this.__offset32=0}toString(){return`[root buffer @${this.get_address(0)}, size ${this.__count} ]`}}class Me{constructor(e,t){this.__buffer=e,this.__index=t}get_address(){return this.__buffer.get_address(this.__index)}get_address_32(){return this.__buffer.get_address_32(this.__index)}get address(){return this.__buffer.get_address(this.__index)}get(){return this.__buffer._unsafe_get(this.__index)}set(e){const t=this.__buffer.get_address(this.__index);return St.mono_wasm_write_managed_pointer_unsafe(t,e),e}copy_from(e){const t=e.address,n=this.address;St.mono_wasm_copy_managed_pointer(n,t)}copy_to(e){const t=this.address,n=e.address;St.mono_wasm_copy_managed_pointer(n,t)}copy_from_address(e){const t=this.address;St.mono_wasm_copy_managed_pointer(t,e)}copy_to_address(e){const t=this.address;St.mono_wasm_copy_managed_pointer(e,t)}get value(){return this.get()}set value(e){this.set(e)}valueOf(){throw new Error("Implicit conversion of roots to pointers is no longer supported. Use .value or .address as appropriate")}clear(){const e=this.__buffer.get_address_32(this.__index);Ie()[e]=0}release(){if(!this.__buffer)throw new Error("No buffer");var e;$e.length>128?(void 0!==(e=this.__index)&&(Ae.set(e,0),Re[Le]=e,Le++),this.__buffer=null,this.__index=0):(this.set(0),$e.push(this))}toString(){return`[root @${this.address}]`}}class Fe{constructor(e){this.__external_address=U,this.__external_address_32=0,this._set_address(e)}_set_address(e){this.__external_address=e,this.__external_address_32=e>>>2}get address(){return this.__external_address}get_address(){return this.__external_address}get_address_32(){return this.__external_address_32}get(){return Ie()[this.__external_address_32]}set(e){return St.mono_wasm_write_managed_pointer_unsafe(this.__external_address,e),e}copy_from(e){const t=e.address,n=this.__external_address;St.mono_wasm_copy_managed_pointer(n,t)}copy_to(e){const t=this.__external_address,n=e.address;St.mono_wasm_copy_managed_pointer(n,t)}copy_from_address(e){const t=this.__external_address;St.mono_wasm_copy_managed_pointer(t,e)}copy_to_address(e){const t=this.__external_address;St.mono_wasm_copy_managed_pointer(e,t)}get value(){return this.get()}set value(e){this.set(e)}valueOf(){throw new Error("Implicit conversion of roots to pointers is no longer supported. Use .value or .address as appropriate")}clear(){Ie()[this.__external_address>>>2]=0}release(){Ce.length<128&&Ce.push(this)}toString(){return`[external root @${this.address}]`}}const Pe=new Map,ze="";let Ve;const We=new Map;let He,Ge,qe,Je,Ye=0,Xe=null,Ze=0;function Ke(e){if(void 0===Je){const n=new Uint8Array(2*e.length);return t.stringToUTF8Array(e,n,0,2*e.length),n}return Je.encode(e)}function Qe(e){const n=Ee();return function(e,n,r){const o=n+r;let a=n;for(;e[a]&&!(a>=o);)++a;if(a-n<=16)return t.UTF8ArrayToString(e,n,r);if(void 0===qe)return t.UTF8ArrayToString(e,n,r);const s=it(e,n,a);return qe.decode(s)}(n,e,n.length-e)}function et(e,t){if(He){const n=it(Ee(),e,t);return He.decode(n)}return tt(e,t)}function tt(e,t){let n="";const r=Ue();for(let o=e;o<t;o+=2){const e=r[o>>>1];n+=String.fromCharCode(e)}return n}function nt(e,t,n){const r=Ue(),o=n.length;for(let a=0;a<o&&(H(r,e,n.charCodeAt(a)),!((e+=2)>=t));a++);}function rt(e){if(e.value===E)return null;const t=Ve+0,n=Ve+4,r=Ve+8;let o;St.mono_wasm_string_get_data_ref(e.address,t,n,r);const a=Ie(),s=ce(a,n),i=ce(a,t),c=ce(a,r);if(c&&(o=We.get(e.value)),void 0===o&&(s&&i?(o=et(i,i+s),c&&We.set(e.value,o)):o=ze),void 0===o)throw new Error(`internal error when decoding string at location ${e.value}`);return o}function ot(e,t){if(t.clear(),null!==e)if("symbol"==typeof e)at(e,t);else{if("string"!=typeof e)throw new Error("Expected string argument, got "+typeof e);if(0===e.length)at(e,t);else{if(e.length<=256){const n=Pe.get(e);if(n)return void t.set(n)}st(e,t)}}}function at(e,t){let n;if("symbol"==typeof e?(n=e.description,"string"!=typeof n&&(n=Symbol.keyFor(e)),"string"!=typeof n&&(n="<unknown Symbol>")):"string"==typeof e&&(n=e),"string"!=typeof n)throw new Error(`Argument to stringToInternedMonoStringRoot must be a string but was ${e}`);if(0===n.length&&Ye)return void t.set(Ye);const r=Pe.get(n);r?t.set(r):(st(n,t),function(e,t,n){if(!t.value)throw new Error("null pointer passed to _store_string_in_intern_table");Ze>=8192&&(Xe=null),Xe||(Xe=Ne(8192,"interned strings"),Ze=0);const r=Xe,o=Ze++;if(St.mono_wasm_intern_string_ref(t.address),!t.value)throw new Error("mono_wasm_intern_string_ref produced a null pointer");Pe.set(e,t.value),We.set(t.value,e),0!==e.length||Ye||(Ye=t.value),r.copy_value_from_address(o,t.address)}(n,t))}function st(e,n){const r=2*(e.length+1),o=t._malloc(r);nt(o,o+r,e),St.mono_wasm_string_from_utf16_ref(o,e.length,n.address),t._free(o)}function it(e,t,n){return e.buffer,e.subarray(t,n)}let ct="MONO_WASM: ";function lt(e,...t){l.diagnosticTracing&&console.debug(ct+e,...t)}function ut(e,...t){console.info(ct+e,...t)}function pt(e,...t){console.warn(ct+e,...t)}function dt(e,...t){t&&t.length>0&&t[0]&&"object"==typeof t[0]&&t[0].silent||console.error(ct+e,...t)}const _t=new Map,ft=[];function mt(e){try{if(0==_t.size)return e;const t=e;for(let n=0;n<ft.length;n++){const r=e.replace(new RegExp(ft[n],"g"),((e,...t)=>{const n=t.find((e=>"object"==typeof e&&void 0!==e.replaceSection));if(void 0===n)return e;const r=n.funcNum,o=n.replaceSection,a=_t.get(Number(r));return void 0===a?e:e.replace(o,`${a} (${o})`)}));if(r!==t)return r}return t}catch(t){return console.debug(`failed to symbolicate: ${t}`),e}}function ht(e){let t=e;return t&&t.stack||(t=new Error(t?""+t:"Unknown error")),mt(t.stack)}function bt(){return[..._t.values()]}ft.push(/at (?<replaceSection>[^:()]+:wasm-function\[(?<funcNum>\d+)\]:0x[a-fA-F\d]+)((?![^)a-fA-F\d])|$)/),ft.push(/(?:WASM \[[\da-zA-Z]+\], (?<replaceSection>function #(?<funcNum>[\d]+) \(''\)))/),ft.push(/(?<replaceSection>[a-z]+:\/\/[^ )]*:wasm-function\[(?<funcNum>\d+)\]:0x[a-fA-F\d]+)/),ft.push(/(?<replaceSection><[^ >]+>[.:]wasm-function\[(?<funcNum>[0-9]+)\])/);const gt=[[!0,"mono_wasm_array_get_ref","void",["number","number","number"]],[!0,"mono_wasm_obj_array_new_ref","void",["number","number"]],[!0,"mono_wasm_obj_array_set_ref","void",["number","number","number"]],[!0,"mono_wasm_try_unbox_primitive_and_get_type_ref","number",["number","number","number"]],[!0,"mono_wasm_box_primitive_ref","void",["number","number","number","number"]],[!0,"mono_wasm_string_array_new_ref","void",["number","number"]],[!0,"mono_wasm_typed_array_new_ref","void",["number","number","number","number","number"]],[!0,"mono_wasm_get_delegate_invoke_ref","number",["number"]],[!0,"mono_wasm_get_type_name","string",["number"]],[!0,"mono_wasm_get_type_aqn","string",["number"]],[!0,"mono_wasm_obj_array_new","number",["number"]],[!0,"mono_wasm_obj_array_set","void",["number","number","number"]],[!0,"mono_wasm_array_length_ref","number",["number"]]],yt=[[!0,"mono_wasm_register_root","number",["number","number","string"]],[!0,"mono_wasm_deregister_root",null,["number"]],[!0,"mono_wasm_string_get_data_ref",null,["number","number","number","number"]],[!0,"mono_wasm_set_is_debugger_attached","void",["bool"]],[!0,"mono_wasm_send_dbg_command","bool",["number","number","number","number","number"]],[!0,"mono_wasm_send_dbg_command_with_parms","bool",["number","number","number","number","number","number","string"]],[!0,"mono_wasm_setenv",null,["string","string"]],[!0,"mono_wasm_parse_runtime_options",null,["number","number"]],[!0,"mono_wasm_strdup","number",["string"]],[!0,"mono_background_exec",null,[]],[!0,"mono_wasm_execute_timer",null,[]],[!0,"mono_wasm_load_icu_data","number",["number"]],[!1,"mono_wasm_add_assembly","number",["string","number","number"]],[!0,"mono_wasm_add_satellite_assembly","void",["string","string","number","number"]],[!1,"mono_wasm_load_runtime",null,["string","number"]],[!0,"mono_wasm_change_debugger_log_level","void",["number"]],[!0,"mono_wasm_get_corlib","number",[]],[!0,"mono_wasm_assembly_load","number",["string"]],[!0,"mono_wasm_assembly_find_class","number",["number","string","string"]],[!0,"mono_wasm_runtime_run_module_cctor","void",["number"]],[!0,"mono_wasm_assembly_find_method","number",["number","string","number"]],[!1,"mono_wasm_invoke_method_ref","void",["number","number","number","number","number"]],[!0,"mono_wasm_string_from_utf16_ref","void",["number","number","number"]],[!0,"mono_wasm_intern_string_ref","void",["number"]],[!0,"mono_wasm_assembly_get_entry_point","number",["number","number"]],[!0,"mono_wasm_class_get_type","number",["number"]],[!1,"mono_wasm_exit","void",["number"]],[!1,"mono_wasm_abort","void",[]],[!0,"mono_wasm_getenv","number",["string"]],[!0,"mono_wasm_set_main_args","void",["number","number"]],[!1,"mono_wasm_enable_on_demand_gc","void",["number"]],[()=>!f,"mono_wasm_profiler_init_aot","void",["string"]],[()=>!m,"mono_wasm_profiler_init_aot","void",["string"]],[!0,"mono_wasm_profiler_init_browser","void",["number"]],[!1,"mono_wasm_exec_regression","number",["number","string"]],[!1,"mono_wasm_invoke_method_bound","number",["number","number","number"]],[!0,"mono_wasm_write_managed_pointer_unsafe","void",["number","number"]],[!0,"mono_wasm_copy_managed_pointer","void",["number","number"]],[!0,"mono_wasm_i52_to_f64","number",["number","number"]],[!0,"mono_wasm_u52_to_f64","number",["number","number"]],[!0,"mono_wasm_f64_to_i52","number",["number","number"]],[!0,"mono_wasm_f64_to_u52","number",["number","number"]],[!0,"mono_wasm_method_get_name","number",["number"]],[!0,"mono_wasm_method_get_full_name","number",["number"]],[!0,"mono_wasm_gc_lock","void",[]],[!0,"mono_wasm_gc_unlock","void",[]],[!0,"mono_wasm_get_i32_unaligned","number",["number"]],[!0,"mono_wasm_get_f32_unaligned","number",["number"]],[!0,"mono_wasm_get_f64_unaligned","number",["number"]],[!0,"mono_jiterp_trace_bailout","void",["number"]],[!0,"mono_jiterp_get_trace_bailout_count","number",["number"]],[!0,"mono_jiterp_value_copy","void",["number","number","number"]],[!0,"mono_jiterp_get_member_offset","number",["number"]],[!0,"mono_jiterp_encode_leb52","number",["number","number","number"]],[!0,"mono_jiterp_encode_leb64_ref","number",["number","number","number"]],[!0,"mono_jiterp_encode_leb_signed_boundary","number",["number","number","number"]],[!0,"mono_jiterp_write_number_unaligned","void",["number","number","number"]],[!0,"mono_jiterp_type_is_byref","number",["number"]],[!0,"mono_jiterp_get_size_of_stackval","number",[]],[!0,"mono_jiterp_parse_option","number",["string"]],[!0,"mono_jiterp_get_options_as_json","number",[]],[!0,"mono_jiterp_get_options_version","number",[]],[!0,"mono_jiterp_adjust_abort_count","number",["number","number"]],[!0,"mono_jiterp_register_jit_call_thunk","void",["number","number"]],[!0,"mono_jiterp_type_get_raw_value_size","number",["number"]],[!0,"mono_jiterp_update_jit_call_dispatcher","void",["number"]],[!0,"mono_jiterp_get_signature_has_this","number",["number"]],[!0,"mono_jiterp_get_signature_return_type","number",["number"]],[!0,"mono_jiterp_get_signature_param_count","number",["number"]],[!0,"mono_jiterp_get_signature_params","number",["number"]],[!0,"mono_jiterp_type_to_ldind","number",["number"]],[!0,"mono_jiterp_type_to_stind","number",["number"]],[!0,"mono_jiterp_imethod_to_ftnptr","number",["number"]],[!0,"mono_jiterp_debug_count","number",[]],[!0,"mono_jiterp_get_trace_hit_count","number",["number"]],[!0,"mono_jiterp_get_polling_required_address","number",[]],[!0,"mono_jiterp_get_rejected_trace_count","number",[]],[!0,"mono_jiterp_boost_back_branch_target","void",["number"]],[!0,"mono_jiterp_is_imethod_var_address_taken","number",["number","number"]],[!0,"mono_jiterp_get_opcode_value_table_entry","number",["number"]],[!0,"mono_jiterp_get_simd_intrinsic","number",["number","number"]],[!0,"mono_jiterp_get_simd_opcode","number",["number","number"]],[!0,"mono_jiterp_get_arg_offset","number",["number","number","number"]],[!0,"mono_jiterp_get_opcode_info","number",["number","number"]],[!0,"mono_wasm_is_zero_page_reserved","number",[]],[!0,"mono_jiterp_is_special_interface","number",["number"]],...gt],wt={};var St=wt;const kt=wt,vt=wt,Et=["void","number",null];function Ut(e,n,r,o){let a=void 0===o&&Et.indexOf(n)>=0&&(!r||r.every((e=>Et.indexOf(e)>=0)))&&t.asm?t.asm[e]:void 0;if(a&&r&&a.length!==r.length&&(dt(`argument count mismatch for cwrap ${e}`),a=void 0),"function"!=typeof a&&(a=t.cwrap(e,n,r,o)),"function"!=typeof a)throw new Error(`cwrap ${e} not found or not a function`);return a}function It(e,t,n){const r=function(e,t,n){let r,o="number"==typeof t?t:0;r="number"==typeof n?o+n:e.length-o;const a={read:function(){if(o>=r)return null;const t=e[o];return o+=1,t}};return Object.defineProperty(a,"eof",{get:function(){return o>=r},configurable:!0,enumerable:!0}),a}(e,t,n);let o="",a=0,s=0,i=0,c=0,l=0,u=0;for(;a=r.read(),s=r.read(),i=r.read(),null!==a;)null===s&&(s=0,l+=1),null===i&&(i=0,l+=1),u=a<<16|s<<8|i<<0,c=(16777215&u)>>18,o+=xt[c],c=(262143&u)>>12,o+=xt[c],l<2&&(c=(4095&u)>>6,o+=xt[c]),2===l?o+="==":1===l?o+="=":(c=(63&u)>>0,o+=xt[c]);return o}const xt=["A","B","C","D","E","F","G","H","I","J","K","L","M","N","O","P","Q","R","S","T","U","V","W","X","Y","Z","a","b","c","d","e","f","g","h","i","j","k","l","m","n","o","p","q","r","s","t","u","v","w","x","y","z","0","1","2","3","4","5","6","7","8","9","+","/"],Tt=new Map;Tt.remove=function(e){const t=this.get(e);return this.delete(e),t};let jt,At,Rt,Lt={},$t=0,Ct=-1;function mono_wasm_runtime_ready(){if(n.mono_wasm_runtime_is_ready=l.mono_wasm_runtime_is_ready=!0,$t=0,Lt={},Ct=-1,globalThis.dotnetDebugger)debugger}function mono_wasm_fire_debugger_agent_message_with_data_to_pause(e){console.assert(!0,`mono_wasm_fire_debugger_agent_message_with_data ${e}`);debugger}function Nt(e){e.length>Ct&&(jt&&t._free(jt),Ct=Math.max(e.length,Ct,256),jt=t._malloc(Ct));const n=atob(e),r=Ee();for(let e=0;e<n.length;e++)r[jt+e]=n.charCodeAt(e)}function Dt(e,t,n,r,o,a,s){Nt(r),St.mono_wasm_send_dbg_command_with_parms(e,t,n,jt,o,a,s.toString());const{res_ok:i,res:c}=Tt.remove(e);if(!i)throw new Error("Failed on mono_wasm_invoke_method_debugger_agent_with_parms");return c}function Bt(e,t,n,r){Nt(r),St.mono_wasm_send_dbg_command(e,t,n,jt,r.length);const{res_ok:o,res:a}=Tt.remove(e);if(!o)throw new Error("Failed on mono_wasm_send_dbg_command");return a}function Ot(){const{res_ok:e,res:t}=Tt.remove(0);if(!e)throw new Error("Failed on mono_wasm_get_dbg_command_info");return t}function Mt(){}function Ft(){St.mono_wasm_set_is_debugger_attached(!1)}function Pt(e){St.mono_wasm_change_debugger_log_level(e)}function zt(e,t={}){if("object"!=typeof e)throw new Error(`event must be an object, but got ${JSON.stringify(e)}`);if(void 0===e.eventName)throw new Error(`event.eventName is a required parameter, in event: ${JSON.stringify(e)}`);if("object"!=typeof t)throw new Error(`args must be an object, but got ${JSON.stringify(t)}`);console.debug("mono_wasm_debug_event_raised:aef14bca-5519-4dfe-b35a-f867abc123ae",JSON.stringify(e),JSON.stringify(t))}function Vt(){-1==l.waitForDebugger&&(l.waitForDebugger=1),St.mono_wasm_set_is_debugger_attached(!0)}function Wt(e){if(null!=e.arguments&&!Array.isArray(e.arguments))throw new Error(`"arguments" should be an array, but was ${e.arguments}`);const t=e.objectId,n=e.details;let r={};if(t.startsWith("dotnet:cfo_res:")){if(!(t in Lt))throw new Error(`Unknown object id ${t}`);r=Lt[t]}else r=function(e,t){if(e.startsWith("dotnet:array:")){let e;if(void 0===t.items)return e=t.map((e=>e.value)),e;if(void 0===t.dimensionsDetails||1===t.dimensionsDetails.length)return e=t.items.map((e=>e.value)),e}const n={};return Object.keys(t).forEach((e=>{const r=t[e];void 0!==r.get?Object.defineProperty(n,r.name,{get:()=>Bt(r.get.id,r.get.commandSet,r.get.command,r.get.buffer),set:function(e){return Dt(r.set.id,r.set.commandSet,r.set.command,r.set.buffer,r.set.length,r.set.valtype,e),!0}}):void 0!==r.set?Object.defineProperty(n,r.name,{get:()=>r.value,set:function(e){return Dt(r.set.id,r.set.commandSet,r.set.command,r.set.buffer,r.set.length,r.set.valtype,e),!0}}):n[r.name]=r.value})),n}(t,n);const o=null!=e.arguments?e.arguments.map((e=>JSON.stringify(e.value))):[],a=`const fn = ${e.functionDeclaration}; return fn.apply(proxy, [${o}]);`,s=new Function("proxy",a)(r);if(void 0===s)return{type:"undefined"};if(Object(s)!==s)return"object"==typeof s&&null==s?{type:typeof s,subtype:`${s}`,value:null}:{type:typeof s,description:`${s}`,value:`${s}`};if(e.returnByValue&&null==s.subtype)return{type:"object",value:s};if(Object.getPrototypeOf(s)==Array.prototype){const e=Gt(s);return{type:"object",subtype:"array",className:"Array",description:`Array(${s.length})`,objectId:e}}return void 0!==s.value||void 0!==s.subtype?s:s==r?{type:"object",className:"Object",description:"Object",objectId:t}:{type:"object",className:"Object",description:"Object",objectId:Gt(s)}}function Ht(e,t={}){return function(e,t){if(!(e in Lt))throw new Error(`Could not find any object with id ${e}`);const n=Lt[e],r=Object.getOwnPropertyDescriptors(n);t.accessorPropertiesOnly&&Object.keys(r).forEach((e=>{void 0===r[e].get&&Reflect.deleteProperty(r,e)}));const o=[];return Object.keys(r).forEach((e=>{let t;const n=r[e];t="object"==typeof n.value?Object.assign({name:e},n):void 0!==n.value?{name:e,value:Object.assign({type:typeof n.value,description:""+n.value},n)}:void 0!==n.get?{name:e,get:{className:"Function",description:`get ${e} () {}`,type:"function"}}:{name:e,value:{type:"symbol",value:"<Unknown>",description:"<Unknown>"}},o.push(t)})),{__value_as_json_string__:JSON.stringify(o)}}(`dotnet:cfo_res:${e}`,t)}function Gt(e){const t="dotnet:cfo_res:"+$t++;return Lt[t]=e,t}function qt(e){e in Lt&&delete Lt[e]}function Jt(){if(l.enablePerfMeasure)return globalThis.performance.now()}function Yt(e,t,n){if(l.enablePerfMeasure&&e){const r=a?{start:e}:{startTime:e},o=n?`${t}${n} `:t;globalThis.performance.measure(o,r)}}const Xt=[],Zt=new Map,Kt=new Map,Qt=new Map,en=Symbol.for("wasm bound_cs_function"),tn=Symbol.for("wasm bound_js_function"),nn=Symbol.for("wasm imported_js_function"),rn=16;function on(e){const n=t.stackAlloc(rn*e);return n&&n%8==0||w(!1,"Arg alignment"),hn(an(n,0),R.None),hn(an(n,1),R.None),n}function an(e,t){return e||w(!1,"Null args"),e+t*rn}function sn(e,t){return e||w(!1,"Null signatures"),e+32*t+8}function cn(e){return e||w(!1,"Null sig"),ie(e)}function ln(e){return e||w(!1,"Null sig"),ie(e+16)}function un(e){return e||w(!1,"Null sig"),ie(e+20)}function pn(e){return e||w(!1,"Null sig"),ie(e+24)}function dn(e){return e||w(!1,"Null sig"),ie(e+28)}function _n(e){return e||w(!1,"Null signatures"),_e(e+4)}function fn(e){return e||w(!1,"Null signatures"),_e(e)}function mn(e){return e||w(!1,"Null arg"),ie(e+12)}function hn(e,t){e||w(!1,"Null arg"),q(e+12,t)}function bn(e){return e||w(!1,"Null arg"),ie(e)}function gn(e,t){if(e||w(!1,"Null arg"),"boolean"!=typeof t)throw new Error(`Assert failed: Value is not a Boolean: ${t} (${typeof t})`);V(e,t?1:0)}function yn(e,t){e||w(!1,"Null arg"),q(e,t)}function wn(e,t){e||w(!1,"Null arg"),re(e,t.getTime())}function Sn(e,t){e||w(!1,"Null arg"),re(e,t)}function kn(e){return e||w(!1,"Null arg"),ie(e+4)}function vn(e,t){e||w(!1,"Null arg"),q(e+4,t)}function En(e){return e||w(!1,"Null arg"),ie(e+4)}function Un(e,t){e||w(!1,"Null arg"),q(e+4,t)}function In(e){return e||w(!1,"Null arg"),De(e)}function xn(e){return e||w(!1,"Null arg"),_e(e+8)}function Tn(e,t){e||w(!1,"Null arg"),Z(e+8,t)}class ManagedObject{dispose(){Mr(this,T)}get isDisposed(){return this[Lr]===T}toString(){return`CsObject(gc_handle: ${this[Lr]})`}}class ManagedError extends Error{constructor(e){super(e),this.superStack=Object.getOwnPropertyDescriptor(this,"stack"),Object.defineProperty(this,"stack",{get:this.getManageStack})}getSuperStack(){if(this.superStack){if(void 0!==this.superStack.value)return this.superStack.value;if(void 0!==this.superStack.get)return this.superStack.get.call(this)}return super.stack}getManageStack(){if(this.managed_stack)return this.managed_stack;if(u.is_runtime_running()&&!L){const e=this[Lr];if(e!==T){const t=l.javaScriptExports.get_managed_stack_trace(e);if(t)return this.managed_stack=t+"\n"+this.getSuperStack(),this.managed_stack}}return this.getSuperStack()}dispose(){Mr(this,T)}get isDisposed(){return this[Lr]===T}}function jn(e){return e==R.Byte?1:e==R.Int32?4:e==R.Int52||e==R.Double?8:e==R.String||e==R.Object||e==R.JSObject?rn:-1}class An{constructor(e,t,n){this._pointer=e,this._length=t,this._viewType=n}_unsafe_create_view(){const e=0==this._viewType?new Uint8Array(Ee().buffer,this._pointer,this._length):1==this._viewType?new Int32Array(ke().buffer,this._pointer,this._length):2==this._viewType?new Float64Array(Te().buffer,this._pointer,this._length):null;if(!e)throw new Error("NotImplementedException");return e}set(e,t){if(this.isDisposed)throw new Error("Assert failed: ObjectDisposedException");const n=this._unsafe_create_view();if(!e||!n||e.constructor!==n.constructor)throw new Error(`Assert failed: Expected ${n.constructor}`);n.set(e,t)}copyTo(e,t){if(this.isDisposed)throw new Error("Assert failed: ObjectDisposedException");const n=this._unsafe_create_view();if(!e||!n||e.constructor!==n.constructor)throw new Error(`Assert failed: Expected ${n.constructor}`);const r=n.subarray(t);e.set(r)}slice(e,t){if(this.isDisposed)throw new Error("Assert failed: ObjectDisposedException");return this._unsafe_create_view().slice(e,t)}get length(){if(this.isDisposed)throw new Error("Assert failed: ObjectDisposedException");return this._length}get byteLength(){if(this.isDisposed)throw new Error("Assert failed: ObjectDisposedException");return 0==this._viewType?this._length:1==this._viewType?this._length<<2:2==this._viewType?this._length<<3:0}}class Span extends An{constructor(e,t,n){super(e,t,n),this.is_disposed=!1}dispose(){this.is_disposed=!0}get isDisposed(){return this.is_disposed}}class ArraySegment extends An{constructor(e,t,n){super(e,t,n)}dispose(){Mr(this,T)}get isDisposed(){return this[Lr]===T}}function Rn(e,t,n){if(t===R.None||t===R.Void)return;let r,o,a,s;o=Xr(un(e)),a=Xr(pn(e)),s=Xr(dn(e));const i=ln(e);r=Ln(i),t===R.Nullable&&(t=i);const c=Ln(t),l=un(e),u=n*rn;return e=>c(e+u,l,r,o,a,s)}function Ln(e){if(e===R.None||e===R.Void)return;const t=Kt.get(e);return t&&"function"==typeof t||w(!1,`ERR41: Unknown converter for type ${e}. ${Jr}`),t}function $n(e){return mn(e)==R.None?null:function(e){return e||w(!1,"Null arg"),!!ae(e)}(e)}function Cn(e){return mn(e)==R.None?null:function(e){return e||w(!1,"Null arg"),ae(e)}(e)}function Nn(e){return mn(e)==R.None?null:function(e){return e||w(!1,"Null arg"),se(e)}(e)}function Dn(e){return mn(e)==R.None?null:function(e){return e||w(!1,"Null arg"),de(e)}(e)}function Bn(e){return mn(e)==R.None?null:function(e){return e||w(!1,"Null arg"),_e(e)}(e)}function On(e){return mn(e)==R.None?null:function(e){return e||w(!1,"Null arg"),ge(e)}(e)}function Mn(e){return mn(e)==R.None?null:function(e){return e||w(!1,"Null arg"),he(e)}(e)}function Fn(e){return mn(e)==R.None?null:function(e){return e||w(!1,"Null arg"),be(e)}(e)}function Pn(e){return mn(e)==R.None?null:function(e){return e||w(!1,"Null arg"),ge(e)}(e)}function zn(e){return mn(e)==R.None?null:bn(e)}function Vn(){return null}function Wn(e){return mn(e)===R.None?null:function(e){e||w(!1,"Null arg");const t=ge(e);return new Date(t)}(e)}function Hn(e,t,n,r,o,a){if(mn(e)===R.None)return null;const s=En(e);let i=zr(s);return null==i&&(i=(e,t,i)=>l.javaScriptExports.call_delegate(s,e,t,i,n,r,o,a),i.dispose=()=>{i.isDisposed||(i.isDisposed=!0,Mr(i,s))},i.isDisposed=!1,Or(i,s)),i}function Gn(e,t,n){const r=mn(e);if(r===R.None)return null;if(r!==R.Task){n||(n=Kt.get(r)),n||w(!1,`Unknown sub_converter for type ${R[r]}. ${Jr}`);const t=n(e);return new Promise((e=>e(t)))}const o=kn(e);if(o==x)return new Promise((e=>e(void 0)));const a=Nr(o);a||w(!1,`ERR28: promise not found for js_handle: ${o} `),u.assertIsControllablePromise(a);const s=u.getPromiseController(a),i=s.resolve;return s.resolve=e=>{const t=mn(e);if(t===R.None)return void i(null);n||(n=Kt.get(t)),n||w(!1,`Unknown sub_converter for type ${R[t]}. ${Jr}`);const r=n(e);i(r)},a}function qn(e){if(mn(e)==R.None)return null;const t=In(e);try{return rt(t)}finally{t.release()}}function Jn(e){const t=mn(e);if(t==R.None)return null;if(t==R.JSException)return Nr(kn(e));const n=En(e);let r=zr(n);if(null==r){const t=qn(e);r=new ManagedError(t),Or(r,n)}return r}function Yn(e){return mn(e)==R.None?null:Nr(kn(e))}function Xn(e){const t=mn(e);if(t==R.None)return null;if(t==R.JSObject)return Nr(kn(e));if(t==R.Array){const t=function(e){return e||w(!1,"Null arg"),ie(e+4)}(e);return Kn(e,t)}if(t==R.Object){const t=En(e);if(t===T)return null;let n=zr(t);return n||(n=new ManagedObject,Or(n,t)),n}const n=Kt.get(t);return n||w(!1,`Unknown converter for type ${R[t]}. ${Jr}`),n(e)}function Zn(e,t){return t||w(!1,"Expected valid element_type parameter"),Kn(e,t)}function Kn(e,n){if(mn(e)==R.None)return null;-1==jn(n)&&w(!1,`Element type ${R[n]} not supported`);const r=bn(e),o=xn(e);let a=null;if(n==R.String){a=new Array(o);for(let e=0;e<o;e++){const t=an(r,e);a[e]=qn(t)}St.mono_wasm_deregister_root(r)}else if(n==R.Object){a=new Array(o);for(let e=0;e<o;e++){const t=an(r,e);a[e]=Xn(t)}St.mono_wasm_deregister_root(r)}else if(n==R.JSObject){a=new Array(o);for(let e=0;e<o;e++){const t=an(r,e);a[e]=Yn(t)}}else if(n==R.Byte)a=Ee().subarray(r,r+o).slice();else if(n==R.Int32)a=ke().subarray(r>>2,(r>>2)+o).slice();else{if(n!=R.Double)throw new Error(`NotImplementedException ${R[n]}. ${Jr}`);a=Te().subarray(r>>3,(r>>3)+o).slice()}return t._free(r),a}function Qn(e,t){t||w(!1,"Expected valid element_type parameter");const n=bn(e),r=xn(e);let o=null;if(t==R.Byte)o=new Span(n,r,0);else if(t==R.Int32)o=new Span(n,r,1);else{if(t!=R.Double)throw new Error(`NotImplementedException ${R[t]}. ${Jr}`);o=new Span(n,r,2)}return o}function er(e,t){t||w(!1,"Expected valid element_type parameter");const n=bn(e),r=xn(e);let o=null;if(t==R.Byte)o=new ArraySegment(n,r,0);else if(t==R.Int32)o=new ArraySegment(n,r,1);else{if(t!=R.Double)throw new Error(`NotImplementedException ${R[t]}. ${Jr}`);o=new ArraySegment(n,r,2)}return Or(o,En(e)),o}let tr;const nr=[null];function rr(e,t){ur.set(e,t),lt(`added module imports '${e}'`)}function or(e,t,n){if(!e)throw new Error("Assert failed: Null reference");e[t]=n}function ar(e,t){if(!e)throw new Error("Assert failed: Null reference");return e[t]}function sr(e,t){if(!e)throw new Error("Assert failed: Null reference");return t in e}function ir(e,t){if(!e)throw new Error("Assert failed: Null reference");return typeof e[t]}function cr(){return globalThis}const lr=new Map,ur=new Map;function pr(e,t){e&&"string"==typeof e||w(!1,"module_name must be string"),t&&"string"==typeof t||w(!1,"module_url must be string");let n=lr.get(e);const r=!n;return r&&(lt(`importing ES6 module '${e}' from '${t}'`),n=import(t),lr.set(e,n)),Gr((async()=>{const o=await n;return r&&(ur.set(e,o),lt(`imported ES6 module '${e}' from '${t}'`)),o}))}function dr(e,t,n){const r=function(e,t){let n="unknown exception";if(t){n=t.toString();const e=t.stack;e&&(e.startsWith(n)?n=e:n+="\n"+e),n=mt(n)}return e&&X(e,1),n}(e,t);ot(r,n)}function _r(e,t){e&&X(e,0),t&&t.clear()}function fr(){u.assert_runtime_running(),l.mono_wasm_bindings_is_ready||w(!1,"The runtime must be initialized.")}const mr="function"==typeof globalThis.WeakRef;function hr(e){return mr?new WeakRef(e):{deref:()=>e,dispose:()=>{e=null}}}const br=new Map,gr=new Map;let yr=0;function wr(e){if(br.has(e))return br.get(e);const t=St.mono_wasm_assembly_load(e);return br.set(e,t),t}function Sr(e,t){yr||(yr=St.mono_wasm_get_corlib());let n=function(e,t,n){let r=gr.get(e);r||gr.set(e,r=new Map);let o=r.get(t);return o||(o=new Map,r.set(t,o)),o.get(n)}(yr,e,t);if(void 0!==n)return n;if(n=St.mono_wasm_assembly_find_class(yr,e,t),!n)throw new Error(`Failed to find corlib class ${e}.${t}`);return function(e,t,n,r){const o=gr.get(e);if(!o)throw new Error("internal error");const a=o.get(t);if(!a)throw new Error("internal error");a.set(n,r)}(yr,e,t,n),n}function kr(e,t){fr();const n=Be();try{if(St.mono_wasm_invoke_method_bound(e,t,n.address))throw new Error("ERR24: Unexpected error: "+rt(n));if(function(e){return e||w(!1,"Null args"),mn(e)!==R.None}(t))throw Jn(an(t,0))}finally{n.release()}}const vr=new Map;async function Er(e){if(fr(),!vr.get(e)){const t=Jt(),n=wr(e);if(!n)throw new Error("Could not find assembly: "+e);const r=St.mono_wasm_assembly_find_class(n,l.runtime_interop_namespace,"__GeneratedInitializer");if(r){const e=St.mono_wasm_assembly_find_method(r,"__Register_",-1);if(e){const t=Be(),n=Be();try{if(St.mono_wasm_invoke_method_ref(e,U,j,t.address,n.address),t.value!==S){const e=rt(n);throw new Error(e)}}finally{t.release(),n.release()}}}else St.mono_wasm_runtime_run_module_cctor(n);Yt(t,"mono.getAssemblyExports:",e)}return vr.get(e)||{}}function Ur(e){const t=e.substring(e.indexOf("[")+1,e.indexOf("]")).trim(),n=(e=e.substring(e.indexOf("]")+1).trim()).substring(e.indexOf(":")+1);let r="",o=e=e.substring(0,e.indexOf(":")).trim();if(-1!=e.indexOf(".")){const t=e.lastIndexOf(".");r=e.substring(0,t),o=e.substring(t+1)}if(!t.trim())throw new Error("No assembly name specified "+e);if(!o.trim())throw new Error("No class name specified "+e);if(!n.trim())throw new Error("No method name specified "+e);return{assembly:t,namespace:r,classname:o,methodname:n}}const Ir="function"==typeof globalThis.FinalizationRegistry;let xr;const Tr=[null],jr=[];let Ar=1;const Rr=new Map;Ir&&(xr=new globalThis.FinalizationRegistry(Pr));const Lr=Symbol.for("wasm js_owned_gc_handle"),$r=Symbol.for("wasm cs_owned_js_handle"),Cr=Symbol.for("wasm do_not_force_dispose");function Nr(e){return e!==x&&e!==I?Tr[e]:null}function Dr(e){if(e[$r])return e[$r];const t=jr.length?jr.pop():Ar++;return Tr[t]=e,Object.isExtensible(e)&&(e[$r]=t),t}function Br(e){const t=Tr[e];null!=t&&(void 0!==t[$r]&&(t[$r]=void 0),Tr[e]=void 0,jr.push(e))}function Or(e,t){e[Lr]=t,Ir&&xr.register(e,t,e);const n=hr(e);Rr.set(t,n)}function Mr(e,t){e&&(t=e[Lr],e[Lr]=T,Ir&&xr.unregister(e)),t!==T&&Rr.delete(t)&&l.javaScriptExports.release_js_owned_object_by_gc_handle(t)}function Fr(e){const t=e[Lr];if(t==T)throw new Error("Assert failed: ObjectDisposedException");return t}function Pr(e){u.is_exited()||Mr(null,e)}function zr(e){if(!e)return null;const t=Rr.get(e);return t?t.deref():null}function Vr(e,t){let n=!1,r=!1,o=0,a=0,s=0,i=0;const c=[...Rr.keys()];for(const e of c){const r=Rr.get(e),o=r.deref();if(Ir&&o&&xr.unregister(o),o){const a="boolean"==typeof o[Cr]&&o[Cr];if(t&&pt(`Proxy of C# ${typeof o} with GCHandle ${e} was still alive. ${a?"keeping":"disposing"}.`),a)n=!0;else{const t=u.getPromiseController(o);t&&t.reject(new Error("WebWorker which is origin of the Task is being terminated.")),"function"==typeof o.dispose&&o.dispose(),o[Lr]===e&&(o[Lr]=T),!mr&&r&&r.dispose(),s++}}}n||(Rr.clear(),Ir&&(xr=new globalThis.FinalizationRegistry(Pr)));for(let e=0;e<Tr.length;e++){const n=Tr[e],o=n&&"boolean"==typeof n[Cr]&&n[Cr];if(o||(Tr[e]=void 0),n)if(t&&pt(`Proxy of JS ${typeof n} with JSHandle ${e} was still alive. ${o?"keeping":"disposing"}.`),o)r=!0;else{const t=u.getPromiseController(n);t&&t.reject(new Error("WebWorker which is origin of the Task is being terminated.")),"function"==typeof n.dispose&&n.dispose(),n[$r]===e&&(n[$r]=void 0),i++}}if(r||(Tr.length=1,Ar=1,jr.length=0),e){for(const e of nr)if(e){const t=e[nn];t&&(t.disposed=!0,o++)}nr.length=1;const e=[...vr.values()];for(const t of e)for(const e in t){const n=t[e][en];n&&(n.disposed=!0,a++)}vr.clear()}ut(`forceDisposeProxies done: ${o} imports, ${a} exports, ${s} GCHandles, ${i} JSHandles.`)}const Wr=("object"==typeof Promise||"function"==typeof Promise)&&"function"==typeof Promise.resolve;function Hr(e){return Promise.resolve(e)===e||("object"==typeof e||"function"==typeof e)&&"function"==typeof e.then}function Gr(e){const{promise:t,promise_control:n}=y();return e().then((e=>n.resolve(e))).catch((e=>n.reject(e))),t}function qr(e){const t=zr(e);if(!t)return;const n=t.promise;n||w(!1,`Expected Promise for GCHandle ${e}`),u.assertIsControllablePromise(n),u.getPromiseController(n).reject(new Error("OperationCanceledException"))}const Jr="For more information see https://aka.ms/dotnet-wasm-jsinterop";function Yr(e,t,n){if(t===R.None||t===R.Void)return;let r,o,a,s;o=Ln(un(e)),a=Ln(pn(e)),s=Ln(dn(e));const i=ln(e);r=Xr(i),t===R.Nullable&&(t=i);const c=Xr(t),l=un(e),u=n*rn;return(e,t)=>{c(e+u,t,l,r,o,a,s)}}function Xr(e){if(e===R.None||e===R.Void)return;const t=Qt.get(e);return t&&"function"==typeof t||w(!1,`ERR30: Unknown converter for type ${e}`),t}function Zr(e,t){null==t?hn(e,R.None):(hn(e,R.Boolean),gn(e,t))}function Kr(e,t){null==t?hn(e,R.None):(hn(e,R.Byte),function(e,t){e||w(!1,"Null arg"),V(e,t)}(e,t))}function Qr(e,t){null==t?hn(e,R.None):(hn(e,R.Char),function(e,t){e||w(!1,"Null arg"),W(e,t)}(e,t))}function eo(e,t){null==t?hn(e,R.None):(hn(e,R.Int16),function(e,t){e||w(!1,"Null arg"),Y(e,t)}(e,t))}function to(e,t){null==t?hn(e,R.None):(hn(e,R.Int32),function(e,t){e||w(!1,"Null arg"),Z(e,t)}(e,t))}function no(e,t){null==t?hn(e,R.None):(hn(e,R.Int52),function(e,t){if(e||w(!1,"Null arg"),!Number.isSafeInteger(t))throw new Error(`Assert failed: Value is not an integer: ${t} (${typeof t})`);re(e,t)}(e,t))}function ro(e,t){null==t?hn(e,R.None):(hn(e,R.BigInt64),function(e,t){e||w(!1,"Null arg"),te(e,t)}(e,t))}function oo(e,t){null==t?hn(e,R.None):(hn(e,R.Double),Sn(e,t))}function ao(e,t){null==t?hn(e,R.None):(hn(e,R.Single),function(e,t){e||w(!1,"Null arg"),ne(e,t)}(e,t))}function so(e,t){null==t?hn(e,R.None):(hn(e,R.IntPtr),yn(e,t))}function io(e,t){if(null==t)hn(e,R.None);else{if(!(t instanceof Date))throw new Error("Assert failed: Value is not a Date");hn(e,R.DateTime),wn(e,t)}}function co(e,t){if(null==t)hn(e,R.None);else{if(!(t instanceof Date))throw new Error("Assert failed: Value is not a Date");hn(e,R.DateTimeOffset),wn(e,t)}}function lo(e,t){if(null==t)hn(e,R.None);else{if(hn(e,R.String),"string"!=typeof t)throw new Error("Assert failed: Value is not a String");uo(e,t)}}function uo(e,t){const n=In(e);try{ot(t,n)}finally{n.release()}}function po(e){hn(e,R.None)}function _o(e,t,n,r,o,a,s){if(null==t)return void hn(e,R.None);if(!(t&&t instanceof Function))throw new Error("Assert failed: Value is not a Function");const i=e=>{const n=an(e,0),c=an(e,1),l=an(e,2),u=an(e,3),p=an(e,4);try{let e,n,d;L&&i.isDisposed,o&&(e=o(l)),a&&(n=a(u)),s&&(d=s(p));const _=t(e,n,d);r&&r(c,_)}catch(e){ho(n,e)}};i[tn]=!0,i.isDisposed=!1,i.dispose=()=>{i.isDisposed=!0},vn(e,Dr(i)),hn(e,R.Function)}class fo{constructor(e){this.promise=e}dispose(){Mr(this,T)}get isDisposed(){return this[Lr]===T}}function mo(e,t,n,r){if(null==t)return void hn(e,R.None);if(!Hr(t))throw new Error("Assert failed: Value is not a Promise");const o=l.javaScriptExports.create_task_callback();Un(e,o),hn(e,R.Task);const a=new fo(t);Or(a,o),t.then((e=>{try{u.assert_runtime_running(),a.isDisposed&&w(!1,"This promise can't be propagated to managed code, because the Task was already freed."),l.javaScriptExports.complete_task(o,null,e,r||go),Mr(a,o)}catch(e){pt("Exception marshalling result of JS promise to CS: ",e)}})).catch((e=>{try{u.assert_runtime_running(),a.isDisposed&&w(!1,"This promise can't be propagated to managed code, because the Task was already freed."),l.javaScriptExports.complete_task(o,e,null,void 0),Mr(a,o)}catch(e){u.is_exited()||pt("Exception marshalling error of JS promise to CS: ",e)}}))}function ho(e,t){if(null==t)hn(e,R.None);else if(t instanceof ManagedError)hn(e,R.Exception),Un(e,Fr(t));else{if("object"!=typeof t&&"string"!=typeof t)throw new Error("Assert failed: Value is not an Error "+typeof t);hn(e,R.JSException),uo(e,t.toString());const n=t[$r];vn(e,n||Dr(t))}}function bo(e,t){if(null==t)hn(e,R.None);else{if(void 0!==t[Lr])throw new Error(`Assert failed: JSObject proxy of ManagedObject proxy is not supported. ${Jr}`);if("function"!=typeof t&&"object"!=typeof t)throw new Error(`Assert failed: JSObject proxy of ${typeof t} is not supported`);hn(e,R.JSObject),vn(e,Dr(t))}}function go(e,t){if(null==t)hn(e,R.None);else{const n=t[Lr],r=typeof t;if(void 0===n)if("string"===r||"symbol"===r)hn(e,R.String),uo(e,t);else if("number"===r)hn(e,R.Double),Sn(e,t);else{if("bigint"===r)throw new Error("NotImplementedException: bigint");if("boolean"===r)hn(e,R.Boolean),gn(e,t);else if(t instanceof Date)hn(e,R.DateTime),wn(e,t);else if(t instanceof Error)ho(e,t);else if(t instanceof Uint8Array)wo(e,t,R.Byte);else if(t instanceof Float64Array)wo(e,t,R.Double);else if(t instanceof Int32Array)wo(e,t,R.Int32);else if(Array.isArray(t))wo(e,t,R.Object);else{if(t instanceof Int16Array||t instanceof Int8Array||t instanceof Uint8ClampedArray||t instanceof Uint16Array||t instanceof Uint32Array||t instanceof Float32Array)throw new Error("NotImplementedException: TypedArray");if(Hr(t))mo(e,t);else{if(t instanceof Span)throw new Error("NotImplementedException: Span");if("object"!=r)throw new Error(`JSObject proxy is not supported for ${r} ${t}`);{const n=Dr(t);hn(e,R.JSObject),vn(e,n)}}}}else{if(Fr(t),t instanceof ArraySegment)throw new Error("NotImplementedException: ArraySegment. "+Jr);if(t instanceof ManagedError)hn(e,R.Exception),Un(e,n);else{if(!(t instanceof ManagedObject))throw new Error("NotImplementedException "+r+". "+Jr);hn(e,R.Object),Un(e,n)}}}}function yo(e,t,n){n||w(!1,"Expected valid element_type parameter"),wo(e,t,n)}function wo(e,n,r){if(null==n)hn(e,R.None);else{const o=jn(r);-1==o&&w(!1,`Element type ${R[r]} not supported`);const a=n.length,s=o*a,i=t._malloc(s);if(r==R.String){if(!Array.isArray(n))throw new Error("Assert failed: Value is not an Array");P(i,s),St.mono_wasm_register_root(i,s,"marshal_array_to_cs");for(let e=0;e<a;e++)lo(an(i,e),n[e])}else if(r==R.Object){if(!Array.isArray(n))throw new Error("Assert failed: Value is not an Array");P(i,s),St.mono_wasm_register_root(i,s,"marshal_array_to_cs");for(let e=0;e<a;e++)go(an(i,e),n[e])}else if(r==R.JSObject){if(!Array.isArray(n))throw new Error("Assert failed: Value is not an Array");P(i,s);for(let e=0;e<a;e++)bo(an(i,e),n[e])}else if(r==R.Byte){if(!(Array.isArray(n)||n instanceof Uint8Array))throw new Error("Assert failed: Value is not an Array or Uint8Array");Ee().subarray(i,i+a).set(n)}else if(r==R.Int32){if(!(Array.isArray(n)||n instanceof Int32Array))throw new Error("Assert failed: Value is not an Array or Int32Array");ke().subarray(i>>2,(i>>2)+a).set(n)}else{if(r!=R.Double)throw new Error("not implemented");if(!(Array.isArray(n)||n instanceof Float64Array))throw new Error("Assert failed: Value is not an Array or Float64Array");Te().subarray(i>>3,(i>>3)+a).set(n)}yn(e,i),hn(e,R.Array),function(e,t){e||w(!1,"Null arg"),q(e+4,t)}(e,r),Tn(e,n.length)}}function So(e,t,n){if(n||w(!1,"Expected valid element_type parameter"),t.isDisposed)throw new Error("Assert failed: ObjectDisposedException");vo(n,t._viewType),hn(e,R.Span),yn(e,t._pointer),Tn(e,t.length)}function ko(e,t,n){n||w(!1,"Expected valid element_type parameter");const r=Fr(t);r||w(!1,"Only roundtrip of ArraySegment instance created by C#"),vo(n,t._viewType),hn(e,R.ArraySegment),yn(e,t._pointer),Tn(e,t.length),Un(e,r)}function vo(e,t){if(e==R.Byte){if(0!=t)throw new Error("Assert failed: Expected MemoryViewType.Byte")}else if(e==R.Int32){if(1!=t)throw new Error("Assert failed: Expected MemoryViewType.Int32")}else{if(e!=R.Double)throw new Error(`NotImplementedException ${R[e]} `);if(2!=t)throw new Error("Assert failed: Expected MemoryViewType.Double")}}const Eo={now:function(){return Date.now()}};function Uo(e){void 0===globalThis.performance&&(globalThis.performance=Eo),e.require=n.require,e.scriptDirectory=u.scriptDirectory,t.locateFile===t.__locateFile&&(t.locateFile=u.locateFile),e.fetch=u.fetch_like,e.noExitRuntime=a&&!i;const r=e.updateMemoryViews;l.updateMemoryViews=e.updateMemoryViews=()=>{r()}}async function Io(){var e;if(r){if(globalThis.performance===Eo){const{performance:e}=n.require("perf_hooks");globalThis.performance=e}if(n.process=await import("process"),globalThis.crypto||(globalThis.crypto={}),!globalThis.crypto.getRandomValues){let e;try{e=n.require("node:crypto")}catch(e){}e?e.webcrypto?globalThis.crypto=e.webcrypto:e.randomBytes&&(globalThis.crypto.getRandomValues=t=>{t&&t.set(e.randomBytes(t.length))}):globalThis.crypto.getRandomValues=()=>{throw new Error("Using node without crypto support. To enable current operation, either provide polyfill for 'globalThis.crypto.getRandomValues' or enable 'node:crypto' module.")}}}l.subtle=null===(e=globalThis.crypto)||void 0===e?void 0:e.subtle}function xo(e){const t=St.mono_wasm_assembly_find_method(l.runtime_interop_exports_class,e,-1);if(!t)throw"Can't find method "+l.runtime_interop_namespace+"."+l.runtime_interop_exports_classname+"."+e;return t}function To(){if("function"!=typeof globalThis.fetch||"function"!=typeof globalThis.AbortController)throw new Error(r?"Please install `node-fetch` and `node-abort-controller` npm packages to enable HTTP client support. See also https://aka.ms/dotnet-wasm-features":"This browser doesn't support fetch API. Please use a modern browser. See also https://aka.ms/dotnet-wasm-features")}function jo(e){e.catch((e=>{e&&"AbortError"!==e&&"AbortError"!==e.name&&lt("http muted: "+e)}))}function Ao(){return"undefined"!=typeof Response&&"body"in Response.prototype&&"function"==typeof ReadableStream}function Ro(){return To(),new AbortController}function Lo(e){e.abort()}function $o(e){e.__abort_controller.abort(),e.__reader&&jo(e.__reader.cancel())}function Co(e,t,n,r,o,a,s,i){return No(e,t,n,r,o,a,new Span(s,i,0).slice())}function No(e,t,n,r,o,a,s){To(),e&&"string"==typeof e||w(!1,"expected url string"),t&&n&&Array.isArray(t)&&Array.isArray(n)&&t.length===n.length||w(!1,"expected headerNames and headerValues arrays"),r&&o&&Array.isArray(r)&&Array.isArray(o)&&r.length===o.length||w(!1,"expected headerNames and headerValues arrays");const i=new Headers;for(let e=0;e<t.length;e++)i.append(t[e],n[e]);const c={body:s,headers:i,signal:a.signal};for(let e=0;e<r.length;e++)c[r[e]]=o[e];return Gr((async()=>{const t=await u.fetch_like(e,c);return t.__abort_controller=a,t}))}function Do(e){if(!e.__headerNames&&(e.__headerNames=[],e.__headerValues=[],e.headers&&e.headers.entries)){const t=e.headers.entries();for(const n of t)e.__headerNames.push(n[0]),e.__headerValues.push(n[1])}}function Bo(e){return Do(e),e.__headerNames}function Oo(e){return Do(e),e.__headerValues}function Mo(e){return Gr((async()=>{const t=await e.arrayBuffer();return e.__buffer=t,e.__source_offset=0,t.byteLength}))}function Fo(e,t){if(e.__buffer||w(!1,"expected resoved arrayBuffer"),e.__source_offset==e.__buffer.byteLength)return 0;const n=new Uint8Array(e.__buffer,e.__source_offset);t.set(n,0);const r=Math.min(t.byteLength,n.byteLength);return e.__source_offset+=r,r}function Po(e,t,n){const r=new Span(t,n,0);return Gr((async()=>{if(!e.body)return 0;if(e.__reader||(e.__reader=e.body.getReader(),jo(e.__reader.closed)),e.__chunk||(e.__chunk=await e.__reader.read(),e.__source_offset=0),e.__chunk.done)return 0;const t=e.__chunk.value.byteLength-e.__source_offset;t>0||w(!1,"expected remaining_source to be greater than 0");const n=Math.min(t,r.byteLength),o=e.__chunk.value.subarray(e.__source_offset,e.__source_offset+n);return r.set(o,0),e.__source_offset+=n,t==n&&(e.__chunk=void 0),n}))}let zo,Vo=0,Wo=0;function Ho(){if(!u.isChromium)return;const e=(new Date).valueOf(),t=e+36e4;for(let n=Math.max(e+1e3,Vo);n<t;n+=1e3){const t=n-e;globalThis.setTimeout(Go,t)}Vo=t}function Go(){t.maybeExit(),u.is_runtime_running()&&(St.mono_wasm_execute_timer(),Wo++,qo())}function qo(){if(t.maybeExit(),u.is_runtime_running())for(;Wo>0;)--Wo,St.mono_background_exec()}function mono_wasm_schedule_timer_tick(){t.maybeExit(),u.is_runtime_running()&&(zo=void 0,St.mono_wasm_execute_timer())}class Jo{constructor(){this.queue=[],this.offset=0}getLength(){return this.queue.length-this.offset}isEmpty(){return 0==this.queue.length}enqueue(e){this.queue.push(e)}dequeue(){if(0===this.queue.length)return;const e=this.queue[this.offset];return this.queue[this.offset]=null,2*++this.offset>=this.queue.length&&(this.queue=this.queue.slice(this.offset),this.offset=0),e}peek(){return this.queue.length>0?this.queue[this.offset]:void 0}drain(e){for(;this.getLength();)e(this.dequeue())}}const Yo=Symbol.for("wasm ws_pending_send_buffer"),Xo=Symbol.for("wasm ws_pending_send_buffer_offset"),Zo=Symbol.for("wasm ws_pending_send_buffer_type"),Ko=Symbol.for("wasm ws_pending_receive_event_queue"),Qo=Symbol.for("wasm ws_pending_receive_promise_queue"),ea=Symbol.for("wasm ws_pending_open_promise"),ta=Symbol.for("wasm wasm_ws_pending_open_promise_used"),na=Symbol.for("wasm ws_pending_close_promises"),ra=Symbol.for("wasm ws_pending_send_promises"),oa=Symbol.for("wasm ws_is_aborted"),aa=Symbol.for("wasm ws_on_closed"),sa=Symbol.for("wasm wasm_ws_close_sent"),ia=Symbol.for("wasm wasm_ws_close_received"),ca=Symbol.for("wasm ws_receive_status_ptr"),la=65536,ua=new Uint8Array;function pa(e){var t,n;return e.readyState!=WebSocket.CLOSED?null!==(t=e.readyState)&&void 0!==t?t:-1:0==e[Ko].getLength()?null!==(n=e.readyState)&&void 0!==n?n:-1:WebSocket.OPEN}function da(e,t,n,o){!function(){if(s)throw new Error("WebSockets are not supported in shell JS engine.");if("function"!=typeof globalThis.WebSocket)throw new Error(r?"Please install `ws` npm package to enable networking support. See also https://aka.ms/dotnet-wasm-features":"This browser doesn't support WebSocket API. Please use a modern browser. See also https://aka.ms/dotnet-wasm-features")}(),e&&"string"==typeof e||w(!1,"ERR12: Invalid uri "+typeof e),"function"!=typeof o&&w(!1,"ERR12: Invalid onClosed "+typeof o);const a=new globalThis.WebSocket(e,t||void 0),{promise_control:i}=y();a[Ko]=new Jo,a[Qo]=new Jo,a[ea]=i,a[ra]=[],a[na]=[],a[ca]=n,a[aa]=o,a.binaryType="arraybuffer";const c=()=>{a[oa]||u.is_exited()||(i.resolve(a),Ho())},l=e=>{a[oa]||u.is_exited()||(function(e,t){const n=e[Ko],r=e[Qo];if("string"==typeof t.data)n.enqueue({type:0,data:Ke(t.data),offset:0});else{if("ArrayBuffer"!==t.data.constructor.name)throw new Error("ERR19: WebSocket receive expected ArrayBuffer");n.enqueue({type:1,data:new Uint8Array(t.data),offset:0})}if(r.getLength()&&n.getLength()>1)throw new Error("ERR21: Invalid WS state");for(;r.getLength()&&n.getLength();){const t=r.dequeue();ya(e,n,t.buffer_ptr,t.buffer_length),t.resolve()}Ho()}(a,e),Ho())},p=e=>{if(!(a.removeEventListener("message",l),a[oa]||u.is_exited())){a[ia]=!0,o(e.code,e.reason),i.reject(new Error(e.reason));for(const e of a[na])e.resolve();a[Qo].drain((e=>{Z(n,0),Z(n+4,2),Z(n+8,1),e.resolve()})),a[aa].dispose()}},d=e=>{if(a[oa])return;if(u.is_exited())return;a.removeEventListener("message",l);const t=new Error(e.message||"WebSocket error");pt("WebSocket error",t),ga(a,t)};return a.addEventListener("message",l),a.addEventListener("open",c,{once:!0}),a.addEventListener("close",p,{once:!0}),a.addEventListener("error",d,{once:!0}),a.dispose=()=>{a.removeEventListener("message",l),a.removeEventListener("open",c),a.removeEventListener("close",p),a.removeEventListener("error",d),ba(a)},a}function _a(e){e||w(!1,"ERR17: expected ws instance");const t=e[ea];return e[ta]=!0,t.promise}function fa(e,n,r,o,a){if(e||w(!1,"ERR17: expected ws instance"),e[oa]||e[sa])return Promise.reject(new Error("InvalidState: The WebSocket is not connected."));if(e.readyState===WebSocket.CLOSED)return null;const s=function(e,n,r,o){let a=e[Yo],s=0;const i=n.byteLength;if(a){if(s=e[Xo],r=e[Zo],0!==i){if(s+i>a.length){const t=new Uint8Array(1.5*(s+i+50));t.set(a,0),t.subarray(s).set(n),e[Yo]=a=t}else a.subarray(s).set(n);s+=i,e[Xo]=s}}else o?0!==i&&(a=n,s=i):(0!==i&&(a=n.slice(),s=i,e[Xo]=s,e[Yo]=a),e[Zo]=r);return o?0==s||null==a?ua:0===r?function(e){return void 0===Ge?t.UTF8ArrayToString(e,0,e.byteLength):Ge.decode(e)}(it(a,0,s)):a.subarray(0,s):null}(e,new Uint8Array(Ee().buffer,n,r),o,a);return a&&s?function(e,t){if(e.send(t),e[Yo]=null,e.bufferedAmount<la)return null;const{promise:n,promise_control:r}=y(),o=e[ra];o.push(r);let a=1;const s=()=>{if(0===e.bufferedAmount)r.resolve();else{const t=e.readyState;if(t!=WebSocket.OPEN&&t!=WebSocket.CLOSING)r.reject(new Error(`InvalidState: ${t} The WebSocket is not connected.`));else if(!r.isDone)return globalThis.setTimeout(s,a),void(a=Math.min(1.5*a,1e3))}const t=o.indexOf(r);t>-1&&o.splice(t,1)};return globalThis.setTimeout(s,0),n}(e,s):null}function ma(e,t,n){if(e||w(!1,"ERR18: expected ws instance"),e[oa]){const t=e[ca];return Z(t,0),Z(t+4,2),Z(t+8,1),null}const r=e[Ko],o=e[Qo];if(r.getLength())return 0!=o.getLength()&&w(!1,"ERR20: Invalid WS state"),ya(e,r,t,n),null;if(e[ia]){const t=e[ca];return Z(t,0),Z(t+4,2),Z(t+8,1),null}const{promise:a,promise_control:s}=y(),i=s;return i.buffer_ptr=t,i.buffer_length=n,o.enqueue(i),a}function ha(e,t,n,r){if(e||w(!1,"ERR19: expected ws instance"),e[oa]||e[sa]||e.readyState==WebSocket.CLOSED)return null;if(e[sa]=!0,r){const{promise:r,promise_control:o}=y();return e[na].push(o),"string"==typeof n?e.close(t,n):e.close(t),r}return"string"==typeof n?e.close(t,n):e.close(t),null}function ba(e){var t;if(e||w(!1,"ERR18: expected ws instance"),!e[oa]&&!e[sa]){e[oa]=!0,ga(e,new Error("OperationCanceledException")),null===(t=e[aa])||void 0===t||t.dispose();try{e.close(1e3,"Connection was aborted.")}catch(e){pt("WebSocket error while aborting",e)}}}function ga(e,t){const n=e[ea],r=e[ta];n&&r&&n.reject(t);for(const n of e[na])n.reject(t);for(const n of e[ra])n.reject(t);e[Qo].drain((e=>{e.reject(t)}))}function ya(e,t,n,r){const o=t.peek(),a=Math.min(r,o.data.length-o.offset);if(a>0){const e=o.data.subarray(o.offset,o.offset+a);new Uint8Array(Ee().buffer,n,r).set(e,0),o.offset+=a}const s=o.data.length===o.offset?1:0;s&&t.dequeue();const i=e[ca];Z(i,a),Z(i+4,o.type),Z(i+8,s)}function wa(e){return 1===St.mono_wasm_load_icu_data(e)}function Sa(e,n,r){lt(`Loaded:${e.name} as ${e.behavior} size ${r.length} from ${n}`);const o=Jt(),a="string"==typeof e.virtualPath?e.virtualPath:e.name;let s=null;switch(e.behavior){case"dotnetwasm":case"js-module-threads":case"symbols":break;case"resource":case"assembly":case"pdb":u._loaded_files.push({url:n,file:a});case"heap":case"icu":s=ye(r);break;case"vfs":{const e=a.lastIndexOf("/");let n=e>0?a.substr(0,e):null,o=e>0?a.substr(e+1):a;o.startsWith("/")&&(o=o.substr(1)),n?(lt(`Creating directory '${n}'`),t.FS_createPath("/",n,!0,!0)):n="/",lt(`Creating file '${o}' in directory '${n}'`),t.FS_createDataFile(n,o,r,!0,!0,!0);break}default:throw new Error(`Unrecognized asset behavior:${e.behavior}, for asset ${e.name}`)}if("assembly"===e.behavior){if(!St.mono_wasm_add_assembly(a,s,r.length)){const e=u._loaded_files.findIndex((e=>e.file==a));u._loaded_files.splice(e,1)}}else"pdb"===e.behavior?St.mono_wasm_add_assembly(a,s,r.length):"icu"===e.behavior?wa(s)||t.err(`Error loading ICU asset ${e.name}`):"resource"===e.behavior&&St.mono_wasm_add_satellite_assembly(a,e.culture||"",s,r.length);Yt(o,"mono.instantiateAsset:",e.name),++u.actual_instantiated_assets_count}async function ka(e){try{const t=await e.pendingDownloadInternal.response;(await t.text()).split(/[\r\n]/).forEach((e=>{const t=e.split(/:/);t.length<2||(t[1]=t.splice(1).join(":"),_t.set(Number(t[0]),t[1]))})),lt(`Loaded ${_t.size} symbols`)}catch(t){ut(`Error loading symbol file ${e.name}: ${JSON.stringify(t)}`)}}function va(){return u.loadedFiles}const Ea={};function Ua(e){let t=Ea[e];if("string"!=typeof t){const n=St.mono_jiterp_get_opcode_info(e,0);Ea[e]=t=Qe(n)}return t}const Ia=2,xa=64,Ta=64,ja=["Unknown","InterpreterTiering","NullCheck","VtableNotInitialized","Branch","BackwardBranch","ConditionalBranch","ConditionalBackwardBranch","ComplexBranch","ArrayLoadFailed","ArrayStoreFailed","StringOperationFailed","DivideByZero","Overflow","Return","Call","Throw","AllocFailed","SpanOperationFailed","CastFailed","SafepointBranchTaken","UnboxFailed","CallDelegate","Debugging","Icall","UnexpectedRetIp","LeaveCheck"],Aa={};class Ra{constructor(e){this.locals=new Map,this.permanentFunctionTypeCount=0,this.permanentFunctionTypes={},this.permanentFunctionTypesByShape={},this.permanentFunctionTypesByIndex={},this.functionTypesByIndex={},this.permanentImportedFunctionCount=0,this.permanentImportedFunctions={},this.nextImportIndex=0,this.functions=[],this.estimatedExportBytes=0,this.frame=0,this.traceBuf=[],this.branchTargets=new Set,this.constantSlots=[],this.backBranchOffsets=[],this.callHandlerReturnAddresses=[],this.nextConstantSlot=0,this.compressImportNames=!1,this.lockImports=!1,this._assignParameterIndices=e=>{let t=0;for(const n in e)this.locals.set(n,t),t++;return t},this.stack=[new La],this.clear(e),this.cfg=new $a(this)}clear(e){this.options=cs(),this.stackSize=1,this.inSection=!1,this.inFunction=!1,this.lockImports=!1,this.locals.clear(),this.functionTypeCount=this.permanentFunctionTypeCount,this.functionTypes=Object.create(this.permanentFunctionTypes),this.functionTypesByShape=Object.create(this.permanentFunctionTypesByShape),this.functionTypesByIndex=Object.create(this.permanentFunctionTypesByIndex),this.nextImportIndex=0,this.importedFunctionCount=0,this.importedFunctions=Object.create(this.permanentImportedFunctions);for(const e in this.importedFunctions)this.importedFunctions[e].index=void 0;this.functions.length=0,this.estimatedExportBytes=0,this.argumentCount=0,this.current.clear(),this.traceBuf.length=0,this.branchTargets.clear(),this.activeBlocks=0,this.nextConstantSlot=0,this.constantSlots.length=this.options.useConstants?e:0;for(let e=0;e<this.constantSlots.length;e++)this.constantSlots[e]=0;this.backBranchOffsets.length=0,this.callHandlerReturnAddresses.length=0,this.allowNullCheckOptimization=this.options.eliminateNullChecks}_push(){this.stackSize++,this.stackSize>=this.stack.length&&this.stack.push(new La),this.current.clear()}_pop(e){if(this.stackSize<=1)throw new Error("Stack empty");const t=this.current;return this.stackSize--,e?(this.appendULeb(t.size),t.copyTo(this.current),null):t.getArrayView(!1).slice(0,t.size)}getWasmImports(){const e=t.getMemory();e instanceof WebAssembly.Memory||w(!1,`expected heap import to be WebAssembly.Memory but was ${e}`);const n={c:this.getConstants(),m:{h:e}},r=this.getImportsToEmit();for(let e=0;e<r.length;e++){const t=r[e];if("function"!=typeof t.func)throw new Error(`Import '${t.name}' not found or not a function`);const o=this.getCompressedName(t);let a=n[t.module];a||(a=n[t.module]={}),a[o]=t.func}return n}get bytesGeneratedSoFar(){const e=this.compressImportNames?8:20;return this.stack[0].size+32+this.importedFunctionCount*e+2*this.functions.length+this.estimatedExportBytes}get current(){return this.stack[this.stackSize-1]}get size(){return this.current.size}appendU8(e){if(e!=e>>>0||e>255)throw new Error(`Byte out of range: ${e}`);return this.current.appendU8(e)}appendSimd(e,t){return this.current.appendU8(253),0!=(0|e)||0===e&&!0===t||w(!1,"Expected non-v128_load simd opcode or allowLoad==true"),this.current.appendULeb(e)}appendU32(e){return this.current.appendU32(e)}appendF32(e){return this.current.appendF32(e)}appendF64(e){return this.current.appendF64(e)}appendBoundaryValue(e,t){return this.current.appendBoundaryValue(e,t)}appendULeb(e){return this.current.appendULeb(e)}appendLeb(e){return this.current.appendLeb(e)}appendLebRef(e,t){return this.current.appendLebRef(e,t)}appendBytes(e){return this.current.appendBytes(e)}appendName(e){return this.current.appendName(e)}ret(e){this.ip_const(e),this.appendU8(15)}i32_const(e){this.appendU8(65),this.appendLeb(e)}ptr_const(e){let t=this.options.useConstants?this.constantSlots.indexOf(e):-1;this.options.useConstants&&t<0&&this.nextConstantSlot<this.constantSlots.length&&(t=this.nextConstantSlot++,this.constantSlots[t]=e),t>=0?(this.appendU8(35),this.appendLeb(t)):this.i32_const(e)}ip_const(e){this.appendU8(65),this.appendLeb(e-this.base)}i52_const(e){this.appendU8(66),this.appendLeb(e)}v128_const(e){if(0===e)this.local("v128_zero");else{if("object"!=typeof e)throw new Error("Expected v128_const arg to be 0 or a Uint8Array");{16!==e.byteLength&&w(!1,"Expected v128_const arg to be 16 bytes in size");let t=!0;for(let n=0;n<16;n++)0!==e[n]&&(t=!1);t?this.local("v128_zero"):(this.appendSimd(12),this.appendBytes(e))}}}defineType(e,t,n,r){if(this.functionTypes[e])throw new Error(`Function type ${e} already defined`);if(r&&this.functionTypeCount>this.permanentFunctionTypeCount)throw new Error("New permanent function types cannot be defined after non-permanent ones");let o="";for(const e in t)o+=t[e]+",";o+=n;let a=this.functionTypesByShape[o];"number"!=typeof a&&(a=this.functionTypeCount++,r?(this.permanentFunctionTypeCount++,this.permanentFunctionTypesByShape[o]=a,this.permanentFunctionTypesByIndex[a]=[t,Object.values(t).length,n]):(this.functionTypesByShape[o]=a,this.functionTypesByIndex[a]=[t,Object.values(t).length,n]));const s=[a,t,n,`(${JSON.stringify(t)}) -> ${n}`,r];return r?this.permanentFunctionTypes[e]=s:this.functionTypes[e]=s,a}generateTypeSection(){this.beginSection(1),this.appendULeb(this.functionTypeCount);for(let e=0;e<this.functionTypeCount;e++){const t=this.functionTypesByIndex[e][0],n=this.functionTypesByIndex[e][1],r=this.functionTypesByIndex[e][2];this.appendU8(96),this.appendULeb(n);for(const e in t)this.appendU8(t[e]);64!==r?(this.appendULeb(1),this.appendU8(r)):this.appendULeb(0)}this.endSection()}getImportedFunctionTable(){const e={};for(const t in this.importedFunctions){const n=this.importedFunctions[t];e[this.getCompressedName(n)]=n.func}return e}getCompressedName(e){if(!this.compressImportNames||"number"!=typeof e.index)return e.name;let t=Aa[e.index];return"string"!=typeof t&&(Aa[e.index]=t=e.index.toString(36)),t}getImportsToEmit(){const e=[];for(const t in this.importedFunctions){const n=this.importedFunctions[t];"number"==typeof n.index&&e.push(n)}return e.sort(((e,t)=>e.index-t.index)),e}_generateImportSection(e){const t=this.getImportsToEmit();if(this.lockImports=!0,!1!==e)throw new Error("function table imports are disabled");this.beginSection(2),this.appendULeb(1+t.length+this.constantSlots.length+(!1!==e?1:0));for(let e=0;e<t.length;e++){const n=t[e];this.appendName(n.module),this.appendName(this.getCompressedName(n)),this.appendU8(0),this.appendU8(n.typeIndex)}for(let e=0;e<this.constantSlots.length;e++)this.appendName("c"),this.appendName(e.toString(36)),this.appendU8(3),this.appendU8(127),this.appendU8(0);this.appendName("m"),this.appendName("h"),this.appendU8(2),this.appendU8(0),this.appendULeb(1),!1!==e&&(this.appendName("f"),this.appendName("f"),this.appendU8(1),this.appendU8(112),this.appendU8(0),this.appendULeb(1))}defineImportedFunction(e,t,n,r,o){if(this.lockImports)throw new Error("Import section already generated");if(r&&this.importedFunctionCount>0)throw new Error("New permanent imports cannot be defined after any indexes have been assigned");const a=this.functionTypes[n];if(!a)throw new Error("No function type named "+n);if(r&&!a[4])throw new Error("A permanent import must have a permanent function type");const s=a[0],i=r?this.permanentImportedFunctions:this.importedFunctions;if("number"==typeof o&&(o=Va().get(o)),"function"!=typeof o&&void 0!==o)throw new Error(`Value passed for imported function ${t} was not a function or valid function pointer or undefined`);return i[t]={index:void 0,typeIndex:s,module:e,name:t,func:o}}markImportAsUsed(e){const t=this.importedFunctions[e];if(!t)throw new Error("No imported function named "+e);"number"!=typeof t.index&&(t.index=this.importedFunctionCount++)}defineFunction(e,t){const n={index:this.functions.length,name:e.name,typeName:e.type,typeIndex:this.functionTypes[e.type][0],export:e.export,locals:e.locals,generator:t,error:null,blob:null};return this.functions.push(n),n.export&&(this.estimatedExportBytes+=n.name.length+8),n}emitImportsAndFunctions(e){let t=0;for(let e=0;e<this.functions.length;e++){const n=this.functions[e];n.export&&t++,this.beginFunction(n.typeName,n.locals);try{n.blob=n.generator()}finally{try{n.blob||(n.blob=this.endFunction(!1))}catch(e){}}}this._generateImportSection(e),this.beginSection(3),this.appendULeb(this.functions.length);for(let e=0;e<this.functions.length;e++)this.appendULeb(this.functions[e].typeIndex);this.beginSection(7),this.appendULeb(t);for(let e=0;e<this.functions.length;e++){const t=this.functions[e];t.export&&(this.appendName(t.name),this.appendU8(0),this.appendULeb(this.importedFunctionCount+e))}this.beginSection(10),this.appendULeb(this.functions.length);for(let e=0;e<this.functions.length;e++){const t=this.functions[e];t.blob||w(!1,`expected function ${t.name} to have a body`),this.appendULeb(t.blob.length),this.appendBytes(t.blob)}this.endSection()}call_indirect(){throw new Error("call_indirect unavailable")}callImport(e){const t=this.importedFunctions[e];if(!t)throw new Error("No imported function named "+e);if("number"!=typeof t.index){if(this.lockImports)throw new Error("Import section was emitted before assigning an index to import named "+e);t.index=this.importedFunctionCount++}this.appendU8(16),this.appendULeb(t.index)}beginSection(e){this.inSection&&this._pop(!0),this.appendU8(e),this._push(),this.inSection=!0}endSection(){if(!this.inSection)throw new Error("Not in section");this.inFunction&&this.endFunction(!0),this._pop(!0),this.inSection=!1}_assignLocalIndices(e,t,n,r){e[127]=0,e[126]=0,e[125]=0,e[124]=0,e[123]=0;for(const n in t){const o=t[n];e[o]<=0&&r++,e[o]++}const o=e[127],a=o+e[126],s=a+e[125],i=s+e[124];e[127]=0,e[126]=0,e[125]=0,e[124]=0,e[123]=0;for(const r in t){const c=t[r];let l,u=0;switch(c){case 127:l=0;break;case 126:l=o;break;case 125:l=a;break;case 124:l=s;break;case 123:l=i;break;default:throw new Error(`Unimplemented valtype: ${c}`)}u=e[c]+++l+n,this.locals.set(r,u)}return r}beginFunction(e,t){if(this.inFunction)throw new Error("Already in function");this._push();const n=this.functionTypes[e];this.locals.clear(),this.branchTargets.clear();let r={};const o=[127,126,125,124,123];let a=0;const s=this._assignParameterIndices(n[1]);t?a=this._assignLocalIndices(r,t,s,a):r={},this.appendULeb(a);for(let e=0;e<o.length;e++){const t=o[e],n=r[t];n&&(this.appendULeb(n),this.appendU8(t))}this.inFunction=!0}endFunction(e){if(!this.inFunction)throw new Error("Not in function");if(this.activeBlocks>0)throw new Error(`${this.activeBlocks} unclosed block(s) at end of function`);const t=this._pop(e);return this.inFunction=!1,t}block(e,t){const n=this.appendU8(t||2);return e?this.appendU8(e):this.appendU8(64),this.activeBlocks++,n}endBlock(){if(this.activeBlocks<=0)throw new Error("No blocks active");this.activeBlocks--,this.appendU8(11)}arg(e,t){const n="string"==typeof e?this.locals.has(e)?this.locals.get(e):void 0:e;if("number"!=typeof n)throw new Error("No local named "+e);t&&this.appendU8(t),this.appendULeb(n)}local(e,t){const n="string"==typeof e?this.locals.has(e)?this.locals.get(e):void 0:e+this.argumentCount;if("number"!=typeof n)throw new Error("No local named "+e);t?this.appendU8(t):this.appendU8(32),this.appendULeb(n)}appendMemarg(e,t){this.appendULeb(t),this.appendULeb(e)}lea(e,t){"string"==typeof e?this.local(e):this.i32_const(e),this.i32_const(t),this.appendU8(106)}getArrayView(e){if(this.stackSize>1)throw new Error("Jiterpreter block stack not empty");return this.stack[0].getArrayView(e)}getConstants(){const e={};for(let t=0;t<this.constantSlots.length;t++)e[t.toString(36)]=this.constantSlots[t];return e}}class La{constructor(){this.textBuf=new Uint8Array(1024),this.capacity=16384,this.buffer=t._malloc(this.capacity),Ee().fill(0,this.buffer,this.buffer+this.capacity),this.size=0,this.clear(),"function"==typeof TextEncoder&&(this.encoder=new TextEncoder)}clear(){this.size=0}appendU8(e){if(this.size>=this.capacity)throw new Error("Buffer full");const t=this.size;return Ee()[this.buffer+this.size++]=e,t}appendU32(e){const t=this.size;return St.mono_jiterp_write_number_unaligned(this.buffer+this.size,e,0),this.size+=4,t}appendI32(e){const t=this.size;return St.mono_jiterp_write_number_unaligned(this.buffer+this.size,e,1),this.size+=4,t}appendF32(e){const t=this.size;return St.mono_jiterp_write_number_unaligned(this.buffer+this.size,e,2),this.size+=4,t}appendF64(e){const t=this.size;return St.mono_jiterp_write_number_unaligned(this.buffer+this.size,e,3),this.size+=8,t}appendBoundaryValue(e,t){if(this.size+8>=this.capacity)throw new Error("Buffer full");const n=St.mono_jiterp_encode_leb_signed_boundary(this.buffer+this.size,e,t);if(n<1)throw new Error(`Failed to encode ${e} bit boundary value with sign ${t}`);return this.size+=n,n}appendULeb(e){if("number"!=typeof e&&w(!1,`appendULeb expected number but got ${e}`),e>=0||w(!1,"cannot pass negative value to appendULeb"),e<127){if(this.size+1>=this.capacity)throw new Error("Buffer full");return this.appendU8(e),1}if(this.size+8>=this.capacity)throw new Error("Buffer full");const t=St.mono_jiterp_encode_leb52(this.buffer+this.size,e,0);if(t<1)throw new Error(`Failed to encode value '${e}' as unsigned leb`);return this.size+=t,t}appendLeb(e){if("number"!=typeof e&&w(!1,`appendLeb expected number but got ${e}`),this.size+8>=this.capacity)throw new Error("Buffer full");const t=St.mono_jiterp_encode_leb52(this.buffer+this.size,e,1);if(t<1)throw new Error(`Failed to encode value '${e}' as signed leb`);return this.size+=t,t}appendLebRef(e,t){if(this.size+8>=this.capacity)throw new Error("Buffer full");const n=St.mono_jiterp_encode_leb64_ref(this.buffer+this.size,e,t?1:0);if(n<1)throw new Error("Failed to encode value as leb");return this.size+=n,n}copyTo(e,t){"number"!=typeof t&&(t=this.size),Ee().copyWithin(e.buffer+e.size,this.buffer,this.buffer+t),e.size+=t}appendBytes(e,t){const n=this.size,r=Ee();return e.buffer===r.buffer?("number"!=typeof t&&(t=e.length),r.copyWithin(this.buffer+n,e.byteOffset,e.byteOffset+t),this.size+=t):("number"==typeof t&&(e=new Uint8Array(e.buffer,e.byteOffset,t)),this.getArrayView(!0).set(e,this.size),this.size+=e.length),n}appendName(e){let t=e.length,n=1===e.length?e.charCodeAt(0):-1;if(n>127&&(n=-1),t&&n<0)if(this.encoder)t=this.encoder.encodeInto(e,this.textBuf).written||0;else for(let n=0;n<t;n++){const t=e.charCodeAt(n);if(t>127)throw new Error("Out of range character and no TextEncoder available");this.textBuf[n]=t}this.appendULeb(t),n>=0?this.appendU8(n):t>1&&this.appendBytes(this.textBuf,t)}getArrayView(e){return new Uint8Array(Ee().buffer,this.buffer,e?this.capacity:this.size)}}class $a{constructor(e){this.segments=[],this.backBranchTargets=null,this.lastSegmentEnd=0,this.overheadBytes=0,this.blockStack=[],this.backDispatchOffsets=[],this.dispatchTable=new Map,this.observedBranchTargets=new Set,this.trace=0,this.builder=e}initialize(e,t,n){this.segments.length=0,this.blockStack.length=0,this.startOfBody=e,this.backBranchTargets=t,this.base=this.builder.base,this.ip=this.lastSegmentStartIp=this.builder.base,this.lastSegmentEnd=0,this.overheadBytes=10,this.dispatchTable.clear(),this.observedBranchTargets.clear(),this.trace=n,this.backDispatchOffsets.length=0}entry(e){this.entryIp=e,this.appendBlob(),1!==this.segments.length&&w(!1,"expected 1 segment"),"blob"!==this.segments[0].type&&w(!1,"expected blob"),this.entryBlob=this.segments[0],this.segments.length=0,this.overheadBytes+=9,this.backBranchTargets&&(this.overheadBytes+=20,this.overheadBytes+=this.backBranchTargets.length)}appendBlob(){this.builder.current.size!==this.lastSegmentEnd&&(this.segments.push({type:"blob",ip:this.lastSegmentStartIp,start:this.lastSegmentEnd,length:this.builder.current.size-this.lastSegmentEnd}),this.lastSegmentStartIp=this.ip,this.lastSegmentEnd=this.builder.current.size,this.overheadBytes+=2)}startBranchBlock(e,t){this.appendBlob(),this.segments.push({type:"branch-block-header",ip:e,isBackBranchTarget:t}),this.overheadBytes+=1}branch(e,t,n){this.observedBranchTargets.add(e),this.appendBlob(),this.segments.push({type:"branch",from:this.ip,target:e,isBackward:t,branchType:n}),this.overheadBytes+=4,t&&(this.overheadBytes+=11),3!==n&&2!==n||(this.overheadBytes+=17)}emitBlob(e,t){const n=t.subarray(e.start,e.start+e.length);this.builder.appendBytes(n)}generate(){this.appendBlob();const e=this.builder.endFunction(!1);this.builder._push(),this.builder.base=this.base,this.emitBlob(this.entryBlob,e),this.backBranchTargets&&(this.builder.i32_const(0),this.builder.local("disp",33),this.builder.block(64,3));for(let e=0;e<this.segments.length;e++){const t=this.segments[e];"branch-block-header"===t.type&&this.blockStack.push(t.ip)}this.blockStack.sort(((e,t)=>e-t));for(let e=0;e<this.blockStack.length;e++)this.builder.block(64);if(this.backBranchTargets){this.backDispatchOffsets.length=0;for(let e=0;e<this.backBranchTargets.length;e++){const t=2*this.backBranchTargets[e]+this.startOfBody;this.blockStack.indexOf(t)<0||this.observedBranchTargets.has(t)&&(this.dispatchTable.set(t,this.backDispatchOffsets.length+1),this.backDispatchOffsets.push(t))}if(0===this.backDispatchOffsets.length)this.trace>0&&ut("No back branch targets were reachable after filtering");else if(1===this.backDispatchOffsets.length)this.trace>0&&(this.backDispatchOffsets[0]===this.entryIp?ut(`Exactly one back dispatch offset and it was the entry point 0x${this.entryIp.toString(16)}`):ut(`Exactly one back dispatch offset and it was 0x${this.backDispatchOffsets[0].toString(16)}`)),this.builder.local("disp"),this.builder.appendU8(13),this.builder.appendULeb(this.blockStack.indexOf(this.backDispatchOffsets[0]));else{this.builder.block(64),this.builder.block(64),this.builder.local("disp"),this.builder.appendU8(14),this.builder.appendULeb(this.backDispatchOffsets.length+1),this.builder.appendULeb(1);for(let e=0;e<this.backDispatchOffsets.length;e++)this.builder.appendULeb(this.blockStack.indexOf(this.backDispatchOffsets[e])+2);this.builder.appendULeb(0),this.builder.endBlock(),this.builder.appendU8(0),this.builder.endBlock()}this.backDispatchOffsets.length>0&&this.blockStack.push(0)}this.trace>1&&ut(`blockStack=${this.blockStack}`);for(let t=0;t<this.segments.length;t++){const n=this.segments[t];switch(n.type){case"blob":this.emitBlob(n,e);break;case"branch-block-header":{const e=this.blockStack.indexOf(n.ip);0!==e&&w(!1,`expected ${n.ip} on top of blockStack but found it at index ${e}, top is ${this.blockStack[0]}`),this.builder.endBlock(),this.blockStack.shift();break}case"branch":{const e=n.isBackward?0:n.target;let t=this.blockStack.indexOf(e),r=!1;if(n.isBackward)if(this.dispatchTable.has(n.target)){const e=this.dispatchTable.get(n.target);this.trace>1&&ut(`backward br from ${n.from.toString(16)} to ${n.target.toString(16)}: disp=${e}`),this.builder.i32_const(1),this.builder.local("backbranched",33),this.builder.i32_const(e),this.builder.local("disp",33),r=!0}else this.trace>0&&ut(`br from ${n.from.toString(16)} to ${n.target.toString(16)} failed: back branch target not in dispatch table`),t=-1;if(t>=0||r){let e=0;switch(n.branchType){case 2:Fa(this.builder,n.from),this.builder.appendU8(12);break;case 3:this.builder.block(64,4),Fa(this.builder,n.from),this.builder.appendU8(12),e=1;break;case 0:this.builder.appendU8(12);break;case 1:this.builder.appendU8(13);break;default:throw new Error("Unimplemented branch type")}this.builder.appendULeb(e+t),e&&this.builder.endBlock(),this.trace>1&&ut(`br from ${n.from.toString(16)} to ${n.target.toString(16)} breaking out ${e+t+1} level(s)`)}else{if(this.trace>0){const e=this.base;n.target>=e&&n.target<this.exitIp?ut(`br from ${n.from.toString(16)} to ${n.target.toString(16)} failed (inside of trace!)`):this.trace>1&&ut(`br from ${n.from.toString(16)} to ${n.target.toString(16)} failed (outside of trace 0x${e.toString(16)} - 0x${this.exitIp.toString(16)})`)}const e=1===n.branchType||3===n.branchType;e&&this.builder.block(64,4),Pa(this.builder,n.target,4),e&&this.builder.endBlock()}break}default:throw new Error("unreachable")}}return this.backBranchTargets&&(this.blockStack.length<=1||w(!1,"expected one or zero entries in the block stack at the end"),this.blockStack.length&&this.blockStack.shift(),this.builder.endBlock()),0!==this.blockStack.length&&w(!1,`expected block stack to be empty at end of function but it was ${this.blockStack}`),this.builder.ip_const(this.exitIp),this.builder.appendU8(15),this.builder.appendU8(11),this.builder._pop(!1)}}let Ca,Na=-1,Da=0;const Ba={generation:0,compilation:0},Oa={traceCandidates:0,tracesCompiled:0,entryWrappersCompiled:0,jitCallsCompiled:0,directJitCallsCompiled:0,failures:0,bytesGenerated:0,nullChecksEliminated:0,nullChecksFused:0,backBranchesEmitted:0,backBranchesNotEmitted:0,simdFallback:{}},Ma=globalThis.performance&&globalThis.performance.now?globalThis.performance.now.bind(globalThis.performance):Date.now;function Fa(e,t){e.ptr_const(St.mono_jiterp_get_polling_required_address()),e.appendU8(40),e.appendMemarg(0,2),e.block(64,4),e.local("frame"),e.i32_const(t),e.callImport("safepoint"),e.endBlock()}function Pa(e,t,n){e.ip_const(t),e.options.countBailouts&&(e.i32_const(e.base),e.i32_const(n),e.callImport("bailout")),e.appendU8(15)}function za(e,t,n,r){n<=e.options.monitoringLongDistance+2&&(e.local("cinfo"),e.i32_const(n),e.appendU8(54),e.appendMemarg(4,0),e.local("cinfo"),e.local("backbranched"),e.appendU8(54),e.appendMemarg(0,0)),e.ip_const(t),e.options.countBailouts&&(e.i32_const(e.base),e.i32_const(r),e.callImport("bailout")),e.appendU8(15)}function Va(){if(Ca||(Ca=t.getWasmIndirectFunctionTable()),!Ca)throw new Error("Module did not export the indirect function table");return Ca}function Wa(e){e||w(!1,"Attempting to set null function into table"),l.storeMemorySnapshotPending&&w(!1,"Attempting to set function into table during creation of memory snapshot");const t=Va();Da<=0&&(Na=t.length,Da=512,t.grow(Da));const n=Na;return Na++,Da--,t.set(n,e),n}function Ha(e,t,n,r,o){if(r<=0)return o&&e.appendU8(26),!0;if(r>=xa)return!1;if(0!==n)return!1;const a=o?"memop_dest":"pLocals";o&&e.local(a,33);let s=o?0:t;if(e.options.enableSimd){const t=16;for(;r>=t;)e.local(a),e.v128_const(0),e.appendSimd(11),e.appendMemarg(s,0),s+=t,r-=t}for(;r>=8;)e.local(a),e.i52_const(0),e.appendU8(55),e.appendMemarg(s,0),s+=8,r-=8;for(;r>=1;){e.local(a),e.i32_const(0);let t=r%4;switch(t){case 0:t=4,e.appendU8(54);break;case 1:e.appendU8(58);break;case 3:case 2:t=2,e.appendU8(59)}e.appendMemarg(s,0),s+=t,r-=t}return!0}function Ga(e,t,n){Ha(e,0,t,n,!0)||(e.i32_const(t),e.i32_const(n),e.appendU8(252),e.appendU8(11),e.appendU8(0))}function qa(e,t,n,r,o,a,s){if(r<=0)return o&&(e.appendU8(26),e.appendU8(26)),!0;if(r>=Ta)return!1;o?(a=a||"memop_dest",s=s||"memop_src",e.local(s,33),e.local(a,33)):a&&s||(a=s="pLocals");let i=o?0:t,c=o?0:n;if(e.options.enableSimd){const t=16;for(;r>=t;)e.local(a),e.local(s),e.appendSimd(0,!0),e.appendMemarg(c,0),e.appendSimd(11),e.appendMemarg(i,0),i+=t,c+=t,r-=t}for(;r>=8;)e.local(a),e.local(s),e.appendU8(41),e.appendMemarg(c,0),e.appendU8(55),e.appendMemarg(i,0),i+=8,c+=8,r-=8;for(;r>=1;){let t,n,o=r%4;switch(o){case 0:o=4,t=40,n=54;break;default:case 1:o=1,t=44,n=58;break;case 3:case 2:o=2,t=46,n=59}e.local(a),e.local(s),e.appendU8(t),e.appendMemarg(c,0),e.appendU8(n),e.appendMemarg(i,0),c+=o,i+=o,r-=o}return!0}function Ja(e,t){return qa(e,0,0,t,!0)||(e.i32_const(t),e.appendU8(252),e.appendU8(10),e.appendU8(0),e.appendU8(0)),!0}function Ya(){Oa.failures++,Oa.failures>=Ia&&(ut(`Disabling jiterpreter after ${Oa.failures} failures`),is({enableTraces:!1,enableInterpEntry:!1,enableJitCall:!1}))}const Xa={};function Za(e){const t=Xa[e];return void 0===t?Xa[e]=St.mono_jiterp_get_member_offset(e):t}function Ka(e){const n=t.asm[e];if("function"!=typeof n)throw new Error(`raw cwrap ${e} not found`);return n}const Qa={};function es(e){let t=Qa[e];return"number"!=typeof t&&(t=Qa[e]=St.mono_jiterp_get_opcode_value_table_entry(e)),t}function ts(e,t){return[e,e,t]}let ns;function rs(){if(!St.mono_wasm_is_zero_page_reserved())return!1;if(!0===ns)return!1;const e=Ie();for(let t=0;t<8;t++)if(0!==e[t])return!1===ns&&dt(`Zero page optimizations are enabled but garbage appeared in memory at address ${4*t}: ${e[t]}`),ns=!0,!1;return ns=!1,!0}const os={enableTraces:"jiterpreter-traces-enabled",enableInterpEntry:"jiterpreter-interp-entry-enabled",enableJitCall:"jiterpreter-jit-call-enabled",enableBackwardBranches:"jiterpreter-backward-branch-entries-enabled",enableCallResume:"jiterpreter-call-resume-enabled",enableWasmEh:"jiterpreter-wasm-eh-enabled",enableSimd:"jiterpreter-simd-enabled",zeroPageOptimization:"jiterpreter-zero-page-optimization",enableStats:"jiterpreter-stats-enabled",disableHeuristic:"jiterpreter-disable-heuristic",estimateHeat:"jiterpreter-estimate-heat",countBailouts:"jiterpreter-count-bailouts",dumpTraces:"jiterpreter-dump-traces",useConstants:"jiterpreter-use-constants",eliminateNullChecks:"jiterpreter-eliminate-null-checks",noExitBackwardBranches:"jiterpreter-backward-branches-enabled",directJitCalls:"jiterpreter-direct-jit-calls",minimumTraceValue:"jiterpreter-minimum-trace-value",minimumTraceHitCount:"jiterpreter-minimum-trace-hit-count",monitoringPeriod:"jiterpreter-trace-monitoring-period",monitoringShortDistance:"jiterpreter-trace-monitoring-short-distance",monitoringLongDistance:"jiterpreter-trace-monitoring-long-distance",monitoringMaxAveragePenalty:"jiterpreter-trace-monitoring-max-average-penalty",backBranchBoost:"jiterpreter-back-branch-boost",jitCallHitCount:"jiterpreter-jit-call-hit-count",jitCallFlushThreshold:"jiterpreter-jit-call-queue-flush-threshold",interpEntryHitCount:"jiterpreter-interp-entry-hit-count",interpEntryFlushThreshold:"jiterpreter-interp-entry-queue-flush-threshold",wasmBytesLimit:"jiterpreter-wasm-bytes-limit"};let as=-1,ss={};function is(e){for(const t in e){const n=os[t];if(!n){dt(`Unrecognized jiterpreter option: ${t}`);continue}const r=e[t];"boolean"==typeof r?St.mono_jiterp_parse_option((r?"--":"--no-")+n):"number"==typeof r?St.mono_jiterp_parse_option(`--${n}=${r}`):dt(`Jiterpreter option must be a boolean or a number but was ${typeof r} '${r}'`)}}function cs(){const e=St.mono_jiterp_get_options_version();return e!==as&&(function(){const e=St.mono_jiterp_get_options_as_json(),n=Qe(e);t._free(e);const r=JSON.parse(n);ss={};for(const e in os){const t=os[e];ss[e]=r[t]}}(),as=e),ss}const ls={2:["V128_I1_NEGATION","V128_I2_NEGATION","V128_I4_NEGATION","V128_ONES_COMPLEMENT","V128_U2_WIDEN_LOWER","V128_U2_WIDEN_UPPER","V128_I1_CREATE_SCALAR","V128_I2_CREATE_SCALAR","V128_I4_CREATE_SCALAR","V128_I8_CREATE_SCALAR","V128_I1_EXTRACT_MSB","V128_I2_EXTRACT_MSB","V128_I4_EXTRACT_MSB","V128_I8_EXTRACT_MSB","V128_I1_CREATE","V128_I2_CREATE","V128_I4_CREATE","V128_I8_CREATE","SplatX1","SplatX2","SplatX4","SplatX8","NegateD1","NegateD2","NegateD4","NegateD8","NegateR4","NegateR8","SqrtR4","SqrtR8","CeilingR4","CeilingR8","FloorR4","FloorR8","TruncateR4","TruncateR8","RoundToNearestR4","RoundToNearestR8","NotANY","AnyTrueANY","AllTrueD1","AllTrueD2","AllTrueD4","AllTrueD8","PopCountU1","BitmaskD1","BitmaskD2","BitmaskD4","BitmaskD8","AddPairwiseWideningI1","AddPairwiseWideningU1","AddPairwiseWideningI2","AddPairwiseWideningU2","AbsI1","AbsI2","AbsI4","AbsI8","AbsR4","AbsR8","ConvertToSingleI4","ConvertToSingleU4","ConvertToSingleR8","ConvertToDoubleLowerI4","ConvertToDoubleLowerU4","ConvertToDoubleLowerR8","ConvertToInt32SaturateR4","ConvertToUInt32SaturateR4","ConvertToInt32SaturateR8","ConvertToUInt32SaturateR8","SignExtendWideningLowerD1","SignExtendWideningLowerD2","SignExtendWideningLowerD4","SignExtendWideningUpperD1","SignExtendWideningUpperD2","SignExtendWideningUpperD4","ZeroExtendWideningLowerD1","ZeroExtendWideningLowerD2","ZeroExtendWideningLowerD4","ZeroExtendWideningUpperD1","ZeroExtendWideningUpperD2","ZeroExtendWideningUpperD4","LoadVector128ANY","LoadScalarVector128X4","LoadScalarVector128X8","LoadScalarAndSplatVector128X1","LoadScalarAndSplatVector128X2","LoadScalarAndSplatVector128X4","LoadScalarAndSplatVector128X8","LoadWideningVector128I1","LoadWideningVector128U1","LoadWideningVector128I2","LoadWideningVector128U2","LoadWideningVector128I4","LoadWideningVector128U4"],3:["V128_I1_ADD","V128_I2_ADD","V128_I4_ADD","V128_R4_ADD","V128_I1_SUB","V128_I2_SUB","V128_I4_SUB","V128_R4_SUB","V128_BITWISE_AND","V128_BITWISE_OR","V128_BITWISE_EQUALITY","V128_BITWISE_INEQUALITY","V128_R4_FLOAT_EQUALITY","V128_R8_FLOAT_EQUALITY","V128_EXCLUSIVE_OR","V128_I1_MULTIPLY","V128_I2_MULTIPLY","V128_I4_MULTIPLY","V128_R4_MULTIPLY","V128_R4_DIVISION","V128_I1_LEFT_SHIFT","V128_I2_LEFT_SHIFT","V128_I4_LEFT_SHIFT","V128_I8_LEFT_SHIFT","V128_I1_RIGHT_SHIFT","V128_I2_RIGHT_SHIFT","V128_I4_RIGHT_SHIFT","V128_I1_URIGHT_SHIFT","V128_I2_URIGHT_SHIFT","V128_I4_URIGHT_SHIFT","V128_I8_URIGHT_SHIFT","V128_U1_NARROW","V128_U1_GREATER_THAN","V128_I1_LESS_THAN","V128_U1_LESS_THAN","V128_I2_LESS_THAN","V128_I1_EQUALS","V128_I2_EQUALS","V128_I4_EQUALS","V128_R4_EQUALS","V128_I8_EQUALS","V128_AND_NOT","V128_U2_LESS_THAN_EQUAL","V128_I1_SHUFFLE","V128_I2_SHUFFLE","V128_I4_SHUFFLE","V128_I8_SHUFFLE","ExtractScalarI1","ExtractScalarU1","ExtractScalarI2","ExtractScalarU2","ExtractScalarD4","ExtractScalarD8","ExtractScalarR4","ExtractScalarR8","SwizzleD1","AddD1","AddD2","AddD4","AddD8","AddR4","AddR8","SubtractD1","SubtractD2","SubtractD4","SubtractD8","SubtractR4","SubtractR8","MultiplyD2","MultiplyD4","MultiplyD8","MultiplyR4","MultiplyR8","DivideR4","DivideR8","DotI2","ShiftLeftD1","ShiftLeftD2","ShiftLeftD4","ShiftLeftD8","ShiftRightArithmeticD1","ShiftRightArithmeticD2","ShiftRightArithmeticD4","ShiftRightArithmeticD8","ShiftRightLogicalD1","ShiftRightLogicalD2","ShiftRightLogicalD4","ShiftRightLogicalD8","AndANY","AndNotANY","OrANY","XorANY","CompareEqualD1","CompareEqualD2","CompareEqualD4","CompareEqualD8","CompareEqualR4","CompareEqualR8","CompareNotEqualD1","CompareNotEqualD2","CompareNotEqualD4","CompareNotEqualD8","CompareNotEqualR4","CompareNotEqualR8","CompareLessThanI1","CompareLessThanU1","CompareLessThanI2","CompareLessThanU2","CompareLessThanI4","CompareLessThanU4","CompareLessThanI8","CompareLessThanR4","CompareLessThanR8","CompareLessThanOrEqualI1","CompareLessThanOrEqualU1","CompareLessThanOrEqualI2","CompareLessThanOrEqualU2","CompareLessThanOrEqualI4","CompareLessThanOrEqualU4","CompareLessThanOrEqualI8","CompareLessThanOrEqualR4","CompareLessThanOrEqualR8","CompareGreaterThanI1","CompareGreaterThanU1","CompareGreaterThanI2","CompareGreaterThanU2","CompareGreaterThanI4","CompareGreaterThanU4","CompareGreaterThanI8","CompareGreaterThanR4","CompareGreaterThanR8","CompareGreaterThanOrEqualI1","CompareGreaterThanOrEqualU1","CompareGreaterThanOrEqualI2","CompareGreaterThanOrEqualU2","CompareGreaterThanOrEqualI4","CompareGreaterThanOrEqualU4","CompareGreaterThanOrEqualI8","CompareGreaterThanOrEqualR4","CompareGreaterThanOrEqualR8","ConvertNarrowingSaturateSignedI2","ConvertNarrowingSaturateSignedI4","ConvertNarrowingSaturateUnsignedI2","ConvertNarrowingSaturateUnsignedI4","MultiplyWideningLowerI1","MultiplyWideningLowerI2","MultiplyWideningLowerI4","MultiplyWideningLowerU1","MultiplyWideningLowerU2","MultiplyWideningLowerU4","MultiplyWideningUpperI1","MultiplyWideningUpperI2","MultiplyWideningUpperI4","MultiplyWideningUpperU1","MultiplyWideningUpperU2","MultiplyWideningUpperU4","AddSaturateI1","AddSaturateU1","AddSaturateI2","AddSaturateU2","SubtractSaturateI1","SubtractSaturateU1","SubtractSaturateI2","SubtractSaturateU2","MultiplyRoundedSaturateQ15I2","MinI1","MinI2","MinI4","MinU1","MinU2","MinU4","MaxI1","MaxI2","MaxI4","MaxU1","MaxU2","MaxU4","AverageRoundedU1","AverageRoundedU2","MinR4","MinR8","MaxR4","MaxR8","PseudoMinR4","PseudoMinR8","PseudoMaxR4","PseudoMaxR8","StoreANY"],4:["V128_CONDITIONAL_SELECT","ReplaceScalarD1","ReplaceScalarD2","ReplaceScalarD4","ReplaceScalarD8","ReplaceScalarR4","ReplaceScalarR8","ShuffleD1","BitwiseSelectANY","LoadScalarAndInsertX1","LoadScalarAndInsertX2","LoadScalarAndInsertX4","LoadScalarAndInsertX8","StoreSelectedScalarX1","StoreSelectedScalarX2","StoreSelectedScalarX4","StoreSelectedScalarX8"]},us={13:[65,-1],14:[65,0],15:[65,1],16:[65,2],17:[65,3],18:[65,4],19:[65,5],20:[65,6],21:[65,7],22:[65,8]},ps={463:168,469:174,464:170,470:176},ds={515:[69,40,54],435:[106,40,54],437:[107,40,54],439:[107,40,54],443:[115,40,54],436:[124,41,55],438:[125,41,55],440:[125,41,55],444:[133,41,55],518:[106,40,54],522:[108,40,54],519:[124,41,55],523:[126,41,55],441:[140,42,56],442:[154,43,57],471:[178,40,56],474:[183,40,57],445:[184,40,57],472:[180,41,56],475:[185,41,57],446:[186,41,57],476:[187,42,57],473:[182,43,56],467:[1,52,55],468:[1,53,55],451:[113,40,54],459:[113,40,54],447:[117,40,54],455:[117,40,54],452:[113,41,54],460:[113,41,54],448:[117,41,54],456:[117,41,54],526:[116,40,54],527:[134,41,55],528:[117,40,54],529:[135,41,55],524:[118,40,54],525:[136,41,55],629:[119,40,54],630:[137,41,55],631:[120,40,54],632:[138,41,55],633:[103,40,54],635:[104,40,54],637:[105,40,54],634:[121,41,55],636:[122,41,55],638:[123,41,55]},_s={401:187,402:1,405:187,406:1,409:187,410:1,413:187,414:1,419:187,420:1,423:187,424:1,433:187,434:1,427:187,428:1,65536:187,65537:187,65535:187,65539:1,65540:1,65538:1},fs={351:[106,40,54],369:[106,40,54],371:[106,40,54],355:[107,40,54],359:[108,40,54],373:[108,40,54],375:[108,40,54],363:[109,40,54],367:[110,40,54],387:[111,40,54],391:[112,40,54],381:[113,40,54],383:[114,40,54],385:[115,40,54],395:[116,40,54],397:[117,40,54],393:[118,40,54],352:[124,41,55],356:[125,41,55],360:[126,41,55],364:[127,41,55],388:[129,41,55],368:[128,41,55],392:[130,41,55],382:[131,41,55],384:[132,41,55],386:[133,41,55],396:[134,41,55],398:[135,41,55],394:[136,41,55],353:[146,42,56],357:[147,42,56],361:[148,42,56],365:[149,42,56],354:[160,43,57],358:[161,43,57],362:[162,43,57],366:[163,43,57],399:[70,40,54],403:[71,40,54],421:[72,40,54],407:[74,40,54],425:[76,40,54],411:[78,40,54],431:[73,40,54],417:[75,40,54],429:[77,40,54],415:[79,40,54],400:[81,41,54],404:[82,41,54],422:[83,41,54],408:[85,41,54],426:[87,41,54],412:[89,41,54],432:[84,41,54],418:[86,41,54],430:[88,41,54],416:[90,41,54]},ms={195:399,215:403,203:407,223:417,207:421,231:431,199:411,219:415,211:425,227:429,239:[399,!1,!0],249:[403,!1,!0],243:[407,!1,!0],253:[417,!1,!0],245:[421,!1,!0],257:[431,!1,!0],241:[411,!1,!0],251:[415,!1,!0],247:[425,!1,!0],255:[429,!1,!0],259:[399,65,!0],269:[403,65,!0],263:[407,65,!0],273:[417,65,!0],265:[421,65,!0],277:[431,65,!0],261:[411,65,!0],271:[415,65,!0],267:[425,65,!0],275:[429,65,!0],196:400,216:404,204:408,224:418,208:422,232:432,200:412,220:416,212:426,228:430,260:[400,66,!0],264:[408,66,!0],274:[418,66,!0],266:[422,66,!0],278:[432,66,!0],262:[412,66,!0],272:[416,66,!0],268:[426,66,!0],276:[430,66,!0],197:401,217:65535,205:409,225:419,209:423,233:433,201:413,221:65536,213:427,229:65537,198:402,218:65538,206:410,226:420,210:424,234:434,202:414,222:65539,214:428,230:65540},hs={589:[!0,!1,159],616:[!0,!0,145],576:[!0,!1,155],603:[!0,!0,141],582:[!0,!1,156],609:[!0,!0,142],593:[!0,!1,153],620:[!0,!0,139],571:[!0,!1,"acos"],598:[!0,!0,"acosf"],572:[!0,!1,"acosh"],599:[!0,!0,"acoshf"],577:[!0,!1,"cos"],604:[!0,!0,"cosf"],569:[!0,!1,"asin"],596:[!0,!0,"asinf"],570:[!0,!1,"asinh"],597:[!0,!0,"asinhf"],588:[!0,!1,"sin"],615:[!0,!0,"sinf"],573:[!0,!1,"atan"],600:[!0,!0,"atanf"],574:[!0,!1,"atanh"],601:[!0,!0,"atanhf"],591:[!0,!1,"tan"],618:[!0,!0,"tanf"],578:[!0,!1,"cbrt"],605:[!0,!0,"cbrtf"],580:[!0,!1,"exp"],607:[!0,!0,"expf"],583:[!0,!1,"log"],610:[!0,!0,"logf"],584:[!0,!1,"log2"],611:[!0,!0,"log2f"],585:[!0,!1,"log10"],612:[!0,!0,"log10f"],594:[!1,!1,164],621:[!1,!0,150],595:[!1,!1,165],622:[!1,!0,151],575:[!1,!1,"atan2"],602:[!1,!0,"atan2f"],586:[!1,!1,"pow"],613:[!1,!0,"powf"],390:[!1,!1,"fmod"],389:[!1,!0,"fmodf"]},bs={642:1,643:2,644:4,645:8},gs={642:44,643:46,644:40,645:41},ys={642:58,643:59,644:54,645:55},ws=new Set([20,21,22,23,24,25,26,27,28,29,30]),Ss={47:[16,54],48:[16,54],49:[8,54],50:[8,54],51:[4,54],53:[4,56],52:[2,55],54:[2,57]},ks={1:[16,40],2:[8,40],3:[4,40],5:[4,42],4:[2,41],6:[2,43]},vs=new Set([81,84,85,86,87,82,83,88,89,90,91,92,93]),Es={13:[16],14:[8],15:[4],16:[2]},Us={10:100,11:132,12:164,13:196},Is={6:[44,23],7:[46,26],8:[40,28],9:[41,30]};function xs(e,t){return se(e+2*t)}function Ts(e,t){return de(e+2*t)}function js(e,t){return le(e+2*t)}function As(e,t){return ue(e+2*t)}function Rs(e){return ue(e+Za(4))}function Ls(e,t){const n=ue(Rs(e)+Za(5));return ue(n+t*Ti)}function $s(e,t){const n=ue(Rs(e)+Za(12));return ue(n+t*Ti)}function Cs(e,t,n){if(!n)return!1;for(let r=0;r<n.length;r++)if(2*n[r]+t===e)return!0;return!1}const Ns=new Map;function Ds(e,t){if(!Xs(e,t))return Ns.get(t)}const Bs=new Map;let Os,Ms=-1;function Fs(){Ms=-1,Bs.clear(),Ns.clear()}function Ps(e){Ms===e&&(Ms=-1),Bs.delete(e),Ns.delete(e)}function zs(e,t){for(let n=0;n<t;n+=1)Ps(e+n)}function Vs(e,t,n){e.cfg.startBranchBlock(t,n)}function Ws(e,t,n){let r=0;switch(e%16==0?r=4:e%8==0?r=3:e%4==0?r=2:e%2==0&&(r=1),t){case 253:r=0===n||11===n?Math.min(r,4):0;break;case 41:case 43:case 55:case 57:r=Math.min(r,3);break;case 52:case 53:case 62:case 40:case 42:case 54:case 56:r=Math.min(r,2);break;case 50:case 51:case 46:case 47:case 61:case 59:r=Math.min(r,1);break;default:r=0}return r}function Hs(e,t,n,r){if(e.local("pLocals"),n>=40||w(!1,`Expected load opcode but got ${n}`),e.appendU8(n),void 0!==r)e.appendULeb(r);else if(253===n)throw new Error("PREFIX_simd ldloc without a simdOpcode");const o=Ws(t,n,r);e.appendMemarg(t,o)}function Gs(e,t,n,r){n>=54||w(!1,`Expected store opcode but got ${n}`),e.appendU8(n),void 0!==r&&e.appendULeb(r);const o=Ws(t,n,r);e.appendMemarg(t,o),Ps(t),void 0!==r&&Ps(t+8)}function qs(e,t,n){"number"!=typeof n&&(n=512),n>0&&zs(t,n),e.lea("pLocals",t)}function Js(e,t,n,r){zs(t,r),Ha(e,t,n,r,!1)||(qs(e,t,r),Ga(e,n,r))}function Ys(e,t,n,r){if(zs(t,r),qa(e,t,n,r,!1))return!0;qs(e,t,r),qs(e,n,0),Ja(e,r)}function Xs(e,t){return 0!==St.mono_jiterp_is_imethod_var_address_taken(Rs(e.frame),t)}function Zs(e,t,n,r){if(e.allowNullCheckOptimization&&Bs.has(t)&&!Xs(e,t))return Oa.nullChecksEliminated++,void(Ms===t?r&&e.local("cknull_ptr"):(Hs(e,t,40),e.local("cknull_ptr",r?34:33),Ms=t));Hs(e,t,40),e.local("cknull_ptr",34),e.appendU8(69),e.block(64,4),Pa(e,n,2),e.endBlock(),r&&e.local("cknull_ptr"),e.allowNullCheckOptimization&&!Xs(e,t)?(Bs.set(t,n),Ms=t):Ms=-1}function Ks(e,t,n){let r,o=54;const a=us[n];if(a)e.local("pLocals"),e.appendU8(a[0]),r=a[1],e.appendLeb(r);else switch(n){case 23:e.local("pLocals"),r=Ts(t,2),e.i32_const(r);break;case 24:e.local("pLocals"),r=js(t,2),e.i32_const(r);break;case 25:e.local("pLocals"),e.i52_const(0),o=55;break;case 27:e.local("pLocals"),e.appendU8(66),e.appendLebRef(t+4,!0),o=55;break;case 26:e.local("pLocals"),e.i52_const(Ts(t,2)),o=55;break;case 28:e.local("pLocals"),e.appendU8(67),e.appendF32(function(e,t){return n=e+2*t,St.mono_wasm_get_f32_unaligned(n);var n}(t,2)),o=56;break;case 29:e.local("pLocals"),e.appendU8(68),e.appendF64(function(e,t){return n=e+2*t,St.mono_wasm_get_f64_unaligned(n);var n}(t,2)),o=57;break;default:return!1}e.appendU8(o);const s=xs(t,1);return e.appendMemarg(s,2),Ps(s),"number"==typeof r?Ns.set(s,r):Ns.delete(s),!0}function Qs(e,t,n){let r=40,o=54;switch(n){case 82:r=44;break;case 83:r=45;break;case 84:r=46;break;case 85:r=47;break;case 86:r=45,o=58;break;case 87:r=47,o=59;break;case 88:break;case 89:r=41,o=55;break;case 90:{const n=xs(t,3);return Ys(e,xs(t,1),xs(t,2),n),!0}case 91:return Ys(e,xs(t,1),xs(t,2),8),Ys(e,xs(t,3),xs(t,4),8),!0;case 92:return Ys(e,xs(t,1),xs(t,2),8),Ys(e,xs(t,3),xs(t,4),8),Ys(e,xs(t,5),xs(t,6),8),!0;case 93:return Ys(e,xs(t,1),xs(t,2),8),Ys(e,xs(t,3),xs(t,4),8),Ys(e,xs(t,5),xs(t,6),8),Ys(e,xs(t,7),xs(t,8),8),!0;default:return!1}return e.local("pLocals"),Hs(e,xs(t,2),r),Gs(e,xs(t,1),o),!0}function ei(e,t,n,r){const o=r>=31&&r<=44||r>=58&&r<=68,a=xs(n,o?2:1),s=xs(n,3),i=xs(n,o?1:2),c=e.allowNullCheckOptimization&&Bs.has(a)&&!Xs(e,a);44!==r&&53!==r&&Zs(e,a,n,!1);let l=54,u=40;switch(r){case 31:u=44;break;case 32:u=45;break;case 33:u=46;break;case 34:u=47;break;case 39:case 49:case 35:break;case 51:case 37:u=42,l=56;break;case 52:case 38:u=43,l=57;break;case 45:case 46:l=58;break;case 47:case 48:l=59;break;case 36:case 50:u=41,l=55;break;case 53:return c||e.block(),e.local("pLocals"),e.i32_const(s),e.i32_const(a),e.i32_const(i),e.callImport("stfld_o"),c?(e.appendU8(26),Oa.nullChecksEliminated++):(e.appendU8(13),e.appendULeb(0),Pa(e,n,2),e.endBlock()),!0;case 40:{const t=xs(n,4);return qs(e,i,t),e.local("cknull_ptr"),0!==s&&(e.i32_const(s),e.appendU8(106)),Ja(e,t),!0}case 54:{const r=Ls(t,xs(n,4));return e.local("cknull_ptr"),0!==s&&(e.i32_const(s),e.appendU8(106)),qs(e,i,0),e.ptr_const(r),e.callImport("value_copy"),!0}case 55:{const t=xs(n,4);return e.local("cknull_ptr"),0!==s&&(e.i32_const(s),e.appendU8(106)),qs(e,i,0),Ja(e,t),!0}case 44:case 43:return e.local("pLocals"),Hs(e,a,40),0!==s&&(e.i32_const(s),e.appendU8(106)),Gs(e,i,l),!0;default:return!1}return o&&e.local("pLocals"),e.local("cknull_ptr"),o?(e.appendU8(u),e.appendMemarg(s,0),Gs(e,i,l),!0):(Hs(e,i,u),e.appendU8(l),e.appendMemarg(s,0),!0)}function ti(e,t,n,r){const o=r>=31&&r<=44||r>=58&&r<=68,a=xs(n,1),s=Ls(t,xs(n,2)),i=Ls(t,xs(n,3));!function(e,t,n){e.block(),e.ptr_const(t),e.appendU8(45),e.appendMemarg(Za(0),0),e.appendU8(13),e.appendULeb(0),Pa(e,n,3),e.endBlock()}(e,s,n);let c=54,l=40;switch(r){case 58:l=44;break;case 59:l=45;break;case 60:l=46;break;case 61:l=47;break;case 66:case 73:case 62:break;case 75:case 64:l=42,c=56;break;case 76:case 65:l=43,c=57;break;case 69:case 70:c=58;break;case 71:case 72:c=59;break;case 63:case 74:l=41,c=55;break;case 77:return e.ptr_const(i),qs(e,a,0),e.callImport("copy_ptr"),!0;case 67:{const t=xs(n,4);return qs(e,a,t),e.ptr_const(i),Ja(e,t),!0}case 80:return e.local("pLocals"),e.ptr_const(i),Gs(e,a,c),!0;default:return!1}return o?(e.local("pLocals"),e.ptr_const(i),e.appendU8(l),e.appendMemarg(0,0),Gs(e,a,c),!0):(e.ptr_const(i),Hs(e,a,l),e.appendU8(c),e.appendMemarg(0,0),!0)}function ni(e,t,n){let r,o,a,s,i="math_lhs32",c="math_rhs32",l=!1;const u=_s[n];if(u){e.local("pLocals");const r=1==u;return Hs(e,xs(t,2),r?43:42),r||e.appendU8(u),Hs(e,xs(t,3),r?43:42),r||e.appendU8(u),e.i32_const(n),e.callImport("relop_fp"),Gs(e,xs(t,1),54),!0}switch(n){case 389:case 390:return ii(e,t,n);default:if(s=fs[n],!s)return!1;s.length>3?(r=s[1],o=s[2],a=s[3]):(r=o=s[1],a=s[2])}switch(n){case 363:case 364:case 367:case 368:case 387:case 388:case 391:case 392:{const a=368===n||392===n||364===n||388===n;i=a?"math_lhs64":"math_lhs32",c=a?"math_rhs64":"math_rhs32",e.block(),Hs(e,xs(t,2),r),e.local(i,33),Hs(e,xs(t,3),o),e.local(c,34),l=!0,a&&(e.appendU8(80),e.appendU8(69)),e.appendU8(13),e.appendULeb(0),Pa(e,t,12),e.endBlock(),363!==n&&387!==n&&364!==n&&388!==n||(e.block(),e.local(c),a?e.i52_const(-1):e.i32_const(-1),e.appendU8(a?82:71),e.appendU8(13),e.appendULeb(0),e.local(i),e.appendU8(a?66:65),e.appendBoundaryValue(a?64:32,-1),e.appendU8(a?82:71),e.appendU8(13),e.appendULeb(0),Pa(e,t,13),e.endBlock());break}case 369:case 371:case 373:case 375:Hs(e,xs(t,2),r),e.local(i,34),Hs(e,xs(t,3),o),e.local(c,34),e.i32_const(n),e.callImport(371===n||375===n?"ckovr_u4":"ckovr_i4"),e.block(64,4),Pa(e,t,13),e.endBlock(),l=!0}return e.local("pLocals"),l?(e.local(i),e.local(c)):(Hs(e,xs(t,2),r),Hs(e,xs(t,3),o)),e.appendU8(s[0]),Gs(e,xs(t,1),a),!0}function ri(e,t,n){const r=ds[n];if(!r)return!1;const o=r[1],a=r[2];switch((n<479||n>514)&&e.local("pLocals"),n){case 435:case 437:Hs(e,xs(t,2),o),e.i32_const(1);break;case 439:e.i32_const(0),Hs(e,xs(t,2),o);break;case 443:Hs(e,xs(t,2),o),e.i32_const(-1);break;case 451:case 452:Hs(e,xs(t,2),o),41===o&&e.appendU8(167),e.i32_const(255);break;case 459:case 460:Hs(e,xs(t,2),o),41===o&&e.appendU8(167),e.i32_const(65535);break;case 447:case 448:Hs(e,xs(t,2),o),41===o&&e.appendU8(167),e.i32_const(24),e.appendU8(116),e.i32_const(24);break;case 455:case 456:Hs(e,xs(t,2),o),41===o&&e.appendU8(167),e.i32_const(16),e.appendU8(116),e.i32_const(16);break;case 436:case 438:Hs(e,xs(t,2),o),e.i52_const(1);break;case 440:e.i52_const(0),Hs(e,xs(t,2),o);break;case 444:Hs(e,xs(t,2),o),e.i52_const(-1);break;case 518:case 522:case 526:case 528:case 524:case 629:case 631:Hs(e,xs(t,2),o),e.i32_const(Ts(t,3));break;case 519:case 523:case 527:case 529:case 525:case 630:case 632:Hs(e,xs(t,2),o),e.i52_const(Ts(t,3));break;default:Hs(e,xs(t,2),o)}return 1!==r[0]&&e.appendU8(r[0]),Gs(e,xs(t,1),a),!0}function oi(e,t,n,r){const o=141===r?t+6:t+8,a=$s(n,se(o-2));e.local("pLocals"),e.ptr_const(o),e.appendU8(54),e.appendMemarg(a,0),e.callHandlerReturnAddresses.push(o)}function ai(e,t,n,r,o){const a=r>=235&&r<=278;switch(r){case 140:case 141:case 136:case 137:{const a=140===r||141===r,s=t+2*(o=136===r||140===r?js(t,1):Ts(t,1));return o<=0?e.backBranchOffsets.indexOf(s)>=0?(a&&oi(e,t,n,r),e.cfg.branch(s,!0,0),Oa.backBranchesEmitted++,!0):(s<e.cfg.entryIp?e.cfg.trace>1&&ut(`${Ua(r)} target 0x${s.toString(16)} before start of trace`):e.cfg.trace>0&&ut(`0x${t.toString(16)} ${Ua(r)} target 0x${s.toString(16)} not found in list `+e.backBranchOffsets.map((e=>"0x"+e.toString(16))).join(", ")),St.mono_jiterp_boost_back_branch_target(s),Pa(e,s,5),Oa.backBranchesNotEmitted++,!0):(e.branchTargets.add(s),a&&oi(e,t,n,r),e.cfg.branch(s,!1,0),!0)}case 153:case 151:case 237:case 235:case 154:case 152:{const n=154===r||152===r;o=Ts(t,2),Hs(e,xs(t,1),n?41:40),151===r||235===r?e.appendU8(69):152===r?e.appendU8(80):154===r&&(e.appendU8(80),e.appendU8(69));break}default:if(void 0===ms[r])throw new Error(`Unsupported relop branch opcode: ${Ua(r)}`);if(4!==St.mono_jiterp_get_opcode_info(r,1))throw new Error(`Unsupported long branch opcode: ${Ua(r)}`)}if(!o)throw new Error("Branch had no displacement");const s=t+2*o;return o<0?e.backBranchOffsets.indexOf(s)>=0?(e.cfg.branch(s,!0,a?3:1),Oa.backBranchesEmitted++):(s<e.cfg.entryIp?e.cfg.trace>1&&ut(`${Ua(r)} target 0x${s.toString(16)} before start of trace`):e.cfg.trace>0&&ut(`0x${t.toString(16)} ${Ua(r)} target 0x${s.toString(16)} not found in list `+e.backBranchOffsets.map((e=>"0x"+e.toString(16))).join(", ")),St.mono_jiterp_boost_back_branch_target(s),e.block(64,4),Pa(e,s,5),e.endBlock(),Oa.backBranchesNotEmitted++):(e.branchTargets.add(s),e.cfg.branch(s,!1,a?3:1)),!0}function si(e,t,n,r){const o=ms[r];if(!o)return!1;const a=Array.isArray(o)?o[0]:o,s=fs[a],i=_s[a];if(!s&&!i)return!1;const c=Ts(t,3),l=s?s[1]:1===i?43:42;return Hs(e,xs(t,1),l),s||1===i||e.appendU8(i),Array.isArray(o)&&o[1]?(e.appendU8(o[1]),e.appendLeb(Ts(t,2))):Hs(e,xs(t,2),l),s||1==i||e.appendU8(i),s?e.appendU8(s[0]):(e.i32_const(a),e.callImport("relop_fp")),ai(e,t,n,r,c)}function ii(e,t,n){let r,o,a,s;const i=xs(t,1),c=xs(t,2),l=xs(t,3),u=hs[n];if(!u)return!1;if(r=u[0],o=u[1],"string"==typeof u[2]?a=u[2]:s=u[2],e.local("pLocals"),r){if(Hs(e,c,o?42:43),s)e.appendU8(s);else{if(!a)throw new Error("internal error");e.callImport(a)}return Gs(e,i,o?56:57),!0}if(Hs(e,c,o?42:43),Hs(e,l,o?42:43),s)e.appendU8(s);else{if(!a)throw new Error("internal error");e.callImport(a)}return Gs(e,i,o?56:57),!0}function ci(e,t,n){const r=n>=95&&n<=120,o=n>=115&&n<=120,a=n>=103&&n<=114||n>=128&&n<=135||o,s=n>=109&&n<=114||n>=132&&n<=135||o;let i,c,l=-1,u=0,p=1;o?(i=xs(t,1),c=xs(t,2),l=xs(t,3),u=Ts(t,4),p=Ts(t,5)):a?s?r?(i=xs(t,1),c=xs(t,2),u=Ts(t,3)):(i=xs(t,2),c=xs(t,1),u=Ts(t,3)):r?(i=xs(t,1),c=xs(t,2),l=xs(t,3)):(i=xs(t,3),c=xs(t,1),l=xs(t,2)):r?(c=xs(t,2),i=xs(t,1)):(c=xs(t,1),i=xs(t,2));let d,_=54;switch(n){case 95:case 103:case 109:case 115:d=44;break;case 96:case 104:case 110:case 116:d=45;break;case 97:case 105:case 111:case 117:d=46;break;case 98:case 106:case 112:case 118:d=47;break;case 121:case 128:case 132:d=40,_=58;break;case 122:case 129:case 133:d=40,_=59;break;case 99:case 107:case 113:case 119:case 123:case 130:case 134:case 127:d=40;break;case 101:case 125:d=42,_=56;break;case 102:case 126:d=43,_=57;break;case 100:case 108:case 114:case 120:case 124:case 131:case 135:d=41,_=55;break;default:return!1}return Zs(e,c,t,!1),r?(e.local("pLocals"),e.local("cknull_ptr"),o?(Hs(e,l,40),0!==u&&(e.i32_const(u),e.appendU8(106),u=0),1!==p&&(e.i32_const(p),e.appendU8(108)),e.appendU8(106)):a&&l>=0?(Hs(e,l,40),e.appendU8(106)):u<0&&(e.i32_const(u),e.appendU8(106),u=0),e.appendU8(d),e.appendMemarg(u,0),Gs(e,i,_)):127===n?(e.local("cknull_ptr"),qs(e,i,0),e.callImport("copy_ptr")):(e.local("cknull_ptr"),a&&l>=0?(Hs(e,l,40),e.appendU8(106)):u<0&&(e.i32_const(u),e.appendU8(106),u=0),Hs(e,i,d),e.appendU8(_),e.appendMemarg(u,0)),!0}function li(e,t,n,r,o){e.block(),Hs(e,r,40),e.local("index",34);let a="cknull_ptr";e.options.zeroPageOptimization&&rs()?(Oa.nullChecksFused++,Hs(e,n,40),a="src_ptr",e.local(a,34)):Zs(e,n,t,!0),e.appendU8(40),e.appendMemarg(Za(9),2),e.appendU8(73),e.appendU8(13),e.appendULeb(0),Pa(e,t,9),e.endBlock(),e.local(a),e.i32_const(Za(1)),e.appendU8(106),e.local("index"),1!=o&&(e.i32_const(o),e.appendU8(108)),e.appendU8(106)}function ui(e,t,n,r){const o=r<=336&&r>=323||348===r,a=xs(n,o?2:1),s=xs(n,o?1:3),i=xs(n,o?3:2);let c,l,u=54;switch(r){case 348:return e.local("pLocals"),Zs(e,a,n,!0),e.appendU8(40),e.appendMemarg(Za(9),2),Gs(e,s,54),!0;case 334:return e.local("pLocals"),l=xs(n,4),li(e,n,a,i,l),Gs(e,s,54),!0;case 345:return e.block(),Hs(e,xs(n,1),40),Hs(e,xs(n,2),40),Hs(e,xs(n,3),40),e.callImport("stelem_ref"),e.appendU8(13),e.appendULeb(0),Pa(e,n,10),e.endBlock(),!0;case 332:case 328:case 327:case 341:l=4,c=40;break;case 323:l=1,c=44;break;case 324:l=1,c=45;break;case 338:case 337:l=1,c=40,u=58;break;case 325:l=2,c=46;break;case 326:l=2,c=47;break;case 340:case 339:l=2,c=40,u=59;break;case 330:case 343:l=4,c=42,u=56;break;case 329:case 342:l=8,c=41,u=55;break;case 331:case 344:l=8,c=43,u=57;break;case 333:{const t=xs(n,4);return e.local("pLocals"),e.i32_const(xs(n,1)),e.appendU8(106),li(e,n,a,i,t),Ja(e,t),zs(xs(n,1),t),!0}case 346:{const r=xs(n,5),o=Ls(t,xs(n,4));return li(e,n,a,i,r),qs(e,s,0),e.ptr_const(o),e.callImport("value_copy"),!0}case 347:{const t=xs(n,5);return li(e,n,a,i,t),qs(e,s,0),Ja(e,t),!0}default:return!1}return o?(e.local("pLocals"),li(e,n,a,i,l),e.appendU8(c),e.appendMemarg(0,0),Gs(e,s,u)):(li(e,n,a,i,l),Hs(e,s,c),e.appendU8(u),e.appendMemarg(0,0)),!0}function pi(){if(void 0!==Os)return Os;try{const e=function(){const e=new Ra(0);e.defineType("test",{},64,!0),e.defineFunction({type:"test",name:"test",export:!0,locals:{}},(()=>{e.i32_const(0),e.appendSimd(17),e.appendU8(26),e.appendU8(11)})),e.appendU32(1836278016),e.appendU32(1),e.generateTypeSection(),e.emitImportsAndFunctions(!1);const t=e.getArrayView();return new WebAssembly.Module(t)}();Os=!!e}catch(e){ut("Disabling WASM SIMD support due to JIT failure",e),Os=!1}return Os}function di(e,t,n){const r=`${t}_${n.toString(16)}`;return"object"!=typeof e.importedFunctions[r]&&e.defineImportedFunction("s",r,t,!1,n),r}function _i(e,t,n,r,o,a){if(e.options.enableSimd&&pi())switch(o){case 2:if(function(e,t,n){const r=St.mono_jiterp_get_simd_opcode(1,n);if(r>=0)return vs.has(n)?(e.local("pLocals"),Hs(e,xs(t,2),40),e.appendSimd(r,!0),e.appendMemarg(0,0),fi(e,t)):(mi(e,t),e.appendSimd(r),fi(e,t)),!0;const o=Us[n];if(o)return mi(e,t),e.appendSimd(o),Gs(e,xs(t,1),54),!0;switch(n){case 6:case 7:case 8:case 9:{const r=Is[n];return e.local("pLocals"),e.v128_const(0),Hs(e,xs(t,2),r[0]),e.appendSimd(r[1]),e.appendU8(0),Gs(e,xs(t,1),253,11),!0}case 14:return mi(e,t,7),fi(e,t),!0;case 15:return mi(e,t,8),fi(e,t),!0;case 16:return mi(e,t,9),fi(e,t),!0;case 17:return mi(e,t,10),fi(e,t),!0;default:return!1}}(e,t,a))return!0;break;case 3:if(function(e,t,n){const r=St.mono_jiterp_get_simd_opcode(2,n);if(r>=0){const o=ws.has(n),a=Ss[n];if(o)e.local("pLocals"),Hs(e,xs(t,2),253,0),Hs(e,xs(t,3),40),e.appendSimd(r),fi(e,t);else if(Array.isArray(a)){const n=Ds(e,xs(t,3)),o=a[0];if("number"!=typeof n)return dt(`${e.functions[0].name}: Non-constant lane index passed to ExtractScalar`),!1;if(n>=o||n<0)return dt(`${e.functions[0].name}: ExtractScalar index ${n} out of range (0 - ${o-1})`),!1;e.local("pLocals"),Hs(e,xs(t,2),253,0),e.appendSimd(r),e.appendU8(n),Gs(e,xs(t,1),a[1])}else hi(e,t),e.appendSimd(r),fi(e,t);return!0}switch(n){case 187:return Hs(e,xs(t,2),40),Hs(e,xs(t,3),253,0),e.appendSimd(11),e.appendMemarg(0,0),!0;case 10:case 11:return hi(e,t),e.appendSimd(214),e.appendSimd(195),11===n&&e.appendU8(69),Gs(e,xs(t,1),54),!0;case 12:case 13:{const r=13===n,o=r?71:65;return e.local("pLocals"),Hs(e,xs(t,2),253,0),e.local("math_lhs128",34),Hs(e,xs(t,3),253,0),e.local("math_rhs128",34),e.appendSimd(o),e.local("math_lhs128"),e.local("math_lhs128"),e.appendSimd(o),e.local("math_rhs128"),e.local("math_rhs128"),e.appendSimd(o),e.appendSimd(80),e.appendSimd(77),e.appendSimd(80),e.appendSimd(r?195:163),Gs(e,xs(t,1),54),!0}case 43:{const n=xs(t,3),r=Ds(e,n);return e.local("pLocals"),Hs(e,xs(t,2),253,0),"object"==typeof r?(e.appendSimd(12),e.appendBytes(r)):Hs(e,n,253,0),e.appendSimd(14),fi(e,t),!0}case 44:case 45:return function(e,t,n){const r=16/n,o=xs(t,3),a=Ds(e,o);if(2!==r&&4!==r&&w(!1,"Unsupported shuffle element size"),e.local("pLocals"),Hs(e,xs(t,2),253,0),"object"==typeof a){const t=new Uint8Array(ji),o=2===r?new Uint16Array(a.buffer,a.byteOffset,n):new Uint32Array(a.buffer,a.byteOffset,n);for(let e=0,a=0;e<n;e++,a+=r){const n=o[e];for(let e=0;e<r;e++)t[a+e]=n*r+e}e.appendSimd(12),e.appendBytes(t)}else{Hs(e,o,253,0),4===n&&(e.v128_const(0),e.appendSimd(134)),e.v128_const(0),e.appendSimd(102),e.appendSimd(12);for(let t=0;t<n;t++)for(let n=0;n<r;n++)e.appendU8(t);e.appendSimd(14),e.i32_const(4===n?2:1),e.appendSimd(107),e.appendSimd(12);for(let t=0;t<n;t++)for(let t=0;t<r;t++)e.appendU8(t)}return e.appendSimd(14),fi(e,t),!0}(e,t,44===n?8:4);default:return!1}return!1}(e,t,a))return!0;break;case 4:if(function(e,t,n){const r=St.mono_jiterp_get_simd_opcode(3,n);if(r>=0){const o=ks[n],a=Es[n];if(Array.isArray(o)){const n=o[0],a=Ds(e,xs(t,3));if("number"!=typeof a)return dt(`${e.functions[0].name}: Non-constant lane index passed to ReplaceScalar`),!1;if(a>=n||a<0)return dt(`${e.functions[0].name}: ReplaceScalar index ${a} out of range (0 - ${n-1})`),!1;e.local("pLocals"),Hs(e,xs(t,2),253,0),Hs(e,xs(t,4),o[1]),e.appendSimd(r),e.appendU8(a),fi(e,t)}else if(Array.isArray(a)){const n=a[0],o=Ds(e,xs(t,4));if("number"!=typeof o)return dt(`${e.functions[0].name}: Non-constant lane index passed to store method`),!1;if(o>=n||o<0)return dt(`${e.functions[0].name}: Store lane ${o} out of range (0 - ${n-1})`),!1;Hs(e,xs(t,2),40),Hs(e,xs(t,3),253,0),e.appendSimd(r),e.appendMemarg(0,0),e.appendU8(o)}else!function(e,t){e.local("pLocals"),Hs(e,xs(t,2),253,0),Hs(e,xs(t,3),253,0),Hs(e,xs(t,4),253,0)}(e,t),e.appendSimd(r),fi(e,t);return!0}switch(n){case 0:return e.local("pLocals"),Hs(e,xs(t,3),253,0),Hs(e,xs(t,4),253,0),Hs(e,xs(t,2),253,0),e.appendSimd(82),fi(e,t),!0;case 7:{const n=Ds(e,xs(t,4));if("object"!=typeof n)return dt(`${e.functions[0].name}: Non-constant indices passed to PackedSimd.Shuffle`),!1;for(let t=0;t<32;t++){const r=n[t];if(r<0||r>31)return dt(`${e.functions[0].name}: Shuffle lane index #${t} (${r}) out of range (0 - 31)`),!1}return e.local("pLocals"),Hs(e,xs(t,2),253,0),Hs(e,xs(t,3),253,0),e.appendSimd(13),e.appendBytes(n),fi(e,t),!0}default:return!1}}(e,t,a))return!0}switch(n){case 641:if(e.options.enableSimd&&pi()){e.local("pLocals");const n=Ee().slice(t+4,t+4+ji);e.v128_const(n),fi(e,t),Ns.set(xs(t,1),n)}else qs(e,xs(t,1),ji),e.ptr_const(t+4),Ja(e,ji);return!0;case 642:case 643:case 644:case 645:{const r=bs[n],o=ji/r,a=xs(t,1),s=xs(t,2),i=gs[n],c=ys[n];for(let t=0;t<o;t++)e.local("pLocals"),Hs(e,s+t*Ai,i),Gs(e,a+t*r,c);return!0}case 646:{Oa.simdFallback[r]=(Oa.simdFallback[r]||0)+1,qs(e,xs(t,1),ji),qs(e,xs(t,2),0);const n=di(e,"simd_p_p",St.mono_jiterp_get_simd_intrinsic(1,a));return e.callImport(n),!0}case 647:{Oa.simdFallback[r]=(Oa.simdFallback[r]||0)+1,qs(e,xs(t,1),ji),qs(e,xs(t,2),0),qs(e,xs(t,3),0);const n=di(e,"simd_p_pp",St.mono_jiterp_get_simd_intrinsic(2,a));return e.callImport(n),!0}case 648:{Oa.simdFallback[r]=(Oa.simdFallback[r]||0)+1,qs(e,xs(t,1),ji),qs(e,xs(t,2),0),qs(e,xs(t,3),0),qs(e,xs(t,4),0);const n=di(e,"simd_p_ppp",St.mono_jiterp_get_simd_intrinsic(3,a));return e.callImport(n),!0}default:return ut(`jiterpreter emit_simd failed for ${r}`),!1}}function fi(e,t){Gs(e,xs(t,1),253,11)}function mi(e,t,n){e.local("pLocals"),Hs(e,xs(t,2),253,n||0)}function hi(e,t){e.local("pLocals"),Hs(e,xs(t,2),253,0),Hs(e,xs(t,3),253,0)}const bi=30;let gi,yi;const wi=[],Si=[];class ki{constructor(e){this.name=e,this.eip=0}}class vi{constructor(e,t,n){this.ip=e,this.index=t,this.isVerbose=!!n}get hitCount(){return St.mono_jiterp_get_trace_hit_count(this.index)}}const Ei={};let Ui=1;const Ii={},xi={},Ti=4,ji=16,Ai=8;let Ri,Li;const $i=["asin","acos","atan","asinh","acosh","atanh","cos","sin","tan","cosh","sinh","tanh","exp","log","log2","log10","cbrt"],Ci=["fmod","atan2","pow"],Ni=["asinf","acosf","atanf","asinhf","acoshf","atanhf","cosf","sinf","tanf","coshf","sinhf","tanhf","expf","logf","log2f","log10f","cbrtf"],Di=["fmodf","atan2f","powf"];function Bi(e,t,n){if(St.mono_jiterp_trace_bailout(n),14===n)return e;const r=xi[t];if(!r)return void dt(`trace info not found for ${t}`);let o=r.bailoutCounts;o||(r.bailoutCounts=o={});const a=o[n];return o[n]=a?a+1:1,r.bailoutCount?r.bailoutCount++:r.bailoutCount=1,e}function Oi(){if(Li)return Li;Li=[ts("bailout",Bi),ts("copy_ptr",Ka("mono_wasm_copy_managed_pointer")),ts("entry",Ka("mono_jiterp_increase_entry_count")),ts("value_copy",Ka("mono_jiterp_value_copy")),ts("gettype",Ka("mono_jiterp_gettype_ref")),ts("castv2",Ka("mono_jiterp_cast_v2")),ts("hasparent",Ka("mono_jiterp_has_parent_fast")),ts("imp_iface",Ka("mono_jiterp_implements_interface")),ts("imp_iface_s",Ka("mono_jiterp_implements_special_interface")),ts("box",Ka("mono_jiterp_box_ref")),ts("localloc",Ka("mono_jiterp_localloc")),["ckovr_i4","overflow_check_i4",Ka("mono_jiterp_overflow_check_i4")],["ckovr_u4","overflow_check_i4",Ka("mono_jiterp_overflow_check_u4")],ts("newobj_i",Ka("mono_jiterp_try_newobj_inlined")),ts("newstr",Ka("mono_jiterp_try_newstr")),ts("ld_del_ptr",Ka("mono_jiterp_ld_delegate_method_ptr")),ts("ldtsflda",Ka("mono_jiterp_ldtsflda")),ts("conv",Ka("mono_jiterp_conv")),ts("relop_fp",Ka("mono_jiterp_relop_fp")),ts("safepoint",Ka("mono_jiterp_do_safepoint")),ts("hashcode",Ka("mono_jiterp_get_hashcode")),ts("try_hash",Ka("mono_jiterp_try_get_hashcode")),ts("hascsize",Ka("mono_jiterp_object_has_component_size")),ts("hasflag",Ka("mono_jiterp_enum_hasflag")),ts("array_rank",Ka("mono_jiterp_get_array_rank")),["a_elesize","array_rank",Ka("mono_jiterp_get_array_element_size")],ts("stfld_o",Ka("mono_jiterp_set_object_field")),ts("transfer",Ka("mono_jiterp_trace_transfer")),ts("cmpxchg_i32",Ka("mono_jiterp_cas_i32")),ts("cmpxchg_i64",Ka("mono_jiterp_cas_i64")),ts("stelem_ref",Ka("mono_jiterp_stelem_ref")),ts("fma",Ka("fma")),ts("fmaf",Ka("fmaf"))],Si.length>0&&(Li.push(["trace_eip","trace_eip",Mi]),Li.push(["trace_args","trace_eip",Fi]));const e=(e,t)=>{for(let n=0;n<e.length;n++){const r=e[n];Li.push([r,t,Ka(r)])}};return e(Ni,"mathop_f_f"),e(Di,"mathop_ff_f"),e($i,"mathop_d_d"),e(Ci,"mathop_dd_d"),Li}function Mi(e,t){const n=Ei[e];if(!n)throw new Error(`Unrecognized instrumented trace id ${e}`);n.eip=t,gi=n}function Fi(e,t){if(!gi)throw new Error("No trace active");gi.operand1=e>>>0,gi.operand2=t>>>0}function Pi(e,t,n,r){if("number"==typeof r)St.mono_jiterp_adjust_abort_count(r,1),r=Ua(r);else{let e=Ii[r];"number"!=typeof e?e=1:e++,Ii[r]=e}xi[e].abortReason=r}function zi(e,t){if(!l.runtimeReady)return;if(yi&&void 0===e||(yi=cs()),!yi.enableStats&&void 0!==e)return;const n=Oa.backBranchesEmitted/(Oa.backBranchesEmitted+Oa.backBranchesNotEmitted)*100,r=St.mono_jiterp_get_rejected_trace_count(),o=yi.eliminateNullChecks?Oa.nullChecksEliminated.toString():"off",a=yi.zeroPageOptimization?Oa.nullChecksFused.toString()+(rs()?"":" (disabled)"):"off",s=yi.enableBackwardBranches?`emitted: ${Oa.backBranchesEmitted}, failed: ${Oa.backBranchesNotEmitted} (${n.toFixed(1)}%)`:": off",i=Oa.jitCallsCompiled?yi.directJitCalls?`direct jit calls: ${Oa.directJitCallsCompiled} (${(Oa.directJitCallsCompiled/Oa.jitCallsCompiled*100).toFixed(1)}%)`:"direct jit calls: off":"";if(ut(`// jitted ${Oa.bytesGenerated} bytes; ${Oa.tracesCompiled} traces (${(Oa.tracesCompiled/Oa.traceCandidates*100).toFixed(1)}%) (${r} rejected); ${Oa.jitCallsCompiled} jit_calls; ${Oa.entryWrappersCompiled} interp_entries`),ut(`// cknulls eliminated: ${o}, fused: ${a}; back-branches ${s}; ${i}`),ut(`// time: ${0|Ba.generation}ms generating, ${0|Ba.compilation}ms compiling wasm.`),!t){if(yi.countBailouts){const e=Object.values(xi);e.sort(((e,t)=>(t.bailoutCount||0)-(e.bailoutCount||0)));for(let e=0;e<ja.length;e++){const t=St.mono_jiterp_get_trace_bailout_count(e);t&&ut(`// traces bailed out ${t} time(s) due to ${ja[e]}`)}for(let t=0,n=0;t<e.length&&n<bi;t++){const r=e[t];if(r.bailoutCount){n++,ut(`${r.name}: ${r.bailoutCount} bailout(s)`);for(const e in r.bailoutCounts)ut(`  ${ja[e]} x${r.bailoutCounts[e]}`)}}}if(yi.estimateHeat){const e={},t=Object.values(xi);for(let n=0;n<t.length;n++){const r=t[n];r.abortReason&&"end-of-body"!==r.abortReason&&(e[r.abortReason]?e[r.abortReason]+=r.hitCount:e[r.abortReason]=r.hitCount)}t.sort(((e,t)=>t.hitCount-e.hitCount)),ut("// hottest failed traces:");for(let e=0,n=0;e<t.length&&n<bi;e++)if(t[e].name&&!(t[e].fnPtr||t[e].name.indexOf("Xunit.")>=0)){if(t[e].abortReason){if(t[e].abortReason.startsWith("mono_icall_")||t[e].abortReason.startsWith("ret."))continue;switch(t[e].abortReason){case"trace-too-small":case"trace-too-big":case"call":case"callvirt.fast":case"calli.nat.fast":case"calli.nat":case"call.delegate":case"newobj":case"newobj_vt":case"newobj_slow":case"switch":case"rethrow":case"end-of-body":case"ret":case"intrins_marvin_block":case"intrins_ascii_chars_to_uppercase":continue}}n++,ut(`${t[e].name} @${t[e].ip} (${t[e].hitCount} hits) ${t[e].abortReason}`)}const n=[];for(const t in e)n.push([t,e[t]]);n.sort(((e,t)=>t[1]-e[1])),ut("// heat:");for(let e=0;e<n.length;e++)ut(`// ${n[e][0]}: ${n[e][1]}`)}else{for(let e=0;e<674;e++){const t=Ua(e),n=St.mono_jiterp_adjust_abort_count(e,0);n>0?Ii[t]=n:delete Ii[t]}const e=Object.keys(Ii);e.sort(((e,t)=>Ii[t]-Ii[e]));for(let t=0;t<e.length;t++)ut(`// ${e[t]}: ${Ii[e[t]]} abort(s)`)}for(const e in Oa.simdFallback)ut(`// simd ${e}: ${Oa.simdFallback[e]} fallback insn(s)`);"function"==typeof globalThis.setTimeout&&void 0!==e&&setTimeout((()=>zi(e)),15e3)}}let Vi=!1;function Wi(){if(Vi)throw new Error("GC is already locked");Vi=!0}function Hi(){if(!Vi)throw new Error("GC is not locked");Vi=!1}async function Gi(e){const t=u.config.resources.lazyAssembly;if(!t)throw new Error("No assemblies have been marked as lazy-loadable. Use the 'BlazorWebAssemblyLazyLoad' item group in your project file to enable lazy loading an assembly.");if(!t[e])throw new Error(`${e} must be marked with 'BlazorWebAssemblyLazyLoad' item group in your project file to allow lazy-loading.`);const n={name:e,hash:t[e],behavior:"assembly"};if(u.loadedAssemblies.includes(e))return!1;const r=function(e,t){const n=e.lastIndexOf(".");if(n<0)throw new Error(`No extension to replace in '${e}'`);return e.substring(0,n)+".pdb"}(n.name),o=0!=u.config.debugLevel&&u.isDebuggingSupported()&&Object.prototype.hasOwnProperty.call(t,r),a=u.retrieve_asset_download(n);let s=null,i=null;if(o){const e=t[r]?u.retrieve_asset_download({name:r,hash:t[r],behavior:"pdb"}):Promise.resolve(null),[n,o]=await Promise.all([a,e]);s=new Uint8Array(n),i=o?new Uint8Array(o):null}else{const e=await a;s=new Uint8Array(e),i=null}return l.javaScriptExports.load_lazy_assembly(s,i),!0}async function qi(e){const t=u.config.resources.satelliteResources;t&&await Promise.all(e.filter((e=>Object.prototype.hasOwnProperty.call(t,e))).map((e=>{const n=[];for(const r in t[e]){const o={name:r,hash:t[e][r],behavior:"resource",culture:e};n.push(u.retrieve_asset_download(o))}return n})).reduce(((e,t)=>e.concat(t)),new Array).map((async e=>{const t=await e;l.javaScriptExports.load_satellite_assembly(new Uint8Array(t))})))}const Ji=64;let Yi,Xi,Zi,Ki=0;const Qi=[],ec={};function tc(){return Xi||(Xi=[ts("interp_entry_prologue",Ka("mono_jiterp_interp_entry_prologue")),ts("interp_entry",Ka("mono_jiterp_interp_entry")),ts("unbox",Ka("mono_jiterp_object_unbox")),ts("stackval_from_data",Ka("mono_jiterp_stackval_from_data"))],Xi)}let nc;function rc(){if(Qi.length<=0)return;const e=4*Qi.length+1;let t=Yi;if(t?t.clear(e):(Yi=t=new Ra(e),t.defineType("unbox",{pMonoObject:127},127,!0),t.defineType("interp_entry_prologue",{pData:127,this_arg:127},127,!0),t.defineType("interp_entry",{pData:127,res:127},64,!0),t.defineType("stackval_from_data",{type:127,result:127,value:127},64,!0)),t.options.wasmBytesLimit<=Oa.bytesGenerated)return void(Qi.length=0);const n=Ma();let r=0,o=!0,a=!1;try{t.appendU32(1836278016),t.appendU32(1);for(let e=0;e<Qi.length;e++){const n=Qi[e],r={};n.hasThisReference&&(r.this_arg=127),n.hasReturnValue&&(r.res=127);for(let e=0;e<n.argumentCount;e++)r[`arg${e}`]=127;r.rmethod=127,t.defineType(n.traceName,r,64,!1)}t.generateTypeSection();const e=tc();t.compressImportNames=!0;for(let n=0;n<e.length;n++)e[n]||w(!1,`trace #${n} missing`),t.defineImportedFunction("i",e[n][0],e[n][1],!0,e[n][2]);for(let n=0;n<e.length;n++)t.markImportAsUsed(e[n][0]);t._generateImportSection(!1),t.beginSection(3),t.appendULeb(Qi.length);for(let e=0;e<Qi.length;e++){const n=Qi[e];t.functionTypes[n.traceName]||w(!1,"func type missing"),t.appendULeb(t.functionTypes[n.traceName][0])}t.beginSection(7),t.appendULeb(Qi.length);for(let e=0;e<Qi.length;e++){const n=Qi[e];t.appendName(n.traceName),t.appendU8(0),t.appendULeb(t.importedFunctionCount+e)}t.beginSection(10),t.appendULeb(Qi.length);for(let e=0;e<Qi.length;e++){const n=Qi[e];t.beginFunction(n.traceName,{sp_args:127,need_unbox:127,scratchBuffer:127}),ac(t,n),t.appendU8(11),t.endFunction(!0)}t.endSection(),r=Ma();const n=t.getArrayView();Oa.bytesGenerated+=n.length;const a=new WebAssembly.Module(n),s=t.getWasmImports(),i=new WebAssembly.Instance(a,s);for(let e=0;e<Qi.length;e++){const t=Qi[e],n=i.exports[t.traceName];Zi.set(t.result,n),o=!1,Oa.entryWrappersCompiled++}}catch(e){a=!0,o=!1,dt(`interp_entry code generation failed: ${e}`),Ya()}finally{const e=Ma();if(r?(Ba.generation+=r-n,Ba.compilation+=e-r):Ba.generation+=e-n,a){ut(`// ${Qi.length} trampolines generated, blob follows //`);let e="",n=0;try{t.inSection&&t.endSection()}catch(e){}const r=t.getArrayView();for(let t=0;t<r.length;t++){const o=r[t];o<16&&(e+="0"),e+=o.toString(16),e+=" ",e.length%10==0&&(ut(`${n}\t${e}`),e="",n=t+1)}ut(`${n}\t${e}`),ut("// end blob //")}else o&&!a&&dt("failed to generate trampoline for unknown reason");Qi.length=0}}function oc(e,t,n,r,o){const a=St.mono_jiterp_type_get_raw_value_size(n),s=St.mono_jiterp_get_arg_offset(t,0,o);switch(a){case 256:e.local("sp_args"),e.local(r),e.appendU8(54),e.appendMemarg(s,2);break;case-1:case-2:case 1:case 2:case 4:switch(e.local("sp_args"),e.local(r),a){case-1:e.appendU8(45),e.appendMemarg(0,0);break;case 1:e.appendU8(44),e.appendMemarg(0,0);break;case-2:e.appendU8(47),e.appendMemarg(0,0);break;case 2:e.appendU8(46),e.appendMemarg(0,0);break;case 4:e.appendU8(40),e.appendMemarg(0,2)}e.appendU8(54),e.appendMemarg(s,2);break;default:e.ptr_const(n),e.local("sp_args"),e.i32_const(s),e.appendU8(106),e.local(r),e.callImport("stackval_from_data")}}function ac(e,n){const r=t._malloc(Ji);P(r,Ji),Z(r+Za(13),n.paramTypes.length+(n.hasThisReference?1:0)),n.hasThisReference&&(e.block(),e.local("rmethod"),e.i32_const(1),e.appendU8(113),e.appendU8(69),e.appendU8(13),e.appendULeb(0),e.local("this_arg"),e.callImport("unbox"),e.local("this_arg",33),e.endBlock()),e.ptr_const(r),e.local("scratchBuffer",34),e.local("rmethod"),e.i32_const(-2),e.appendU8(113),e.appendU8(54),e.appendMemarg(Za(6),0),e.local("scratchBuffer"),n.hasThisReference?e.local("this_arg"):e.i32_const(0),e.callImport("interp_entry_prologue"),e.local("sp_args",33),n.hasThisReference&&oc(e,n.imethod,0,"this_arg",0);for(let t=0;t<n.paramTypes.length;t++){const r=n.paramTypes[t];oc(e,n.imethod,r,`arg${t}`,t+(n.hasThisReference?1:0))}return e.local("scratchBuffer"),n.hasReturnValue?e.local("res"):e.i32_const(0),e.callImport("interp_entry"),e.appendU8(15),!0}const sc=16,ic=0;let cc,lc,uc,pc=0;const dc=[],_c={},fc=[];class mc{constructor(e,t,n,r,o){this.queue=[],r||w(!1,"Expected nonzero arg_offsets pointer"),this.method=e,this.rmethod=t,this.catchExceptions=o,this.cinfo=n,this.addr=ue(n+0),this.wrapper=ue(n+8),this.signature=ue(n+12),this.noWrapper=0!==ae(n+28),this.hasReturnValue=-1!==le(n+24),this.returnType=St.mono_jiterp_get_signature_return_type(this.signature),this.paramCount=St.mono_jiterp_get_signature_param_count(this.signature),this.hasThisReference=0!==St.mono_jiterp_get_signature_has_this(this.signature);const a=St.mono_jiterp_get_signature_params(this.signature);this.paramTypes=new Array(this.paramCount);for(let e=0;e<this.paramCount;e++)this.paramTypes[e]=ue(a+4*e);const s=this.paramCount+(this.hasThisReference?1:0);this.argOffsets=new Array(this.paramCount);for(let e=0;e<s;e++)this.argOffsets[e]=ue(r+4*e);this.target=this.noWrapper?this.addr:this.wrapper,this.result=0,this.wasmNativeReturnType=this.returnType&&this.hasReturnValue?wc[St.mono_jiterp_type_to_stind(this.returnType)]:64,this.wasmNativeSignature=this.paramTypes.map((e=>wc[St.mono_jiterp_type_to_ldind(e)])),this.enableDirect=cs().directJitCalls&&!this.noWrapper&&this.wasmNativeReturnType&&(0===this.wasmNativeSignature.length||this.wasmNativeSignature.every((e=>e))),this.enableDirect&&(this.target=this.addr);let i=this.target.toString(16);const c=pc++;this.name=`${this.enableDirect?"jcp":"jcw"}_${i}_${c.toString(16)}`}}function hc(e){let t=dc[e];return t||(e>=dc.length&&(dc.length=e+1),lc||(lc=Va()),dc[e]=t=lc.get(e)),t}let bc;function gc(){if(void 0!==uc)return uc;try{bc=function(){const e=new Ra(0);e.defineType("jit_call_cb",{cb_data:127},64,!0),e.defineType("do_jit_call",{unused:127,cb_data:127,thrown:127},64,!0),e.defineImportedFunction("i","jit_call_cb","jit_call_cb",!0),e.defineFunction({type:"do_jit_call",name:"do_jit_call_indirect",export:!0,locals:{}},(()=>{e.block(64,6),e.local("cb_data"),e.callImport("jit_call_cb"),e.appendU8(25),e.local("thrown"),e.i32_const(1),e.appendU8(54),e.appendMemarg(0,0),e.endBlock(),e.appendU8(11)})),e.appendU32(1836278016),e.appendU32(1),e.generateTypeSection(),e.emitImportsAndFunctions(!1);const t=e.getArrayView();return new WebAssembly.Module(t)}(),uc=!0}catch(e){ut("Disabling WASM EH support due to JIT failure",e),uc=!1}return uc}function yc(){if(0===fc.length)return;let e=cc;if(e?e.clear(0):(cc=e=new Ra(0),e.defineType("trampoline",{ret_sp:127,sp:127,ftndesc:127,thrown:127},64,!0)),e.options.wasmBytesLimit<=Oa.bytesGenerated)return void(fc.length=0);e.options.enableWasmEh&&(gc()||(is({enableWasmEh:!1}),e.options.enableWasmEh=!1));const t=Ma();let n=0,r=!0,o=!1;const a=[];try{lc||(lc=Va()),e.appendU32(1836278016),e.appendU32(1);for(let t=0;t<fc.length;t++){const n=fc[t],r={};if(n.enableDirect){n.hasThisReference&&(r.this=127);for(let e=0;e<n.wasmNativeSignature.length;e++)r[`arg${e}`]=n.wasmNativeSignature[e];r.rgctx=127}else{const e=(n.hasThisReference?1:0)+(n.hasReturnValue?1:0)+n.paramCount;for(let t=0;t<e;t++)r[`arg${t}`]=127;r.ftndesc=127}e.defineType(n.name,r,n.enableDirect?n.wasmNativeReturnType:64,!1);const o=hc(n.target);"function"!=typeof o&&w(!1,`expected call target to be function but was ${o}`),a.push([n.name,n.name,o])}e.generateTypeSection(),e.compressImportNames=!0;for(let t=0;t<a.length;t++)e.defineImportedFunction("i",a[t][0],a[t][1],!1,a[t][2]);for(let t=0;t<a.length;t++)e.markImportAsUsed(a[t][0]);e._generateImportSection(!1),e.beginSection(3),e.appendULeb(fc.length),e.functionTypes.trampoline||w(!1,"func type missing");for(let t=0;t<fc.length;t++)e.appendULeb(e.functionTypes.trampoline[0]);e.beginSection(7),e.appendULeb(fc.length);for(let t=0;t<fc.length;t++){const n=fc[t];e.appendName(n.name),e.appendU8(0),e.appendULeb(e.importedFunctionCount+t)}e.beginSection(10),e.appendULeb(fc.length);for(let t=0;t<fc.length;t++){const n=fc[t];if(e.beginFunction("trampoline",{old_sp:127}),!Ec(e,n))throw new Error(`Failed to generate ${n.name}`);e.appendU8(11),e.endFunction(!0)}e.endSection(),n=Ma();const t=e.getArrayView();Oa.bytesGenerated+=t.length;const o=new WebAssembly.Module(t),s=e.getWasmImports(),i=new WebAssembly.Instance(o,s);for(let e=0;e<fc.length;e++){const t=fc[e],n=Wa(i.exports[t.name]);if(!n)throw new Error("add_function_pointer returned a 0 index");t.result=n,St.mono_jiterp_register_jit_call_thunk(t.cinfo,n);for(let e=0;e<t.queue.length;e++)St.mono_jiterp_register_jit_call_thunk(t.queue[e],n);t.enableDirect&&Oa.directJitCallsCompiled++,Oa.jitCallsCompiled++,t.queue.length=0,r=!1}}catch(e){o=!0,r=!1,dt(`jit_call code generation failed: ${e}`),Ya()}finally{const a=Ma();if(n?(Ba.generation+=n-t,Ba.compilation+=a-n):Ba.generation+=a-t,o||r)for(let e=0;e<fc.length;e++)fc[e].result=-1;if(o){ut(`// ${fc.length} jit call wrappers generated, blob follows //`);for(let e=0;e<fc.length;e++)ut(`// #${e} === ${fc[e].name} hasThis=${fc[e].hasThisReference} hasRet=${fc[e].hasReturnValue} wasmArgTypes=${fc[e].wasmNativeSignature}`);let t="",n=0;try{e.inSection&&e.endSection()}catch(e){}const r=e.getArrayView();for(let e=0;e<r.length;e++){const o=r[e];o<16&&(t+="0"),t+=o.toString(16),t+=" ",t.length%10==0&&(ut(`${n}\t${t}`),t="",n=e+1)}ut(`${n}\t${t}`),ut("// end blob //")}else r&&!o&&dt("failed to generate trampoline for unknown reason");fc.length=0}}const wc={65535:127,70:127,71:127,72:127,73:127,74:127,75:127,76:126,77:127,78:125,79:124,80:127,81:127,82:127,83:127,84:127,85:126,86:125,87:124,223:127},Sc={70:44,71:45,72:46,73:47,74:40,75:40,76:41,77:40,78:42,79:43,80:40,81:54,82:58,83:59,84:54,85:55,86:56,87:57,223:54};function kc(e,t,n){e.local("sp"),e.appendU8(n),e.appendMemarg(t,0)}function vc(e,t){e.local("sp"),e.i32_const(t),e.appendU8(106)}function Ec(e,t){let n=0;e.options.enableWasmEh&&e.block(64,6),t.hasReturnValue&&t.enableDirect&&e.local("ret_sp"),t.hasThisReference&&(kc(e,t.argOffsets[0],40),n++),t.hasReturnValue&&!t.enableDirect&&e.local("ret_sp");for(let r=0;r<t.paramCount;r++){const o=t.argOffsets[n+r];if(ae(ue(t.cinfo+sc)+r)==ic)kc(e,o,40);else if(t.enableDirect){const n=St.mono_jiterp_type_to_ldind(t.paramTypes[r]);if(n||w(!1,`No load opcode for ${t.paramTypes[r]}`),65535===n)vc(e,o);else{const a=Sc[n];if(!a)return dt(`No wasm load op for arg #${r} type ${t.paramTypes[r]} cil opcode ${n}`),!1;kc(e,o,a)}}else vc(e,o)}if(e.local("ftndesc"),(t.enableDirect||t.noWrapper)&&(e.appendU8(40),e.appendMemarg(4,0)),e.callImport(t.name),t.hasReturnValue&&t.enableDirect){const n=St.mono_jiterp_type_to_stind(t.returnType),r=Sc[n];if(!r)return dt(`No wasm store op for return type ${t.returnType} cil opcode ${n}`),!1;e.appendU8(r),e.appendMemarg(0,0)}return e.options.enableWasmEh&&(e.appendU8(25),e.local("thrown"),e.i32_const(1),e.appendU8(54),e.appendMemarg(0,2),e.endBlock()),e.appendU8(15),!0}var Uc,Ic;!function(e){e[e.Sending=0]="Sending",e[e.Closed=1]="Closed",e[e.Error=2]="Error"}(Uc||(Uc={})),function(e){e[e.Idle=0]="Idle",e[e.PartialCommand=1]="PartialCommand",e[e.Error=2]="Error"}(Ic||(Ic={}));function xc(e,t){return"\ud800"<=e[t]&&e[t]<="\udbff"&&t+1<e.length&&"\udc00"<=e[t+1]&&e[t+1]<="\udfff"}function Tc(e,t,n,r){H(e,t+2*r,n.charCodeAt(0)),H(e,t+2*(r+1),n.charCodeAt(1))}function jc(e,t,n,r){switch(r){case 0:return n&&"ja"===n.split("-")[0]?-2:e.localeCompare(t,n);case 8:return n&&"ja"!==n.split("-")[0]?-2:e.localeCompare(t,n);case 1:return e=e.toLocaleLowerCase(n),t=t.toLocaleLowerCase(n),e.localeCompare(t,n);case 4:case 12:return e.localeCompare(t,n,{ignorePunctuation:!0});case 5:return e=e.toLocaleLowerCase(n),t=t.toLocaleLowerCase(n),e.localeCompare(t,n,{ignorePunctuation:!0});case 9:return e.localeCompare(t,n,{sensitivity:"accent"});case 10:return e.localeCompare(t,n,{sensitivity:"case"});case 11:return e.localeCompare(t,n,{sensitivity:"base"});case 13:return e.localeCompare(t,n,{sensitivity:"accent",ignorePunctuation:!0});case 14:return e.localeCompare(t,n,{sensitivity:"case",ignorePunctuation:!0});case 15:return e.localeCompare(t,n,{sensitivity:"base",ignorePunctuation:!0});default:throw new Error(`Invalid comparison option. Option=${r}`)}}function Ac(e,t){return Rc(et(e,e+2*t))}function Rc(e){return e.normalize().replace(/[\u200B-\u200D\uFEFF\0]/g,"")}const Lc="||";function $c(e){if(e)try{(e=e.toLocaleLowerCase()).includes("zh")&&(e=e.replace("chs","HANS").replace("cht","HANT"));const t=Intl.getCanonicalLocales(e.replace("_","-"));return t.length>0?t[0]:void 0}catch(t){throw new Error(`Get culture info failed for culture = ${e} with error: ${t}`)}}const Cc="MMMM",Nc="yyyy",Dc="d",Bc="dddd",Oc=[Cc,Nc,Dc,Bc];function Mc(e,t,n,r){let o=n;const a=t.indexOf(n);if(-1==a||-1!=a&&t.length>a+n.length&&" "!=t[a+n.length]&&","!=t[a+n.length]&&"،"!=t[a+n.length]){const a=r.format(e).toLowerCase();o=t.split(/,| /).filter((e=>!a.split(/,| /).includes(e)&&e[0]==n[0]))[0]}return o}async function Fc(e,t){try{const n=await Pc(e,t);return u.mono_exit(n),n}catch(e){try{u.mono_exit(1,e)}catch(e){}return e&&"number"==typeof e.status?e.status:1}}async function Pc(e,n){!function(e,n){const r=n.length+1,o=t._malloc(4*r);let a=0;t.setValue(o+4*a,St.mono_wasm_strdup(e),"i32"),a+=1;for(let e=0;e<n.length;++e)t.setValue(o+4*a,St.mono_wasm_strdup(n[e]),"i32"),a+=1;St.mono_wasm_set_main_args(r,o)}(e,n),-1==l.waitForDebugger&&(ut("waiting for debugger..."),await new Promise((e=>{const t=setInterval((()=>{1==l.waitForDebugger&&(clearInterval(t),e())}),100)})));const r=zc(e);return l.javaScriptExports.call_entry_point(r,n)}function zc(e){u.assert_runtime_running(),fr();const t=wr(e);if(!t)throw new Error("Could not find assembly: "+e);let n=0;1==l.waitForDebugger&&(n=1);const r=St.mono_wasm_assembly_get_entry_point(t,n);if(!r)throw new Error("Could not find entry point for assembly: "+e);return r}let Vc,Wc;const Hc={},Gc=Symbol.for("wasm type");function qc(e){return"undefined"!=typeof SharedArrayBuffer?e.buffer instanceof ArrayBuffer||e.buffer instanceof SharedArrayBuffer:e.buffer instanceof ArrayBuffer}function Jc(e,t,n){switch(!0){case null===t:case void 0===t:return void n.clear();case"symbol"==typeof t:case"string"==typeof t:return void dl._create_uri_ref(t,n.address);default:return void Zc(e,t,n)}}function Yc(e){ul();const t=Be();try{return Xc(e,t,!1),t.value}finally{t.release()}}function Xc(e,t,n){if(ul(),A(t))throw new Error("Expected (value, WasmRoot, boolean)");switch(!0){case null===e:case void 0===e:return void t.clear();case"number"==typeof e:{let n;return(0|e)===e?(X(Hc._box_buffer,e),n=Hc._class_int32):e>>>0===e?(G(Hc._box_buffer,e),n=Hc._class_uint32):(re(Hc._box_buffer,e),n=Hc._class_double),void kt.mono_wasm_box_primitive_ref(n,Hc._box_buffer,8,t.address)}case"string"==typeof e:return void ot(e,t);case"symbol"==typeof e:return void at(e,t);case"boolean"==typeof e:return z(Hc._box_buffer,e),void kt.mono_wasm_box_primitive_ref(Hc._class_boolean,Hc._box_buffer,4,t.address);case!0===Hr(e):return void function(e,t){if(!e)return t.clear(),null;const n=Dr(e),r=dl._create_tcs(),o={tcs_gc_handle:r};Or(o,r),e.then((e=>{dl._set_tcs_result_ref(r,e)}),(e=>{dl._set_tcs_failure(r,e?e.toString():"")})).finally((()=>{Br(n),Mr(o,r)})),dl._get_tcs_task_ref(r,t.address)}(e,t);case"Date"===e.constructor.name:return void dl._create_date_time_ref(e.getTime(),t.address);default:return void Zc(n,e,t)}}function Zc(e,t,n){if(n.clear(),null!=t)if(void 0===t[Lr]){if(t[$r]&&(function(e,t,n){e!==x&&e!==I?dl._get_cs_owned_object_by_js_handle_ref(e,t?1:0,n):X(n,0)}(t[$r],e,n.address),n.value||delete t[$r]),!n.value){const r=t[Gc],o=void 0===r?0:r,a=Dr(t);dl._create_cs_owned_proxy_ref(a,o,e?1:0,n.address)}}else vl(Fr(t),n.address)}function Kc(e,n){if(!qc(e)||!e.BYTES_PER_ELEMENT)throw new Error("Object '"+e+"' is not a typed array");{const r=e[Gc],o=function(e){ul();const n=e.length*e.BYTES_PER_ELEMENT,r=t._malloc(n),o=Ee(),a=new Uint8Array(o.buffer,r,n);return a.set(new Uint8Array(e.buffer,e.byteOffset,n)),a}(e);kt.mono_wasm_typed_array_new_ref(o.byteOffset,e.length,e.BYTES_PER_ELEMENT,r,n.address),t._free(o.byteOffset)}}function Qc(e){const t=Be();try{return Kc(e,t),t.value}finally{t.release()}}function el(e){if("number"!=typeof e)throw new Error(`Expected numeric value for enum argument, got '${e}'`);return 0|e}const tl=/[^A-Za-z0-9_$]/g,nl=new Map,rl=new Map,ol=new Map;function al(e,t,n,r){let o=null,a=null,s=null;if(r){s=Object.keys(r),a=new Array(s.length);for(let e=0,t=s.length;e<t;e++)a[e]=r[s[e]]}const i=function(e,t,n,r){let o="",a="";e?(o="//# sourceURL=https://dotnet.generated.invalid/"+e+"\r\n",a=e):a="unnamed";let s="function "+a+"("+t.join(", ")+") {\r\n"+n+"\r\n};\r\n";s=o+'"use strict";\r\n'+s.replace(/\r(\n?)/g,"\r\n    ")+`    return ${a};\r\n`;let i=null,c=null;return c=r?r.concat([s]):[s],i=Function.apply(Function,c),i}(e,t,n,s);return o=i.apply(null,a),o}function sl(e,n,r,o){if(ul(),"string"!=typeof n)throw new Error("args_marshal argument invalid, expected string");const a=`managed_${e}_${n}`;let s=ol.get(a);if(s)return s;o||(o=a);let i=null;"string"==typeof n&&(i=function(e){const n=function(e){let t=rl.get(e);return t||(t=function(e){const t=[];let n=0,r=!1,o=!1,a=-1,s=!1;for(let i=0;i<e.length;++i){const c=e[i];if(i===e.length-1){if("!"===c){r=!0;continue}"m"===c&&(o=!0,a=e.length-1)}else if("!"===c)throw new Error("! must be at the end of the signature");const l=nl.get(c);if(!l)throw new Error("Unknown parameter type "+c);const u=Object.create(l.steps[0]);u.size=l.size,l.needs_root&&(s=!0),u.needs_root=l.needs_root,u.key=c,t.push(u),n+=l.size}return{steps:t,size:n,args_marshal:e,is_result_definitely_unmarshaled:r,is_result_possibly_unmarshaled:o,result_unmarshaled_if_argc:a,needs_root_buffer:s}}(e),rl.set(e,t)),t}(e);if("string"!=typeof n.args_marshal)throw new Error("Corrupt converter for '"+e+"'");if(n.compiled_function&&n.compiled_variadic_function)return n;const r=e.replace("!","_result_unmarshaled");n.name=r;let o=[],a=["method"];const s={Module:t,setI32:Z,setU32:q,setF32:ne,setF64:re,setU52:ee,setI52:Q,setB32:z,setI32_unchecked:X,setU32_unchecked:G,scratchValueRoot:n.scratchValueRoot,stackAlloc:t.stackAlloc,_zero_region:P};let i=0;const c=8*((4*e.length+7)/8|0),l=n.size+4*e.length+16;o.push("if (!method) throw new Error('no method provided');",`const buffer = stackAlloc(${l});`,`_zero_region(buffer, ${l});`,`const indirectStart = buffer + ${c};`,"");for(let e=0;e<n.steps.length;e++){const r=n.steps[e],c="step"+e,l="value"+e,u="arg"+e,p=`(indirectStart + ${i})`;if(a.push(u),r.convert_root){if(r.indirect&&w(!1,"converter step cannot both be rooted and indirect"),!n.scratchValueRoot){const e=t.stackSave();n.scratchValueRoot=De(e),s.scratchValueRoot=n.scratchValueRoot}s[c]=r.convert_root,o.push(`scratchValueRoot._set_address(${p});`),o.push(`${c}(${u}, scratchValueRoot);`),r.byref?o.push(`let ${l} = ${p};`):o.push(`let ${l} = scratchValueRoot.value;`)}else r.convert?(s[c]=r.convert,o.push(`let ${l} = ${c}(${u}, method, ${e});`)):o.push(`let ${l} = ${u};`);if(r.needs_root&&!r.convert_root&&(o.push("if (!rootBuffer) throw new Error('no root buffer provided');"),o.push(`rootBuffer.set (${e}, ${l});`)),r.indirect){switch(r.indirect){case"bool":o.push(`setB32(${p}, ${l});`);break;case"u32":o.push(`setU32(${p}, ${l});`);break;case"i32":o.push(`setI32(${p}, ${l});`);break;case"float":o.push(`setF32(${p}, ${l});`);break;case"double":o.push(`setF64(${p}, ${l});`);break;case"i52":o.push(`setI52(${p}, ${l});`);break;case"u52":o.push(`setU52(${p}, ${l});`);break;default:throw new Error("Unimplemented indirect type: "+r.indirect)}o.push(`setU32_unchecked(buffer + (${e} * 4), ${p});`),i+=r.size}else o.push(`setU32_unchecked(buffer + (${e} * 4), ${l});`),i+=4;o.push("")}o.push("return buffer;");let u=o.join("\r\n"),p=null,d=null;try{p=al("converter_"+r,a,u,s),n.compiled_function=p}catch(e){throw n.compiled_function=null,pt("compiling converter failed for",u,"with error",e),e}a=["method","args"];const _={converter:p};o=["return converter(","  method,"];for(let e=0;e<n.steps.length;e++)o.push("  args["+e+(e==n.steps.length-1?"]":"], "));o.push(");"),u=o.join("\r\n");try{d=al("variadic_converter_"+r,a,u,_),n.compiled_variadic_function=d}catch(e){throw n.compiled_variadic_function=null,pt("compiling converter failed for",u,"with error",e),e}return n.scratchRootBuffer=null,n.scratchBuffer=j,n}(n));const c=t._malloc(128),l={method:e,converter:i,scratchRootBuffer:null,scratchBuffer:j,scratchResultRoot:Be(),scratchExceptionRoot:Be(),scratchThisArgRoot:Be()},u={Module:t,mono_wasm_new_root:Be,get_js_owned_object_by_gc_handle_ref:vl,_create_temp_frame:M,_handle_exception_for_call:il,_teardown_after_call:Il,mono_wasm_try_unbox_primitive_and_get_type_ref:kt.mono_wasm_try_unbox_primitive_and_get_type_ref,_unbox_mono_obj_root_with_known_nonprimitive_type:yl,invoke_method_ref:kt.mono_wasm_invoke_method_ref,method:e,token:l,unbox_buffer:c,unbox_buffer_size:128,getB32:oe,getI32:_e,getU32:ie,getF32:be,getF64:ge,stackSave:t.stackSave},p=i?"converter_"+i.name:"";i&&(u[p]=i);const d=[],_=["_create_temp_frame();","let resultRoot = token.scratchResultRoot, exceptionRoot = token.scratchExceptionRoot, thisArgRoot = token.scratchThisArgRoot , sp = stackSave();","token.scratchResultRoot = null;","token.scratchExceptionRoot = null;","token.scratchThisArgRoot = null;","if (resultRoot === null)","\tresultRoot = mono_wasm_new_root ();","if (exceptionRoot === null)","\texceptionRoot = mono_wasm_new_root ();","if (thisArgRoot === null)","\tthisArgRoot = mono_wasm_new_root ();",""];if(i){_.push(`let buffer = ${p}.compiled_function(`,"    method,");for(let e=0;e<i.steps.length;e++){const t="arg"+e;d.push(t),_.push("    "+t+(e==i.steps.length-1?"":", "))}_.push(");")}else _.push("let buffer = 0;");if(i&&i.is_result_definitely_unmarshaled?_.push("let is_result_marshaled = false;"):i&&i.is_result_possibly_unmarshaled?_.push(`let is_result_marshaled = arguments.length !== ${i.result_unmarshaled_if_argc};`):_.push("let is_result_marshaled = true;"),_.push("","",""),r?(_.push("get_js_owned_object_by_gc_handle_ref(this.this_arg_gc_handle, thisArgRoot.address);"),_.push("invoke_method_ref (method, thisArgRoot.address, buffer, exceptionRoot.address, resultRoot.address);")):_.push("invoke_method_ref (method, 0, buffer, exceptionRoot.address, resultRoot.address);"),_.push(`_handle_exception_for_call (${p}, token, buffer, resultRoot, exceptionRoot, thisArgRoot, sp);`,"","let resultPtr = resultRoot.value, result = undefined;"),!i)throw new Error("No converter");i.is_result_possibly_unmarshaled&&_.push("if (!is_result_marshaled) "),(i.is_result_definitely_unmarshaled||i.is_result_possibly_unmarshaled)&&_.push("    result = resultPtr;"),i.is_result_definitely_unmarshaled||_.push("if (is_result_marshaled) {","    let resultType = mono_wasm_try_unbox_primitive_and_get_type_ref (resultRoot.address, unbox_buffer, unbox_buffer_size);","    switch (resultType) {","    case 1:","        result = getI32(unbox_buffer); break;","    case 32:","    case 25:","        result = getU32(unbox_buffer); break;","    case 24:","        result = getF32(unbox_buffer); break;","    case 2:","        result = getF64(unbox_buffer); break;","    case 8:","        result = getB32(unbox_buffer); break;","    case 28:","        result = String.fromCharCode(getI32(unbox_buffer)); break;","    case 0:","        result = null; break;","    default:","        result = _unbox_mono_obj_root_with_known_nonprimitive_type (resultRoot, resultType, unbox_buffer); break;","    }","}");let f=o.replace(tl,"_");return r&&(f+="_this"),_.push(`_teardown_after_call (${p}, token, buffer, resultRoot, exceptionRoot, thisArgRoot, sp);`,"return result;"),s=al(f,d,_.join("\r\n"),u),ol.set(a,s),s}function il(e,t,n,r,o,a,s){const i=function(e,t){if(t.value===S)return null;const n=rt(e);return new Error(n)}(r,o);if(i)throw Il(0,t,0,r,o,a,s),i}function cl(e){const{assembly:t,namespace:n,classname:r,methodname:o}=Ur(e),a=kt.mono_wasm_assembly_load(t);if(!a)throw new Error("Could not find assembly: "+t);const s=kt.mono_wasm_assembly_find_class(a,n,r);if(!s)throw new Error("Could not find class: "+n+":"+r+" in assembly "+t);const i=kt.mono_wasm_assembly_find_method(s,o,-1);if(!i)throw new Error("Could not find method: "+o);return i}function ll(e,t){return dl._get_call_sig_ref(e,t?t.address:Hc._null_root.address)}function ul(){fr()}const pl=[[!0,"_get_cs_owned_object_by_js_handle_ref","GetCSOwnedObjectByJSHandleRef","iim"],[!0,"_get_cs_owned_object_js_handle_ref","GetCSOwnedObjectJSHandleRef","mi"],[!0,"_try_get_cs_owned_object_js_handle_ref","TryGetCSOwnedObjectJSHandleRef","mi"],[!0,"_create_cs_owned_proxy_ref","CreateCSOwnedProxyRef","iiim"],[!0,"_get_js_owned_object_by_gc_handle_ref","GetJSOwnedObjectByGCHandleRef","im"],[!0,"_get_js_owned_object_gc_handle_ref","GetJSOwnedObjectGCHandleRef","m"],[!0,"_create_tcs","CreateTaskSource",""],[!0,"_set_tcs_result_ref","SetTaskSourceResultRef","iR"],[!0,"_set_tcs_failure","SetTaskSourceFailure","is"],[!0,"_get_tcs_task_ref","GetTaskSourceTaskRef","im"],[!0,"_setup_js_cont_ref","SetupJSContinuationRef","mo"],[!0,"_object_to_string_ref","ObjectToStringRef","m"],[!0,"_get_date_value_ref","GetDateValueRef","m"],[!0,"_create_date_time_ref","CreateDateTimeRef","dm"],[!0,"_create_uri_ref","CreateUriRef","sm"],[!0,"_is_simple_array_ref","IsSimpleArrayRef","m"],[!0,"_get_call_sig_ref","GetCallSignatureRef","im"]],dl={};function _l(e,t){const n=function(e){const t=St.mono_wasm_assembly_find_method(Hc.runtime_legacy_exports_class,e,-1);if(!t)throw"Can't find method "+l.runtime_interop_namespace+"."+Hc.runtime_legacy_exports_classname+"."+e;return t}(e);return sl(n,t,!1,"BINDINGS_"+e)}let fl;function ml(e){ul();const t=Be();try{return ot(e,t),t.value}finally{t.release()}}function hl(e){if(0===e.length)return ze;const t=Be();try{at(e,t);const n=We.get(t.value);return A(n)&&w(!1,"internal error: interned_string_table did not contain string after stringToMonoStringIntern"),n}finally{t.release()}}const bl=Symbol.for("wasm delegate_invoke");function gl(e){if(ul(),e===S)return;const t=Be(e);try{return wl(t)}finally{t.release()}}function yl(e,t,n){if(t>=512)throw new Error(`Got marshaling error ${t} when attempting to unbox object at address ${e.value} (root located at ${e.address})`);let r=v;if((4===t||7==t)&&(r=ie(n),r<1024))throw new Error(`Got invalid MonoType ${r} for object at address ${e.value} (root located at ${e.address})`);return function(e,t,n,r){switch(t){case 0:return null;case 26:case 27:throw new Error("int64 not available");case 3:case 29:return rt(e);case 4:throw new Error("no idea on how to unbox value types");case 5:return function(e){return e.value===S?null:function(e){let t=zr(e);if(t)Fr(t);else{t=function(...e){return Fr(t),(0,t[bl])(...e)};const n=Be();vl(e,n.address);try{if(void 0===t[bl]){const r=kt.mono_wasm_get_delegate_invoke_ref(n.address),o=sl(r,ll(r,n),!0);if(t[bl]=o.bind({this_arg_gc_handle:e}),!t[bl])throw new Error("System.Delegate Invoke method can not be resolved.")}}finally{n.release()}Or(t,e)}return t}(dl._get_js_owned_object_gc_handle_ref(e.address))}(e);case 6:return function(e){if(e.value===S)return null;if(!Wr)throw new Error("Promises are not supported thus 'System.Threading.Tasks.Task' can not work in this context.");const t=dl._get_js_owned_object_gc_handle_ref(e.address);let n=zr(t);if(!n){const r=()=>Mr(n,t),{promise:o,promise_control:a}=y(r,r);n=o,dl._setup_js_cont_ref(e.address,a),Or(n,t)}return n}(e);case 7:return function(e){if(e.value===S)return null;const t=dl._try_get_cs_owned_object_js_handle_ref(e.address,0);if(t){if(t===I)throw new Error("Cannot access a disposed JSObject at "+e.value);return Nr(t)}const n=dl._get_js_owned_object_gc_handle_ref(e.address);let r=zr(n);return A(r)&&(r=new ManagedObject,Or(r,n)),r}(e);case 10:case 11:case 12:case 13:case 14:case 15:case 16:case 17:case 18:throw new Error("Marshaling of primitive arrays are not supported.");case 20:return new Date(dl._get_date_value_ref(e.address));case 21:case 22:return dl._object_to_string_ref(e.address);case 23:return function(e){return Nr(dl._get_cs_owned_object_js_handle_ref(e.address,0))}(e);case 30:return;default:throw new Error(`no idea on how to unbox object of MarshalType ${t} at offset ${e.value} (root address is ${e.address})`)}}(e,t)}function wl(e){if(0===e.value)return;const t=Hc._unbox_buffer,n=kt.mono_wasm_try_unbox_primitive_and_get_type_ref(e.address,t,Hc._unbox_buffer_size);switch(n){case 1:return _e(t);case 25:case 32:return ie(t);case 24:return be(t);case 2:return ge(t);case 8:return 0!==_e(t);case 28:return String.fromCharCode(_e(t));case 0:return null;default:return yl(e,n,t)}}function Sl(e){if(ul(),e===k)return null;const t=Be(e);try{return kl(t)}finally{t.release()}}function kl(e){if(e.value===k)return null;const t=e.address,n=Be(),r=n.address;try{const e=kt.mono_wasm_array_length_ref(t),a=new Array(e);for(let s=0;s<e;++s)kt.mono_wasm_array_get_ref(t,s,r),o=n,dl._is_simple_array_ref(o.address)?a[s]=kl(n):a[s]=wl(n);return a}finally{n.release()}var o}function vl(e,t){e?dl._get_js_owned_object_by_gc_handle_ref(e,t):X(t,0)}function El(e){return ul(),function(e){if(e===E)return null;ul(),fl||(fl=Be()),fl.value=e;const t=rt(fl);return fl.value=E,t}(e)}const Ul=new Map;function Il(e,n,r,o,a,s,i){!function(){if(!$.length)throw new Error("No temp frames have been created at this point");D=$.pop()}(),t.stackRestore(i),"object"==typeof o&&(o.clear(),null!==n&&null===n.scratchResultRoot?n.scratchResultRoot=o:o.release()),"object"==typeof a&&(a.clear(),null!==n&&null===n.scratchExceptionRoot?n.scratchExceptionRoot=a:a.release()),"object"==typeof s&&(s.clear(),null!==n&&null===n.scratchThisArgRoot?n.scratchThisArgRoot=s:s.release())}function xl(e,t){ul();const n=`${e}-${t}`;let r=Ul.get(n);if(void 0===r){const o=cl(e);void 0===t&&(t=ll(o,void 0)),r=sl(o,t,!1,e),Ul.set(n,r)}return r}function Tl(e,t,n){return ul(),t||(t=[[]]),function(e,t){ul();const n=zc(e);"string"!=typeof t&&(t=ll(n,void 0));const r=sl(n,t,!1,"_"+e+"__entrypoint");return async function(...e){return u.assert_runtime_running(),e.length>0&&Array.isArray(e[0])&&(e[0]=function(e,t,n){const r=Be();kt.mono_wasm_string_array_new_ref(e.length,r.address);const o=Be(S),a=r.address,s=o.address;try{for(let t=0;t<e.length;++t){let n=e[t];n=n.toString(),Xc(n,o,!1),kt.mono_wasm_obj_array_set_ref(a,t,s)}return r.value}finally{Oe(r,o)}}(e[0])),r(...e)}}(e,n)(...t)}const jl="ss",Al=[jl,"mm","tt","HH"];function Rl(e,t){let n=e.toLocaleTimeString(t,{hourCycle:"h12"});const r=(0).toLocaleString(t);if(n.includes(r)){const e=12..toLocaleString(t);n=n.replace(r,e)}const o=e.toLocaleTimeString(t,{hourCycle:"h24"}),a=n.replace(o,"").trim();if(new RegExp("[0-9]$").test(a)){const e=n.split(" ").filter((e=>new RegExp("^((?![0-9]).)*$").test(e)));return e&&0!=e.length?e.join(" "):""}return a}function Ll(e){try{return new Intl.Locale(e).weekInfo}catch(t){try{return new Intl.Locale(e).getWeekInfo()}catch(e){return}}}const $l=[function(e){zo&&(globalThis.clearTimeout(zo),zo=void 0),zo=t.safeSetTimeout(mono_wasm_schedule_timer_tick,e)},function(e,t,n,r,o){if(!0!==l.mono_wasm_runtime_is_ready)return;const a=Ee(),s=0!==e?Qe(e).concat(".dll"):"",i=It(new Uint8Array(a.buffer,t,n));let c;r&&(c=It(new Uint8Array(a.buffer,r,o))),zt({eventName:"AssemblyLoaded",assembly_name:s,assembly_b64:i,pdb_b64:c})},function(e,t){const r=Qe(t);n.logging&&"function"==typeof n.logging.debugger&&n.logging.debugger(e,r)},function(e,t,n,r){const o={res_ok:e,res:{id:t,value:It(new Uint8Array(Ee().buffer,n,r))}};Tt.has(t)&&pt(`Adding an id (${t}) that already exists in commands_received`),Tt.set(t,o)},function mono_wasm_fire_debugger_agent_message_with_data(e,t){mono_wasm_fire_debugger_agent_message_with_data_to_pause(It(new Uint8Array(Ee().buffer,e,t)))},mono_wasm_fire_debugger_agent_message_with_data_to_pause,function(){++Wo,t.safeSetTimeout(qo,0)},function(e,n,r,o,a,s,i){if(r||w(!1,"expected instruction pointer"),yi||(yi=cs()),!yi.enableTraces)return 1;if(yi.wasmBytesLimit<=Oa.bytesGenerated)return 1;let c,u=xi[r];if(u||(xi[r]=u=new vi(r,o,i)),Oa.traceCandidates++,yi.estimateHeat||Si.length>0||u.isVerbose){const e=St.mono_wasm_method_get_full_name(n);c=Qe(e),t._free(e)}const p=Qe(St.mono_wasm_method_get_name(n));u.name=c||p;const d=ue(Za(4)+e),_=ue(Za(11)+d),f=ue(Za(10)+d);let m=_?new Uint16Array(Ee().buffer,f,_):null;if(m&&r!==a){const e=(r-a)/2;let t=!1;for(let n=0;n<m.length;n++)if(m[n]>e){t=!0;break}t||(m=null)}const h=function(e,t,n,r,o,a,s){let i=Ri;i?i.clear(8):(Ri=i=new Ra(8),function(e){e.defineType("trace",{frame:127,pLocals:127,cinfo:127},127,!0),e.defineType("bailout",{retval:127,base:127,reason:127},127,!0),e.defineType("copy_ptr",{dest:127,src:127},64,!0),e.defineType("value_copy",{dest:127,src:127,klass:127},64,!0),e.defineType("entry",{imethod:127},127,!0),e.defineType("strlen",{ppString:127,pResult:127},127,!0),e.defineType("getchr",{ppString:127,pIndex:127,pResult:127},127,!0),e.defineType("getspan",{destination:127,span:127,index:127,element_size:127},127,!0),e.defineType("overflow_check_i4",{lhs:127,rhs:127,opcode:127},127,!0),e.defineType("mathop_d_d",{value:124},124,!0),e.defineType("mathop_dd_d",{lhs:124,rhs:124},124,!0),e.defineType("mathop_f_f",{value:125},125,!0),e.defineType("mathop_ff_f",{lhs:125,rhs:125},125,!0),e.defineType("fmaf",{x:125,y:125,z:125},125,!0),e.defineType("fma",{x:124,y:124,z:124},124,!0),e.defineType("trace_eip",{traceId:127,eip:127},64,!0),e.defineType("newobj_i",{ppDestination:127,vtable:127},127,!0),e.defineType("newstr",{ppDestination:127,length:127},127,!0),e.defineType("localloc",{destination:127,len:127,frame:127},64,!0),e.defineType("ld_del_ptr",{ppDestination:127,ppSource:127},64,!0),e.defineType("ldtsflda",{ppDestination:127,offset:127},64,!0),e.defineType("gettype",{destination:127,source:127},127,!0),e.defineType("castv2",{destination:127,source:127,klass:127,opcode:127},127,!0),e.defineType("hasparent",{klass:127,parent:127},127,!0),e.defineType("imp_iface",{vtable:127,klass:127},127,!0),e.defineType("imp_iface_s",{obj:127,vtable:127,klass:127},127,!0),e.defineType("box",{vtable:127,destination:127,source:127,vt:127},64,!0),e.defineType("conv",{destination:127,source:127,opcode:127},127,!0),e.defineType("relop_fp",{lhs:124,rhs:124,opcode:127},127,!0),e.defineType("safepoint",{frame:127,ip:127},64,!0),e.defineType("hashcode",{ppObj:127},127,!0),e.defineType("try_hash",{ppObj:127},127,!0),e.defineType("hascsize",{ppObj:127},127,!0),e.defineType("hasflag",{klass:127,dest:127,sp1:127,sp2:127},64,!0),e.defineType("array_rank",{destination:127,source:127},127,!0),e.defineType("stfld_o",{locals:127,fieldOffsetBytes:127,targetLocalOffsetBytes:127,sourceLocalOffsetBytes:127},127,!0),e.defineType("notnull",{ptr:127,expected:127,traceIp:127,ip:127},64,!0),e.defineType("cmpxchg_i32",{dest:127,newVal:127,expected:127},127,!0),e.defineType("cmpxchg_i64",{dest:127,newVal:127,expected:127,oldVal:127},64,!0),e.defineType("transfer",{displacement:127,trace:127,frame:127,locals:127,cinfo:127},127,!0),e.defineType("stelem_ref",{o:127,aindex:127,ref:127},127,!0),e.defineType("simd_p_p",{arg0:127,arg1:127},64,!0),e.defineType("simd_p_pp",{arg0:127,arg1:127,arg2:127},64,!0),e.defineType("simd_p_ppp",{arg0:127,arg1:127,arg2:127,arg3:127},64,!0);const t=Oi();for(let n=0;n<t.length;n++)t[n]||w(!1,`trace #${n} missing`),e.defineImportedFunction("i",t[n][0],t[n][1],!0,t[n][2])}(i)),yi=i.options;const c=r+o,u=`${t}:${(n-r).toString(16)}`,p=Ma();let d=0,_=!0,f=!1;const m=xi[n],h=m.isVerbose||a&&Si.findIndex((e=>a.indexOf(e)>=0))>=0;h&&!a&&w(!1,"Expected methodFullName if trace is instrumented");const b=h?Ui++:0;h&&(ut(`instrumenting: ${a}`),Ei[b]=new ki(a)),i.compressImportNames=!h;try{i.appendU32(1836278016),i.appendU32(1),i.generateTypeSection();const t={disp:127,cknull_ptr:127,dest_ptr:127,src_ptr:127,memop_dest:127,memop_src:127,index:127,count:127,math_lhs32:127,math_rhs32:127,math_lhs64:126,math_rhs64:126,temp_f32:125,temp_f64:124,backbranched:127};i.options.enableSimd&&(t.v128_zero=123,t.math_lhs128=123,t.math_rhs128=123);let o=!0,a=0;if(i.defineFunction({type:"trace",name:u,export:!0,locals:t},(()=>{if(i.base=n,i.frame=e,663!==se(n))throw new Error(`Expected *ip to be MINT_TIER_PREPARE_JITERPRETER but was ${se(n)}`);return i.cfg.initialize(r,s,h?1:0),a=function(e,t,n,r,o,a,s,i){let c=!0,l=!1,u=!0,p=!1,d=!1,_=!1,f=0,m=0,h=0;const b=n;Fs();let g=n+=2*St.mono_jiterp_get_opcode_info(665,1);for(a.cfg.entry(n);n&&n;){if(a.cfg.ip=n,n>=o){Pi(b,0,0,"end-of-body"),s&&ut(`instrumented trace ${t} exited at end of body @${n.toString(16)}`);break}const y=3840-a.bytesGeneratedSoFar-a.cfg.overheadBytes;if(a.size>=y){Pi(b,0,0,"trace-too-big"),s&&ut(`instrumented trace ${t} exited because of size limit at @${n.toString(16)} (spaceLeft=${y}b)`);break}let S=se(n);const k=St.mono_jiterp_get_opcode_info(S,2),v=St.mono_jiterp_get_opcode_info(S,3),E=St.mono_jiterp_get_opcode_info(S,1),U=S>=646&&S<=648,I=U?S-646+2:0,x=U?xs(n,1+I):0;S>=0&&S<674||w(!1,`invalid opcode ${S}`);const T=U?ls[I][x]:Ua(S),j=n,A=a.options.noExitBackwardBranches&&Cs(n,r,i),R=a.branchTargets.has(n),L=A||R||c&&i,$=h+m+a.branchTargets.size;let C=!1,N=es(S);switch(A&&a.backBranchOffsets.push(n),L&&(d=!1,_=!1,Vs(a,n,A),l=!0,u=!0,Fs(),h=0),N<-1&&l&&(N=-2===N?2:0),c=!1,279===S||(wi.indexOf(S)>=0?(Pa(a,n,23),S=667):d&&(S=667)),S){case 667:d&&(_||a.appendU8(0),_=!0);break;case 321:case 322:Js(a,xs(n,1),0,xs(n,2));break;case 320:qs(a,xs(n,1)),Hs(a,xs(n,2),40),a.local("frame"),a.callImport("localloc");break;case 294:Hs(a,xs(n,1),40),Ga(a,0,xs(n,2));break;case 318:{const e=xs(n,3),t=xs(n,2),r=xs(n,1),o=Ds(a,e);0!==o&&("number"!=typeof o?(Hs(a,e,40),a.local("count",34),a.block(64,4)):(a.i32_const(o),a.local("count",33)),Hs(a,r,40),a.local("dest_ptr",34),a.appendU8(69),Hs(a,t,40),a.local("src_ptr",34),a.appendU8(69),a.appendU8(114),a.block(64,4),Pa(a,n,2),a.endBlock(),"number"==typeof o&&qa(a,0,0,o,!1,"dest_ptr","src_ptr")||(a.local("dest_ptr"),a.local("src_ptr"),a.local("count"),a.appendU8(252),a.appendU8(10),a.appendU8(0),a.appendU8(0)),"number"!=typeof o&&a.endBlock());break}case 319:{const e=xs(n,3),t=xs(n,2);Zs(a,xs(n,1),n,!0),Hs(a,t,40),Hs(a,e,40),a.appendU8(252),a.appendU8(11),a.appendU8(0);break}case 151:case 153:case 235:case 237:case 152:case 154:case 137:case 140:case 141:ai(a,n,e,S)?l=!0:n=0;break;case 537:{const e=xs(n,2),t=xs(n,1);e!==t?(a.local("pLocals"),Zs(a,e,n,!0),Gs(a,t,54)):Zs(a,e,n,!1),a.allowNullCheckOptimization&&Bs.set(t,n),C=!0;break}case 627:case 628:{const t=ue(e+Za(4));a.ptr_const(t),a.callImport("entry"),a.block(64,4),Pa(a,n,1),a.endBlock();break}case 665:if(N=0,f>=a.options.minimumTraceValue&&!a.options.noExitBackwardBranches&&(!l||u)){const e=As(n,1);a.ip_const(n),a.i32_const(e),a.local("frame"),a.local("pLocals"),a.local("cinfo"),a.callImport("transfer"),a.appendU8(15),n=0}break;case 146:Fa(a,n);break;case 94:{a.local("pLocals");const e=xs(n,2);Xs(a,e)||dt(`${t}: Expected local ${e} to have address taken flag`),qs(a,e),Gs(a,xs(n,1),54);break}case 280:case 308:case 309:case 555:{a.local("pLocals");let t=Ls(e,xs(n,2));308===S&&(t=St.mono_jiterp_imethod_to_ftnptr(t)),a.ptr_const(t),Gs(a,xs(n,1),54);break}case 313:{const t=Ls(e,xs(n,3));Hs(a,xs(n,1),40),Hs(a,xs(n,2),40),a.ptr_const(t),a.callImport("value_copy");break}case 314:{const e=xs(n,3);Hs(a,xs(n,1),40),Hs(a,xs(n,2),40),Ja(a,e);break}case 315:{const e=xs(n,3);qs(a,xs(n,1),e),Zs(a,xs(n,2),n,!0),Ja(a,e);break}case 316:{const t=Ls(e,xs(n,3));Hs(a,xs(n,1),40),qs(a,xs(n,2),0),a.ptr_const(t),a.callImport("value_copy");break}case 317:{const e=xs(n,3);Hs(a,xs(n,1),40),qs(a,xs(n,2),0),Ja(a,e);break}case 539:a.local("pLocals"),Zs(a,xs(n,2),n,!0),a.appendU8(40),a.appendMemarg(Za(2),2),Gs(a,xs(n,1),54);break;case 538:{a.block(),Hs(a,xs(n,3),40),a.local("index",34);let e="cknull_ptr";a.options.zeroPageOptimization&&rs()?(Oa.nullChecksFused++,Hs(a,xs(n,2),40),e="src_ptr",a.local(e,34)):Zs(a,xs(n,2),n,!0),a.appendU8(40),a.appendMemarg(Za(2),2),a.appendU8(72),a.local("index"),a.i32_const(0),a.appendU8(78),a.appendU8(113),a.appendU8(13),a.appendULeb(0),Pa(a,n,11),a.endBlock(),a.local("pLocals"),a.local("index"),a.i32_const(2),a.appendU8(108),a.local(e),a.appendU8(106),a.appendU8(47),a.appendMemarg(Za(3),1),Gs(a,xs(n,1),54);break}case 349:case 350:{const e=Ts(n,4);a.block(),Hs(a,xs(n,3),40),a.local("index",34);let t="cknull_ptr";349===S?Zs(a,xs(n,2),n,!0):(qs(a,xs(n,2),0),t="src_ptr",a.local(t,34)),a.appendU8(40),a.appendMemarg(Za(7),2),a.appendU8(73),a.local("index"),a.i32_const(0),a.appendU8(78),a.appendU8(113),a.appendU8(13),a.appendULeb(0),Pa(a,n,18),a.endBlock(),a.local("pLocals"),a.local(t),a.appendU8(40),a.appendMemarg(Za(8),2),a.local("index"),a.i32_const(e),a.appendU8(108),a.appendU8(106),Gs(a,xs(n,1),54);break}case 653:a.block(),Hs(a,xs(n,3),40),a.local("count",34),a.i32_const(0),a.appendU8(78),a.appendU8(13),a.appendULeb(0),Pa(a,n,18),a.endBlock(),qs(a,xs(n,1),16),a.local("dest_ptr",34),Hs(a,xs(n,2),40),a.appendU8(54),a.appendMemarg(0,0),a.local("dest_ptr"),a.local("count"),a.appendU8(54),a.appendMemarg(4,0);break;case 567:qs(a,xs(n,1),8),qs(a,xs(n,2),8),a.callImport("ld_del_ptr");break;case 81:qs(a,xs(n,1),4),a.ptr_const(js(n,2)),a.callImport("ldtsflda");break;case 652:a.block(),qs(a,xs(n,1),4),qs(a,xs(n,2),0),a.callImport("gettype"),a.appendU8(13),a.appendULeb(0),Pa(a,n,2),a.endBlock();break;case 649:{const t=Ls(e,xs(n,4));a.ptr_const(t),qs(a,xs(n,1),4),qs(a,xs(n,2),0),qs(a,xs(n,3),0),a.callImport("hasflag");break}case 658:{const e=Za(1);a.local("pLocals"),Zs(a,xs(n,2),n,!0),a.i32_const(e),a.appendU8(106),Gs(a,xs(n,1),54);break}case 650:a.local("pLocals"),qs(a,xs(n,2),0),a.callImport("hashcode"),Gs(a,xs(n,1),54);break;case 651:a.local("pLocals"),qs(a,xs(n,2),0),a.callImport("try_hash"),Gs(a,xs(n,1),54);break;case 654:a.local("pLocals"),qs(a,xs(n,2),0),a.callImport("hascsize"),Gs(a,xs(n,1),54);break;case 659:a.local("pLocals"),Hs(a,xs(n,2),40),a.local("math_lhs32",34),Hs(a,xs(n,3),40),a.appendU8(115),a.i32_const(2),a.appendU8(116),a.local("math_rhs32",33),a.local("math_lhs32"),a.i32_const(327685),a.appendU8(106),a.i32_const(10485920),a.appendU8(114),a.i32_const(1703962),a.appendU8(106),a.i32_const(-8388737),a.appendU8(114),a.local("math_rhs32"),a.appendU8(113),a.appendU8(69),Gs(a,xs(n,1),54);break;case 540:case 541:a.block(),qs(a,xs(n,1),4),qs(a,xs(n,2),0),a.callImport(540===S?"array_rank":"a_elesize"),a.appendU8(13),a.appendULeb(0),Pa(a,n,2),a.endBlock();break;case 297:case 298:{const t=Ls(e,xs(n,3)),r=St.mono_jiterp_is_special_interface(t),o=297===S,s=xs(n,1);if(!t){Pi(b,0,0,"null-klass"),n=0;continue}a.block(),a.options.zeroPageOptimization&&rs()?(Hs(a,xs(n,2),40),a.local("dest_ptr",34),Oa.nullChecksFused++):(a.block(),Hs(a,xs(n,2),40),a.local("dest_ptr",34),a.appendU8(13),a.appendULeb(0),a.local("pLocals"),a.i32_const(0),Gs(a,s,54),a.appendU8(12),a.appendULeb(1),a.endBlock(),a.local("dest_ptr")),r&&a.local("dest_ptr"),a.appendU8(40),a.appendMemarg(Za(14),0),a.ptr_const(t),a.callImport(r?"imp_iface_s":"imp_iface"),o&&(a.local("dest_ptr"),a.appendU8(69),a.appendU8(114)),a.block(64,4),a.local("pLocals"),a.local("dest_ptr"),Gs(a,s,54),a.appendU8(5),o?Pa(a,n,19):(a.local("pLocals"),a.i32_const(0),Gs(a,s,54)),a.endBlock(),a.endBlock();break}case 299:case 300:case 295:case 296:{const t=Ls(e,xs(n,3)),r=299===S||300===S,o=295===S||299===S,s=xs(n,1);if(!t){Pi(b,0,0,"null-klass"),n=0;continue}a.block(),a.options.zeroPageOptimization&&rs()?(Hs(a,xs(n,2),40),a.local("dest_ptr",34),Oa.nullChecksFused++):(a.block(),Hs(a,xs(n,2),40),a.local("dest_ptr",34),a.appendU8(13),a.appendULeb(0),a.local("pLocals"),a.i32_const(0),Gs(a,s,54),a.appendU8(12),a.appendULeb(1),a.endBlock(),a.local("dest_ptr")),a.appendU8(40),a.appendMemarg(Za(14),0),a.appendU8(40),a.appendMemarg(Za(15),0),r&&a.local("src_ptr",34),a.i32_const(t),a.appendU8(70),a.block(64,4),a.local("pLocals"),a.local("dest_ptr"),Gs(a,s,54),a.appendU8(5),r?(a.local("src_ptr"),a.ptr_const(t),a.callImport("hasparent"),o&&(a.local("dest_ptr"),a.appendU8(69),a.appendU8(114)),a.block(64,4),a.local("pLocals"),a.local("dest_ptr"),Gs(a,s,54),a.appendU8(5),o?Pa(a,n,19):(a.local("pLocals"),a.i32_const(0),Gs(a,s,54)),a.endBlock()):(qs(a,xs(n,1),4),a.local("dest_ptr"),a.ptr_const(t),a.i32_const(S),a.callImport("castv2"),a.appendU8(69),a.block(64,4),Pa(a,n,19),a.endBlock()),a.endBlock(),a.endBlock();break}case 303:case 304:a.ptr_const(Ls(e,xs(n,3))),qs(a,xs(n,1),4),qs(a,xs(n,2),0),a.i32_const(304===S?1:0),a.callImport("box");break;case 307:{const t=Ls(e,xs(n,3)),r=Za(17),o=xs(n,1),s=ue(t+r);if(!t||!s){Pi(b,0,0,"null-klass"),n=0;continue}a.options.zeroPageOptimization&&rs()?(Hs(a,xs(n,2),40),a.local("dest_ptr",34),Oa.nullChecksFused++):(Zs(a,xs(n,2),n,!0),a.local("dest_ptr",34)),a.appendU8(40),a.appendMemarg(Za(14),0),a.appendU8(40),a.appendMemarg(Za(15),0),a.local("src_ptr",34),a.appendU8(40),a.appendMemarg(r,0),a.i32_const(s),a.appendU8(70),a.local("src_ptr"),a.appendU8(45),a.appendMemarg(Za(16),0),a.appendU8(69),a.appendU8(113),a.block(64,4),a.local("pLocals"),a.local("dest_ptr"),a.i32_const(Za(18)),a.appendU8(106),Gs(a,o,54),a.appendU8(5),Pa(a,n,21),a.endBlock();break}case 302:a.block(),qs(a,xs(n,1),4),Hs(a,xs(n,2),40),a.callImport("newstr"),a.appendU8(13),a.appendULeb(0),Pa(a,n,17),a.endBlock();break;case 291:a.block(),qs(a,xs(n,1),4),a.ptr_const(Ls(e,xs(n,2))),a.callImport("newobj_i"),a.appendU8(13),a.appendULeb(0),Pa(a,n,17),a.endBlock();break;case 293:{const e=xs(n,3);qs(a,xs(n,2),e),Ga(a,0,e),a.local("pLocals"),qs(a,xs(n,2),e),Gs(a,xs(n,1),54);break}case 290:case 292:case 543:case 542:l?(za(a,n,$,15),d=!0,N=0):n=0;break;case 545:case 546:case 547:case 548:case 544:l?(za(a,n,$,544==S?22:15),d=!0):n=0;break;case 145:case 142:Pa(a,n,16),d=!0;break;case 138:case 139:Pa(a,n,26),d=!0;break;case 144:if(a.callHandlerReturnAddresses.length>0&&a.callHandlerReturnAddresses.length<=3){const t=$s(e,xs(n,1));a.local("pLocals"),a.appendU8(40),a.appendMemarg(t,0),a.local("index",33);for(let e=0;e<a.callHandlerReturnAddresses.length;e++){const t=a.callHandlerReturnAddresses[e];a.local("index"),a.ptr_const(t),a.appendU8(70),a.cfg.branch(t,t<n,1)}Pa(a,n,25)}else n=0;break;case 143:case 624:case 625:n=0;break;case 500:case 505:case 501:case 503:case 510:case 502:case 509:case 504:a.block(),qs(a,xs(n,1),8),qs(a,xs(n,2),0),a.i32_const(S),a.callImport("conv"),a.appendU8(13),a.appendULeb(0),Pa(a,n,13),a.endBlock();break;case 463:case 464:case 469:case 470:{const e=463===S||469===S,t=469===S||470===S,r=t?0x8000000000000000:2147483648,o=e?"temp_f32":"temp_f64";a.local("pLocals"),Hs(a,xs(n,2),e?42:43),a.local(o,34),a.appendU8(e?139:153),a.appendU8(e?67:68),e?a.appendF32(r):a.appendF64(r),a.appendU8(e?93:99),a.block(t?126:127,4),a.local(o),a.appendU8(ps[S]),a.appendU8(5),a.appendU8(t?66:65),a.appendBoundaryValue(t?64:32,-1),a.endBlock(),Gs(a,xs(n,1),t?55:54);break}case 520:case 521:{const e=520===S;a.local("pLocals"),Hs(a,xs(n,2),e?40:41);const t=Ts(n,3),r=Ts(n,4);e?a.i32_const(t):a.i52_const(t),a.appendU8(e?106:124),e?a.i32_const(r):a.i52_const(r),a.appendU8(e?108:126),Gs(a,xs(n,1),e?54:55);break}case 560:a.local("pLocals"),Hs(a,xs(n,2),40),Hs(a,xs(n,3),40),Hs(a,xs(n,4),40),a.callImport("cmpxchg_i32"),Gs(a,xs(n,1),54);break;case 561:Hs(a,xs(n,2),40),qs(a,xs(n,3),0),qs(a,xs(n,4),0),qs(a,xs(n,1),8),a.callImport("cmpxchg_i64");break;case 639:case 640:{const e=640===S;a.local("pLocals"),Hs(a,xs(n,2),e?41:40),e?a.i52_const(1):a.i32_const(1),a.appendU8(e?132:114),a.appendU8(e?121:103),e&&a.appendU8(167),a.i32_const(e?63:31),a.appendU8(115),Gs(a,xs(n,1),54);break}case 530:case 531:{const e=530===S,t=e?40:41,r=e?54:55;a.local("pLocals"),Hs(a,xs(n,2),t),Hs(a,xs(n,3),t),e?a.i32_const(31):a.i52_const(63),a.appendU8(e?113:131),a.appendU8(e?116:134),Gs(a,xs(n,1),r);break}case 581:case 608:{const e=608===S,t=e?42:43,r=e?56:57;a.local("pLocals"),Hs(a,xs(n,2),t),Hs(a,xs(n,3),t),Hs(a,xs(n,4),t),a.callImport(e?"fmaf":"fma"),Gs(a,xs(n,1),r);break}default:S>=3&&S<=12||S>=516&&S<=517?l||a.options.countBailouts?(Pa(a,n,14),d=!0):n=0:S>=13&&S<=29?Ks(a,n,S)?C=!0:n=0:S>=82&&S<=93?Qs(a,n,S)||(n=0):S>=351&&S<=434?ni(a,n,S)||(n=0):ds[S]?ri(a,n,S)||(n=0):ms[S]?si(a,n,e,S)?l=!0:n=0:S>=31&&S<=57?ei(a,e,n,S)||(n=0):S>=58&&S<=81?ti(a,e,n,S)||(n=0):S>=95&&S<=135?ci(a,n,S)||(n=0):S>=569&&S<=622?ii(a,n,S)||(n=0):S>=323&&S<=348?ui(a,e,n,S)||(n=0):S>=235&&S<=278?a.branchTargets.size>0?(za(a,n,$,8),d=!0):n=0:S>=641&&S<=648?_i(a,n,S,T,I,x)?(p=!0,C=!0):n=0:0===N||(n=0)}if(n){if(!C){const e=n+2;for(let t=0;t<v;t++)Ps(se(e+2*t))}if(yi.dumpTraces||s){let e=`${n.toString(16)} ${T} `;const t=n+2,r=t+2*v;for(let t=0;t<k;t++)0!==t&&(e+=", "),e+=se(r+2*t);v>0&&(e+=" -> ");for(let n=0;n<v;n++)0!==n&&(e+=", "),e+=se(t+2*n);a.traceBuf.push(e)}N>0&&(l?h++:m++,f+=N),(n+=2*E)<=o&&(g=n)}else s&&ut(`instrumented trace ${t} aborted for opcode ${T} @${j.toString(16)}`),Pi(b,0,0,S)}for(;a.activeBlocks>0;)a.endBlock();return a.cfg.exitIp=g,p&&(f+=10240),f}(e,u,n,r,c,i,b,s),o=a>=yi.minimumTraceValue,i.cfg.generate()})),i.emitImportsAndFunctions(!1),!o)return m&&"end-of-body"===m.abortReason&&(m.abortReason="trace-too-small"),0;d=Ma();const p=i.getArrayView();if(Oa.bytesGenerated+=p.length,p.length>=4080)return pt(`Jiterpreter generated too much code (${p.length} bytes) for trace ${u}. Please report this issue.`),0;const f=new WebAssembly.Module(p),g=i.getWasmImports(),y=new WebAssembly.Instance(f,g).exports[u];_=!1,l.storeMemorySnapshotPending&&w(!1,"Attempting to set function into table during creation of memory snapshot");const S=Wa(y);if(!S)throw new Error("add_function_pointer returned a 0 index");return i.options.enableStats&&Oa.tracesCompiled&&Oa.tracesCompiled%500==0&&zi(!1,!0),S}catch(e){return f=!0,_=!1,dt(`${a||u} code generation failed: ${e} ${e.stack}`),Ya(),0}finally{const e=Ma();if(d?(Ba.generation+=d-p,Ba.compilation+=e-d):Ba.generation+=e-p,f||!_&&yi.dumpTraces||h){if(f||yi.dumpTraces||h)for(let e=0;e<i.traceBuf.length;e++)ut(i.traceBuf[e]);ut(`// ${a||u} generated, blob follows //`);let e="",t=0;try{for(;i.activeBlocks>0;)i.endBlock();i.inSection&&i.endSection()}catch(e){}const n=i.getArrayView();for(let r=0;r<n.length;r++){const o=n[r];o<16&&(e+="0"),e+=o.toString(16),e+=" ",e.length%10==0&&(ut(`${t}\t${e}`),e="",t=r+1)}ut(`${t}\t${e}`),ut("// end blob //")}}}(e,p,r,a,s,c,m);return h?(Oa.tracesCompiled++,u.fnPtr=h,h):yi.estimateHeat?0:1},function(e){const t=ec[e&=-2];if(t){if(nc||(nc=cs()),t.hitCount++,t.hitCount===nc.interpEntryFlushThreshold)rc();else if(t.hitCount!==nc.interpEntryHitCount)return;Qi.push(t),Qi.length>=4?rc():Ki>0||"function"==typeof globalThis.setTimeout&&(Ki=globalThis.setTimeout((()=>{Ki=0,rc()}),10))}},function(e,t,n,r,o,a,s,i,c){if(n>16)return 0;const l=new class{constructor(e,t,n,r,o,a,s,i,c){this.imethod=e,this.method=t,this.argumentCount=n,this.unbox=o,this.hasThisReference=a,this.hasReturnValue=s,this.name=i,this.paramTypes=new Array(n);for(let e=0;e<n;e++)this.paramTypes[e]=ue(r+4*e);this.defaultImplementation=c,this.result=0;let l=i;if(l){const e=24;l.length>e&&(l=l.substring(l.length-e,l.length)),l=`${this.imethod.toString(16)}_${l}`}else l=`${this.imethod.toString(16)}_${this.hasThisReference?"i":"s"}${this.hasReturnValue?"_r":""}_${this.argumentCount}`;this.traceName=l,this.hitCount=0}}(e,t,n,r,o,a,s,Qe(i),c);Zi||(Zi=Va());const u=Zi.get(c);return l.result=Wa(u),ec[e]=l,l.result},function(e,t,n,r,o){const a=ue(n+0),s=_c[a];if(s)return void(s.result>0?St.mono_jiterp_register_jit_call_thunk(n,s.result):(s.queue.push(n),s.queue.length>12&&yc()));const i=new mc(e,t,n,r,0!==o);_c[a]=i,fc.push(i),fc.length>=6&&yc()},function(e,t,n,r,o){const a=hc(e);try{a(t,n,r,o)}catch(e){G(o,1)}},yc,function(e,n,r){l.storeMemorySnapshotPending&&w(!1,"Attempting to set function into table during creation of memory snapshot");const o=Va().get(e),a=function(e,t,n){try{o(t)}catch(e){G(n,1)}};let s=!gc();if(!s)try{const e=new WebAssembly.Instance(bc,{i:{jit_call_cb:o},m:{h:t.getMemory()}}).exports.do_jit_call_indirect;if("function"!=typeof e)throw new Error("Did not find exported do_jit_call handler");const n=Wa(e);St.mono_jiterp_update_jit_call_dispatcher(n),s=!1}catch(e){dt("failed to compile do_jit_call handler",e),s=!0}if(s)try{const e=t.addFunction(a,"viii");St.mono_jiterp_update_jit_call_dispatcher(e)}catch(e){St.mono_jiterp_update_jit_call_dispatcher(0)}a(0,n,r)},function(){l.enablePerfMeasure&&Xt.push(globalThis.performance.now())},function(e){if(l.enablePerfMeasure){const t=Xt.pop(),n=a?{start:t}:{startTime:t};let r=Zt.get(e);r||(r=Qe(vt.mono_wasm_method_get_name(e)),Zt.set(e,r)),globalThis.performance.measure(r,n)}},function(e,t,r,o,a){const s=Qe(r),i=!!o,c=Qe(e),l=a,u=Qe(t),p=`[MONO] ${s}`;if(n.logging&&"function"==typeof n.logging.trace)n.logging.trace(c,u,p,i,l);else switch(u){case"critical":case"error":console.error(ht(p));break;case"warning":console.warn(p);break;case"message":default:console.log(p);break;case"info":console.info(p);break;case"debug":console.debug(p)}},function(e,t){At=Qe(e).concat(".dll"),Rt=t,console.assert(!0,`Adding an entrypoint breakpoint ${At} at method token  ${Rt}`);debugger},function(){},Br,function(e,r,o,a,s,i){fr();const c=De(e),l=De(r),u=De(i);try{const e=fn(o);1!==e&&w(!1,`Signature version ${e} mismatch.`);const t=rt(c),r=Jt(),i=rt(l);lt(`Binding [JSImport] ${t} from ${i} module`);const p=function(e,t){e&&"string"==typeof e||w(!1,"function_name must be string");let r={};const o=e.split(".");t?(r=ur.get(t),r||w(!1,`ES6 module ${t} was not imported yet, please call JSHost.ImportAsync() first.`)):"INTERNAL"===o[0]?(r=n,o.shift()):"globalThis"===o[0]&&(r=globalThis,o.shift());for(let t=0;t<o.length-1;t++){const n=o[t],a=r[n];a||w(!1,`${n} not found while looking up ${e}`),r=a}const a=r[o[o.length-1]];return"function"!=typeof a&&w(!1,`${e} must be a Function but was ${typeof a}`),a.bind(r)}(t,i),d=_n(o),_=new Array(d),f=new Array(d);let m=!1;for(let e=0;e<d;e++){const t=sn(o,e+2),n=cn(t),r=Rn(t,n,e+2);r||w(!1,"ERR42: argument marshaler must be resolved"),_[e]=r,n===R.Span?(f[e]=e=>{e&&e.dispose()},m=!0):R.Task}const h=sn(o,1),b=cn(h);R.Task;const g=Yr(h,b,1),y={fn:p,fqn:i+":"+t,args_count:d,arg_marshalers:_,res_converter:g,has_cleanup:m,arg_cleanup:f,isDisposed:!1};let S;S=0!=d||g?1!=d||m||g?1==d&&!m&&g?function(e){const t=e.fn,n=e.arg_marshalers[0],r=e.res_converter,o=e.fqn;return e=null,function(a){const s=Jt();try{L&&e.isDisposed;const o=n(a),s=t(o);r(a,s)}catch(e){ho(a,e)}finally{Yt(s,"mono.callCsFunction:",o)}}}(y):2==d&&!m&&g?function(e){const t=e.fn,n=e.arg_marshalers[0],r=e.arg_marshalers[1],o=e.res_converter,a=e.fqn;return e=null,function(s){const i=Jt();try{L&&e.isDisposed;const a=n(s),i=r(s),c=t(a,i);o(s,c)}catch(e){ho(s,e)}finally{Yt(i,"mono.callCsFunction:",a)}}}(y):function(e){const t=e.args_count,n=e.arg_marshalers,r=e.res_converter,o=e.arg_cleanup,a=e.has_cleanup,s=e.fn,i=e.fqn;return e=null,function(c){const l=Jt();try{L&&e.isDisposed;const i=new Array(t);for(let e=0;e<t;e++){const t=(0,n[e])(c);i[e]=t}const l=s(...i);if(r&&r(c,l),a)for(let e=0;e<t;e++){const t=o[e];t&&t(i[e])}}catch(e){ho(c,e)}finally{Yt(l,"mono.callCsFunction:",i)}}}(y):function(e){const t=e.fn,n=e.arg_marshalers[0],r=e.fqn;return e=null,function(o){const a=Jt();try{L&&e.isDisposed;const r=n(o);t(r)}catch(e){ho(o,e)}finally{Yt(a,"mono.callCsFunction:",r)}}}(y):function(e){const t=e.fn,n=e.fqn;return e=null,function(r){const o=Jt();try{L&&e.isDisposed,t()}catch(e){ho(r,e)}finally{Yt(o,"mono.callCsFunction:",n)}}}(y),S[nn]=y;const k=nr.length;nr.push(S),Z(a,k),_r(s,u),Yt(r,"mono.bindJsFunction:",t)}catch(e){Z(a,0),t.err(e.toString()),dr(s,e,u)}finally{u.release(),c.release()}},function(e,t){const n=Nr(e);n&&"function"==typeof n&&n[tn]||w(!1,`Bound function handle expected ${e}`),n(t)},function(e,t){const n=nr[e];n||w(!1,`Imported function handle expected ${e}`),n(t)},function(e,n,r,o,a){fr();const s=De(e),i=De(a),c=Jt();try{const e=fn(r);1!==e&&w(!1,`Signature version ${e} mismatch.`);const a=_n(r),l=rt(s);l||w(!1,"fully_qualified_name must be string"),lt(`Binding [JSExport] ${l}`);const{assembly:p,namespace:d,classname:_,methodname:f}=Ur(l),m=wr(p);if(!m)throw new Error("Could not find assembly: "+p);const h=St.mono_wasm_assembly_find_class(m,d,_);if(!h)throw new Error("Could not find class: "+d+":"+_+" in assembly "+p);const b=`__Wrapper_${f}_${n}`,g=St.mono_wasm_assembly_find_method(h,b,-1);if(!g)throw new Error(`Could not find method: ${b} in ${h} [${p}]`);const y=new Array(a);for(let e=0;e<a;e++){const t=sn(r,e+2),n=cn(t);R.Task;const o=Yr(t,n,e+2);o||w(!1,"ERR43: argument marshaler must be resolved"),y[e]=o}const S=sn(r,1),k=cn(S);R.Task;const v=Rn(S,k,1),E={method:g,fqn:l,args_count:a,arg_marshalers:y,res_converter:v,isDisposed:!1};let U;U=0!=a||v?1!=a||v?1==a&&v?function(e){const n=e.method,r=e.arg_marshalers[0],o=e.res_converter,a=e.fqn;return e=null,function(e){const s=Jt();u.assert_runtime_running();const i=t.stackSave();try{const t=on(3);return r(t,e),kr(n,t),o(t)}finally{t.stackRestore(i),Yt(s,"mono.callCsFunction:",a)}}}(E):2==a&&v?function(e){const n=e.method,r=e.arg_marshalers[0],o=e.arg_marshalers[1],a=e.res_converter,s=e.fqn;return e=null,function(e,i){const c=Jt();u.assert_runtime_running();const l=t.stackSave();try{const t=on(4);return r(t,e),o(t,i),kr(n,t),a(t)}finally{t.stackRestore(l),Yt(c,"mono.callCsFunction:",s)}}}(E):function(e){const n=e.args_count,r=e.arg_marshalers,o=e.res_converter,a=e.method,s=e.fqn;return e=null,function(...e){const i=Jt();u.assert_runtime_running();const c=t.stackSave();try{const t=on(2+n);for(let o=0;o<n;o++){const n=r[o];n&&n(t,e[o])}if(kr(a,t),o)return o(t)}finally{t.stackRestore(c),Yt(i,"mono.callCsFunction:",s)}}}(E):function(e){const n=e.method,r=e.arg_marshalers[0],o=e.fqn;return e=null,function(e){const a=Jt();u.assert_runtime_running();const s=t.stackSave();try{const t=on(3);r(t,e),kr(n,t)}finally{t.stackRestore(s),Yt(a,"mono.callCsFunction:",o)}}}(E):function(e){const n=e.method,r=e.fqn;return e=null,function(){const e=Jt();u.assert_runtime_running();const o=t.stackSave();try{const e=on(2);kr(n,e)}finally{t.stackRestore(o),Yt(e,"mono.callCsFunction:",r)}}}(E),U[en]=E,function(e,t,n,r,o,a){const s=`${t}.${n}`.replace(/\//g,".").split(".");let i,c=vr.get(e);c||(c={},vr.set(e,c),vr.set(e+".dll",c)),i=c;for(let e=0;e<s.length;e++){const t=s[e];if(""!=t){let e=i[t];void 0===e&&(e={},i[t]=e),e||w(!1,`${t} not found while looking up ${n}`),i=e}}i[r]||(i[r]=a),i[`${r}.${o}`]=a}(p,d,_,f,n,U),Yt(c,"mono.bindCsFunction:",l),_r(o,i)}catch(e){t.err(e.toString()),dr(o,e,i)}finally{i.release(),s.release()}},function(e){const t=an(e,0),n=an(e,1),r=an(e,2),o=an(e,3),a=mn(t),s=mn(o),i=kn(r);if(i===x){const{promise:e,promise_control:r}=y();if(vn(n,Dr(e)),a!==R.None){const e=Jn(t);r.reject(e)}else if(s!==R.Task){const e=Kt.get(s);e||w(!1,`Unknown sub_converter for type ${R[s]}. ${Jr}`);const t=e(o);r.resolve(t)}}else{const e=Nr(i);e||w(!1,`ERR25: promise not found for js_handle: ${i} `),u.assertIsControllablePromise(e);const n=u.getPromiseController(e);if(a!==R.None){const e=Jn(t);n.reject(e)}else s!==R.Task&&n.resolve(o)}hn(n,R.Task),hn(t,R.None)},function(e,t,n,r,o,a,s){const i=De(s);try{const s=tt(e,e+2*t),c=o?s.toUpperCase():s.toLowerCase();if(c.length<=r)return nt(n,n+2*r,c),void _r(a,i);const l=Ue();let u=1;if(o)for(let e=0;e<s.length;e+=u)if(xc(s,e)){u=2;const t=s.substring(e,e+2),r=t.toUpperCase();Tc(l,n,r.length>2?t:r,e)}else{u=1;const t=s[e].toUpperCase();H(l,n+2*e,(t.length>1?s[e]:t).charCodeAt(0))}else for(let e=0;e<s.length;e+=u)if(xc(s,e)){u=2;const t=s.substring(e,e+2),r=t.toLowerCase();Tc(l,n,r.length>2?t:r,e)}else{u=1;const t=s[e].toLowerCase();H(l,n+2*e,(t.length>1?s[e]:t).charCodeAt(0))}}catch(e){dr(a,e,i)}finally{i.release()}},function(e,t,n,r,o,a,s,i){const c=De(e),l=De(i);try{const e=rt(c);if(!e)throw new Error("Cannot change case, the culture name is null.");const i=tt(t,t+2*n),u=a?i.toLocaleUpperCase(e):i.toLocaleLowerCase(e);if(u.length<=i.length)return nt(r,r+2*o,u),void _r(s,l);const p=Ue();let d=1;if(a)for(let t=0;t<i.length;t+=d)if(xc(i,t)){d=2;const n=i.substring(t,t+2),o=n.toLocaleUpperCase(e);Tc(p,r,o.length>2?n:o,t)}else{d=1;const n=i[t].toLocaleUpperCase(e);H(p,r+2*t,(n.length>1?i[t]:n).charCodeAt(0))}else for(let t=0;t<i.length;t+=d)if(xc(i,t)){d=2;const n=i.substring(t,t+2),o=n.toLocaleLowerCase(e);Tc(p,r,o.length>2?n:o,t)}else{d=1;const n=i[t].toLocaleLowerCase(e);H(p,r+2*t,(n.length>1?i[t]:n).charCodeAt(0))}_r(s,l)}catch(e){dr(s,e,l)}finally{c.release(),l.release()}},function(e,t,n,r,o,a,s,i){const c=De(e),l=De(i);try{const e=rt(c),i=et(t,t+2*n),u=et(r,r+2*o),p=31&a,d=e||void 0;return _r(s,l),jc(i,u,d,p)}catch(e){return dr(s,e,l),-2}finally{c.release(),l.release()}},function(e,t,n,r,o,a,s,i){const c=De(e),l=De(i);try{const e=rt(c),i=Ac(r,o);if(0==i.length)return 1;const u=Ac(t,n);if(u.length<i.length)return 0;const p=jc(u.slice(0,i.length),i,e||void 0,31&a);return _r(s,l),0===p?1:0}catch(e){return dr(s,e,l),-1}finally{c.release(),l.release()}},function(e,t,n,r,o,a,s,i){const c=De(e),l=De(i);try{const e=rt(c),i=Ac(r,o);if(0==i.length)return 1;const u=Ac(t,n),p=u.length-i.length;if(p<0)return 0;const d=jc(u.slice(p,u.length),i,e||void 0,31&a);return _r(s,l),0===d?1:0}catch(e){return dr(s,e,l),-1}finally{c.release(),l.release()}},function(e,t,n,r,o,a,s,i,c){const l=De(e),u=De(c);try{const e=et(t,t+2*n);if(0==Rc(e).length)return _r(i,u),s?0:o;const c=et(r,r+2*o);if(0==Rc(c).length)return _r(i,u),s?0:o;const d=rt(l)||void 0,_=31&a,f=new Intl.Segmenter(d,{granularity:"grapheme"}),m=Array.from(f.segment(e)).map((e=>e.segment));let h=0,b=!1,g=-1,y=0,w=0,S=0;for(;!b;){const e=f.segment(c.slice(h,c.length))[Symbol.iterator]();let t=e.next();if(t.done)break;let n=p(t.value.segment,m[0],d,_);if(w=S,t=e.next(),t.done){g=n?w:g;break}if(y=t.value.index,S=w+y,n){for(let r=1;r<m.length;r++){if(t.done){b=!0;break}if(n=p(t.value.segment,m[r],d,_),!n)break;t=e.next()}if(b)break}if(n&&(g=w,s))break;h=S}return _r(i,u),g}catch(e){return dr(i,e,u),-1}finally{l.release(),u.release()}function p(e,t,n,r){return 0===jc(e,t,n,r)}},function(e,t,n,r,o,a){const s=De(e),i=De(a);try{const e=rt(s)||void 0,a={EnglishName:"",YearMonth:"",MonthDay:"",LongDates:"",ShortDates:"",EraNames:"",AbbreviatedEraNames:"",DayNames:"",AbbreviatedDayNames:"",ShortestDayNames:"",MonthNames:"",AbbreviatedMonthNames:"",MonthGenitiveNames:"",AbbrevMonthGenitiveNames:""},c=new Date(999,10,22);a.EnglishName=function(e){const t=function(e){try{return new Intl.Locale(e).calendars}catch(t){try{return new Intl.Locale(e).getCalendars()}catch(e){return}}}(e);return t&&0!=t.length?t[0]:""}(e);const l=function(e){const t=new Date(2023,5,25),n=[],r=[],o=[];for(let a=0;a<7;a++)n[a]=t.toLocaleDateString(e,{weekday:"long"}),r[a]=t.toLocaleDateString(e,{weekday:"short"}),o[a]=t.toLocaleDateString(e,{weekday:"narrow"}),t.setDate(t.getDate()+1);return{long:n,abbreviated:r,shortest:o}}(e);a.DayNames=l.long.join(Lc),a.AbbreviatedDayNames=l.abbreviated.join(Lc),a.ShortestDayNames=l.shortest.join(Lc);const u=function(e){const t=e?e.split("-")[0]:"",n="ar"==t?8:"fa"==t?3:0,r=new Date(2021,n,1),o=[],a=[],s=[],i=[];let c,l;for(let t=n;t<12+n;t++){const u=t%12;r.setMonth(u);const p=r.toLocaleDateString(e,{month:"long"}),d=r.toLocaleDateString(e,{month:"short"});if(o[t-n]=p,a[t-n]=d,c=null!=c?c:"月"==p.charAt(p.length-1),c){s[t-n]=p,i[t-n]=d;continue}const _=new Intl.DateTimeFormat(e,{day:"numeric"}),f=r.toLocaleDateString(e,{month:"long",day:"numeric"});if(s[t-n]=Mc(r,f,p,_),l=null!=l?l:/^\d+$/.test(d),l){i[t-n]=d;continue}const m=r.toLocaleDateString(e,{month:"short",day:"numeric"});i[t-n]=Mc(r,m,d,_)}return{long:o,abbreviated:a,longGenitive:s,abbreviatedGenitive:i}}(e);a.MonthNames=u.long.join(Lc),a.AbbreviatedMonthNames=u.abbreviated.join(Lc),a.MonthGenitiveNames=u.longGenitive.join(Lc),a.AbbrevMonthGenitiveNames=u.abbreviatedGenitive.join(Lc),a.YearMonth=function(e,t){let n=t.toLocaleDateString(e,{year:"numeric",month:"long"}).toLowerCase();const r=t.toLocaleString(e,{month:"long"}).toLowerCase().trim();if("月"==r.charAt(r.length-1))return"yyyy年M月";n=n.replace(r,Cc),n=n.replace("999",Nc);const o=t.toLocaleDateString(e,{year:"numeric"});return n.replace(o,Nc)}(e,c),a.MonthDay=function(e,t){let n=t.toLocaleDateString(e,{month:"long",day:"numeric"}).toLowerCase();const r=t.toLocaleString(e,{month:"long"}).toLowerCase().trim();if("月"==r.charAt(r.length-1))return"M月d日";const o=new Intl.DateTimeFormat(e,{day:"numeric"}),a=Mc(t,n,r,o);n=n.replace(a,Cc),n=n.replace("22",Dc);const s=o.format(t);return n.replace(s,Dc)}(e,c),a.ShortDates=function(e){if("fa"==(null==e?void 0:e.substring(0,2)))return"yyyy/M/d";const t=new Date(2014,0,2);let n=t.toLocaleDateString(e,{dateStyle:"short"});if(n.includes("14"))n=n.replace("2014",Nc),n=n.replace("14",Nc);else{const r=t.toLocaleDateString(e,{year:"numeric"}),o=r.substring(r.length-2,r.length);n=n.replace(r,Nc),o&&(n=n.replace(o,Nc))}if(n.includes("1"))n=n.replace("01","MM"),n=n.replace("1","M");else{const r=t.toLocaleDateString(e,{month:"numeric"}),o=1==r.length?"M":"MM";n=n.replace(r,o)}if(n.includes("2"))n=n.replace("02","dd"),n=n.replace("2","d");else{const r=t.toLocaleDateString(e,{day:"numeric"}),o=1==r.length?"d":"dd";n=n.replace(r,o)}return n}(e),a.LongDates=function(e,t){if("th-TH"==e)return"ddddที่ d MMMM g yyyy";let n=new Intl.DateTimeFormat(e,{weekday:"long",year:"numeric",month:"long",day:"numeric"}).format(t).toLowerCase();const r=t.toLocaleString(e,{month:"long"}).trim().toLowerCase(),o=r.charAt(r.length-1);if("月"==o||"월"==o){const r=t.toLocaleString(e,{month:"short"});n=n.replace(r,`M${o}`)}else{const o=Mc(t,n,r,new Intl.DateTimeFormat(e,{weekday:"long",year:"numeric",day:"numeric"}));n=n.replace(o,Cc)}n=n.replace("999",Nc);const a=t.toLocaleDateString(e,{year:"numeric"});n=n.replace(a,Nc);const s=t.toLocaleDateString(e,{weekday:"long"}).toLowerCase(),i=Mc(t,n,s,new Intl.DateTimeFormat(e,{year:"numeric",month:"long",day:"numeric"}));n=n.replace(i,Bc),n=n.replace("22",Dc);const c=t.toLocaleDateString(e,{day:"numeric"});return n=n.replace(c,Dc),function(e,t){const n=e.split(/\s+/);if(n.length<=2||(null==t?void 0:t.startsWith("ko")))return e;for(let e=0;e<n.length;e++)if(!(Oc.includes(n[e].replace(",",""))||Oc.includes(n[e].replace(".",""))||Oc.includes(n[e].replace("،",""))||Oc.includes(n[e].replace("ב",""))))if(n[e].endsWith(".,")){const t=n[e].slice(0,-2);1==n.filter((e=>e==t)).length&&(n[e]=`'${n[e].slice(0,-2)}'.,`)}else n[e].endsWith(".")?n[e]=`'${n[e].slice(0,-1)}'.`:n[e].endsWith(",")?n[e]=`'${n[e].slice(0,-1)}',`:n[e]=`'${n[e]}'`;return n.join(" ")}(n,e)}(e,c);const p=function(e,t,n){if(function(e){return e>1&&e<15||22==e||23==e}(n))return{eraNames:"",abbreviatedEraNames:""};const r=e.toLocaleDateString(t,{year:"numeric"}),o=e.toLocaleDateString(t,{day:"numeric"}),a=e.toLocaleDateString(t,{era:"short"}),s=e.toLocaleDateString(t,{era:"narrow"}),i=a.includes(r)?l(r):l(e.getFullYear().toString());return{eraNames:c(i.eraDateParts,i.ignoredPart),abbreviatedEraNames:c(i.abbrEraDateParts,i.ignoredPart)};function c(e,n){const r=new RegExp(`^((?!${n}|[0-9]).)*$`),o=e.filter((e=>r.test(e)));if(0==o.length)throw new Error(`Internal error, era for locale ${t} was in non-standard format.`);return o[0].trim()}function l(e){return a.startsWith(e)||a.endsWith(e)?{eraDateParts:a.split(o),abbrEraDateParts:s.split(o),ignoredPart:e}:{eraDateParts:a.split(e),abbrEraDateParts:s.split(e),ignoredPart:o}}}(c,e,t);a.EraNames=p.eraNames,a.AbbreviatedEraNames=p.abbreviatedEraNames;const d=Object.values(a).join("##");if(d.length>r)throw new Error(`Calendar info exceeds length of ${r}.`);return nt(n,n+2*d.length,d),_r(o,i),d.length}catch(e){return dr(o,e,i),-1}finally{s.release(),i.release()}},function(e,t,n,r,o){const a=De(e),s=De(o);try{const e=rt(a),o={AmDesignator:"",PmDesignator:"",LongTimePattern:"",ShortTimePattern:""},i=$c(e),c=function(e){const t=new Date("August 19, 1975 12:15:33"),n=new Date("August 19, 1975 11:15:33"),r=Rl(t,e);return{am:Rl(n,e),pm:r}}(i);o.AmDesignator=c.am,o.PmDesignator=c.pm,o.LongTimePattern=function(e,t){const n=18..toLocaleString(e),r=6..toLocaleString(e),o=new Date("August 19, 1975 18:15:30"),a=new Intl.DateTimeFormat(e,{timeStyle:"medium"}),s=a.format(o),i=o.toLocaleTimeString(e,{minute:"numeric"}),c=o.toLocaleTimeString(e,{second:"numeric"});let l=s.replace(t.pm,"tt").replace(i,"mm").replace(c,jl);const u=l.includes(n),p=`${(0).toLocaleString(e)}${r}`,d=new Date("August 19, 1975 6:15:30"),_=a.format(d);let f;if(u)f=_.includes(p)?"HH":"H",l=l.replace(n,f);else{const e=_.includes(p);f=e?"hh":"h",l=l.replace(e?p:r,f)}return function(e){const t=e.split(/\s+/);for(let e=0;e<t.length;e++)t[e].includes(":")||t[e].includes(".")||Al.includes(t[e])||(t[e]=`'${t[e]}'`);return t.join(" ")}(l)}(i,c),o.ShortTimePattern=function(e){const t=e.indexOf(jl);if(t>0){const n=`${e[t-1]}ss`,r=e.replace(n,"");e=r.length>t&&"t"!=r[r.length-1]?e.split(n)[0]:r}return e}(o.LongTimePattern);const l=Object.values(o).join("##");if(l.length>n)throw new Error(`Culture info exceeds length of ${n}.`);return nt(t,t+2*l.length,l),_r(r,s),l.length}catch(e){return dr(r,e,s),-1}finally{a.release(),s.release()}},function(e,t,n){const r=De(e),o=De(n);try{return function(e){const t=Ll(e);if(t)return 7==t.firstDay?0:t.firstDay;if(["en-AE","en-SD","fa-IR"].includes(e))return 6;const n=e.split("-")[0];return["zh","th","pt","mr","ml","ko","kn","ja","id","hi","he","gu","fil","bn","am","ar"].includes(n)||["ta-SG","ta-IN","sw-KE","ms-SG","fr-CA","es-MX","en-US","en-ZW","en-ZA","en-WS","en-VI","en-UM","en-TT","en-SG","en-PR","en-PK","en-PH","en-MT","en-MO","en-MH","en-KE","en-JM","en-IN","en-IL","en-HK","en-GU","en-DM","en-CA","en-BZ","en-BW","en-BS","en-AU","en-AS","en-AG"].includes(e)?0:1}($c(rt(r)))}catch(e){return dr(t,e,o),-1}finally{r.release(),o.release()}},function(e,t,n){const r=De(e),o=De(n);try{return function(e){const t=Ll(e);if(t)return 7==t.minimalDays?1:t.minimalDays<4?0:2;const n=e.split("-")[0];return["pt-PT","fr-CH","fr-FR","fr-BE","es-ES","en-SE","en-NL","en-JE","en-IM","en-IE","en-GI","en-GG","en-GB","en-FJ","en-FI","en-DK","en-DE","en-CH","en-BE","en-AT","el-GR"].includes(e)||["sv","sk","ru","pl","nl","no","lt","it","hu","fi","et","de","da","cs","ca","bg"].includes(n)?2:0}($c(rt(r)))}catch(e){return dr(t,e,o),-1}finally{r.release(),o.release()}}],Cl=[...$l,function(e,t,n,r,o){ul();const a=De(n),s=De(t),i=De(o);try{const t=rt(s);if(!t||"string"!=typeof t)return void dr(r,"ERR12: Invalid method name object @"+s.value,i);const n=function(e){return e!==x&&e!==I?Nr(e):null}(e);if(A(n))return void dr(r,"ERR13: Invalid JS object handle '"+e+"' while invoking '"+t+"'",i);const o=kl(a);try{const e=n[t];if(void 0===e)throw new Error("Method: '"+t+"' not found for: '"+Object.prototype.toString.call(n)+"'");Xc(e.apply(n,o),i,!0),_r(r)}catch(e){dr(r,e,i)}}finally{a.release(),s.release(),i.release()}},function(e,t,n,r){ul();const o=De(t),a=De(r);try{const t=rt(o);if(!t)return void dr(n,"Invalid property name object '"+o.value+"'",a);const r=Nr(e);if(A(r))return void dr(n,"ERR01: Invalid JS object handle '"+e+"' while geting '"+t+"'",a);Xc(r[t],a,!0),_r(n)}catch(e){dr(n,e,a)}finally{a.release(),o.release()}},function(e,t,n,r,o,a,s){ul();const i=De(n),c=De(t),l=De(s);try{const n=rt(c);if(!n)return void dr(a,"Invalid property name object '"+t+"'",l);const s=Nr(e);if(A(s))return void dr(a,"ERR02: Invalid JS object handle '"+e+"' while setting '"+n+"'",l);const u=wl(i);if(r)s[n]=u;else{if(!r&&!Object.prototype.hasOwnProperty.call(s,n))return;!0===o?Object.prototype.hasOwnProperty.call(s,n)&&(s[n]=u):s[n]=u}_r(a,l)}catch(e){dr(a,e,l)}finally{l.release(),c.release(),i.release()}},function(e,t,n,r){ul();const o=De(r);try{const r=Nr(e);if(A(r))return void dr(n,"ERR03: Invalid JS object handle '"+e+"' while getting ["+t+"]",o);Xc(r[t],o,!0),_r(n)}catch(e){dr(n,e,o)}finally{o.release()}},function(e,t,n,r,o){ul();const a=De(n),s=De(o);try{const n=Nr(e);if(A(n))return void dr(r,"ERR04: Invalid JS object handle '"+e+"' while setting ["+t+"]",s);const o=wl(a);n[t]=o,_r(r,s)}catch(e){dr(r,e,s)}finally{s.release(),a.release()}},function(e,r,o){ul();const a=De(e),s=De(o);try{const e=rt(a);let o;if(o=e?"Module"==e?t:"INTERNAL"==e?n:globalThis[e]:globalThis,null===o||void 0===typeof o)return void dr(r,"Global object '"+e+"' not found.",s);Xc(o,s,!0),_r(r)}catch(e){dr(r,e,s)}finally{s.release(),a.release()}},function(e,t,n,r){const o=De(t),a=De(e),s=De(r);try{const e=rt(a);if(!e)return void dr(n,"Invalid name @"+a.value,s);const t=globalThis[e];if(null==t)return void dr(n,"JavaScript host object '"+e+"' not found.",s);try{const e=kl(o),r=function(e,t){let n=[];return n[0]=e,t&&(n=n.concat(t)),new(e.bind.apply(e,n))};Xc(Dr(r(t,e)),s,!1),_r(n)}catch(e){return void dr(n,e,s)}}finally{s.release(),o.release(),a.release()}},function(e,t,n){const r=De(n);try{const n=Nr(e);if(A(n))return void dr(t,"ERR06: Invalid JS object handle '"+e+"'",r);Kc(n,r),_r(t)}catch(e){dr(t,String(e),r)}finally{r.release()}},function(e,t,n,r,o,a,s){const i=De(s);try{const s=function(e,t,n,r,o){let a=null;switch(o){case 5:a=new Int8Array(n-t);break;case 6:a=new Uint8Array(n-t);break;case 7:a=new Int16Array(n-t);break;case 8:a=new Uint16Array(n-t);break;case 9:a=new Int32Array(n-t);break;case 10:a=new Uint32Array(n-t);break;case 13:a=new Float32Array(n-t);break;case 14:a=new Float64Array(n-t);break;case 15:a=new Uint8ClampedArray(n-t);break;default:throw new Error("Unknown array type "+o)}return function(e,t,n,r,o){if(qc(e)&&e.BYTES_PER_ELEMENT){if(o!==e.BYTES_PER_ELEMENT)throw new Error("Inconsistent element sizes: TypedArray.BYTES_PER_ELEMENT '"+e.BYTES_PER_ELEMENT+"' sizeof managed element: '"+o+"'");let a=(r-n)*o;const s=e.length*e.BYTES_PER_ELEMENT;a>s&&(a=s);const i=n*o;return new Uint8Array(e.buffer,0,a).set(Ee().subarray(t+i,t+i+a)),a}throw new Error("Object '"+e+"' is not a typed array")}(a,e,t,n,r),a}(e,t,n,r,o);Xc(s,i,!0),_r(a)}catch(e){dr(a,String(e),i)}finally{i.release()}},function(e,t,n,r,o){try{ul();const e=globalThis.Blazor;if(!e)throw new Error("The blazor.webassembly.js library is not loaded.");return e._internal.invokeJSFromDotNet(t,n,r,o)}catch(t){const n=t.message+"\n"+t.stack,r=Be();return ot(n,r),r.copy_to_address(e),r.release(),0}}];function Nl(e){const t=e.env||e.a;if(!t)return void pt("WARNING: Neither imports.env or imports.a were present when instantiating the wasm module. This likely indicates an emscripten configuration issue.");const n=new Array(Cl.length);for(const e in t){const r=t[e];if("function"==typeof r&&-1!==r.toString().indexOf("runtime_idx"))try{const{runtime_idx:t}=r();if(void 0!==n[t])throw new Error(`Duplicate runtime_idx ${t}`);n[t]=e}catch(e){}}for(const[e,r]of Cl.entries()){const o=n[e];if(void 0!==o){const e=t[o];if("function"!=typeof e)throw new Error(`Expected ${o} to be a function`);t[o]=r,lt(`Replaced WASM import ${o} stub ${e.name} with ${r.name||"minified implementation"}`)}}}const Dl="https://dotnet.generated.invalid/wasm-memory";async function Bl(){if(void 0===globalThis.caches)return null;if(a&&!1===globalThis.window.isSecureContext)return null;const e=`dotnet-resources${document.baseURI.substring(document.location.origin.length)}`;try{return await globalThis.caches.open(e)||null}catch(e){return pt("Failed to open cache"),null}}async function Ol(){if(l.memorySnapshotCacheKey)return l.memorySnapshotCacheKey;if(!l.subtle)return null;const t=Object.assign({},l.config);t.resourcesHash=t.resources.hash,delete t.assets,delete t.resources,t.preferredIcuAsset=u.preferredIcuAsset,delete t.forwardConsoleLogsToWS,delete t.diagnosticTracing,delete t.appendElementOnExit,delete t.assertAfterExit,delete t.interopCleanupOnExit,delete t.logExitCode,delete t.pthreadPoolSize,delete t.asyncFlushOnExit,delete t.remoteSources,delete t.ignorePdbLoadErrors,delete t.maxParallelDownloads,delete t.enableDownloadRetry,delete t.exitAfterSnapshot,delete t.extensions,t.GitHash=u.gitHash,t.ProductVersion=e;const n=JSON.stringify(t),r=await l.subtle.digest("SHA-256",(new TextEncoder).encode(n)),o=new Uint8Array(r),a=Array.from(o).map((e=>e.toString(16).padStart(2,"0"))).join("");return l.memorySnapshotCacheKey=`${Dl}-${a}`,l.memorySnapshotCacheKey}async function Ml(e){e.out||(e.out=console.log.bind(console)),e.err||(e.err=console.error.bind(console)),e.print||(e.print=e.out),e.printErr||(e.printErr=e.err),u.out=e.print,u.err=e.printErr,await Io(),await async function(){try{if(!l.config.startupMemoryCache)return;const e=await Ol();if(!e)return;const t=await Bl();if(!t)return;const n=await t.match(e),r=null==n?void 0:n.headers.get("content-length"),o=r?parseInt(r):void 0;l.loadedMemorySnapshotSize=o,l.storeMemorySnapshotPending=!o}catch(e){pt("Failed find memory snapshot in the cache",e)}finally{l.loadedMemorySnapshotSize||u.memorySnapshotSkippedOrDone.promise_control.resolve()}}()}function Fl(e){const n=Jt();e.locateFile||(e.locateFile=e.__locateFile=e=>u.scriptDirectory+e),e.mainScriptUrlOrBlob=u.scriptUrl;const h=e.instantiateWasm,b=e.preInit?"function"==typeof e.preInit?[e.preInit]:e.preInit:[],g=e.preRun?"function"==typeof e.preRun?[e.preRun]:e.preRun:[],y=e.postRun?"function"==typeof e.postRun?[e.postRun]:e.postRun:[],S=e.onRuntimeInitialized?e.onRuntimeInitialized:()=>{};e.instantiateWasm=(e,n)=>function(e,n,r){const o=Jt();if(r){const t=r(e,((e,t)=>{Yt(o,"mono.instantiateWasm"),l.afterInstantiateWasm.promise_control.resolve(),n(e,t)}));return t}return async function(e,n){var r;try{await u.afterConfigLoaded,lt("instantiate_wasm_module"),await l.beforePreInit.promise,t.addRunDependency("instantiate_wasm_module");const o=async function(){d&&(await u.simd()||w(!1,"This browser/engine doesn't support WASM SIMD. Please use a modern version. See also https://aka.ms/dotnet-wasm-features")),_&&(await u.exceptions()||w(!1,"This browser/engine doesn't support WASM exception handling. Please use a modern version. See also https://aka.ms/dotnet-wasm-features"))}();Nl(e);const i=await u.wasmDownloadPromise.promise;if(await o,await async function(e,t,n){e&&e.pendingDownloadInternal&&e.pendingDownloadInternal.response||w(!1,"Can't load dotnet.native.wasm");const r=await e.pendingDownloadInternal.response,o=r.headers&&r.headers.get?r.headers.get("Content-Type"):void 0;let i,c;if("function"==typeof WebAssembly.instantiateStreaming&&"application/wasm"===o){lt("instantiate_wasm_module streaming");const e=await WebAssembly.instantiateStreaming(r,t);i=e.instance,c=e.module}else{a&&"application/wasm"!==o&&pt('WebAssembly resource does not have the expected content type "application/wasm", so falling back to slower ArrayBuffer instantiation.');const e=await r.arrayBuffer();if(lt("instantiate_wasm_module buffered"),s)c=new WebAssembly.Module(e),i=new WebAssembly.Instance(c,t);else{const n=await WebAssembly.instantiate(e,t);i=n.instance,c=n.module}}n(i,c)}(i,e,n),i.pendingDownloadInternal=null,i.pendingDownload=null,i.buffer=null,i.moduleExports=null,lt("instantiate_wasm_module done"),l.loadedMemorySnapshotSize){try{const e=(null===(r=t.asm)||void 0===r?void 0:r.memory)||t.wasmMemory;e.grow(l.loadedMemorySnapshotSize-e.buffer.byteLength+65535>>>16),l.updateMemoryViews()}catch(e){pt("failed to resize memory for the snapshot",e),l.loadedMemorySnapshotSize=void 0}u.memorySnapshotSkippedOrDone.promise_control.resolve()}l.afterInstantiateWasm.promise_control.resolve()}catch(e){throw dt("instantiate_wasm_module() failed",e),u.mono_exit(1,e),e}t.removeRunDependency("instantiate_wasm_module")}(e,n),[]}(e,n,h),e.preInit=[()=>function(e){t.addRunDependency("mono_pre_init");const n=Jt();try{zl(!1),lt("preInit"),l.beforePreInit.promise_control.resolve(),e.forEach((e=>e()))}catch(e){throw dt("user preInint() failed",e),u.mono_exit(1,e),e}(async()=>{try{await async function(){lt("mono_wasm_pre_init_essential_async"),t.addRunDependency("mono_wasm_pre_init_essential_async"),t.removeRunDependency("mono_wasm_pre_init_essential_async")}(),Yt(n,"mono.preInit")}catch(e){throw u.mono_exit(1,e),e}l.afterPreInit.promise_control.resolve(),t.removeRunDependency("mono_pre_init")})()}(b)],e.preRun=[()=>async function(e){t.addRunDependency("mono_pre_run_async");try{await l.afterInstantiateWasm.promise,await l.afterPreInit.promise,lt("preRunAsync");const t=Jt();e.map((e=>e())),Yt(t,"mono.preRun")}catch(e){throw dt("user callback preRun() failed",e),u.mono_exit(1,e),e}l.afterPreRun.promise_control.resolve(),t.removeRunDependency("mono_pre_run_async")}(g)],e.onRuntimeInitialized=()=>async function(e){try{await l.afterPreRun.promise,lt("onRuntimeInitialized"),l.mono_wasm_exit=St.mono_wasm_exit,l.abort=e=>{throw u.is_exited()||St.mono_wasm_abort(),e};const n=Jt();if(l.beforeOnRuntimeInitialized.promise_control.resolve(),await async function(){await l.allAssetsInMemory.promise,l.config.assets&&(u.actual_downloaded_assets_count!=u.expected_downloaded_assets_count&&w(!1,`Expected ${u.expected_downloaded_assets_count} assets to be downloaded, but only finished ${u.actual_downloaded_assets_count}`),u.actual_instantiated_assets_count!=u.expected_instantiated_assets_count&&w(!1,`Expected ${u.expected_instantiated_assets_count} assets to be in memory, but only instantiated ${u.actual_instantiated_assets_count}`),u._loaded_files.forEach((e=>u.loadedFiles.push(e.url))),lt("all assets are loaded in wasm memory"))}(),L&&l.config.startupMemoryCache,await async function(){const e=Jt();if(l.loadedMemorySnapshotSize){const e=await async function(){try{const e=await Ol();if(!e)return;const t=await Bl();if(!t)return;const n=await t.match(e);if(!n)return;return n.arrayBuffer()}catch(e){return void pt("Failed load memory snapshot from the cache",e)}}(),t=Ee();return e.byteLength!==t.byteLength&&w(!1,"Loaded memory is not the expected size"),t.set(new Uint8Array(e),0),void lt("Loaded WASM linear memory from browser cache")}for(const e in l.config.environmentVariables){const t=l.config.environmentVariables[e];if("string"!=typeof t)throw new Error(`Expected environment variable '${e}' to be a string but it was ${typeof t}: '${t}'`);Vl(e,t)}l.config.startupMemoryCache&&St.mono_jiterp_update_jit_call_dispatcher(0),l.config.runtimeOptions&&function(e){if(!Array.isArray(e))throw new Error("Expected runtimeOptions to be an array of strings");const n=t._malloc(4*e.length);let r=0;for(let o=0;o<e.length;++o){const a=e[o];if("string"!=typeof a)throw new Error("Expected runtimeOptions to be an array of strings");t.setValue(n+4*r,St.mono_wasm_strdup(a),"i32"),r+=1}St.mono_wasm_parse_runtime_options(e.length,n)}(l.config.runtimeOptions),l.config.aotProfilerOptions&&function(e){f||w(!1,"AOT profiler is not enabled, please use <WasmProfilers>aot;</WasmProfilers> in your project file."),null==e&&(e={}),"writeAt"in e||(e.writeAt="System.Runtime.InteropServices.JavaScript.JavaScriptExports::StopProfile"),"sendTo"in e||(e.sendTo="Interop/Runtime::DumpAotProfileData");const t="aot:write-at-method="+e.writeAt+",send-to-method="+e.sendTo;vt.mono_wasm_profiler_init_aot(t)}(l.config.aotProfilerOptions),l.config.browserProfilerOptions&&(l.config.browserProfilerOptions,m||w(!1,"Browser profiler is not enabled, please use <WasmProfilers>browser;</WasmProfilers> in your project file."),vt.mono_wasm_profiler_init_browser("browser:")),Wl(),l.config.startupMemoryCache&&(St.mono_jiterp_update_jit_call_dispatcher(-1),await async function(e){try{const t=await Ol();if(!t)return;const n=await Bl();if(!n)return;const r=L?new Uint8Array(e).slice(0):e,o=new Response(r,{headers:{"content-type":"wasm-memory","content-length":e.byteLength.toString()}});await n.put(t,o),async function(e){try{const t=await Bl();if(!t)return;const n=await t.keys();for(const r of n)r.url&&r.url!==e&&r.url.startsWith(Dl)&&await t.delete(r)}catch(e){return}}(t)}catch(e){return void pt("Failed to store memory snapshot in the cache",e)}}(Ee().buffer),l.storeMemorySnapshotPending=!1),Yt(e,"mono.memorySnapshot")}(),l.config.exitAfterSnapshot){const e=l.ExitStatus?new l.ExitStatus(0):new Error("Snapshot taken, exiting because exitAfterSnapshot was set.");return e.silent=!0,void u.mono_exit(0,e)}L&&l.config.startupMemoryCache,function(){if(!l.mono_wasm_bindings_is_ready){lt("bindings_init"),l.mono_wasm_bindings_is_ready=!0;try{const e=Jt();Ve||("undefined"!=typeof TextDecoder&&(He=new TextDecoder("utf-16le"),Ge=new TextDecoder("utf-8",{fatal:!1}),qe=new TextDecoder("utf-8"),Je=new TextEncoder),Ve=t._malloc(12)),function(){const e="System.Runtime.InteropServices.JavaScript";if(l.runtime_interop_module=St.mono_wasm_assembly_load(e),!l.runtime_interop_module)throw"Can't find bindings module assembly: "+e;if(l.runtime_interop_namespace="System.Runtime.InteropServices.JavaScript",l.runtime_interop_exports_classname="JavaScriptExports",l.runtime_interop_exports_class=St.mono_wasm_assembly_find_class(l.runtime_interop_module,l.runtime_interop_namespace,l.runtime_interop_exports_classname),!l.runtime_interop_exports_class)throw"Can't find "+l.runtime_interop_namespace+"."+l.runtime_interop_exports_classname+" class";const n=xo("CallEntrypoint");n||w(!1,"Can't find CallEntrypoint method");const r=xo("ReleaseJSOwnedObjectByGCHandle");r||w(!1,"Can't find ReleaseJSOwnedObjectByGCHandle method");const o=xo("CreateTaskCallback");o||w(!1,"Can't find CreateTaskCallback method");const a=xo("CompleteTask");a||w(!1,"Can't find CompleteTask method");const s=xo("CallDelegate");s||w(!1,"Can't find CallDelegate method");const i=xo("GetManagedStackTrace");i||w(!1,"Can't find GetManagedStackTrace method");const c=xo("LoadSatelliteAssembly");c||w(!1,"Can't find LoadSatelliteAssembly method");const p=xo("LoadLazyAssembly");p||w(!1,"Can't find LoadLazyAssembly method"),l.javaScriptExports.call_entry_point=async(e,r)=>{u.assert_runtime_running();const o=t.stackSave();try{t.runtimeKeepalivePush();const o=on(4),a=an(o,1),s=an(o,2),i=an(o,3);so(s,e),r&&0==r.length&&(r=void 0),wo(i,r,R.String),kr(n,o);let c=Gn(a,0,Bn);return null==c&&(c=Promise.resolve(0)),c[Cr]=!0,await c}finally{t.runtimeKeepalivePop(),t.stackRestore(o)}},l.javaScriptExports.load_satellite_assembly=e=>{const n=t.stackSave();try{const t=on(3),n=an(t,2);hn(n,R.Array),yo(n,e,R.Byte),kr(c,t)}finally{t.stackRestore(n)}},l.javaScriptExports.load_lazy_assembly=(e,n)=>{const r=t.stackSave();try{const t=on(4),r=an(t,2),o=an(t,3);hn(r,R.Array),hn(o,R.Array),yo(r,e,R.Byte),yo(o,n,R.Byte),kr(p,t)}finally{t.stackRestore(r)}},l.javaScriptExports.release_js_owned_object_by_gc_handle=e=>{e||w(!1,"Must be valid gc_handle"),u.assert_runtime_running();const n=t.stackSave();try{const t=on(3),n=an(t,2);hn(n,R.Object),Un(n,e),kr(r,t)}finally{t.stackRestore(n)}},l.javaScriptExports.create_task_callback=()=>{const e=t.stackSave();u.assert_runtime_running();try{const e=on(2);return kr(o,e),En(an(e,1))}finally{t.stackRestore(e)}},l.javaScriptExports.complete_task=(e,n,r,o)=>{u.assert_runtime_running();const s=t.stackSave();try{const t=on(5),s=an(t,2);hn(s,R.Object),Un(s,e);const i=an(t,3);if(n)ho(i,n);else{hn(i,R.None);const e=an(t,4);o||w(!1,"res_converter missing"),o(e,r)}kr(a,t)}finally{t.stackRestore(s)}},l.javaScriptExports.call_delegate=(e,n,r,o,a,i,c,l)=>{u.assert_runtime_running();const p=t.stackSave();try{const t=on(6),u=an(t,2);if(hn(u,R.Object),Un(u,e),i&&i(an(t,3),n),c&&c(an(t,4),r),l&&l(an(t,5),o),kr(s,t),a)return a(an(t,1))}finally{t.stackRestore(p)}},l.javaScriptExports.get_managed_stack_trace=e=>{u.assert_runtime_running();const n=t.stackSave();try{const t=on(3),n=an(t,2);return hn(n,R.Exception),Un(n,e),kr(i,t),qn(an(t,1))}finally{t.stackRestore(n)}}}(),p||i||function(){if(Object.prototype[Gc]=0,Array.prototype[Gc]=1,ArrayBuffer.prototype[Gc]=2,DataView.prototype[Gc]=3,Function.prototype[Gc]=4,Uint8Array.prototype[Gc]=11,Hc._unbox_buffer_size=65536,Hc._box_buffer=t._malloc(65536),Hc._unbox_buffer=t._malloc(Hc._unbox_buffer_size),Hc._class_int32=Sr("System","Int32"),Hc._class_uint32=Sr("System","UInt32"),Hc._class_double=Sr("System","Double"),Hc._class_boolean=Sr("System","Boolean"),Hc._null_root=Be(),function(){const e=nl;e.set("m",{steps:[{}],size:0}),e.set("s",{steps:[{convert_root:ot.bind(t)}],size:0,needs_root:!0}),e.set("S",{steps:[{convert_root:at.bind(t)}],size:0,needs_root:!0}),e.set("o",{steps:[{convert_root:Xc.bind(t)}],size:0,needs_root:!0}),e.set("u",{steps:[{convert_root:Jc.bind(t,!1)}],size:0,needs_root:!0}),e.set("R",{steps:[{convert_root:Xc.bind(t),byref:!0}],size:0,needs_root:!0}),e.set("j",{steps:[{convert:el.bind(t),indirect:"i32"}],size:8}),e.set("b",{steps:[{indirect:"bool"}],size:8}),e.set("i",{steps:[{indirect:"i32"}],size:8}),e.set("I",{steps:[{indirect:"u32"}],size:8}),e.set("l",{steps:[{indirect:"i52"}],size:8}),e.set("L",{steps:[{indirect:"u52"}],size:8}),e.set("f",{steps:[{indirect:"float"}],size:8}),e.set("d",{steps:[{indirect:"double"}],size:8})}(),Hc.runtime_legacy_exports_classname="LegacyExports",Hc.runtime_legacy_exports_class=St.mono_wasm_assembly_find_class(l.runtime_interop_module,l.runtime_interop_namespace,Hc.runtime_legacy_exports_classname),!Hc.runtime_legacy_exports_class)throw"Can't find "+l.runtime_interop_namespace+"."+Hc.runtime_legacy_exports_classname+" class";for(const e of pl){const t=dl,[n,r,o,a]=e;if(n)t[r]=function(...e){const n=_l(o,a);return t[r]=n,n(...e)};else{const e=_l(o,a);t[r]=e}}}(),0==Kt.size&&(Kt.set(R.Array,Zn),Kt.set(R.Span,Qn),Kt.set(R.ArraySegment,er),Kt.set(R.Boolean,$n),Kt.set(R.Byte,Cn),Kt.set(R.Char,Nn),Kt.set(R.Int16,Dn),Kt.set(R.Int32,Bn),Kt.set(R.Int52,On),Kt.set(R.BigInt64,Mn),Kt.set(R.Single,Fn),Kt.set(R.IntPtr,zn),Kt.set(R.Double,Pn),Kt.set(R.String,qn),Kt.set(R.Exception,Jn),Kt.set(R.JSException,Jn),Kt.set(R.JSObject,Yn),Kt.set(R.Object,Xn),Kt.set(R.DateTime,Wn),Kt.set(R.DateTimeOffset,Wn),Kt.set(R.Task,Gn),Kt.set(R.Action,Hn),Kt.set(R.Function,Hn),Kt.set(R.None,Vn),Kt.set(R.Void,Vn),Kt.set(R.Discard,Vn)),0==Qt.size&&(Qt.set(R.Array,yo),Qt.set(R.Span,So),Qt.set(R.ArraySegment,ko),Qt.set(R.Boolean,Zr),Qt.set(R.Byte,Kr),Qt.set(R.Char,Qr),Qt.set(R.Int16,eo),Qt.set(R.Int32,to),Qt.set(R.Int52,no),Qt.set(R.BigInt64,ro),Qt.set(R.Double,oo),Qt.set(R.Single,ao),Qt.set(R.IntPtr,so),Qt.set(R.DateTime,io),Qt.set(R.DateTimeOffset,co),Qt.set(R.String,lo),Qt.set(R.Exception,ho),Qt.set(R.JSException,ho),Qt.set(R.JSObject,bo),Qt.set(R.Object,go),Qt.set(R.Task,mo),Qt.set(R.Action,_o),Qt.set(R.Function,_o),Qt.set(R.None,po),Qt.set(R.Discard,po),Qt.set(R.Void,po)),l._i52_error_scratch_buffer=t._malloc(4),Yt(e,"mono.bindingsInit")}catch(e){throw dt("Error in bindings_init",e),e}}}(),l.runtimeReady=!0,r&&!o&&t.runtimeKeepalivePush(),l.mono_wasm_runtime_is_ready||mono_wasm_runtime_ready(),0!==u.config.debugLevel&&u.config.cacheBootResources&&u.logDownloadStatsToConsole(),setTimeout((()=>{u.purgeUnusedCacheEntriesAsync()}),u.config.cachedResourcesPurgeDelay);try{e()}catch(e){throw dt("user callback onRuntimeInitialized() failed",e),e}await async function(){lt("mono_wasm_after_user_runtime_initialized");try{if(!t.disableDotnet6Compatibility&&t.exports){const e=globalThis;for(let n=0;n<t.exports.length;++n){const r=t.exports[n],o=t[r];null!=o?e[r]=o:pt(`The exported symbol ${r} could not be found in the emscripten module`)}}if(lt("Initializing mono runtime"),t.onDotnetReady)try{await t.onDotnetReady()}catch(e){throw dt("onDotnetReady () failed",e),e}}catch(e){throw dt("mono_wasm_after_user_runtime_initialized () failed",e),e}}(),Yt(n,"mono.onRuntimeInitialized")}catch(e){throw dt("onRuntimeInitializedAsync() failed",e),u.mono_exit(1,e),e}l.afterOnRuntimeInitialized.promise_control.resolve()}(S),e.postRun=[()=>async function(e){try{await l.afterOnRuntimeInitialized.promise,lt("postRunAsync");const n=Jt();t.FS_createPath("/","usr",!0,!0),t.FS_createPath("/","usr/share",!0,!0),e.map((e=>e())),Yt(n,"mono.postRun")}catch(e){throw dt("user callback posRun() failed",e),u.mono_exit(1,e),e}l.afterPostRun.promise_control.resolve()}(y)],e.ready.then((async()=>{await l.afterPostRun.promise,Yt(n,"mono.emscriptenStartup"),l.dotnetReady.promise_control.resolve(c)})).catch((e=>{l.dotnetReady.promise_control.reject(e)})),e.ready=l.dotnetReady.promise,e.onAbort||(e.onAbort=e=>{u.mono_exit(1,e)}),e.onExit||(e.onExit=e=>{u.mono_exit(e,null)})}async function Pl(e,n){await u.afterConfigLoaded.promise,Nl(e),n(new WebAssembly.Instance(t.wasmModule,e),void 0),t.wasmModule=null}function zl(e){var r,o,a;e||t.addRunDependency("mono_wasm_pre_init_essential"),lt("mono_wasm_pre_init_essential"),u.gitHash!==l.gitHash&&pt("The version of dotnet.runtime.js is different from the version of dotnet.js!"),u.gitHash!==l.moduleGitHash&&pt("The version of dotnet.native.js is different from the version of dotnet.js!"),function(){const e=[...yt,...p?[]:gt];for(const t of e){const e=wt,[n,r,o,a,s]=t,i="function"==typeof n;if(!0===n||i)e[r]=function(...t){!i||!n()||w(!1,`cwrap ${r} should not be called when binding was skipped`);const c=Ut(r,o,a,s);return e[r]=c,c(...t)};else{const t=Ut(r,o,a,s);e[r]=t}}}(),r=n,Object.assign(r,{mono_wasm_exit:St.mono_wasm_exit,mono_wasm_enable_on_demand_gc:St.mono_wasm_enable_on_demand_gc,mono_wasm_profiler_init_aot:vt.mono_wasm_profiler_init_aot,mono_wasm_profiler_init_browser:vt.mono_wasm_profiler_init_browser,mono_wasm_exec_regression:St.mono_wasm_exec_regression}),p||(a=Vc,Object.assign(a,{mono_wasm_add_assembly:kt.mono_wasm_add_assembly}),o=Wc,Object.assign(o,{mono_obj_array_new:kt.mono_wasm_obj_array_new,mono_obj_array_set:kt.mono_wasm_obj_array_set,mono_obj_array_new_ref:kt.mono_wasm_obj_array_new_ref,mono_obj_array_set_ref:kt.mono_wasm_obj_array_set_ref})),e||t.removeRunDependency("mono_wasm_pre_init_essential")}function Vl(e,t){St.mono_wasm_setenv(e,t)}function Wl(){lt("mono_wasm_load_runtime");try{const e=Jt();let t=l.config.debugLevel;null==t&&(t=0,l.config.debugLevel&&(t=0+t)),u.isDebuggingSupported()&&l.config.resources.pdb||(t=0),St.mono_wasm_load_runtime("unused",t),Yt(e,"mono.loadRuntime")}catch(e){throw dt("mono_wasm_load_runtime () failed",e),u.mono_exit(1,e),e}}async function Hl(e){tr=null,null.addEventListener("dotnet:pthread:created",(e=>{lt("pthread created 0x"+e.pthread_self.pthreadId.toString(16))})),e.preInit=[()=>async function(){lt("worker initializing essential C exports and APIs");const e=Jt();try{lt("preInitWorker"),l.beforePreInit.promise_control.resolve(),zl(!0),await Io(),l.afterPreInit.promise_control.resolve(),Yt(e,"mono.preInitWorker")}catch(e){throw dt("user preInitWorker() failed",e),u.mono_exit(1,e),e}}()],e.instantiateWasm=Pl,await l.afterPreInit.promise}function Gl(n){const r=t,o=n,a=globalThis;p||function(e){Vc=e.mono,Wc=e.binding}(o),p||(Object.assign(o.mono,{mono_wasm_setenv:Vl,mono_wasm_load_bytes_into_heap:ye,mono_wasm_load_icu_data:wa,mono_wasm_runtime_ready:mono_wasm_runtime_ready,mono_wasm_new_root_buffer:Ne,mono_wasm_new_root:Be,mono_wasm_new_external_root:De,mono_wasm_release_roots:Oe,mono_run_main:Pc,mono_run_main_and_exit:Fc,mono_wasm_add_assembly:null,mono_wasm_load_runtime:Wl,config:l.config,loaded_files:[],setB32:z,setI8:J,setI16:Y,setI32:Z,setI52:Q,setU52:ee,setI64Big:te,setU8:V,setU16:W,setU32:q,setF32:ne,setF64:re,getB32:oe,getI8:pe,getI16:de,getI32:_e,getI52:fe,getU52:me,getI64Big:he,getU8:ae,getU16:se,getU32:ie,getF32:be,getF64:ge}),Object.assign(o.binding,{bind_static_method:xl,call_assembly_entry_point:Tl,mono_obj_array_new:null,mono_obj_array_set:null,js_string_to_mono_string:ml,js_typed_array_to_array:Qc,mono_array_to_js_array:Sl,js_to_mono_obj:Yc,conv_string:El,unbox_mono_obj:gl,mono_obj_array_new_ref:null,mono_obj_array_set_ref:null,js_string_to_mono_string_root:ot,js_typed_array_to_array_root:Kc,js_to_mono_obj_root:Xc,conv_string_root:rt,unbox_mono_obj_root:wl,mono_array_root_to_js_array:kl}),Object.assign(o.internal,{stringToMonoStringIntern:hl,mono_method_resolve:cl})),Object.assign(o.internal,{mono_wasm_exit:e=>{t.err("early exit "+e)},forceDisposeProxies:Vr,logging:void 0,mono_wasm_stringify_as_error_with_stack:ht,mono_wasm_get_loaded_files:va,mono_wasm_send_dbg_command_with_parms:Dt,mono_wasm_send_dbg_command:Bt,mono_wasm_get_dbg_command_info:Ot,mono_wasm_get_details:Ht,mono_wasm_release_object:qt,mono_wasm_call_function_on:Wt,mono_wasm_debugger_resume:Mt,mono_wasm_detach_debugger:Ft,mono_wasm_raise_debug_event:zt,mono_wasm_change_debugger_log_level:Pt,mono_wasm_debugger_attached:Vt,mono_wasm_runtime_is_ready:l.mono_wasm_runtime_is_ready,mono_wasm_get_func_id_to_name_mappings:bt,get_property:ar,set_property:or,has_property:sr,get_typeof_property:ir,get_global_this:cr,get_dotnet_instance:()=>c,dynamic_import:pr,mono_wasm_cancel_promise:qr,ws_wasm_create:da,ws_wasm_open:_a,ws_wasm_send:fa,ws_wasm_receive:ma,ws_wasm_close:ha,ws_wasm_abort:ba,ws_get_state:pa,http_wasm_supports_streaming_response:Ao,http_wasm_create_abort_controler:Ro,http_wasm_abort_request:Lo,http_wasm_abort_response:$o,http_wasm_fetch:No,http_wasm_fetch_bytes:Co,http_wasm_get_response_header_names:Bo,http_wasm_get_response_header_values:Oo,http_wasm_get_response_bytes:Fo,http_wasm_get_response_length:Mo,http_wasm_get_streamed_response_bytes:Po,jiterpreter_dump_stats:zi,jiterpreter_apply_options:is,jiterpreter_get_options:cs,mono_wasm_gc_lock:Wi,mono_wasm_gc_unlock:Hi,loadLazyAssembly:Gi,loadSatelliteAssemblies:qi}),Object.assign(l,{stringify_as_error_with_stack:ht,instantiate_symbols_asset:ka,instantiate_asset:Sa,jiterpreter_dump_stats:zi,forceDisposeProxies:Vr});const s={runMain:Pc,runMainAndExit:Fc,setEnvironmentVariable:Vl,getAssemblyExports:Er,setModuleImports:rr,getConfig:()=>l.config,invokeLibraryInitializers:u.invokeLibraryInitializers,setHeapB32:z,setHeapU8:V,setHeapU16:W,setHeapU32:q,setHeapI8:J,setHeapI16:Y,setHeapI32:Z,setHeapI52:Q,setHeapU52:ee,setHeapI64Big:te,setHeapF32:ne,setHeapF64:re,getHeapB32:oe,getHeapU8:ae,getHeapU16:se,getHeapU32:ie,getHeapI8:pe,getHeapI16:de,getHeapI32:_e,getHeapI52:fe,getHeapU52:me,getHeapI64Big:he,getHeapF32:be,getHeapF64:ge,localHeapViewU8:Ee,localHeapViewU16:Ue,localHeapViewU32:Ie,localHeapViewI8:we,localHeapViewI16:Se,localHeapViewI32:ke,localHeapViewI64Big:ve,localHeapViewF32:xe,localHeapViewF64:Te};if(Object.assign(c,{INTERNAL:o.internal,Module:r,runtimeBuildInfo:{productVersion:e,gitHash:l.gitHash,buildConfiguration:"Release"},...s}),p||Object.assign(c,{MONO:o.mono,BINDING:o.binding}),void 0===r.disableDotnet6Compatibility&&(r.disableDotnet6Compatibility=!0),!r.disableDotnet6Compatibility){Object.assign(r,c),p||(r.mono_bind_static_method=(e,t)=>(pt("Module.mono_bind_static_method is obsolete, please use [JSExportAttribute] interop instead"),xl(e,t)));const e=(e,t)=>{if(void 0!==a[e])return;let n;Object.defineProperty(globalThis,e,{get:()=>{if(A(n)){const r=(new Error).stack,o=r?r.substr(r.indexOf("\n",8)+1):"";pt(`global ${e} is obsolete, please use Module.${e} instead ${o}`),n=t()}return n}})};a.MONO=o.mono,a.BINDING=o.binding,a.INTERNAL=o.internal,a.Module=r,e("cwrap",(()=>r.cwrap)),e("addRunDependency",(()=>r.addRunDependency)),e("removeRunDependency",(()=>r.removeRunDependency))}let i;return a.getDotnetRuntime?i=a.getDotnetRuntime.__list:(a.getDotnetRuntime=e=>a.getDotnetRuntime.__list.getRuntime(e),a.getDotnetRuntime.__list=i=new ql),i.registerRuntime(c),c}class ql{constructor(){this.list={}}registerRuntime(e){return e.runtimeId=Object.keys(this.list).length,this.list[e.runtimeId]=hr(e),e.runtimeId}getRuntime(e){const t=this.list[e];return t?t.deref():void 0}}export{Fl as configureEmscriptenStartup,Ml as configureRuntimeStartup,Hl as configureWorkerStartup,Gl as initializeExports,Uo as initializeReplacements,b as passEmscriptenInternals,g as setRuntimeGlobals};
//# sourceMappingURL=dotnet.runtime.js.map
