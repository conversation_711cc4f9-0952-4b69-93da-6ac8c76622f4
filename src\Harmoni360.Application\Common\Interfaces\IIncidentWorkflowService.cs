namespace Harmoni360.Application.Common.Interfaces;

/// <summary>
/// Service for managing incident workflows
/// </summary>
public interface IIncidentWorkflowService
{
    /// <summary>
    /// Starts the incident management workflow for a given incident
    /// </summary>
    /// <param name="incidentId">The incident ID</param>
    /// <param name="cancellationToken">Cancellation token</param>
    /// <returns>The workflow instance ID if successful, null if failed</returns>
    Task<string?> StartIncidentWorkflowAsync(int incidentId, CancellationToken cancellationToken = default);
}