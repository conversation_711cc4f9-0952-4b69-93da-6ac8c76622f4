using Harmoni360.Application.Common.Interfaces;
using Harmoni360.Domain.Common;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Hosting;
using Microsoft.Extensions.Logging;
using Microsoft.Extensions.Options;
using Microsoft.Extensions.Diagnostics.HealthChecks;
using System.Text.Json;

namespace Harmoni360.Infrastructure.Services;

public class HealthMonitoringService : BackgroundService
{
    private readonly IServiceProvider _serviceProvider;
    private readonly ILogger<HealthMonitoringService> _logger;
    private readonly HealthMonitoringSettings _settings;

    public HealthMonitoringService(
        IServiceProvider serviceProvider,
        ILogger<HealthMonitoringService> logger,
        IOptions<HealthMonitoringSettings> settings)
    {
        _serviceProvider = serviceProvider;
        _logger = logger;
        _settings = settings.Value;
    }

    protected override async Task ExecuteAsync(CancellationToken stoppingToken)
    {
        if (!_settings.EnableBackgroundMonitoring)
        {
            _logger.LogInformation("Background health monitoring is disabled");
            return;
        }

        _logger.LogInformation("Health monitoring service started with interval: {Interval} minutes", 
            _settings.CheckIntervalMinutes);

        var checkInterval = TimeSpan.FromMinutes(_settings.CheckIntervalMinutes);

        while (!stoppingToken.IsCancellationRequested)
        {
            try
            {
                await PerformHealthChecks(stoppingToken);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error occurred during health monitoring cycle");
            }

            await Task.Delay(checkInterval, stoppingToken);
        }
    }

    private async Task PerformHealthChecks(CancellationToken cancellationToken)
    {
        try
        {
            using var scope = _serviceProvider.CreateScope();
            var healthCheckService = scope.ServiceProvider.GetRequiredService<HealthCheckService>();

            _logger.LogDebug("Starting health check cycle");
            var healthReport = await healthCheckService.CheckHealthAsync(cancellationToken);

            // Log overall health status
            _logger.LogInformation("Health check completed - Status: {Status}, Duration: {Duration}ms, Checks: {CheckCount}",
                healthReport.Status,
                healthReport.TotalDuration.TotalMilliseconds,
                healthReport.Entries.Count);

            // Process individual health check results
            await ProcessHealthCheckResults(healthReport, cancellationToken);

            // Store health check results for trending
            await StoreHealthCheckResults(healthReport, scope, cancellationToken);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to perform health checks");
        }
    }

    private async Task ProcessHealthCheckResults(HealthReport healthReport, CancellationToken cancellationToken)
    {
        var unhealthyChecks = healthReport.Entries
            .Where(e => e.Value.Status == HealthStatus.Unhealthy)
            .ToList();

        var degradedChecks = healthReport.Entries
            .Where(e => e.Value.Status == HealthStatus.Degraded)
            .ToList();

        // Handle unhealthy systems
        foreach (var unhealthyCheck in unhealthyChecks)
        {
            _logger.LogError("Unhealthy system detected: {CheckName} - {Description} - {Exception}",
                unhealthyCheck.Key,
                unhealthyCheck.Value.Description,
                unhealthyCheck.Value.Exception?.Message);

            await HandleUnhealthySystem(unhealthyCheck.Key, unhealthyCheck.Value, cancellationToken);
        }

        // Handle degraded systems
        foreach (var degradedCheck in degradedChecks)
        {
            _logger.LogWarning("Degraded system detected: {CheckName} - {Description}",
                degradedCheck.Key,
                degradedCheck.Value.Description);

            await HandleDegradedSystem(degradedCheck.Key, degradedCheck.Value, cancellationToken);
        }

        // Send summary notification if there are issues
        if (unhealthyChecks.Any() || degradedChecks.Any())
        {
            await SendHealthStatusNotification(healthReport, cancellationToken);
        }
    }

    private async Task HandleUnhealthySystem(string systemName, HealthReportEntry entry, CancellationToken cancellationToken)
    {
        if (!_settings.SendAlerts)
            return;

        try
        {
            var alertRequest = new NotificationRequest
            {
                Email = _settings?.AlertEmail ?? "<EMAIL>",
                Subject = $"[CRITICAL] System Health Alert - {systemName}",
                TemplateName = "SystemHealthAlert",
                Data = new Dictionary<string, object>
                {
                    ["MessageBody"] = $@"
                    <h2>Critical System Health Alert</h2>
                    <p><strong>System:</strong> {systemName}</p>
                    <p><strong>Status:</strong> Unhealthy</p>
                    <p><strong>Description:</strong> {entry.Description}</p>
                    <p><strong>Duration:</strong> {entry.Duration.TotalMilliseconds}ms</p>
                    <p><strong>Timestamp:</strong> {DateTime.UtcNow:yyyy-MM-dd HH:mm:ss} UTC</p>
                    {(entry.Exception != null ? $"<p><strong>Error:</strong> {entry.Exception.Message}</p>" : "")}
                    
                    <h3>System Data:</h3>
                    <pre>{JsonSerializer.Serialize(entry.Data, new JsonSerializerOptions { WriteIndented = true })}</pre>
                    
                    <p>Please investigate this issue immediately.</p>
                ",
                    ["MessageType"] = "Critical"
                },
                Priority = NotificationPriority.Critical
            };

            using var scope = _serviceProvider.CreateScope();
            var notificationService = scope.ServiceProvider.GetRequiredService<INotificationService>();
            await notificationService.SendUrgentNotificationAsync(alertRequest);
            _logger.LogInformation("Critical health alert sent for system: {SystemName}", systemName);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to send critical health alert for system: {SystemName}", systemName);
        }
    }

    private async Task HandleDegradedSystem(string systemName, HealthReportEntry entry, CancellationToken cancellationToken)
    {
        if (!_settings.SendAlerts || !_settings.SendDegradedAlerts)
            return;

        try
        {
            var alertRequest = new NotificationRequest
            {
                Email = _settings?.AlertEmail ?? "<EMAIL>",
                Subject = $"[WARNING] System Health Alert - {systemName}",
                TemplateName = "SystemHealthWarning",
                Data = new Dictionary<string, object>
                {
                    ["MessageBody"] = $@"
                    <h2>System Performance Warning</h2>
                    <p><strong>System:</strong> {systemName}</p>
                    <p><strong>Status:</strong> Degraded</p>
                    <p><strong>Description:</strong> {entry.Description}</p>
                    <p><strong>Duration:</strong> {entry.Duration.TotalMilliseconds}ms</p>
                    <p><strong>Timestamp:</strong> {DateTime.UtcNow:yyyy-MM-dd HH:mm:ss} UTC</p>
                    
                    <h3>System Data:</h3>
                    <pre>{JsonSerializer.Serialize(entry.Data, new JsonSerializerOptions { WriteIndented = true })}</pre>
                    
                    <p>The system is functional but may be experiencing performance issues.</p>
                ",
                    ["MessageType"] = "Warning"
                },
                Priority = NotificationPriority.High
            };

            using var scope = _serviceProvider.CreateScope();
            var notificationService = scope.ServiceProvider.GetRequiredService<INotificationService>();
            await notificationService.SendEmailNotificationAsync(alertRequest);
            _logger.LogInformation("Degraded health alert sent for system: {SystemName}", systemName);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to send degraded health alert for system: {SystemName}", systemName);
        }
    }

    private async Task SendHealthStatusNotification(HealthReport healthReport, CancellationToken cancellationToken)
    {
        if (!_settings.SendSummaryReports)
            return;

        try
        {
            var healthyCount = healthReport.Entries.Count(e => e.Value.Status == HealthStatus.Healthy);
            var degradedCount = healthReport.Entries.Count(e => e.Value.Status == HealthStatus.Degraded);
            var unhealthyCount = healthReport.Entries.Count(e => e.Value.Status == HealthStatus.Unhealthy);

            var summaryRequest = new NotificationRequest
            {
                Email = _settings?.SummaryEmail ?? _settings?.AlertEmail ?? "<EMAIL>",
                Subject = $"System Health Summary - {healthReport.Status}",
                TemplateName = "SystemHealthSummary",
                Data = new Dictionary<string, object>
                {
                    ["MessageBody"] = $@"
                    <h2>System Health Summary</h2>
                    <p><strong>Overall Status:</strong> {healthReport.Status}</p>
                    <p><strong>Total Duration:</strong> {healthReport.TotalDuration.TotalMilliseconds}ms</p>
                    <p><strong>Timestamp:</strong> {DateTime.UtcNow:yyyy-MM-dd HH:mm:ss} UTC</p>
                    
                    <h3>Health Check Summary</h3>
                    <ul>
                        <li><strong>Healthy:</strong> {healthyCount}</li>
                        <li><strong>Degraded:</strong> {degradedCount}</li>
                        <li><strong>Unhealthy:</strong> {unhealthyCount}</li>
                    </ul>
                    
                    <h3>Detailed Results</h3>
                    <table border='1' style='border-collapse: collapse;'>
                        <tr>
                            <th>System</th>
                            <th>Status</th>
                            <th>Duration (ms)</th>
                            <th>Description</th>
                        </tr>
                        {string.Join("", healthReport.Entries.Select(entry => $@"
                        <tr style='background-color: {GetStatusColor(entry.Value.Status)}'>
                            <td>{entry.Key}</td>
                            <td>{entry.Value.Status}</td>
                            <td>{entry.Value.Duration.TotalMilliseconds:F1}</td>
                            <td>{entry.Value.Description}</td>
                        </tr>"))}
                    </table>
                ",
                    ["MessageType"] = healthReport.Status == HealthStatus.Healthy ? "Information" : "Warning"
                },
                Priority = healthReport.Status == HealthStatus.Healthy ? NotificationPriority.Normal : NotificationPriority.High
            };

            using var scope = _serviceProvider.CreateScope();
            var notificationService = scope.ServiceProvider.GetRequiredService<INotificationService>();
            await notificationService.SendEmailNotificationAsync(summaryRequest);
            _logger.LogInformation("Health summary report sent");
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to send health summary notification");
        }
    }

    private async Task StoreHealthCheckResults(HealthReport healthReport, IServiceScope scope, CancellationToken cancellationToken)
    {
        if (!_settings.StoreResults)
            return;

        try
        {
            // Store health check results for historical analysis
            var auditService = scope.ServiceProvider.GetService<IWorkflowAuditService>();
            if (auditService != null)
            {
                await auditService.LogPerformanceMetricAsync(
                    "system-health-monitoring",
                    "OverallHealthStatus",
                    (double)healthReport.Status,
                    "enum",
                    new Dictionary<string, object>
                    {
                        ["total_duration_ms"] = healthReport.TotalDuration.TotalMilliseconds,
                        ["healthy_count"] = healthReport.Entries.Count(e => e.Value.Status == HealthStatus.Healthy),
                        ["degraded_count"] = healthReport.Entries.Count(e => e.Value.Status == HealthStatus.Degraded),
                        ["unhealthy_count"] = healthReport.Entries.Count(e => e.Value.Status == HealthStatus.Unhealthy),
                        ["check_details"] = JsonSerializer.Serialize(healthReport.Entries.ToDictionary(
                            e => e.Key,
                            e => new
                            {
                                Status = e.Value.Status.ToString(),
                                Duration = e.Value.Duration.TotalMilliseconds,
                                Description = e.Value.Description
                            }))
                    });
            }
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to store health check results");
        }
    }

    private static string GetStatusColor(HealthStatus status) => status switch
    {
        HealthStatus.Healthy => "#d4edda",
        HealthStatus.Degraded => "#fff3cd",
        HealthStatus.Unhealthy => "#f8d7da",
        _ => "#ffffff"
    };
}

public class HealthMonitoringSettings
{
    public bool EnableBackgroundMonitoring { get; set; } = true;
    public int CheckIntervalMinutes { get; set; } = 5;
    public bool SendAlerts { get; set; } = true;
    public bool SendDegradedAlerts { get; set; } = true;
    public bool SendSummaryReports { get; set; } = false;
    public bool StoreResults { get; set; } = true;
    public string AlertEmail { get; set; } = string.Empty;
    public string? SummaryEmail { get; set; }
}