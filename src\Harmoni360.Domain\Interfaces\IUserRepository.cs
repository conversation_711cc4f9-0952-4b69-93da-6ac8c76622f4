using Harmoni360.Domain.Entities;

namespace Harmoni360.Domain.Interfaces;

/// <summary>
/// Repository interface for User entity operations
/// </summary>
public interface IUserRepository : IRepository<User>
{
    /// <summary>
    /// Gets a user by email address
    /// </summary>
    Task<User?> GetByEmailAsync(string email);
    
    /// <summary>
    /// Gets a user by employee ID
    /// </summary>
    Task<User?> GetByEmployeeIdAsync(string employeeId);
    
    /// <summary>
    /// Gets a user with their roles
    /// </summary>
    Task<User?> GetByIdWithRolesAsync(int userId);
    
    /// <summary>
    /// Gets users by role
    /// </summary>
    Task<IEnumerable<User>> GetUsersByRoleAsync(int roleId);
    
    /// <summary>
    /// Gets all active users
    /// </summary>
    Task<IEnumerable<User>> GetActiveUsersAsync();
}