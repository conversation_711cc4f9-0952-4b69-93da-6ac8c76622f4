# Comprehensive Workflow Visibility & Synchronization Guide

## Overview
This guide explains the automatic workflow discovery system that ensures all workflows (code-based and designer-created) are properly synchronized and visible in Elsa Studio.

## Automatic Workflow Discovery System

### 🚀 **Automatic Synchronization** (Default)
Workflows are now **automatically discovered and synchronized** on application startup and periodically thereafter. This includes:

1. **Code-based workflows** - Automatically registered from C# code
2. **Designer-created workflows** - Validated and metadata updated  
3. **Orphaned instances** - Cleaned up automatically
4. **Metadata synchronization** - Ensures consistency

### 🔄 **Synchronization Schedule**
- **Initial sync**: 5 seconds after application startup
- **Periodic sync**: Every 30 minutes
- **Manual sync**: Available via API endpoint

## What Gets Synchronized

### 1. **Code-Based Workflows**
- **IncidentManagementWorkflow** - Main incident lifecycle workflow
- **IncidentEscalationWorkflow** - Escalation handling
- **IncidentInvestigationWorkflow** - Investigation process
- **IncidentReviewWorkflow** - Review and approval

### 2. **Designer-Created Workflows**
- Any workflows created through Elsa Studio designer
- Metadata validation and updates
- Structure integrity checks

### 3. **Workflow Metadata**
- Display names (auto-formatted from code names)
- Descriptions (auto-generated for code-based workflows)
- Categories and tags
- Synchronization timestamps
- Version management

## How to Access Workflows

### **Automatic Access** (Recommended)
1. Start the application: `dotnet run --project src/Harmoni360.Web`
2. Wait 5 seconds for initial sync (check logs)
3. Login as SuperAdmin or Developer
4. Navigate to `http://localhost:5173/workflows`
5. You'll be redirected to Elsa Studio with workflows visible

### **Manual Synchronization** (Optional)
If you need to force immediate synchronization:

```javascript
// Trigger manual synchronization
fetch('/api/WorkflowRegistration/synchronize', {
  method: 'POST',
  headers: {
    'Authorization': `Bearer ${localStorage.getItem('harmoni360_token')}`,
    'Content-Type': 'application/json'
  }
})
.then(res => res.json())
.then(data => console.log(data));
```

### **Check Synchronization Status**
```javascript
// Check current workflow status
fetch('/api/WorkflowRegistration/status', {
  headers: {
    'Authorization': `Bearer ${localStorage.getItem('harmoni360_token')}`
  }
})
.then(res => res.json())
.then(data => console.log(data));
```

**Expected Response:**
```json
{
  "totalWorkflows": 4,
  "codeBasedCount": 4,
  "designerCount": 0,
  "workflows": [
    {
      "id": "...",
      "name": "IncidentManagementWorkflow",
      "displayName": "Incident Management Workflow",
      "version": 1,
      "isPublished": true,
      "isLatest": true,
      "type": "code-based",
      "lastSynchronized": "2025-01-23T12:00:00Z"
    }
  ]
}
```

## Troubleshooting

### 1. Still No Workflows Visible?

**Check Elsa API is accessible:**
```bash
curl http://localhost:5173/elsa/api/workflow-definitions \
  -H "Authorization: Bearer YOUR_JWT_TOKEN"
```

**Check database directly:**
```sql
-- Connect to PostgreSQL
SELECT * FROM "WorkflowDefinitions";
SELECT * FROM "WorkflowInstances";
```

### 2. Authentication Issues

If you get 401 Unauthorized:
- Ensure you're logged in as SuperAdmin or Developer
- Check token is properly set in localStorage
- Verify token hasn't expired

### 3. Registration Fails

Common causes:
- Database migrations not applied
- Elsa tables not created
- Connection string issues

**Fix:**
```bash
# Apply migrations
dotnet ef database update -p src/Harmoni360.Infrastructure -s src/Harmoni360.Web
```

### 4. Workflows Visible but Can't Execute

Ensure workflow activities are registered:
```csharp
// Check in DependencyInjection.cs
.AddActivitiesFrom<IncidentActivityBase>()
.AddWorkflowsFrom<IncidentManagementWorkflow>()
```

## Creating Workflow Instances

Once workflows are visible, you can:

### 1. Via UI (When Incident is Created)
- Create an incident through the UI
- This automatically triggers the workflow
- Check instances in Elsa Studio

### 2. Via API
```javascript
// Create incident via API
fetch('/api/incidents', {
  method: 'POST',
  headers: {
    'Authorization': `Bearer ${localStorage.getItem('harmoni360_token')}`,
    'Content-Type': 'application/json'
  },
  body: JSON.stringify({
    title: "Test Incident",
    description: "Testing workflow trigger",
    severity: "Minor",
    category: "Safety"
  })
})
.then(res => res.json())
.then(data => {
  console.log('Incident created:', data);
  console.log('Check workflow instance in Elsa Studio');
});
```

## Workflow Execution Monitoring

### In Elsa Studio:
1. Navigate to `Workflows > Instances`
2. You'll see running instances
3. Click on an instance to see:
   - Current state
   - Activity history
   - Variables
   - Execution log

### Via API:
```bash
# Get workflow instances
curl http://localhost:5173/elsa/api/workflow-instances \
  -H "Authorization: Bearer YOUR_JWT_TOKEN"
```

## Best Practices

1. **Always Register After Code Changes**
   - When you modify workflow code
   - Run registration API again
   - Version will increment automatically

2. **Use Descriptive Names**
   - Workflows should have clear DisplayName
   - Add meaningful descriptions

3. **Monitor Execution**
   - Check Elsa Studio regularly
   - Look for stuck workflows
   - Review execution logs

4. **Test in Development**
   - Register workflows in dev first
   - Test execution paths
   - Verify all activities work

## System Architecture

### **WorkflowAutoDiscoveryService**
- **Background service** running continuously
- **Automatic discovery** of code-based workflows via reflection
- **Periodic synchronization** every 30 minutes
- **Metadata management** for all workflow types
- **Cleanup** of orphaned instances

### **Integration Points**
```mermaid
graph TD
    A[Application Startup] --> B[WorkflowAutoDiscoveryService]
    B --> C[Discover Code Workflows]
    B --> D[Validate Designer Workflows]
    B --> E[Update Metadata]
    B --> F[Cleanup Orphaned]
    
    G[Elsa Studio] --> H[WorkflowDefinitionStore]
    I[Designer Created] --> H
    C --> H
    
    J[Manual Sync API] --> B
    K[Status API] --> H
```

## Benefits of Automatic Discovery

1. **🚀 Zero Configuration**: Workflows appear automatically
2. **🔄 Always Synchronized**: Code changes are detected and updated
3. **🧹 Self-Maintaining**: Cleanup of orphaned instances
4. **🔍 Comprehensive**: Handles both code-based and designer workflows
5. **📊 Metadata Consistency**: Ensures proper display names and descriptions
6. **⚡ Performance**: Minimal overhead with efficient discovery
7. **🛡️ Reliability**: Continues running even if individual workflows fail

## Quick Start Checklist

- [ ] Application running (`dotnet run`)
- [ ] Wait 5 seconds for automatic sync (check logs for "✅ Workflow synchronization completed")
- [ ] Logged in as SuperAdmin/Developer  
- [ ] Navigate to Elsa Studio via `/workflows`
- [ ] Verify workflows visible in `Workflows > Definitions`
- [ ] Create test incident to trigger workflow
- [ ] Monitor execution in `Workflows > Instances`

## Common Workflow States

| State | Description | Action |
|-------|-------------|--------|
| Running | Workflow is executing | Monitor progress |
| Suspended | Waiting for input | Check pending tasks |
| Faulted | Error occurred | Review logs, fix issue |
| Finished | Completed successfully | Review results |
| Cancelled | Manually stopped | Check reason |

## Next Steps

1. Review test scenarios in `05_TEST_SCENARIOS.md`
2. Execute test cases
3. Monitor workflow performance
4. Implement additional workflows as needed