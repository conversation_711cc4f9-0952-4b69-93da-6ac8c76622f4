using Elsa.Workflows;
using Elsa.Workflows.Attributes;
using Harmoni360.Application.Common.Interfaces;
using Harmoni360.Application.Features.WorkflowManagement.IncidentWorkflow.Models;
using Harmoni360.Domain.Interfaces;
using Microsoft.Extensions.Logging;
using System.ComponentModel;
using Elsa.Extensions;
using Elsa.Workflows.Models;

namespace Harmoni360.Application.Features.WorkflowManagement.IncidentWorkflow.Activities;

/// <summary>
/// Activity that classifies incident severity based on business rules
/// </summary>
[Activity("Incident Management", "Classify Incident", "Classifies incident severity based on predefined business rules")]
public class ClassifyIncidentActivity : IncidentActivityBase<IncidentWorkflowContext>
{
    public ClassifyIncidentActivity(
        ILogger<ClassifyIncidentActivity> logger,
        IIncidentRepository incidentRepository,
        ICurrentUserService currentUserService)
        : base(logger, incidentRepository, currentUserService)
    {
    }
    
    /// <summary>
    /// The incident workflow context
    /// </summary>
    [Input(
        Description = "The incident workflow context to classify",
        DisplayName = "Incident Context"
    )]
    public Input<IncidentWorkflowContext> Context { get; set; } = default!;
    
    /// <summary>
    /// The generated incident number
    /// </summary>
    [Input(
        Description = "The generated incident number",
        DisplayName = "Incident Number"
    )]
    public Input<string> IncidentNumber { get; set; } = default!;
    
    protected override async ValueTask ExecuteAsync(ActivityExecutionContext context)
    {
        const string activityName = nameof(ClassifyIncidentActivity);
        LogActivity(activityName, "Starting incident classification");
        
        try
        {
            var workflowContext = Context.Get(context);
            var incidentNumber = IncidentNumber.Get(context);
            
            // Update incident number in context
            workflowContext.IncidentNumber = incidentNumber;
            
            // Apply classification rules
            var classificationResult = await ClassifyIncident(workflowContext);
            
            // Update context with classification results
            workflowContext.Severity = classificationResult.Severity;
            workflowContext.Status = "Classified";
            
            // Update incident in database
            if (int.TryParse(workflowContext.IncidentId, out var incidentId))
            {
                var incident = await IncidentRepository.GetByIdAsync(incidentId);
                if (incident != null)
                {
                    incident.UpdateSeverity(MapSeverity(classificationResult.Severity));
                    incident.UpdateStatus(Domain.Entities.IncidentStatus.UnderInvestigation);
                    // Note: Classification would need to be added as a property to Incident entity
                    await IncidentRepository.UpdateAsync(incident);
                }
            }
            
            LogActivity(activityName, 
                "Incident classified - ID: {IncidentId}, Number: {IncidentNumber}, Severity: {Severity}, Classification: {Classification}",
                workflowContext.IncidentId, 
                workflowContext.IncidentNumber, 
                classificationResult.Severity, 
                classificationResult.Classification);
            
            // Set the output
            Result.Set(context, workflowContext);
        }
        catch (Exception ex)
        {
            LogActivityError(activityName, ex, "Failed to classify incident");
            
            // Return context with default classification
            var workflowContext = Context.Get(context);
            workflowContext.Severity = "Major"; // Default to Major for safety
            workflowContext.Status = "Classification Failed";
            Result.Set(context, workflowContext);
        }
    }
    
    private async Task<IncidentClassificationResult> ClassifyIncident(IncidentWorkflowContext workflowContext)
    {
        var result = new IncidentClassificationResult();
        
        // Get additional incident details from database if needed
        if (int.TryParse(workflowContext.IncidentId, out var incidentId))
        {
            var incident = await IncidentRepository.GetByIdAsync(incidentId);
            if (incident != null)
            {
                // Apply classification business rules
                result = ApplyClassificationRules(workflowContext, incident);
            }
        }
        
        // Fallback classification if no database record found
        if (string.IsNullOrEmpty(result.Severity))
        {
            result = ApplyBasicClassificationRules(workflowContext);
        }
        
        return result;
    }
    
    private IncidentClassificationResult ApplyClassificationRules(
        IncidentWorkflowContext context, 
        Domain.Entities.Incident incident)
    {
        var result = new IncidentClassificationResult();
        
        // Rule 1: Fatality incidents are always Critical
        if (ContainsFatalityKeywords(context.Description))
        {
            result.Severity = "Fatality";
            result.Classification = "Critical";
            result.RequiresInvestigation = true;
            result.RequiresImmediateEscalation = true;
            return result;
        }
        
        // Rule 2: Major injury or significant property damage
        if (ContainsMajorInjuryKeywords(context.Description) || 
            ContainsPropertyDamageKeywords(context.Description))
        {
            result.Severity = "Major";
            result.Classification = "High";
            result.RequiresInvestigation = true;
            result.RequiresImmediateEscalation = false;
            return result;
        }
        
        // Rule 3: Near miss events
        if (context.IncidentType.Equals("NearMiss", StringComparison.OrdinalIgnoreCase))
        {
            // Near miss with high potential
            if (ContainsHighPotentialKeywords(context.Description))
            {
                result.Severity = "Major";
                result.Classification = "High Potential Near Miss";
                result.RequiresInvestigation = true;
            }
            else
            {
                result.Severity = "Minor";
                result.Classification = "Low Potential Near Miss";
                result.RequiresInvestigation = false;
            }
            return result;
        }
        
        // Rule 4: Environmental incidents
        if (ContainsEnvironmentalKeywords(context.Description))
        {
            result.Severity = "Major";
            result.Classification = "Environmental";
            result.RequiresInvestigation = true;
            return result;
        }
        
        // Rule 5: First aid incidents
        if (ContainsFirstAidKeywords(context.Description))
        {
            result.Severity = "Minor";
            result.Classification = "First Aid";
            result.RequiresInvestigation = false;
            return result;
        }
        
        // Default classification
        result.Severity = "Major"; // Default to Major for safety
        result.Classification = "Standard";
        result.RequiresInvestigation = true;
        
        return result;
    }
    
    private IncidentClassificationResult ApplyBasicClassificationRules(IncidentWorkflowContext context)
    {
        var result = new IncidentClassificationResult
        {
            Severity = context.IncidentType.Equals("NearMiss", StringComparison.OrdinalIgnoreCase) ? "Minor" : "Major",
            Classification = "Standard",
            RequiresInvestigation = !context.IncidentType.Equals("NearMiss", StringComparison.OrdinalIgnoreCase)
        };
        
        return result;
    }
    
    private static bool ContainsFatalityKeywords(string description)
    {
        var keywords = new[] { "death", "died", "fatal", "fatality", "deceased", "killed", "lifeless" };
        return keywords.Any(keyword => description.Contains(keyword, StringComparison.OrdinalIgnoreCase));
    }
    
    private static bool ContainsMajorInjuryKeywords(string description)
    {
        var keywords = new[] { 
            "fracture", "broken bone", "hospitalization", "surgery", "amputation", 
            "severe laceration", "head injury", "spinal injury", "internal bleeding",
            "unconscious", "cardiac arrest", "respiratory failure"
        };
        return keywords.Any(keyword => description.Contains(keyword, StringComparison.OrdinalIgnoreCase));
    }
    
    private static bool ContainsPropertyDamageKeywords(string description)
    {
        var keywords = new[] { 
            "explosion", "fire", "structural damage", "equipment failure", 
            "major damage", "extensive damage", "total loss"
        };
        return keywords.Any(keyword => description.Contains(keyword, StringComparison.OrdinalIgnoreCase));
    }
    
    private static bool ContainsHighPotentialKeywords(string description)
    {
        var keywords = new[] { 
            "high potential", "could have been fatal", "serious injury potential",
            "major consequence potential", "high energy", "height", "confined space",
            "hazardous chemical", "electrical", "machinery"
        };
        return keywords.Any(keyword => description.Contains(keyword, StringComparison.OrdinalIgnoreCase));
    }
    
    private static bool ContainsEnvironmentalKeywords(string description)
    {
        var keywords = new[] { 
            "spill", "leak", "contamination", "environmental", "water pollution",
            "air pollution", "soil contamination", "waste discharge"
        };
        return keywords.Any(keyword => description.Contains(keyword, StringComparison.OrdinalIgnoreCase));
    }
    
    private static bool ContainsFirstAidKeywords(string description)
    {
        var keywords = new[] { 
            "first aid", "minor cut", "bruise", "scrape", "bandage",
            "minor burn", "small cut", "minor injury"
        };
        return keywords.Any(keyword => description.Contains(keyword, StringComparison.OrdinalIgnoreCase));
    }
    
    private static Domain.Entities.IncidentSeverity MapSeverity(string severity)
    {
        return severity.ToLowerInvariant() switch
        {
            "minor" => Domain.Entities.IncidentSeverity.Minor,
            "major" => Domain.Entities.IncidentSeverity.Major,
            "fatality" => Domain.Entities.IncidentSeverity.Fatal,
            _ => Domain.Entities.IncidentSeverity.Major
        };
    }
}

/// <summary>
/// Result of incident classification
/// </summary>
public class IncidentClassificationResult
{
    public string Severity { get; set; } = string.Empty;
    public string Classification { get; set; } = string.Empty;
    public bool RequiresInvestigation { get; set; }
    public bool RequiresImmediateEscalation { get; set; }
}