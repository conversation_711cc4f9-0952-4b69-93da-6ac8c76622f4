#!/bin/bash

echo "=== Harmoni360 Build Test Script ==="
echo "This script simulates the full build process for development and production"
echo ""

# Colors for output
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
RED='\033[0;31m'
NC='\033[0m' # No Color

# Function to check if directory exists and count files
check_directory() {
    local dir=$1
    if [ -d "$dir" ]; then
        local count=$(find "$dir" -type f | wc -l)
        echo -e "${GREEN}✓${NC} $dir exists with $count files"
    else
        echo -e "${RED}✗${NC} $dir does not exist"
    fi
}

# Step 1: Clean everything
echo -e "${YELLOW}Step 1: Cleaning all generated files...${NC}"
cd src/Harmoni360.Web

# Clean .NET build artifacts
dotnet clean
rm -rf bin obj

# Clean Elsa Studio build artifacts
cd ../Harmoni360.ElsaStudio
dotnet clean
rm -rf bin obj
cd ../Harmoni360.Web

# Clean generated directories
rm -rf wwwroot/elsa-studio
rm -rf ClientApp/dist
rm -rf ClientApp/public/elsa-studio
rm -rf ClientApp/public/monaco-editor
rm -rf ClientApp/public/min

echo -e "${GREEN}Cleaned all directories${NC}"
echo ""

# Step 2: Install dependencies
echo -e "${YELLOW}Step 2: Installing dependencies...${NC}"

# Restore NuGet packages first
echo "Restoring NuGet packages..."
dotnet restore ../Harmoni360.ElsaStudio/Harmoni360.ElsaStudio.csproj
dotnet restore Harmoni360.Web.csproj

# Install npm packages
echo "Installing npm packages..."
cd ClientApp
npm install
cd ..
echo ""

# Step 3: Development Build
echo -e "${YELLOW}Step 3: Running DEVELOPMENT build (dotnet build)...${NC}"
dotnet build --configuration Debug

echo ""
echo -e "${YELLOW}Development build results:${NC}"
check_directory "ClientApp/public/elsa-studio"
check_directory "ClientApp/public/monaco-editor"
check_directory "ClientApp/public/min"
check_directory "wwwroot/elsa-studio"
echo ""

# Step 4: Production Build
echo -e "${YELLOW}Step 4: Running PRODUCTION build (dotnet publish)...${NC}"
dotnet publish --configuration Release --output ./publish-test

echo ""
echo -e "${YELLOW}Production build results:${NC}"
check_directory "publish-test/wwwroot/elsa-studio"
check_directory "publish-test/ClientApp/dist"
echo ""

# Step 5: Verify key files
echo -e "${YELLOW}Step 5: Verifying key files exist...${NC}"

# Check Elsa Studio files
if [ -f "wwwroot/elsa-studio/index.html" ]; then
    echo -e "${GREEN}✓${NC} Elsa Studio index.html exists"
else
    echo -e "${RED}✗${NC} Elsa Studio index.html missing"
fi

if [ -f "wwwroot/elsa-studio/_framework/blazor.webassembly.js" ]; then
    echo -e "${GREEN}✓${NC} Blazor WebAssembly runtime exists"
else
    echo -e "${RED}✗${NC} Blazor WebAssembly runtime missing"
fi

if [ -d "wwwroot/elsa-studio/_content/BlazorMonaco/lib/monaco-editor" ]; then
    echo -e "${GREEN}✓${NC} Monaco Editor files exist in Elsa Studio"
else
    echo -e "${RED}✗${NC} Monaco Editor files missing in Elsa Studio"
fi

# Check React build
if [ -f "publish-test/ClientApp/dist/index.html" ]; then
    echo -e "${GREEN}✓${NC} React app built successfully"
else
    echo -e "${RED}✗${NC} React app build missing"
fi

echo ""
echo -e "${YELLOW}Step 6: File structure preview:${NC}"
echo "Development (ClientApp/public):"
find ClientApp/public -type d -name "elsa-studio" -o -name "monaco-editor" -o -name "min" | head -10

echo ""
echo "Production (wwwroot):"
find wwwroot/elsa-studio -type d | head -10

echo ""
echo -e "${GREEN}Build test complete!${NC}"
echo ""
echo "To test locally:"
echo "  Development: dotnet run (uses ClientApp/public files)"
echo "  Production: dotnet publish-test/Harmoni360.Web.dll (uses wwwroot files)"