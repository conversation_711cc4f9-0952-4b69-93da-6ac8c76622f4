using Elsa.Workflows;
using Elsa.Workflows.Attributes;
using Harmoni360.Application.Common.Interfaces;
using Harmoni360.Application.Features.WorkflowManagement.IncidentWorkflow.Models;
using Harmoni360.Domain.Interfaces;
using Microsoft.Extensions.Logging;
using Elsa.Extensions;
using Elsa.Workflows.Models;

namespace Harmoni360.Application.Features.WorkflowManagement.IncidentWorkflow.Activities;

/// <summary>
/// Activity that determines corrective actions based on investigation findings
/// </summary>
[Activity("Incident Management", "Determine Corrective Actions", "Analyzes investigation findings and determines required corrective actions")]
public class DetermineCorrectiveActionsActivity : IncidentActivityBase<IncidentWorkflowContext>
{
    private readonly ICorrectiveActionService _correctiveActionService;
    
    public DetermineCorrectiveActionsActivity(
        ILogger<DetermineCorrectiveActionsActivity> logger,
        IIncidentRepository incidentRepository,
        ICurrentUserService currentUserService,
        ICorrectiveActionService correctiveActionService)
        : base(logger, incidentRepository, currentUserService)
    {
        _correctiveActionService = correctiveActionService;
    }
    
    /// <summary>
    /// The incident workflow context
    /// </summary>
    [Input(
        Description = "The incident workflow context with investigation results",
        DisplayName = "Incident Context"
    )]
    public Input<IncidentWorkflowContext> Context { get; set; } = default!;
    
    protected override async ValueTask ExecuteAsync(ActivityExecutionContext context)
    {
        const string activityName = nameof(DetermineCorrectiveActionsActivity);
        LogActivity(activityName, "Starting corrective action determination");
        
        try
        {
            var workflowContext = Context.Get(context);
            
            // Generate corrective actions based on investigation findings
            var correctiveActions = await GenerateCorrectiveActions(workflowContext);
            
            // Update workflow context
            workflowContext.CorrectiveActions = correctiveActions;
            workflowContext.Status = "Actions Determined";
            
            LogActivity(activityName, 
                "Generated {ActionCount} corrective actions for incident: {IncidentNumber}",
                correctiveActions.Count, workflowContext.IncidentNumber);
            
            // Set the output
            Result.Set(context, workflowContext);
        }
        catch (Exception ex)
        {
            LogActivityError(activityName, ex, "Failed to determine corrective actions");
            
            // Return context with empty actions list
            var workflowContext = Context.Get(context);
            workflowContext.CorrectiveActions = new List<CorrectiveActionWorkflowModel>();
            workflowContext.Status = "Action Determination Failed";
            Result.Set(context, workflowContext);
        }
    }
    
    private async Task<List<CorrectiveActionWorkflowModel>> GenerateCorrectiveActions(IncidentWorkflowContext context)
    {
        var actions = new List<CorrectiveActionWorkflowModel>();
        
        // Generate actions based on investigation findings
        if (context.InvestigationData?.Findings?.Any() == true)
        {
            actions.AddRange(await GenerateActionsFromFindings(context));
        }
        
        // Generate actions based on root causes
        if (context.InvestigationData?.RootCauses?.Any() == true)
        {
            actions.AddRange(await GenerateActionsFromRootCauses(context));
        }
        
        // Generate standard actions based on incident type and severity
        actions.AddRange(await GenerateStandardActions(context));
        
        // Assign priorities and due dates
        AssignActionPriorities(actions, context.Severity);
        
        return actions.GroupBy(a => a.Description).Select(g => g.First()).ToList(); // Remove duplicates
    }
    
    private async Task<List<CorrectiveActionWorkflowModel>> GenerateActionsFromFindings(IncidentWorkflowContext context)
    {
        var actions = new List<CorrectiveActionWorkflowModel>();
        
        foreach (var finding in context.InvestigationData!.Findings)
        {
            var action = await CreateActionFromFinding(finding, context);
            if (action != null)
            {
                actions.Add(action);
            }
        }
        
        return actions;
    }
    
    private async Task<CorrectiveActionWorkflowModel?> CreateActionFromFinding(FindingWorkflowModel finding, IncidentWorkflowContext context)
    {
        // Generate action based on finding category
        var actionDescription = finding.Category.ToLowerInvariant() switch
        {
            "equipment" => $"Inspect and repair/replace equipment related to: {finding.Description}",
            "procedure" => $"Review and update procedures for: {finding.Description}",
            "training" => $"Provide additional training to address: {finding.Description}",
            "supervision" => $"Enhance supervision and monitoring for: {finding.Description}",
            "environmental" => $"Implement environmental controls for: {finding.Description}",
            _ => $"Address finding: {finding.Description}"
        };
        
        var assignedDepartment = DetermineDepartmentFromCategory(finding.Category);
        var assignedTo = await GetDepartmentResponsible(assignedDepartment);
        
        return new CorrectiveActionWorkflowModel
        {
            Id = Guid.NewGuid().ToString(),
            Description = actionDescription,
            AssignedTo = assignedTo,
            AssignedDepartment = assignedDepartment,
            Priority = MapSeverityToPriority(finding.Severity),
            Status = "Pending Assignment"
        };
    }
    
    private async Task<List<CorrectiveActionWorkflowModel>> GenerateActionsFromRootCauses(IncidentWorkflowContext context)
    {
        var actions = new List<CorrectiveActionWorkflowModel>();
        
        foreach (var rootCause in context.InvestigationData!.RootCauses)
        {
            var action = await CreateActionFromRootCause(rootCause, context);
            if (action != null)
            {
                actions.Add(action);
            }
        }
        
        return actions;
    }
    
    private async Task<CorrectiveActionWorkflowModel?> CreateActionFromRootCause(RootCauseWorkflowModel rootCause, IncidentWorkflowContext context)
    {
        var actionDescription = rootCause.Category.ToLowerInvariant() switch
        {
            "human error" => $"Implement error prevention measures for: {rootCause.Description}",
            "equipment failure" => $"Implement equipment reliability improvements for: {rootCause.Description}",
            "process gap" => $"Develop and implement process improvements for: {rootCause.Description}",
            "communication" => $"Improve communication processes regarding: {rootCause.Description}",
            "system failure" => $"Enhance system controls for: {rootCause.Description}",
            _ => $"Address root cause: {rootCause.Description}"
        };
        
        var assignedDepartment = DetermineDepartmentFromCategory(rootCause.Category);
        var assignedTo = await GetDepartmentResponsible(assignedDepartment);
        
        return new CorrectiveActionWorkflowModel
        {
            Id = Guid.NewGuid().ToString(),
            Description = actionDescription,
            AssignedTo = assignedTo,
            AssignedDepartment = assignedDepartment,
            Priority = "High", // Root cause actions are typically high priority
            Status = "Pending Assignment"
        };
    }
    
    private async Task<List<CorrectiveActionWorkflowModel>> GenerateStandardActions(IncidentWorkflowContext context)
    {
        var actions = new List<CorrectiveActionWorkflowModel>();
        
        // Standard action: Update risk assessment
        actions.Add(new CorrectiveActionWorkflowModel
        {
            Id = Guid.NewGuid().ToString(),
            Description = $"Review and update risk assessment for area: {context.Location}",
            AssignedTo = await GetDepartmentResponsible("HSE"),
            AssignedDepartment = "HSE",
            Priority = "Medium",
            Status = "Pending Assignment"
        });
        
        // For major incidents, add management review
        if (context.Severity == "Major" || context.Severity == "Fatality")
        {
            actions.Add(new CorrectiveActionWorkflowModel
            {
                Id = Guid.NewGuid().ToString(),
                Description = "Conduct management review of incident and implementation of controls",
                AssignedTo = await GetDepartmentResponsible("Management"),
                AssignedDepartment = "Management",
                Priority = "High",
                Status = "Pending Assignment"
            });
        }
        
        // Standard action: Communication to affected personnel
        actions.Add(new CorrectiveActionWorkflowModel
        {
            Id = Guid.NewGuid().ToString(),
            Description = "Communicate lessons learned to all affected personnel",
            AssignedTo = await GetDepartmentResponsible(GetDepartmentFromLocation(context.Location)),
            AssignedDepartment = GetDepartmentFromLocation(context.Location),
            Priority = "Medium",
            Status = "Pending Assignment"
        });
        
        return actions;
    }
    
    private void AssignActionPriorities(List<CorrectiveActionWorkflowModel> actions, string incidentSeverity)
    {
        var baseDueDays = incidentSeverity.ToLowerInvariant() switch
        {
            "fatality" => 7,   // 1 week for fatality
            "major" => 14,     // 2 weeks for major
            "minor" => 30,     // 1 month for minor
            _ => 21            // 3 weeks default
        };
        
        foreach (var action in actions)
        {
            var additionalDays = action.Priority.ToLowerInvariant() switch
            {
                "high" => 0,           // No additional time for high priority
                "medium" => baseDueDays / 2,  // 50% more time for medium
                "low" => baseDueDays,         // Double time for low priority
                _ => baseDueDays / 2
            };
            
            action.DueDate = DateTime.UtcNow.AddDays(baseDueDays + additionalDays);
        }
    }
    
    private string DetermineDepartmentFromCategory(string category)
    {
        return category.ToLowerInvariant() switch
        {
            "equipment" => "Maintenance",
            "procedure" => "HSE",
            "training" => "Training",
            "supervision" => "Operations",
            "environmental" => "HSE",
            "human error" => "Training",
            "equipment failure" => "Maintenance",
            "process gap" => "HSE",
            "communication" => "Management",
            "system failure" => "IT",
            _ => "HSE"
        };
    }
    
    private string GetDepartmentFromLocation(string location)
    {
        return location.ToLowerInvariant() switch
        {
            var l when l.Contains("warehouse") => "Operations",
            var l when l.Contains("office") => "Administration",
            var l when l.Contains("lab") => "Research",
            var l when l.Contains("workshop") => "Maintenance",
            _ => "General"
        };
    }
    
    private string MapSeverityToPriority(string severity)
    {
        return severity.ToLowerInvariant() switch
        {
            "critical" => "High",
            "high" => "High",
            "medium" => "Medium",
            "low" => "Low",
            _ => "Medium"
        };
    }
    
    private async Task<string> GetDepartmentResponsible(string department)
    {
        // In a real implementation, this would query the user service
        // For now, return a placeholder
        return $"responsible.{department.ToLower()}@company.com";
    }
}

/// <summary>
/// Interface for corrective action service operations
/// </summary>
public interface ICorrectiveActionService
{
    Task<List<CorrectiveActionWorkflowModel>> GenerateCorrectiveActions(IncidentWorkflowContext context);
    Task AssignCorrectiveAction(CorrectiveActionWorkflowModel action);
    Task<CorrectiveActionWorkflowModel?> GetCorrectiveActionStatus(string actionId);
}