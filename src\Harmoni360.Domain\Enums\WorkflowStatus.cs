namespace Harmoni360.Domain.Enums;

/// <summary>
/// Represents the execution status of a workflow
/// </summary>
public enum WorkflowStatus
{
    /// <summary>
    /// The workflow is idle and not currently executing
    /// </summary>
    Idle = 0,

    /// <summary>
    /// The workflow is currently running
    /// </summary>
    Running = 1,

    /// <summary>
    /// The workflow has completed successfully
    /// </summary>
    Finished = 2,

    /// <summary>
    /// The workflow was suspended (temporarily paused)
    /// </summary>
    Suspended = 3,

    /// <summary>
    /// The workflow encountered an error and faulted
    /// </summary>
    Faulted = 4,

    /// <summary>
    /// The workflow was cancelled
    /// </summary>
    Cancelled = 5
}

/// <summary>
/// Represents the sub-status providing more granular information about workflow execution
/// </summary>
public enum WorkflowSubStatus
{
    /// <summary>
    /// The workflow is actively executing activities
    /// </summary>
    Executing = 0,

    /// <summary>
    /// The workflow is waiting for external input or event
    /// </summary>
    Suspended = 1,

    /// <summary>
    /// The workflow completed successfully
    /// </summary>
    Finished = 2,

    /// <summary>
    /// The workflow was cancelled by user or system
    /// </summary>
    Cancelled = 3,

    /// <summary>
    /// The workflow encountered an unhandled exception
    /// </summary>
    Faulted = 4
}

/// <summary>
/// Represents the type of events that can be logged during workflow execution
/// </summary>
public enum WorkflowLogEventType
{
    /// <summary>
    /// Workflow execution started
    /// </summary>
    WorkflowStarted = 1,

    /// <summary>
    /// Workflow execution completed
    /// </summary>
    WorkflowCompleted = 2,

    /// <summary>
    /// Workflow execution was cancelled
    /// </summary>
    WorkflowCancelled = 3,

    /// <summary>
    /// Workflow execution faulted
    /// </summary>
    WorkflowFaulted = 4,

    /// <summary>
    /// Workflow execution was suspended
    /// </summary>
    WorkflowSuspended = 5,

    /// <summary>
    /// Workflow execution was resumed
    /// </summary>
    WorkflowResumed = 6,

    /// <summary>
    /// Activity started executing
    /// </summary>
    ActivityStarted = 10,

    /// <summary>
    /// Activity completed execution
    /// </summary>
    ActivityCompleted = 11,

    /// <summary>
    /// Activity was cancelled
    /// </summary>
    ActivityCancelled = 12,

    /// <summary>
    /// Activity encountered an error
    /// </summary>
    ActivityFaulted = 13,

    /// <summary>
    /// Activity was suspended
    /// </summary>
    ActivitySuspended = 14,

    /// <summary>
    /// Activity was resumed
    /// </summary>
    ActivityResumed = 15,

    /// <summary>
    /// Bookmark was created
    /// </summary>
    BookmarkCreated = 20,

    /// <summary>
    /// Bookmark was resumed
    /// </summary>
    BookmarkResumed = 21,

    /// <summary>
    /// Variable was set
    /// </summary>
    VariableSet = 30,

    /// <summary>
    /// Variable was retrieved
    /// </summary>
    VariableRetrieved = 31,

    /// <summary>
    /// Custom event logged
    /// </summary>
    CustomEvent = 100
}