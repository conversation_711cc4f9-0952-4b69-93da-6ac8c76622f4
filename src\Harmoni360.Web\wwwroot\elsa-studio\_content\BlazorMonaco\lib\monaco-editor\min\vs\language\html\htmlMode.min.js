define("vs/language/html/htmlMode",["require","require"],r=>{"use strict";var j,e,t,N=Object.create,o=Object.defineProperty,U=Object.getOwnPropertyDescriptor,V=Object.getOwnPropertyNames,W=Object.getPrototypeOf,H=Object.prototype.hasOwnProperty,O=(i=function(e){if(typeof r<"u")return r.apply(this,arguments);throw Error('Dynamic require of "'+e+'" is not supported')},typeof r<"u"?r:typeof Proxy<"u"?new Proxy(i,{get:(e,t)=>(typeof r<"u"?r:e)[t]}):i),n=(t,r,n,i)=>{if(r&&"object"==typeof r||"function"==typeof r)for(let e of V(r))H.call(t,e)||e===n||o(t,e,{get:()=>r[e],enumerable:!(i=U(r,e))||i.enumerable});return t},K=(e,t,r)=>(r=null!=e?N(W(e)):{},n(!t&&e&&e.__esModule?r:o(r,"default",{value:e,enumerable:!0}),e)),i=(j=(e,t)=>{var r=K(O("vs/editor/editor.api"));t.exports=r},()=>(e||j((e={exports:{}}).exports,e),e.exports)),X={},$=X,q={CompletionAdapter:()=>ye,DefinitionAdapter:()=>Se,DiagnosticsAdapter:()=>ke,DocumentColorAdapter:()=>Ne,DocumentFormattingEditProvider:()=>Le,DocumentHighlightAdapter:()=>Ce,DocumentLinkAdapter:()=>Ae,DocumentRangeFormattingEditProvider:()=>Fe,DocumentSymbolAdapter:()=>De,FoldingRangeAdapter:()=>Ue,HoverAdapter:()=>xe,ReferenceAdapter:()=>Pe,RenameAdapter:()=>Me,SelectionRangeAdapter:()=>Ve,WorkerManager:()=>_e,fromPosition:()=>T,fromRange:()=>Ie,setupMode:()=>function(r){let e=[],n=[],t=new _e(r),i=(e.push(t),(...e)=>t.getLanguageServiceWorker(...e));return function(){var{languageId:e,modeConfiguration:t}=r;Oe(n),t.completionItems&&n.push(M.languages.registerCompletionItemProvider(e,new We(i))),t.hovers&&n.push(M.languages.registerHoverProvider(e,new xe(i))),t.documentHighlights&&n.push(M.languages.registerDocumentHighlightProvider(e,new Ce(i))),t.links&&n.push(M.languages.registerLinkProvider(e,new Ae(i))),t.documentSymbols&&n.push(M.languages.registerDocumentSymbolProvider(e,new De(i))),t.rename&&n.push(M.languages.registerRenameProvider(e,new Me(i))),t.foldingRanges&&n.push(M.languages.registerFoldingRangeProvider(e,new Ue(i))),t.selectionRanges&&n.push(M.languages.registerSelectionRangeProvider(e,new Ve(i))),t.documentFormattingEdits&&n.push(M.languages.registerDocumentFormattingEditProvider(e,new Le(i))),t.documentRangeFormattingEdits&&n.push(M.languages.registerDocumentRangeFormattingEditProvider(e,new Fe(i)))}(),e.push(He(n)),He(e)},setupMode1:()=>function(e){let t=new _e(e),r=(...e)=>t.getLanguageServiceWorker(...e),n=e.languageId;M.languages.registerCompletionItemProvider(n,new We(r)),M.languages.registerHoverProvider(n,new xe(r)),M.languages.registerDocumentHighlightProvider(n,new Ce(r)),M.languages.registerLinkProvider(n,new Ae(r)),M.languages.registerFoldingRangeProvider(n,new Ue(r)),M.languages.registerDocumentSymbolProvider(n,new De(r)),M.languages.registerSelectionRangeProvider(n,new Ve(r)),M.languages.registerRenameProvider(n,new Me(r)),"html"===n&&(M.languages.registerDocumentFormattingEditProvider(n,new Le(r)),M.languages.registerDocumentRangeFormattingEditProvider(n,new Fe(r)))},toRange:()=>A,toTextEdit:()=>L};for(t in q)o($,t,{get:q[t],enumerable:!0});var a,s,u,c,l,d,g,m,f,B,p,z,G,J,Q,Y,Z,h,v,b,w,ee,_,te,re,ne,k,y,ie,oe,ae,I,x,se,E,ue,ce,le,C,de,ge,S,me,R,fe,pe,he,ve,be,we,P,M={},_e=(_=M,i=K(i()),n(_,i,"default"),x&&n(x,i,"default"),class{constructor(e){this._defaults=e,this._worker=null,this._client=null,this._idleCheckInterval=window.setInterval(()=>this._checkIfIdle(),3e4),this._lastUsedTime=0,this._configChangeListener=this._defaults.onDidChange(()=>this._stopWorker())}_stopWorker(){this._worker&&(this._worker.dispose(),this._worker=null),this._client=null}dispose(){clearInterval(this._idleCheckInterval),this._configChangeListener.dispose(),this._stopWorker()}_checkIfIdle(){this._worker&&12e4<Date.now()-this._lastUsedTime&&this._stopWorker()}_getClient(){return this._lastUsedTime=Date.now(),this._client||(this._worker=M.editor.createWebWorker({moduleId:"vs/language/html/htmlWorker",createData:{languageSettings:this._defaults.options,languageId:this._defaults.languageId},label:this._defaults.languageId}),this._client=this._worker.getProxy()),this._client}getLanguageServiceWorker(...t){let r;return this._getClient().then(e=>{r=e}).then(e=>{if(this._worker)return this._worker.withSyncedResources(t)}).then(e=>r)}});(F||{}).is=function(e){return"string"==typeof e},(a||(a={})).is=function(e){return"string"==typeof e},(u=s=s||{}).MIN_VALUE=0,u.MAX_VALUE=2147483647,u.is=function(e){return"number"==typeof e&&u.MIN_VALUE<=e&&e<=u.MAX_VALUE},(_=c=c||{}).create=function(e,t){return{line:e=e===Number.MAX_VALUE?s.MAX_VALUE:e,character:t=t===Number.MAX_VALUE?s.MAX_VALUE:t}},_.is=function(e){return P.objectLiteral(e)&&P.uinteger(e.line)&&P.uinteger(e.character)},(x=l=l||{}).create=function(e,t,r,n){if(P.uinteger(e)&&P.uinteger(t)&&P.uinteger(r)&&P.uinteger(n))return{start:c.create(e,t),end:c.create(r,n)};if(c.is(e)&&c.is(t))return{start:e,end:t};throw new Error(`Range#create called with invalid arguments[${e}, ${t}, ${r}, ${n}]`)},x.is=function(e){return P.objectLiteral(e)&&c.is(e.start)&&c.is(e.end)},(i=d=d||{}).create=function(e,t){return{uri:e,range:t}},i.is=function(e){return P.objectLiteral(e)&&l.is(e.range)&&(P.string(e.uri)||P.undefined(e.uri))},(F=R=R||{}).create=function(e,t,r,n){return{red:e,green:t,blue:r,alpha:n}},F.is=function(e){return P.objectLiteral(e)&&P.numberRange(e.red,0,1)&&P.numberRange(e.green,0,1)&&P.numberRange(e.blue,0,1)&&P.numberRange(e.alpha,0,1)},(_=g=g||{}).Comment="comment",_.Imports="imports",_.Region="region",(x=m=m||{}).create=function(e,t){return{location:e,message:t}},x.is=function(e){return P.defined(e)&&d.is(e.location)&&P.string(e.message)},(i=f=f||{}).Error=1,i.Warning=2,i.Information=3,i.Hint=4,(C||{}).is=function(e){return P.objectLiteral(e)&&P.string(e.href)},(R=S=S||{}).create=function(e,t,r,n,i,o){let a={range:e,message:t};return P.defined(r)&&(a.severity=r),P.defined(n)&&(a.code=n),P.defined(i)&&(a.source=i),P.defined(o)&&(a.relatedInformation=o),a},R.is=function(e){var t;return P.defined(e)&&l.is(e.range)&&P.string(e.message)&&(P.number(e.severity)||P.undefined(e.severity))&&(P.integer(e.code)||P.string(e.code)||P.undefined(e.code))&&(P.undefined(e.codeDescription)||P.string(null==(t=e.codeDescription)?void 0:t.href))&&(P.string(e.source)||P.undefined(e.source))&&(P.undefined(e.relatedInformation)||P.typedArray(e.relatedInformation,m.is))},(F=B=B||{}).create=function(e,t,...r){let n={title:e,command:t};return P.defined(r)&&0<r.length&&(n.arguments=r),n},F.is=function(e){return P.defined(e)&&P.string(e.title)&&P.string(e.command)},(_=w=w||{}).replace=function(e,t){return{range:e,newText:t}},_.insert=function(e,t){return{range:{start:e,end:e},newText:t}},_.del=function(e){return{range:e,newText:""}},_.is=function(e){return P.objectLiteral(e)&&P.string(e.newText)&&l.is(e.range)},(x=y=y||{}).create=function(e,t,r){let n={label:e};return void 0!==t&&(n.needsConfirmation=t),void 0!==r&&(n.description=r),n},x.is=function(e){return P.objectLiteral(e)&&P.string(e.label)&&(P.boolean(e.needsConfirmation)||void 0===e.needsConfirmation)&&(P.string(e.description)||void 0===e.description)},(p||(p={})).is=function(e){return P.string(e)},(i=z=z||{}).create=function(e,t){return{textDocument:e,edits:t}},i.is=function(e){return P.defined(e)&&Y.is(e.textDocument)&&Array.isArray(e.edits)},(C=G=G||{}).create=function(e,t,r){let n={kind:"create",uri:e};return void 0===t||void 0===t.overwrite&&void 0===t.ignoreIfExists||(n.options=t),void 0!==r&&(n.annotationId=r),n},C.is=function(e){return e&&"create"===e.kind&&P.string(e.uri)&&(void 0===e.options||(void 0===e.options.overwrite||P.boolean(e.options.overwrite))&&(void 0===e.options.ignoreIfExists||P.boolean(e.options.ignoreIfExists)))&&(void 0===e.annotationId||p.is(e.annotationId))},(S=J=J||{}).create=function(e,t,r,n){let i={kind:"rename",oldUri:e,newUri:t};return void 0===r||void 0===r.overwrite&&void 0===r.ignoreIfExists||(i.options=r),void 0!==n&&(i.annotationId=n),i},S.is=function(e){return e&&"rename"===e.kind&&P.string(e.oldUri)&&P.string(e.newUri)&&(void 0===e.options||(void 0===e.options.overwrite||P.boolean(e.options.overwrite))&&(void 0===e.options.ignoreIfExists||P.boolean(e.options.ignoreIfExists)))&&(void 0===e.annotationId||p.is(e.annotationId))},(R=Q=Q||{}).create=function(e,t,r){let n={kind:"delete",uri:e};return void 0===t||void 0===t.recursive&&void 0===t.ignoreIfNotExists||(n.options=t),void 0!==r&&(n.annotationId=r),n},R.is=function(e){return e&&"delete"===e.kind&&P.string(e.uri)&&(void 0===e.options||(void 0===e.options.recursive||P.boolean(e.options.recursive))&&(void 0===e.options.ignoreIfNotExists||P.boolean(e.options.ignoreIfNotExists)))&&(void 0===e.annotationId||p.is(e.annotationId))},(D||(D={})).is=function(e){let t=e;return t&&(void 0!==t.changes||void 0!==t.documentChanges)&&(void 0===t.documentChanges||t.documentChanges.every(e=>P.string(e.kind)?G.is(e)||J.is(e)||Q.is(e):z.is(e)))},(F=Y=Y||{}).create=function(e,t){return{uri:e,version:t}},F.is=function(e){return P.defined(e)&&P.string(e.uri)&&(null===e.version||P.integer(e.version))},(h=Z=Z||{}).PlainText="plaintext",h.Markdown="markdown",h.is=function(e){return e===h.PlainText||e===h.Markdown},(v||(v={})).is=function(e){var t=e;return P.objectLiteral(e)&&Z.is(t.kind)&&P.string(t.value)},(w=b=b||{}).Text=1,w.Method=2,w.Function=3,w.Constructor=4,w.Field=5,w.Variable=6,w.Class=7,w.Interface=8,w.Module=9,w.Property=10,w.Unit=11,w.Value=12,w.Enum=13,w.Keyword=14,w.Snippet=15,w.Color=16,w.File=17,w.Reference=18,w.Folder=19,w.EnumMember=20,w.Constant=21,w.Struct=22,w.Event=23,w.Operator=24,w.TypeParameter=25,(_=ee=ee||{}).PlainText=1,_.Snippet=2,(te||{}).is=function(e){return e&&(P.string(e.detail)||void 0===e.detail)&&(P.string(e.description)||void 0===e.description)},(re||{}).create=function(e){return{label:e}},(ne||{}).create=function(e,t){return{items:e||[],isIncomplete:!!t}},(y=k=k||{}).fromPlainText=function(e){return e.replace(/[\\`*_{}[\]()#+\-.!]/g,"\\$&")},y.is=function(e){return P.string(e)||P.objectLiteral(e)&&P.string(e.language)&&P.string(e.value)},(ie||{}).is=function(e){var t=e;return!!t&&P.objectLiteral(t)&&(v.is(t.contents)||k.is(t.contents)||P.typedArray(t.contents,k.is))&&(void 0===e.range||l.is(e.range))},(oe||{}).create=function(e,t){return t?{label:e,documentation:t}:{label:e}},(ae||{}).create=function(e,t,...r){let n={label:e};return P.defined(t)&&(n.documentation=t),P.defined(r)?n.parameters=r:n.parameters=[],n},(x=I=I||{}).Text=1,x.Read=2,x.Write=3,(se||{}).create=function(e,t){let r={range:e};return P.number(t)&&(r.kind=t),r},(i=E=E||{}).File=1,i.Module=2,i.Namespace=3,i.Package=4,i.Class=5,i.Method=6,i.Property=7,i.Field=8,i.Constructor=9,i.Enum=10,i.Interface=11,i.Function=12,i.Variable=13,i.Constant=14,i.String=15,i.Number=16,i.Boolean=17,i.Array=18,i.Object=19,i.Key=20,i.Null=21,i.EnumMember=22,i.Struct=23,i.Event=24,i.Operator=25,i.TypeParameter=26,(ue||{}).create=function(e,t,r,n,i){let o={name:e,kind:t,location:{uri:n,range:r}};return i&&(o.containerName=i),o},(ce||{}).create=function(e,t,r,n){return void 0!==n?{name:e,kind:t,location:{uri:r,range:n}}:{name:e,kind:t,location:{uri:r}}},(C=le=le||{}).Invoked=1,C.Automatic=2,(de||{}).is=function(e){return P.objectLiteral(e)&&(void 0===e.resultId||"string"==typeof e.resultId)&&Array.isArray(e.data)&&(0===e.data.length||"number"==typeof e.data[0])},(S=ge=ge||{}).Type=1,S.Parameter=2,S.is=function(e){return 1===e||2===e},(R=me=me||{}).create=function(e){return{value:e}},R.is=function(e){return P.objectLiteral(e)&&(void 0===e.tooltip||P.string(e.tooltip)||v.is(e.tooltip))&&(void 0===e.location||d.is(e.location))&&(void 0===e.command||B.is(e.command))},(fe||{}).createSnippet=function(e){return{kind:"snippet",value:e}},(pe||{}).create=function(e,t,r,n){return{insertText:e,filterText:t,range:r,command:n}},(he||{}).create=function(e){return{items:e}},(ve||{}).create=function(e,t){return{range:e,text:t}},(be||{}).create=function(e,t){return{triggerKind:e,selectedCompletionInfo:t}},(we||{}).is=function(e){return P.objectLiteral(e)&&a.is(e.uri)&&P.string(e.name)};{var D=P=P||{};let n=Object.prototype.toString;D.defined=function(e){return typeof e<"u"},D.undefined=function(e){return"u"<typeof e},D.boolean=function(e){return!0===e||!1===e},D.string=function(e){return"[object String]"===n.call(e)},D.number=function(e){return"[object Number]"===n.call(e)},D.numberRange=function(e,t,r){return"[object Number]"===n.call(e)&&t<=e&&e<=r},D.integer=function(e){return"[object Number]"===n.call(e)&&-2147483648<=e&&e<=2147483647},D.uinteger=function(e){return"[object Number]"===n.call(e)&&0<=e&&e<=2147483647},D.func=function(e){return"[object Function]"===n.call(e)},D.objectLiteral=function(e){return null!==e&&"object"==typeof e},D.typedArray=function(e,t){return Array.isArray(e)&&e.every(t)}}var ke=class{constructor(e,t,r){this._languageId=e,this._worker=t,this._disposables=[],this._listener=Object.create(null);let n=t=>{let r=t.getLanguageId();if(r===this._languageId){let e;this._listener[t.uri.toString()]=t.onDidChangeContent(()=>{window.clearTimeout(e),e=window.setTimeout(()=>this._doValidate(t.uri,r),500)}),this._doValidate(t.uri,r)}},i=e=>{M.editor.setModelMarkers(e,this._languageId,[]);let t=e.uri.toString(),r=this._listener[t];r&&(r.dispose(),delete this._listener[t])};this._disposables.push(M.editor.onDidCreateModel(n)),this._disposables.push(M.editor.onWillDisposeModel(i)),this._disposables.push(M.editor.onDidChangeModelLanguage(e=>{i(e.model),n(e.model)})),this._disposables.push(r(e=>{M.editor.getModels().forEach(e=>{e.getLanguageId()===this._languageId&&(i(e),n(e))})})),this._disposables.push({dispose:()=>{for(var e in M.editor.getModels().forEach(i),this._listener)this._listener[e].dispose()}}),M.editor.getModels().forEach(n)}dispose(){this._disposables.forEach(e=>e&&e.dispose()),this._disposables.length=0}_doValidate(n,i){this._worker(n).then(e=>e.doValidation(n.toString())).then(e=>{let t=e.map(e=>{var t="number"==typeof e.code?String(e.code):e.code;return{severity:function(e){switch(e){case f.Error:return M.MarkerSeverity.Error;case f.Warning:return M.MarkerSeverity.Warning;case f.Information:return M.MarkerSeverity.Info;case f.Hint:return M.MarkerSeverity.Hint;default:return M.MarkerSeverity.Info}}(e.severity),startLineNumber:e.range.start.line+1,startColumn:e.range.start.character+1,endLineNumber:e.range.end.line+1,endColumn:e.range.end.character+1,message:e.message,code:t,source:e.source}}),r=M.editor.getModel(n);r&&r.getLanguageId()===i&&M.editor.setModelMarkers(r,i,t)}).then(void 0,e=>{console.error(e)})}},ye=class{constructor(e,t){this._worker=e,this._triggerCharacters=t}get triggerCharacters(){return this._triggerCharacters}provideCompletionItems(i,o,e,t){let r=i.uri;return this._worker(r).then(e=>e.doComplete(r.toString(),T(o))).then(r=>{if(r){let e=i.getWordUntilPosition(o),n=new M.Range(o.lineNumber,e.startColumn,o.lineNumber,e.endColumn),t=r.items.map(e=>{let t={label:e.label,insertText:e.insertText||e.label,sortText:e.sortText,filterText:e.filterText,documentation:e.documentation,detail:e.detail,command:(r=e.command)&&"editor.action.triggerSuggest"===r.command?{id:r.command,title:r.title,arguments:r.arguments}:void 0,range:n,kind:function(e){var t=M.languages.CompletionItemKind;switch(e){case b.Text:return t.Text;case b.Method:return t.Method;case b.Function:return t.Function;case b.Constructor:return t.Constructor;case b.Field:return t.Field;case b.Variable:return t.Variable;case b.Class:return t.Class;case b.Interface:return t.Interface;case b.Module:return t.Module;case b.Property:return t.Property;case b.Unit:return t.Unit;case b.Value:return t.Value;case b.Enum:return t.Enum;case b.Keyword:return t.Keyword;case b.Snippet:return t.Snippet;case b.Color:return t.Color;case b.File:return t.File;case b.Reference:return t.Reference}return t.Property}(e.kind)};var r;return e.textEdit&&(typeof(r=e.textEdit).insert<"u"&&typeof r.replace<"u"?t.range={insert:A(e.textEdit.insert),replace:A(e.textEdit.replace)}:t.range=A(e.textEdit.range),t.insertText=e.textEdit.newText),e.additionalTextEdits&&(t.additionalTextEdits=e.additionalTextEdits.map(L)),e.insertTextFormat===ee.Snippet&&(t.insertTextRules=M.languages.CompletionItemInsertTextRule.InsertAsSnippet),t});return{isIncomplete:r.isIncomplete,suggestions:t}}})}};function T(e){if(e)return{character:e.column-1,line:e.lineNumber-1}}function Ie(e){if(e)return{start:{line:e.startLineNumber-1,character:e.startColumn-1},end:{line:e.endLineNumber-1,character:e.endColumn-1}}}function A(e){if(e)return new M.Range(e.start.line+1,e.start.character+1,e.end.line+1,e.end.character+1)}function L(e){if(e)return{range:A(e.range),text:e.newText}}var xe=class{constructor(e){this._worker=e}provideHover(e,t,r){let n=e.uri;return this._worker(n).then(e=>e.doHover(n.toString(),T(t))).then(e=>{if(e)return{range:A(e.range),contents:function(e){if(e)return Array.isArray(e)?e.map(Ee):[Ee(e)]}(e.contents)}})}};function Ee(e){return"string"==typeof e?{value:e}:(t=e)&&"object"==typeof t&&"string"==typeof t.kind?"plaintext"===e.kind?{value:e.value.replace(/[\\`*_{}[\]()#+\-.!]/g,"\\$&")}:{value:e.value}:{value:"```"+e.language+`
`+e.value+"\n```\n"};var t}var Ce=class{constructor(e){this._worker=e}provideDocumentHighlights(e,t,r){let n=e.uri;return this._worker(n).then(e=>e.findDocumentHighlights(n.toString(),T(t))).then(e=>{if(e)return e.map(e=>({range:A(e.range),kind:function(e){switch(e){case I.Read:return M.languages.DocumentHighlightKind.Read;case I.Write:return M.languages.DocumentHighlightKind.Write;case I.Text:return M.languages.DocumentHighlightKind.Text}return M.languages.DocumentHighlightKind.Text}(e.kind)}))})}},Se=class{constructor(e){this._worker=e}provideDefinition(e,t,r){let n=e.uri;return this._worker(n).then(e=>e.findDefinition(n.toString(),T(t))).then(e=>{if(e)return[Re(e)]})}};function Re(e){return{uri:M.Uri.parse(e.uri),range:A(e.range)}}var Pe=class{constructor(e){this._worker=e}provideReferences(e,t,r,n){let i=e.uri;return this._worker(i).then(e=>e.findReferences(i.toString(),T(t))).then(e=>{if(e)return e.map(Re)})}},Me=class{constructor(e){this._worker=e}provideRenameEdits(e,t,r,n){let i=e.uri;return this._worker(i).then(e=>e.doRename(i.toString(),T(t),r)).then(e=>{var t=e;if(t&&t.changes){let e=[];for(var r in t.changes){var n,i=M.Uri.parse(r);for(n of t.changes[r])e.push({resource:i,versionId:void 0,textEdit:{range:A(n.range),text:n.newText}})}return{edits:e}}})}},De=class{constructor(e){this._worker=e}provideDocumentSymbols(e,t){let r=e.uri;return this._worker(r).then(e=>e.findDocumentSymbols(r.toString())).then(e=>{if(e)return e.map(e=>"children"in e?function t(e){return{name:e.name,detail:e.detail??"",kind:Te(e.kind),range:A(e.range),selectionRange:A(e.selectionRange),tags:e.tags??[],children:(e.children??[]).map(e=>t(e))}}(e):{name:e.name,detail:"",containerName:e.containerName,kind:Te(e.kind),range:A(e.location.range),selectionRange:A(e.location.range),tags:[]})})}};function Te(e){var t=M.languages.SymbolKind;switch(e){case E.File:return t.File;case E.Module:return t.Module;case E.Namespace:return t.Namespace;case E.Package:return t.Package;case E.Class:return t.Class;case E.Method:return t.Method;case E.Property:return t.Property;case E.Field:return t.Field;case E.Constructor:return t.Constructor;case E.Enum:return t.Enum;case E.Interface:return t.Interface;case E.Function:return t.Function;case E.Variable:return t.Variable;case E.Constant:return t.Constant;case E.String:return t.String;case E.Number:return t.Number;case E.Boolean:return t.Boolean;case E.Array:return t.Array}return t.Function}var Ae=class{constructor(e){this._worker=e}provideLinks(e,t){let r=e.uri;return this._worker(r).then(e=>e.findDocumentLinks(r.toString())).then(e=>{if(e)return{links:e.map(e=>({range:A(e.range),url:e.target}))}})}},Le=class{constructor(e){this._worker=e}provideDocumentFormattingEdits(e,t,r){let n=e.uri;return this._worker(n).then(e=>e.format(n.toString(),null,je(t)).then(e=>{if(e&&0!==e.length)return e.map(L)}))}},Fe=class{constructor(e){this._worker=e,this.canFormatMultipleRanges=!1}provideDocumentRangeFormattingEdits(e,t,r,n){let i=e.uri;return this._worker(i).then(e=>e.format(i.toString(),Ie(t),je(r)).then(e=>{if(e&&0!==e.length)return e.map(L)}))}};function je(e){return{tabSize:e.tabSize,insertSpaces:e.insertSpaces}}var F,Ne=class{constructor(e){this._worker=e}provideDocumentColors(e,t){let r=e.uri;return this._worker(r).then(e=>e.findDocumentColors(r.toString())).then(e=>{if(e)return e.map(e=>({color:e.color,range:A(e.range)}))})}provideColorPresentations(e,t,r){let n=e.uri;return this._worker(n).then(e=>e.getColorPresentations(n.toString(),t.color,Ie(t.range))).then(e=>{if(e)return e.map(e=>{let t={label:e.label};return e.textEdit&&(t.textEdit=L(e.textEdit)),e.additionalTextEdits&&(t.additionalTextEdits=e.additionalTextEdits.map(L)),t})})}},Ue=class{constructor(e){this._worker=e}provideFoldingRanges(e,t,r){let n=e.uri;return this._worker(n).then(e=>e.getFoldingRanges(n.toString(),t)).then(e=>{if(e)return e.map(e=>{let t={start:e.startLine+1,end:e.endLine+1};return typeof e.kind<"u"&&(t.kind=function(e){switch(e){case g.Comment:return M.languages.FoldingRangeKind.Comment;case g.Imports:return M.languages.FoldingRangeKind.Imports;case g.Region:return M.languages.FoldingRangeKind.Region}}(e.kind)),t})})}},Ve=class{constructor(e){this._worker=e}provideSelectionRanges(e,t,r){let n=e.uri;return this._worker(n).then(e=>e.getSelectionRanges(n.toString(),t.map(T))).then(e=>{if(e)return e.map(e=>{let t=[];for(;e;)t.push({range:A(e.range)}),e=e.parent;return t})})}},We=class extends ye{constructor(e){super(e,[".",":","<",'"',"=","/"])}};function He(e){return{dispose:()=>Oe(e)}}function Oe(e){for(;e.length;)e.pop().dispose()}return F=X,n(o({},"__esModule",{value:!0}),F)});