using Harmoni360.Domain.Entities;
using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;

namespace Harmoni360.Infrastructure.Persistence.Configurations;

public class WorkflowExecutionContextConfiguration : IEntityTypeConfiguration<WorkflowExecutionContext>
{
    public void Configure(EntityTypeBuilder<WorkflowExecutionContext> builder)
    {
        builder.ToTable("WorkflowExecutionContexts");

        builder.HasKey(e => e.Id);

        builder.Property(e => e.Id)
            .ValueGeneratedOnAdd();

        builder.Property(e => e.WorkflowExecutionId)
            .IsRequired();

        builder.Property(e => e.ActivityId)
            .HasMaxLength(100)
            .IsRequired();

        builder.Property(e => e.ActivityName)
            .HasMaxLength(500)
            .IsRequired();

        builder.Property(e => e.ActivityType)
            .HasMaxLength(100)
            .IsRequired();

        builder.Property(e => e.PropertyName)
            .HasMaxLength(500)
            .IsRequired();

        builder.Property(e => e.PropertyValue)
            .HasColumnType("text")
            .IsRequired(false);

        builder.Property(e => e.PropertyType)
            .HasMaxLength(200)
            .IsRequired(false);

        builder.Property(e => e.UpdatedAt)
            .IsRequired();

        builder.Property(e => e.UpdatedBy)
            .HasMaxLength(100)
            .IsRequired(false);

        // Indexes
        builder.HasIndex(e => e.WorkflowExecutionId)
            .HasDatabaseName("IX_WorkflowExecutionContexts_WorkflowExecutionId");

        builder.HasIndex(e => e.ActivityId)
            .HasDatabaseName("IX_WorkflowExecutionContexts_ActivityId");

        builder.HasIndex(e => e.PropertyName)
            .HasDatabaseName("IX_WorkflowExecutionContexts_PropertyName");

        builder.HasIndex(e => e.UpdatedAt)
            .HasDatabaseName("IX_WorkflowExecutionContexts_UpdatedAt");

        builder.HasIndex(e => new { e.WorkflowExecutionId, e.ActivityId })
            .HasDatabaseName("IX_WorkflowExecutionContexts_WorkflowExecutionId_ActivityId");

        builder.HasIndex(e => new { e.ActivityId, e.PropertyName })
            .HasDatabaseName("IX_WorkflowExecutionContexts_ActivityId_PropertyName");

        // Unique constraint for workflow execution, activity, and property combination
        builder.HasIndex(e => new { e.WorkflowExecutionId, e.ActivityId, e.PropertyName })
            .IsUnique()
            .HasDatabaseName("IX_WorkflowExecutionContexts_WorkflowExecutionId_ActivityId_PropertyName");

        // Relationships
        builder.HasOne(e => e.WorkflowExecution)
            .WithMany(w => w.ExecutionContexts)
            .HasForeignKey(e => e.WorkflowExecutionId)
            .OnDelete(DeleteBehavior.Cascade);
    }
}