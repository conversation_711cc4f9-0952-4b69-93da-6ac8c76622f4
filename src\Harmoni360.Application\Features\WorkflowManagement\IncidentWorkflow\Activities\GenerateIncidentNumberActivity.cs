using Elsa.Workflows;
using Elsa.Workflows.Attributes;
using Harmoni360.Application.Common.Interfaces;
using Harmoni360.Application.Features.WorkflowManagement.Common;
using Harmoni360.Application.Features.WorkflowManagement.IncidentWorkflow.Models;
using Harmoni360.Domain.Interfaces;
using Microsoft.Extensions.Logging;
using Microsoft.EntityFrameworkCore;
using System.ComponentModel;
using Elsa.Extensions;
using Elsa.Workflows.Models;

namespace Harmoni360.Application.Features.WorkflowManagement.IncidentWorkflow.Activities;

/// <summary>
/// Activity that generates a unique incident number based on the incident type
/// </summary>
[Activity("Incident Management", "Generate Incident Number", "Generates a unique incident number following the standard format")]
public class GenerateIncidentNumberActivity : IncidentActivityBase<string>
{
    private readonly IIncidentSequenceService _sequenceService;
    
    public GenerateIncidentNumberActivity(
        ILogger<GenerateIncidentNumberActivity> logger,
        IIncidentRepository incidentRepository,
        ICurrentUserService currentUserService,
        IIncidentSequenceService sequenceService)
        : base(logger, incidentRepository, currentUserService)
    {
        _sequenceService = sequenceService;
    }
    
    /// <summary>
    /// The workflow context containing incident information
    /// </summary>
    [Input(
        Description = "The incident workflow context",
        DisplayName = "Incident Context"
    )]
    public Input<IncidentWorkflowContext> Context { get; set; } = default!;
    
    protected override async ValueTask ExecuteAsync(ActivityExecutionContext context)
    {
        const string activityName = nameof(GenerateIncidentNumberActivity);
        LogActivity(activityName, "Starting incident number generation");
        
        try
        {
            var workflowContext = Context.Get(context);
            var currentDate = DateTime.Now;
            
            // Get next sequence number for the incident type
            var sequenceNumber = await _sequenceService.GetNextSequenceNumber(
                workflowContext.IncidentType, 
                currentDate);
            
            // Generate incident number based on category code
            // Format: Number/HSE-CategoryCode/MM/YYYY
            var categoryCode = workflowContext.CategoryCode;
            if (string.IsNullOrEmpty(categoryCode))
            {
                categoryCode = "UNKNOWN";
            }
            
            var incidentNumber = $"{sequenceNumber:000}/HSE-{categoryCode}/{currentDate:MM}/{currentDate:yyyy}";
            
            // Update the incident in database with the generated number
            if (int.TryParse(workflowContext.IncidentId, out var incidentId))
            {
                var incident = await IncidentRepository.GetByIdAsync(incidentId);
                if (incident != null)
                {
                    incident.SetIncidentNumber(incidentNumber);
                    await IncidentRepository.UpdateAsync(incident);
                }
            }
            
            LogActivity(activityName, "Generated incident number: {IncidentNumber} for type: {IncidentType}", 
                incidentNumber, workflowContext.IncidentType);
            
            // Set the output for CodeActivity<T>
            Result = incidentNumber;
        }
        catch (Exception ex)
        {
            LogActivityError(activityName, ex, "Failed to generate incident number");
            
            // Return a fallback incident number
            var fallbackNumber = $"ERR{DateTime.Now:yyyyMMddHHmmss}";
            Result = fallbackNumber;
        }
    }
}

/// <summary>
/// Service interface for managing incident sequence numbers
/// </summary>
public interface IIncidentSequenceService
{
    Task<int> GetNextSequenceNumber(string incidentType, DateTime date);
}

/// <summary>
/// Implementation of incident sequence service
/// </summary>
public class IncidentSequenceService : IIncidentSequenceService
{
    private readonly IApplicationDbContext _dbContext;
    private readonly ILogger<IncidentSequenceService> _logger;
    
    public IncidentSequenceService(
        IApplicationDbContext dbContext,
        ILogger<IncidentSequenceService> logger)
    {
        _dbContext = dbContext;
        _logger = logger;
    }
    
    public async Task<int> GetNextSequenceNumber(string incidentType, DateTime date)
    {
        try
        {
            // Get the count of incidents of this type for the current month/year
            var startOfMonth = new DateTime(date.Year, date.Month, 1);
            var endOfMonth = startOfMonth.AddMonths(1).AddDays(-1);
            
            var count = await _dbContext.Incidents
                .Where(i => i.CreatedAt >= startOfMonth && 
                           i.CreatedAt <= endOfMonth &&
                           i.Type.ToString() == incidentType)
                .CountAsync();
            
            return count + 1;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error generating sequence number for incident type: {IncidentType}", incidentType);
            
            // Fallback to timestamp-based sequence
            return (int)(DateTime.Now.Ticks % 1000);
        }
    }
}