import React from 'react';
import { <PERSON><PERSON>, <PERSON>ard<PERSON>ody, CCardHeader, <PERSON><PERSON>, <PERSON>ontainer, <PERSON>ow } from '@coreui/react';
import ElsaStudioWrapper from '../../components/workflows/ElsaStudioWrapper';

const WorkflowsDashboard: React.FC = () => {
  return (
    <CContainer fluid>
      <CRow>
        <CCol>
          <CCard className="mb-4">
            <CCardHeader>
              <h4 className="mb-0">Workflow Management</h4>
              <small className="text-muted">Design and manage business workflows</small>
            </CCardHeader>
            <CCardBody style={{ padding: 0 }}>
              <ElsaStudioWrapper 
                style={{ 
                  height: 'calc(100vh - 200px)',
                  minHeight: '800px'
                }}
              />
            </CCardBody>
          </CCard>
        </CCol>
      </CRow>
    </CContainer>
  );
};

export default WorkflowsDashboard;