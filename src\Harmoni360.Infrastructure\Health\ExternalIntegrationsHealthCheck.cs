using Harmoni360.Application.Common.Interfaces;
using Microsoft.Extensions.Diagnostics.HealthChecks;
using Microsoft.Extensions.Logging;
using Microsoft.Extensions.Options;

namespace Harmoni360.Infrastructure.Health;

public class ExternalIntegrationsHealthCheck : IHealthCheck
{
    private readonly ITicketingService _ticketingService;
    private readonly INotificationService _notificationService;
    private readonly TicketingSettings _ticketingSettings;
    private readonly SmtpSettings _smtpSettings;
    private readonly ILogger<ExternalIntegrationsHealthCheck> _logger;

    public ExternalIntegrationsHealthCheck(
        ITicketingService ticketingService,
        INotificationService notificationService,
        IOptions<TicketingSettings> ticketingSettings,
        IOptions<SmtpSettings> smtpSettings,
        ILogger<ExternalIntegrationsHealthCheck> logger)
    {
        _ticketingService = ticketingService;
        _notificationService = notificationService;
        _ticketingSettings = ticketingSettings.Value;
        _smtpSettings = smtpSettings.Value;
        _logger = logger;
    }

    public async Task<HealthCheckResult> CheckHealthAsync(
        HealthCheckContext context,
        CancellationToken cancellationToken = default)
    {
        var healthData = new Dictionary<string, object>();
        var issues = new List<string>();
        var warnings = new List<string>();

        try
        {
            // Check ticketing service health
            var ticketingHealth = await CheckTicketingServiceHealth(healthData, cancellationToken);
            if (ticketingHealth == ServiceHealthStatus.Unhealthy)
            {
                issues.Add("Ticketing service is unavailable");
            }
            else if (ticketingHealth == ServiceHealthStatus.Degraded)
            {
                warnings.Add("Ticketing service is degraded");
            }

            // Check notification service health
            var notificationHealth = await CheckNotificationServiceHealth(healthData, cancellationToken);
            if (notificationHealth == ServiceHealthStatus.Unhealthy)
            {
                issues.Add("Notification service is unavailable");
            }
            else if (notificationHealth == ServiceHealthStatus.Degraded)
            {
                warnings.Add("Notification service is degraded");
            }

            // Add configuration information
            AddConfigurationInfo(healthData);

            // Determine overall health status
            if (issues.Any())
            {
                var description = $"External integrations have issues: {string.Join(", ", issues)}";
                if (warnings.Any())
                {
                    description += $". Warnings: {string.Join(", ", warnings)}";
                }
                return HealthCheckResult.Unhealthy(description, data: healthData);
            }

            if (warnings.Any())
            {
                var description = $"External integrations are degraded: {string.Join(", ", warnings)}";
                return HealthCheckResult.Degraded(description, data: healthData);
            }

            return HealthCheckResult.Healthy(
                "All external integrations are healthy", 
                data: healthData);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "External integrations health check failed");
            return HealthCheckResult.Unhealthy(
                "External integrations health check failed",
                ex,
                healthData);
        }
    }

    private async Task<ServiceHealthStatus> CheckTicketingServiceHealth(
        Dictionary<string, object> healthData,
        CancellationToken cancellationToken)
    {
        var startTime = DateTime.UtcNow;
        
        try
        {
            // Create a minimal health check request
            // Create a mock health check request for testing external integrations
            var healthCheckRequest = new CreateTicketRequest
            {
                Title = $"[HEALTH CHECK] {DateTime.UtcNow:yyyy-MM-dd HH:mm:ss}",
                Description = "Automated health check - please ignore",
                Priority = TicketPriority.Low,
                Type = TicketType.Task,
                ReporterId = "system",
                ReporterEmail = "<EMAIL>"
            };

            using var timeoutCts = CancellationTokenSource.CreateLinkedTokenSource(cancellationToken);
            timeoutCts.CancelAfter(TimeSpan.FromSeconds(15));

            var ticketId = await _ticketingService.CreateTicketAsync(healthCheckRequest, timeoutCts.Token);
            var responseTime = (DateTime.UtcNow - startTime).TotalMilliseconds;

            healthData["ticketing_response_time_ms"] = responseTime;
            healthData["ticketing_provider"] = _ticketingSettings?.Provider ?? "Unknown";
            healthData["ticketing_graceful_degradation_enabled"] = _ticketingSettings?.EnableGracefulDegradation ?? false;

            if (!string.IsNullOrEmpty(ticketId))
            {
                healthData["ticketing_status"] = "healthy";
                healthData["test_ticket_id"] = ticketId;
                
                // Try to clean up the test ticket
                _ = Task.Run(async () =>
                {
                    try
                    {
                        await Task.Delay(5000); // Wait 5 seconds
                        // In a real implementation, you might want to delete the test ticket
                        // or add a comment indicating it's a health check
                        _logger.LogDebug("Health check created test ticket: {TicketId}", ticketId);
                    }
                    catch (Exception ex)
                    {
                        _logger.LogWarning(ex, "Could not clean up health check ticket: {TicketId}", ticketId);
                    }
                });

                return responseTime > 10000 ? ServiceHealthStatus.Degraded : ServiceHealthStatus.Healthy;
            }
            else
            {
                healthData["ticketing_status"] = "degraded";
                healthData["ticketing_error"] = "No ticket ID returned";
                
                _logger.LogWarning("Ticketing service health check failed: No ticket ID returned");
                
                // If graceful degradation is enabled, this might still be acceptable
                return _ticketingSettings.EnableGracefulDegradation 
                    ? ServiceHealthStatus.Degraded 
                    : ServiceHealthStatus.Unhealthy;
            }
        }
        catch (OperationCanceledException)
        {
            healthData["ticketing_status"] = "timeout";
            healthData["ticketing_error"] = "Health check timed out";
            
            _logger.LogWarning("Ticketing service health check timed out");
            return ServiceHealthStatus.Unhealthy;
        }
        catch (Exception ex)
        {
            healthData["ticketing_status"] = "error";
            healthData["ticketing_error"] = ex.Message;
            
            _logger.LogError(ex, "Ticketing service health check failed");
            return ServiceHealthStatus.Unhealthy;
        }
    }

    private async Task<ServiceHealthStatus> CheckNotificationServiceHealth(
        Dictionary<string, object> healthData,
        CancellationToken cancellationToken)
    {
        var startTime = DateTime.UtcNow;

        try
        {
            using var timeoutCts = CancellationTokenSource.CreateLinkedTokenSource(cancellationToken);
            timeoutCts.CancelAfter(TimeSpan.FromSeconds(10));

            // For SMTP, we'll do a lightweight check - just verify we can get status
            var status = await _notificationService.GetNotificationStatusAsync(cancellationToken);

            var responseTime = (DateTime.UtcNow - startTime).TotalMilliseconds;

            healthData["notification_response_time_ms"] = responseTime;
            healthData["notification_smtp_host"] = _smtpSettings.Host;
            healthData["notification_smtp_port"] = _smtpSettings.Port;

            if (status != null)
            {
                healthData["notification_status"] = "healthy";
                return responseTime > 5000 ? ServiceHealthStatus.Degraded : ServiceHealthStatus.Healthy;
            }
            else
            {
                healthData["notification_status"] = "degraded";
                healthData["notification_warning"] = "Service responded but returned null status";
                return ServiceHealthStatus.Degraded;
            }
        }
        catch (OperationCanceledException)
        {
            healthData["notification_status"] = "timeout";
            healthData["notification_error"] = "Health check timed out";
            
            _logger.LogWarning("Notification service health check timed out");
            return ServiceHealthStatus.Unhealthy;
        }
        catch (Exception ex)
        {
            healthData["notification_status"] = "error";
            healthData["notification_error"] = ex.Message;
            
            _logger.LogError(ex, "Notification service health check failed");
            
            // For notification service, we might be more lenient as workflows can continue without it
            return ServiceHealthStatus.Degraded;
        }
    }

    private void AddConfigurationInfo(Dictionary<string, object> healthData)
    {
        // Add non-sensitive configuration information
        healthData["ticketing_provider"] = _ticketingSettings.Provider;
        healthData["ticketing_api_configured"] = !string.IsNullOrEmpty(_ticketingSettings.ApiUrl);
        healthData["ticketing_fallback_enabled"] = _ticketingSettings.EnableGracefulDegradation;
        healthData["ticketing_fallback_provider"] = _ticketingSettings.FallbackProvider ?? "none";
        
        healthData["smtp_host_configured"] = !string.IsNullOrEmpty(_smtpSettings.Host);
        healthData["smtp_ssl_enabled"] = _smtpSettings.EnableSsl;
        healthData["smtp_from_address_configured"] = !string.IsNullOrEmpty(_smtpSettings.FromAddress);
    }
}

public enum ServiceHealthStatus
{
    Healthy,
    Degraded,
    Unhealthy
}

public class TicketingSettings
{
    public string Provider { get; set; } = string.Empty;
    public string ApiUrl { get; set; } = string.Empty;
    public string ApiKey { get; set; } = string.Empty;
    public string Username { get; set; } = string.Empty;
    public string DefaultProject { get; set; } = string.Empty;
    public int ConnectionTimeoutSeconds { get; set; } = 30;
    public bool EnableGracefulDegradation { get; set; } = true;
    public string? FallbackProvider { get; set; }
}

public class SmtpSettings
{
    public string Host { get; set; } = string.Empty;
    public int Port { get; set; } = 587;
    public bool EnableSsl { get; set; } = true;
    public string Username { get; set; } = string.Empty;
    public string Password { get; set; } = string.Empty;
    public string FromAddress { get; set; } = string.Empty;
    public string FromName { get; set; } = string.Empty;
}