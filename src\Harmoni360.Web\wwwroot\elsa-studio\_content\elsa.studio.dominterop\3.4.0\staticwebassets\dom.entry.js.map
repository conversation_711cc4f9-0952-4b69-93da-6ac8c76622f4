{"version": 3, "file": "dom.entry.js", "mappings": ";;;;;;;;;;;;;AAAyC;AAElC,SAAS,YAAY,CAAC,sBAAwC;IACjE,MAAM,OAAO,GAAG,wDAAU,CAAC,sBAAsB,CAAgB,CAAC;IAElE,OAAO,CAAC,KAAK,EAAE,CAAC;AACpB,CAAC;;;;;;;;;;;;;;;;ACNwC;AAElC,SAAS,qBAAqB,CAAC,sBAAwC;IAC1E,MAAM,OAAO,GAAG,wDAAU,CAAC,sBAAsB,CAAC,CAAC;IACnD,OAAO,OAAO,CAAC,qBAAqB,EAAE,CAAC;AAC3C,CAAC;;;;;;;;;;;;;;;ACLM,SAAS,UAAU,CAAC,sBAAwC;IAC/D,OAAO,OAAO,sBAAsB,KAAK,QAAQ,CAAC,CAAC,CAAC,QAAQ,CAAC,aAAa,CAAC,sBAAsB,CAAC,CAAC,CAAC,CAAC,sBAAsB,CAAC;AAChI,CAAC;;;;;;;;;;;;;;;;ACFwC;AAElC,SAAS,gBAAgB,CAAC,sBAAwC;IACrE,MAAM,OAAO,GAAG,wDAAU,CAAC,sBAAsB,CAAC,CAAC;IAEnD,IAAG,CAAC,OAAO;QACP,OAAO,CAAC,CAAC;IAEb,MAAM,IAAI,GAAG,OAAO,CAAC,qBAAqB,EAAE,CAAC;IAC7C,MAAM,YAAY,GAAG,MAAM,CAAC,WAAW,CAAC;IAExC,IAAI,IAAI,CAAC,MAAM,GAAG,YAAY,EAAE,CAAC;QAC7B,MAAM,aAAa,GAAG,YAAY,GAAG,IAAI,CAAC,GAAG,CAAC;QAC9C,OAAO,aAAa,GAAG,CAAC,CAAC,CAAC,CAAC,aAAa,CAAC,CAAC,CAAC,CAAC,CAAC;IACjD,CAAC;IAED,OAAO,IAAI,CAAC,MAAM,CAAC;AACvB,CAAC;;;;;;;;;;;;;;;;;;;;;;ACjB+B;AACW;AACb;AACO;;;;;;;;;;;;;;;;;;;ACHX;;;;;;;SCA1B;SACA;;SAEA;SACA;SACA;SACA;SACA;SACA;SACA;SACA;SACA;SACA;SACA;SACA;SACA;;SAEA;SACA;;SAEA;SACA;SACA;;;;;UCtBA;UACA;UACA;UACA;UACA,yCAAyC,wCAAwC;UACjF;UACA;UACA;;;;;UCPA;;;;;UCAA;UACA;UACA;UACA,uDAAuD,iBAAiB;UACxE;UACA,gDAAgD,aAAa;UAC7D;;;;;;;;;;;;;;;;;;ACNuB", "sources": ["webpack://elsa-studio-dom-interop/./src/dom/element/click-element.ts", "webpack://elsa-studio-dom-interop/./src/dom/element/get-bounding-client-rect.ts", "webpack://elsa-studio-dom-interop/./src/dom/element/get-element.ts", "webpack://elsa-studio-dom-interop/./src/dom/element/get-visible-height.ts", "webpack://elsa-studio-dom-interop/./src/dom/element/index.ts", "webpack://elsa-studio-dom-interop/./src/dom/index.ts", "webpack://elsa-studio-dom-interop/webpack/bootstrap", "webpack://elsa-studio-dom-interop/webpack/runtime/define property getters", "webpack://elsa-studio-dom-interop/webpack/runtime/hasOwnProperty shorthand", "webpack://elsa-studio-dom-interop/webpack/runtime/make namespace object", "webpack://elsa-studio-dom-interop/./src/dom.ts"], "sourcesContent": ["import {getElement} from \"./get-element\";\n\nexport function clickElement(elementOrQuerySelector: Element | string) {\n    const element = getElement(elementOrQuerySelector) as HTMLElement;\n    \n    element.click();\n}", "import {getElement} from \"./get-element\";\n\nexport function getBoundingClientRect(elementOrQuerySelector: Element | string): DOMRect {\n    const element = getElement(elementOrQuerySelector);\n    return element.getBoundingClientRect();\n}", "export function getElement(elementOrQuerySelector: Element | string): Element {\n    return typeof elementOrQuerySelector === 'string' ? document.querySelector(elementOrQuerySelector) : elementOrQuerySelector;\n}", "import {getElement} from \"./get-element\";\n\nexport function getVisibleHeight(elementOrQuerySelector: Element | string): number {\n    const element = getElement(elementOrQuerySelector);\n    \n    if(!element)\n        return 0;\n    \n    const rect = element.getBoundingClientRect();\n    const windowHeight = window.innerHeight;\n\n    if (rect.bottom > windowHeight) {\n        const visibleHeight = windowHeight - rect.top;\n        return visibleHeight > 0 ? visibleHeight : 0;\n    }\n\n    return rect.height;\n}", "export * from './click-element';\nexport * from './get-bounding-client-rect';\nexport * from './get-element';\nexport * from './get-visible-height';\n\n", "export * from './element';", "// The module cache\nvar __webpack_module_cache__ = {};\n\n// The require function\nfunction __webpack_require__(moduleId) {\n\t// Check if module is in cache\n\tvar cachedModule = __webpack_module_cache__[moduleId];\n\tif (cachedModule !== undefined) {\n\t\treturn cachedModule.exports;\n\t}\n\t// Create a new module (and put it into the cache)\n\tvar module = __webpack_module_cache__[moduleId] = {\n\t\t// no module.id needed\n\t\t// no module.loaded needed\n\t\texports: {}\n\t};\n\n\t// Execute the module function\n\t__webpack_modules__[moduleId](module, module.exports, __webpack_require__);\n\n\t// Return the exports of the module\n\treturn module.exports;\n}\n\n", "// define getter functions for harmony exports\n__webpack_require__.d = (exports, definition) => {\n\tfor(var key in definition) {\n\t\tif(__webpack_require__.o(definition, key) && !__webpack_require__.o(exports, key)) {\n\t\t\tObject.defineProperty(exports, key, { enumerable: true, get: definition[key] });\n\t\t}\n\t}\n};", "__webpack_require__.o = (obj, prop) => (Object.prototype.hasOwnProperty.call(obj, prop))", "// define __esModule on exports\n__webpack_require__.r = (exports) => {\n\tif(typeof Symbol !== 'undefined' && Symbol.toStringTag) {\n\t\tObject.defineProperty(exports, Symbol.toStringTag, { value: 'Module' });\n\t}\n\tObject.defineProperty(exports, '__esModule', { value: true });\n};", "export * from \"./dom/\";"], "names": [], "sourceRoot": ""}