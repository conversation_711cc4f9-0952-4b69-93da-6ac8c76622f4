import React, { useEffect, useRef } from 'react';
import { useSelector } from 'react-redux';
import { RootState } from '../../store';

interface ElsaStudioWrapperProps {
  className?: string;
  style?: React.CSSProperties;
}

const ElsaStudioWrapper: React.FC<ElsaStudioWrapperProps> = ({ className, style }) => {
  const iframeRef = useRef<HTMLIFrameElement>(null);
  const { token } = useSelector((state: RootState) => state.auth);

  useEffect(() => {
    const handleMessage = (event: MessageEvent) => {
      // Ensure we only respond to messages from our Elsa Studio iframe
      if (event.source !== iframeRef.current?.contentWindow) {
        return;
      }

      // Handle different message types from Elsa Studio
      switch (event.data.type) {
        case 'ELSA_READY':
          console.log('Elsa Studio is ready');
          break;
          
        case 'REQUEST_AUTH_TOKEN':
          console.log('Elsa Studio requesting auth token');
          if (token && iframeRef.current?.contentWindow) {
            // Send the current auth token to Elsa Studio
            iframeRef.current.contentWindow.postMessage({
              type: 'HARMONI360_AUTH_TOKEN',
              token: token
            }, '*');
            console.log('Auth token sent to Elsa Studio');
          } else {
            console.warn('No auth token available to send to Elsa Studio');
          }
          break;
          
        default:
          // Handle other Elsa Studio messages if needed
          break;
      }
    };

    // Listen for messages from the Elsa Studio iframe
    window.addEventListener('message', handleMessage);

    return () => {
      window.removeEventListener('message', handleMessage);
    };
  }, [token]);

  // Send updated token when it changes
  useEffect(() => {
    if (token && iframeRef.current?.contentWindow) {
      // Small delay to ensure iframe is fully loaded
      setTimeout(() => {
        iframeRef.current?.contentWindow?.postMessage({
          type: 'HARMONI360_AUTH_TOKEN',
          token: token
        }, '*');
      }, 1000);
    }
  }, [token]);

  return (
    <div className={className} style={style}>
      <iframe
        ref={iframeRef}
        src="/elsa-studio/"
        style={{
          width: '100%',
          height: '100%',
          border: 'none',
          minHeight: '800px'
        }}
        title="Elsa Studio"
        allow="fullscreen"
        sandbox="allow-scripts allow-same-origin allow-forms allow-popups allow-modals"
      />
    </div>
  );
};

export default ElsaStudioWrapper;