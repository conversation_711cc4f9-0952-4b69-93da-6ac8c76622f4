using Microsoft.AspNetCore.Components.Authorization;
using Microsoft.JSInterop;
using System.Security.Claims;
using System.Text.Json;
using Microsoft.IdentityModel.JsonWebTokens;

namespace Harmoni360.ElsaStudio.Services;

public class Harmoni360AuthenticationStateProvider : AuthenticationStateProvider
{
    private readonly IJSRuntime _jsRuntime;
    private readonly IElsaAuthenticationService _authService;
    private readonly ILogger<Harmoni360AuthenticationStateProvider> _logger;

    private ClaimsPrincipal _anonymous = new ClaimsPrincipal(new ClaimsIdentity());

    public Harmoni360AuthenticationStateProvider(
        IJSRuntime jsRuntime, 
        IElsaAuthenticationService authService,
        ILogger<Harmoni360AuthenticationStateProvider> logger)
    {
        _jsRuntime = jsRuntime;
        _authService = authService;
        _logger = logger;
    }

    public override async Task<AuthenticationState> GetAuthenticationStateAsync()
    {
        try
        {
            _logger.LogInformation("🔍 Getting authentication state for Elsa Studio");
            
            // Try to get token from JavaScript side (localStorage, sessionStorage, or cookies)
            var token = await GetTokenFromBrowser();
            
            _logger.LogInformation("🔑 Token retrieved: {HasToken}", !string.IsNullOrWhiteSpace(token));
            
            if (string.IsNullOrWhiteSpace(token))
            {
                _logger.LogWarning("❌ No token found - returning anonymous user");
                return new AuthenticationState(_anonymous);
            }

            // Validate and parse the JWT token
            var claims = ParseJwtToken(token);
            if (claims == null || !claims.Any())
            {
                _logger.LogWarning("❌ Invalid token or no claims found - returning anonymous user");
                return new AuthenticationState(_anonymous);
            }

            // Update auth service with valid token
            _authService.SetAuthToken(token);

            var authenticatedUser = new ClaimsPrincipal(new ClaimsIdentity(claims, "jwt"));
            
            _logger.LogInformation("✅ User authenticated via JWT token: {UserName} with {ClaimCount} claims", 
                authenticatedUser.Identity?.Name ?? "Unknown", claims.Count);

            return new AuthenticationState(authenticatedUser);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "❌ Error getting authentication state");
            return new AuthenticationState(_anonymous);
        }
    }

    public async Task MarkUserAsAuthenticated(string token)
    {
        var claims = ParseJwtToken(token);
        if (claims != null && claims.Any())
        {
            _authService.SetAuthToken(token);
            var authenticatedUser = new ClaimsPrincipal(new ClaimsIdentity(claims, "jwt"));
            
            var authState = new AuthenticationState(authenticatedUser);
            NotifyAuthenticationStateChanged(Task.FromResult(authState));
            
            _logger.LogInformation("User marked as authenticated: {UserName}", 
                authenticatedUser.Identity?.Name ?? "Unknown");
        }
    }

    public void MarkUserAsLoggedOut()
    {
        _authService.SetAuthToken(string.Empty);
        var anonymous = new ClaimsPrincipal(new ClaimsIdentity());
        var authState = new AuthenticationState(anonymous);
        NotifyAuthenticationStateChanged(Task.FromResult(authState));
        
        _logger.LogInformation("User marked as logged out");
    }

    private async Task<string?> GetTokenFromBrowser()
    {
        try
        {
            _logger.LogInformation("🔍 Searching for tokens in browser storage");
            
            // Try multiple sources for the token
            // 1. Query parameter (for initial authentication)
            var tokenFromQuery = await _jsRuntime.InvokeAsync<string?>("getQueryParameter", "token");
            _logger.LogInformation("📋 Query parameter token: {HasToken}", !string.IsNullOrWhiteSpace(tokenFromQuery));
            if (!string.IsNullOrWhiteSpace(tokenFromQuery))
            {
                return tokenFromQuery;
            }

            // 2. localStorage (primary storage)
            var tokenFromStorage = await _jsRuntime.InvokeAsync<string?>("localStorage.getItem", "harmoni360_token");
            _logger.LogInformation("💾 localStorage token: {HasToken}", !string.IsNullOrWhiteSpace(tokenFromStorage));
            if (!string.IsNullOrWhiteSpace(tokenFromStorage))
            {
                return tokenFromStorage;
            }

            // 3. sessionStorage (fallback)
            var tokenFromSession = await _jsRuntime.InvokeAsync<string?>("sessionStorage.getItem", "harmoni360_token");
            _logger.LogInformation("🗃️ sessionStorage token: {HasToken}", !string.IsNullOrWhiteSpace(tokenFromSession));
            if (!string.IsNullOrWhiteSpace(tokenFromSession))
            {
                return tokenFromSession;
            }

            // 4. Cookie (last resort)
            var tokenFromCookie = await _jsRuntime.InvokeAsync<string?>("getCookie", "harmoni360_token");
            _logger.LogInformation("🍪 Cookie token: {HasToken}", !string.IsNullOrWhiteSpace(tokenFromCookie));
            return tokenFromCookie;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "❌ Error getting token from browser");
            return null;
        }
    }

    private IList<Claim>? ParseJwtToken(string token)
    {
        try
        {
            var handler = new JsonWebTokenHandler();
            JsonWebToken jsonToken;
            try
            {
                jsonToken = new JsonWebToken(token);
            }
            catch (Exception ex)
            {
                _logger.LogWarning(ex, "Invalid JWT token format");
                return null;
            }

            // Check if token is expired
            if (jsonToken.ValidTo < DateTime.UtcNow)
            {
                _logger.LogWarning("JWT token has expired");
                return null;
            }

            var claims = new List<Claim>();

            // Extract standard claims from the token
            var claimsFromToken = jsonToken.Claims;
            foreach (var claim in claimsFromToken)
            {
                // The claim.Value property is already a string
                var claimType = MapClaimType(claim.Type);
                claims.Add(new Claim(claimType, claim.Value));
            }

            // Add authentication method claim
            claims.Add(new Claim(ClaimTypes.AuthenticationMethod, "Bearer"));
            
            return claims;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error parsing JWT token");
            return null;
        }
    }

    private static string MapClaimType(string jwtClaimType)
    {
        return jwtClaimType switch
        {
            "sub" => ClaimTypes.NameIdentifier,
            "name" => ClaimTypes.Name,
            "email" => ClaimTypes.Email,
            "role" => ClaimTypes.Role,
            "given_name" => ClaimTypes.GivenName,
            "family_name" => ClaimTypes.Surname,
            _ => jwtClaimType
        };
    }
}

/// <summary>
/// JavaScript helper functions for token management
/// </summary>
public static class TokenJavaScriptHelpers
{
    public const string JavaScriptHelpers = """
        window.getQueryParameter = (name) => {
            const urlParams = new URLSearchParams(window.location.search);
            return urlParams.get(name);
        };

        window.getCookie = (name) => {
            const value = `; ${document.cookie}`;
            const parts = value.split(`; ${name}=`);
            if (parts.length === 2) return parts.pop().split(';').shift();
            return null;
        };

        window.setCookie = (name, value, days = 30) => {
            const expires = new Date();
            expires.setTime(expires.getTime() + (days * 24 * 60 * 60 * 1000));
            document.cookie = `${name}=${value}; expires=${expires.toUTCString()}; path=/; SameSite=Strict; Secure`;
        };

        window.deleteCookie = (name) => {
            document.cookie = `${name}=; expires=Thu, 01 Jan 1970 00:00:00 UTC; path=/;`;
        };

        window.redirectToLogin = () => {
            // Redirect to main app login
            const currentUrl = encodeURIComponent(window.location.href);
            window.location.href = `/login?returnUrl=${currentUrl}`;
        };

        window.initializeElsaAuth = () => {
            // Check if user needs to be redirected to login
            const token = localStorage.getItem('harmoni360_token') || 
                         sessionStorage.getItem('harmoni360_token') ||
                         new URLSearchParams(window.location.search).get('token');
            
            if (!token) {
                console.log('No authentication token found, redirecting to login');
                setTimeout(() => window.redirectToLogin(), 1000);
            }
        };
        """;
}