define([],function(){globalThis._VSCODE_NLS_MESSAGES=["{0} ({1})","entrée","Respecter la casse","Mot entier","Utiliser une expression régulière","entrée","Préserver la casse","Inspectez ceci dans l’affichage accessible avec {0}.","Inspectez ceci dans l’affichage accessible via la commande Open Accessible View qui ne peut pas être déclenchée via une combinaison de touches pour l’instant.","Erreur : {0}","Avertissement : {0}","Info : {0}"," ou {0} pour l'histoire"," ({0} pour l'histoire)","Entrée effacée","Indépendant","Zone de sélection","Plus d'actions...","Filtrer","Correspondance approximative","Type à filtrer","Entrer le texte à rechercher","Entrer le texte à rechercher","<PERSON><PERSON><PERSON>","Aucun résultat","Aucun résultat trouvé.",null,"(vide)","{0}: {1}","Une erreur système s'est produite ({0})","Une erreur inconnue s’est produite. Veuillez consulter le journal pour plus de détails.","Une erreur inconnue s’est produite. Veuillez consulter le journal pour plus de détails.","{0} ({1} erreurs au total)","Une erreur inconnue s’est produite. Veuillez consulter le journal pour plus de détails.","Ctrl","Maj","Alt","Windows","Ctrl","Maj","Alt","Super","Contrôle","Maj","Option","Commande","Contrôle","Maj","Alt","Windows","Contrôle","Maj","Alt","Super",null,null,null,null,null,"Aligner par rapport à la fin même en cas de passage à des lignes plus longues","Aligner par rapport à la fin même en cas de passage à des lignes plus longues","Curseurs secondaires supprimés","Ann&&uler","Annuler","&&Rétablir","Rétablir","&&Sélectionner tout","Tout sélectionner","Maintenez la touche {0} enfoncée pour pointer avec la souris","Chargement...","Le nombre de curseurs a été limité à {0}. Envisagez d’utiliser [rechercher et remplacer](https://code.visualstudio.com/docs/editor/codebasics#_find-and-replace) pour les modifications plus importantes ou augmentez la limite du nombre de curseurs multiples du paramètre.","Augmenter la limite de curseurs multiples","Activer/désactiver réduire les régions inchangées","Activer/désactiver l’affichage des blocs de code déplacés","Activer/désactiver Utiliser la vue inline lorsque l'espace est limité","Éditeur de différences","Changer de côté","Quitter Comparer le déplacement","Réduire toutes les régions inchangées","Afficher toutes les régions inchangées","Restaurer","Visionneuse Diff accessible","Accéder à la différence suivante","Accéder la différence précédente","Icône « Insérer » dans la visionneuse diff accessible.","Icône « Supprimer » dans la visionneuse diff accessible.","Icône de « Fermer » dans la visionneuse diff accessible.","Fermer","Visionneuse diff accessible. Utilisez les flèches haut et bas pour naviguer.","aucune ligne changée","1 ligne changée","{0} lignes changées","Différence {0} sur {1} : ligne d'origine {2}, {3}, ligne modifiée {4}, {5}","vide","{0} ligne inchangée {1}","{0} ligne d'origine {1} ligne modifiée {2}","+ {0} ligne modifiée {1}","- {0} ligne d'origine {1}"," utilisez {0} pour ouvrir l’aide sur l’accessibilité.","Copier les lignes supprimées","Copier la ligne supprimée","Copier les lignes modifiées","Copier la ligne modifiée","Copier la ligne supprimée ({0})","Copier la ligne modifiée ({0})","Annuler la modification","Utiliser la vue inline lorsque l'espace est limité","Afficher les blocs de code déplacés","Rétablir le bloc","Rétablir la sélection","Ouvrir la visionneuse diff accessible","Replier la région inchangée","{0} lignes masquées","Cliquez ou faites glisser pour afficher plus d'éléments au-dessus","Afficher la région inchangée","Cliquez ou faites glisser pour afficher plus d'éléments en dessous","{0} lignes masquées","Double-cliquer pour déplier","Code déplacé avec des modifications vers la ligne {0}-{1}","Code déplacé avec des modifications à partir de la ligne {0}-{1}","Code déplacé vers la ligne {0}-{1}","Code déplacé à partir de la ligne {0}-{1}","Rétablir les modifications sélectionnées","Rétablir la modification","Couleur de bordure du texte déplacé dans l’éditeur de diff.","Couleur de bordure active du texte déplacé dans l’éditeur de différences.","Couleur de l’ombre autour des widgets de région inchangés.","Élément décoratif de ligne pour les insertions dans l'éditeur de différences.","Élément décoratif de ligne pour les suppressions dans l'éditeur de différences.","Couleur d’arrière-plan de l’en-tête de l’éditeur de différences","Couleur d’arrière-plan de l’éditeur de différences de fichiers multiples","Couleur de bordure de l’éditeur de différences de fichiers multiples","Aucun fichier modifié","Éditeur","Le nombre d’espaces auxquels une tabulation est égale. Ce paramètre est substitué basé sur le contenu du fichier lorsque {0} est activé.",'Nombre d’espaces utilisés pour la mise en retrait ou `"tabSize"` pour utiliser la valeur de `#editor.tabSize#`. Ce paramètre est remplacé en fonction du contenu du fichier quand `#editor.detectIndentation#` est activé.',"Espaces insérés quand vous appuyez sur la touche Tab. Ce paramètre est remplacé en fonction du contenu du fichier quand {0} est activé.","Contrôle si {0} et {1} sont automatiquement détectés lors de l’ouverture d’un fichier en fonction de son contenu.","Supprimer l'espace blanc de fin inséré automatiquement.","Traitement spécial des fichiers volumineux pour désactiver certaines fonctionnalités utilisant beaucoup de mémoire.","Désactivez les suggestions basées sur Word.","Suggère uniquement des mots dans le document actif.","Suggère des mots dans tous les documents ouverts du même langage.","Suggère des mots dans tous les documents ouverts.","Contrôle si les complétions doivent être calculées en fonction des mots du document et à partir de quels documents elles sont calculées.","Coloration sémantique activée pour tous les thèmes de couleur.","Coloration sémantique désactivée pour tous les thèmes de couleur.","La coloration sémantique est configurée par le paramètre 'semanticHighlighting' du thème de couleur actuel.","Contrôle si semanticHighlighting est affiché pour les langages qui le prennent en charge.","Maintenir les éditeurs d'aperçu ouverts même si l'utilisateur double-clique sur son contenu ou appuie sur la touche Échap.","Les lignes plus longues que cette valeur ne sont pas tokenisées pour des raisons de performances","Contrôle si la création de jetons doit se produire de manière asynchrone sur un worker web.","Contrôle si la création de jetons asynchrones doit être journalisée. Pour le débogage uniquement.","Contrôle si la segmentation du texte en unités lexicales asynchrones doit être vérifiée par rapport à la segmentation du texte en unités lexicales en arrière-plan héritée. Peut ralentir la segmentation du texte en unités lexicales. Pour le débogage uniquement.","Contrôle si l’analyse de sitter (système) d’arborescence doit être activée et la télémétrie collectée. La définition de `editor.experimental.preferTreeSitter` pour des langages spécifiques est prioritaire.","Définit les symboles de type crochet qui augmentent ou diminuent le retrait.","Séquence de chaînes ou de caractères de crochets ouvrants.","Séquence de chaînes ou de caractères de crochets fermants.","Définit les paires de crochets qui sont colorisées par leur niveau d’imbrication si la colorisation des paires de crochets est activée.","Séquence de chaînes ou de caractères de crochets ouvrants.","Séquence de chaînes ou de caractères de crochets fermants.","Délai d'expiration en millisecondes avant annulation du calcul de diff. Utilisez 0 pour supprimer le délai d'expiration.","Taille de fichier maximale en Mo pour laquelle calculer les différences. Utilisez 0 pour ne pas avoir de limite.","Contrôle si l'éditeur de différences affiche les différences en mode côte à côte ou inline.","Si l'éditeur de différences est moins large que cette valeur, la vue inline est utilisée.","Si cette option est activée et que la largeur de l'éditeur est trop étroite, la vue inline est utilisée.","Lorsqu’il est activé, l’éditeur de différences affiche des flèches dans sa marge de glyphe pour rétablir les modifications.","Lorsque cette option est activée, l’éditeur de différences affiche une marge spéciale pour les actions de rétablissement et d’index.","Quand il est activé, l'éditeur de différences ignore les changements d'espace blanc de début ou de fin.","Contrôle si l'éditeur de différences affiche les indicateurs +/- pour les changements ajoutés/supprimés .","Contrôle si l'éditeur affiche CodeLens.","Le retour automatique à la ligne n'est jamais effectué.","Le retour automatique à la ligne s'effectue en fonction de la largeur de la fenêtre d'affichage.","Le retour automatique à la ligne dépend du paramètre {0}.","Utilise l’algorithme de comparaison hérité.","Utilise l’algorithme de comparaison avancé.","Contrôle si l'éditeur de différences affiche les régions inchangées.","Contrôle le nombre de lignes utilisées pour les régions inchangées.","Contrôle le nombre de lignes utilisées comme minimum pour les régions inchangées.","Contrôle le nombre de lignes utilisées comme contexte lors de la comparaison des régions inchangées.","Contrôle si l’éditeur de différences doit afficher les déplacements de code détectés.","Contrôle si l’éditeur de différences affiche des décorations vides pour voir où les caractères ont été insérés ou supprimés.","Si cette option est activée et que l’éditeur utilise la vue inline, les modifications apportées aux mots sont restituées inline.","Utilisez les API de la plateforme pour détecter lorsqu'un lecteur d'écran est connecté.","Optimiser pour une utilisation avec un lecteur d'écran.","Supposons qu’aucun lecteur d’écran ne soit connecté.","Contrôle si l’interface utilisateur doit s’exécuter dans un mode où elle est optimisée pour les lecteurs d’écran.","Contrôle si un espace est inséré pour les commentaires.","Contrôle si les lignes vides doivent être ignorées avec des actions d'activation/de désactivation, d'ajout ou de suppression des commentaires de ligne.","Contrôle si la copie sans sélection permet de copier la ligne actuelle.","Contrôle si le curseur doit sauter pour rechercher les correspondances lors de la saisie.","Ne lancez jamais la chaîne de recherche dans la sélection de l’éditeur.","Toujours amorcer la chaîne de recherche à partir de la sélection de l’éditeur, y compris le mot à la position du curseur.","Chaîne de recherche initiale uniquement dans la sélection de l’éditeur.","Détermine si la chaîne de recherche dans le Widget Recherche est initialisée avec la sélection de l’éditeur.","Ne jamais activer automatiquement la recherche dans la sélection (par défaut).","Toujours activer automatiquement la recherche dans la sélection.","Activez Rechercher automatiquement dans la sélection quand plusieurs lignes de contenu sont sélectionnées.","Contrôle la condition d'activation automatique de la recherche dans la sélection.","Détermine si le Widget Recherche devrait lire ou modifier le presse-papiers de recherche partagé sur macOS.","Contrôle si le widget Recherche doit ajouter des lignes supplémentaires en haut de l'éditeur. Quand la valeur est true, vous pouvez faire défiler au-delà de la première ligne si le widget Recherche est visible.","Contrôle si la recherche redémarre automatiquement depuis le début (ou la fin) quand il n'existe aucune autre correspondance.","Active/désactive les ligatures de police (fonctionnalités de police 'calt' et 'liga'). Remplacez ceci par une chaîne pour contrôler de manière précise la propriété CSS 'font-feature-settings'.","Propriété CSS 'font-feature-settings' explicite. Vous pouvez passer une valeur booléenne à la place si vous devez uniquement activer/désactiver les ligatures.","Configure les ligatures de police ou les fonctionnalités de police. Il peut s'agir d'une valeur booléenne permettant d'activer/de désactiver les ligatures, ou d'une chaîne correspondant à la valeur de la propriété CSS 'font-feature-settings'.","Active/désactive la traduction de font-weight en font-variation-settings. Remplacez ce paramètre par une chaîne pour un contrôle affiné de la propriété CSS 'font-variation-settings'.","Propriété CSS 'font-variation-settings' explicite. Une valeur booléenne peut être passée à la place si une seule valeur doit traduire font-weight en font-variation-settings.","Configure les variations de la police. Il peut s’agir d’une valeur booléenne pour activer/désactiver la traduction de font-weight en font-variation-settings ou d’une chaîne pour la valeur de la propriété CSS 'font-variation-settings'.","Contrôle la taille de police en pixels.",'Seuls les mots clés "normal" et "bold", ou les nombres compris entre 1 et 1 000 sont autorisés.',`Contrôle l'épaisseur de police. Accepte les mots clés "normal" et "bold", ou les nombres compris entre 1 et 1 000.`,"Montrer l’aperçu des résultats (par défaut)","Accéder au résultat principal et montrer un aperçu","Accéder au résultat principal et activer l’accès sans aperçu pour les autres","Ce paramètre est déprécié, utilisez des paramètres distincts comme 'editor.editor.gotoLocation.multipleDefinitions' ou 'editor.editor.gotoLocation.multipleImplementations' à la place.","Contrôle le comportement de la commande 'Atteindre la définition' quand plusieurs emplacements cibles existent.","Contrôle le comportement de la commande 'Atteindre la définition de type' quand plusieurs emplacements cibles existent.","Contrôle le comportement de la commande 'Atteindre la déclaration' quand plusieurs emplacements cibles existent.","Contrôle le comportement de la commande 'Atteindre les implémentations' quand plusieurs emplacements cibles existent.","Contrôle le comportement de la commande 'Atteindre les références' quand plusieurs emplacements cibles existent.","ID de commande alternatif exécuté quand le résultat de 'Atteindre la définition' est l'emplacement actuel.","ID de commande alternatif exécuté quand le résultat de 'Atteindre la définition de type' est l'emplacement actuel.","ID de commande alternatif exécuté quand le résultat de 'Atteindre la déclaration' est l'emplacement actuel.","ID de commande alternatif exécuté quand le résultat de 'Atteindre l'implémentation' est l'emplacement actuel.","ID de commande alternatif exécuté quand le résultat de 'Atteindre la référence' est l'emplacement actuel.","Contrôle si le pointage est affiché.","Contrôle le délai en millisecondes, après lequel le survol est affiché.","Contrôle si le pointage doit rester visible quand la souris est déplacée au-dessus.","Contrôle le délai en millisecondes après lequel le survol est masqué. Nécessite que « editor.hover.sticky » soit activé.","Préférez afficher les points au-dessus de la ligne, s’il y a de l’espace.","Suppose que tous les caractères ont la même largeur. Il s'agit d'un algorithme rapide qui fonctionne correctement pour les polices à espacement fixe et certains scripts (comme les caractères latins) où les glyphes ont la même largeur.","Délègue le calcul des points de wrapping au navigateur. Il s'agit d'un algorithme lent qui peut provoquer le gel des grands fichiers, mais qui fonctionne correctement dans tous les cas.","Contrôle l’algorithme qui calcule les points d’habillage. Notez qu’en mode d’accessibilité, les options avancées sont utilisées pour une expérience optimale.","Désactiver le menu d’action du code.","Afficher le menu d’action du code lorsque le curseur se trouve sur des lignes avec du code.","Afficher le menu d’action du code lorsque le curseur se trouve sur des lignes avec du code ou sur des lignes vides.","Active l’ampoule d’action de code dans l’éditeur.","Affiche les étendues actives imbriqués pendant le défilement en haut de l’éditeur.","Définit le nombre maximal de lignes rémanentes à afficher.","Définit le modèle à utiliser pour déterminer les lignes à coller. Si le modèle hiérarchique n’existe pas, il revient au modèle de fournisseur de pliage qui revient au modèle de mise en retrait. Cette demande est respectée dans les trois cas.","Activez le défilement de Sticky Scroll avec la barre de défilement horizontale de l'éditeur.","Active les indicateurs inlay dans l’éditeur.","Les indicateurs d’inlay sont activés.","Les indicateurs d’inlay sont affichés par défaut et masqués lors de la conservation {0}","Les indicateurs d’inlay sont masqués par défaut et s’affichent lorsque vous maintenez {0}","Les indicateurs d’inlay sont désactivés.","Contrôle la taille de police des indicateurs d’inlay dans l’éditeur. Par défaut, le {0} est utilisé lorsque la valeur configurée est inférieure à {1} ou supérieure à la taille de police de l’éditeur.","Contrôle la famille de polices des indicateurs d’inlay dans l’éditeur. Lorsqu’il est défini sur vide, le {0} est utilisé.","Active le remplissage autour des indicateurs d’inlay dans l’éditeur.",`Contrôle la hauteur de ligne. \r
 - Utilisez 0 pour calculer automatiquement la hauteur de ligne à partir de la taille de police.\r
 : les valeurs comprises entre 0 et 8 sont utilisées comme multiplicateur avec la taille de police.\r
 : les valeurs supérieures ou égales à 8 seront utilisées comme valeurs effectives.`,"Contrôle si la minimap est affichée.","Contrôle si la minimap est masquée automatiquement.","Le minimap a la même taille que le contenu de l'éditeur (défilement possible).","Le minimap s'agrandit ou se réduit selon les besoins pour remplir la hauteur de l'éditeur (pas de défilement).","Le minimap est réduit si nécessaire pour ne jamais dépasser la taille de l'éditeur (pas de défilement).","Contrôle la taille du minimap.","Contrôle le côté où afficher la minimap.","Contrôle quand afficher le curseur du minimap.","Échelle du contenu dessiné dans le minimap : 1, 2 ou 3.","Afficher les caractères réels sur une ligne par opposition aux blocs de couleur.","Limiter la largeur de la minimap pour afficher au plus un certain nombre de colonnes.","Contrôle si les régions nommées sont affichées en tant qu’en-têtes de section dans la minimap.","Contrôle si les commentaires MARK : sont affichés en tant qu’en-têtes de section dans la minimap.","Contrôle la taille de police des en-têtes de section dans le minimap.","Contrôle la quantité d’espace (en pixels) entre les caractères de l’en-tête de section. Cela permet de lire l’en-tête en petites tailles de police.","Contrôle la quantité d’espace entre le bord supérieur de l’éditeur et la première ligne.","Contrôle la quantité d'espace entre le bord inférieur de l'éditeur et la dernière ligne.","Active une fenêtre contextuelle qui affiche de la documentation sur les paramètres et des informations sur les types à mesure que vous tapez.","Détermine si le menu de suggestions de paramètres se ferme ou reviens au début lorsque la fin de la liste est atteinte.","Des suggestions rapides s’affichent dans le widget de suggestion","Les suggestions rapides s’affichent sous forme de texte fantôme","Les suggestions rapides sont désactivées","Activez les suggestions rapides dans les chaînes.","Activez les suggestions rapides dans les commentaires.","Activez les suggestions rapides en dehors des chaînes et des commentaires.","Contrôle si les suggestions doivent s’afficher automatiquement lors de la saisie. Cela peut être contrôlé pour la saisie dans des commentaires, des chaînes et d’autres codes. Vous pouvez configurer la suggestion rapide pour qu’elle s’affiche sous forme de texte fantôme ou avec le widget de suggestion. Tenez également compte du {0}-setting qui contrôle si les suggestions sont déclenchées par des caractères spéciaux.","Les numéros de ligne ne sont pas affichés.","Les numéros de ligne sont affichés en nombre absolu.","Les numéros de ligne sont affichés sous la forme de distance en lignes à la position du curseur.","Les numéros de ligne sont affichés toutes les 10 lignes.","Contrôle l'affichage des numéros de ligne.","Nombre de caractères monospace auxquels cette règle d'éditeur effectue le rendu.","Couleur de cette règle d'éditeur.","Rendre les règles verticales après un certain nombre de caractères à espacement fixe. Utiliser plusieurs valeurs pour plusieurs règles. Aucune règle n'est dessinée si le tableau est vide.","La barre de défilement verticale sera visible uniquement lorsque cela est nécessaire.","La barre de défilement verticale est toujours visible.","La barre de défilement verticale est toujours masquée.","Contrôle la visibilité de la barre de défilement verticale.","La barre de défilement horizontale sera visible uniquement lorsque cela est nécessaire.","La barre de défilement horizontale est toujours visible.","La barre de défilement horizontale est toujours masquée.","Contrôle la visibilité de la barre de défilement horizontale.","Largeur de la barre de défilement verticale.","Hauteur de la barre de défilement horizontale.","Contrôle si les clics permettent de faire défiler par page ou d’accéder à la position de clic.","Lorsqu'elle est définie, la barre de défilement horizontale n'augmentera pas la taille du contenu de l'éditeur.","Contrôle si tous les caractères ASCII non basiques sont mis en surbrillance. Seuls les caractères compris entre U+0020 et U+007E, tabulation, saut de ligne et retour chariot sont considérés comme des ASCII de base.","Contrôle si les caractères qui réservent de l’espace ou qui n’ont pas de largeur sont mis en surbrillance.","Contrôle si les caractères mis en surbrillance peuvent être déconcertés avec des caractères ASCII de base, à l’exception de ceux qui sont courants dans les paramètres régionaux utilisateur actuels.","Contrôle si les caractères des commentaires doivent également faire l’objet d’une mise en surbrillance Unicode.","Contrôle si les caractères des chaînes de texte doivent également faire l’objet d’une mise en surbrillance Unicode.","Définit les caractères autorisés qui ne sont pas mis en surbrillance.","Les caractères Unicode communs aux paramètres régionaux autorisés ne sont pas mis en surbrillance.","Contrôle si les suggestions en ligne doivent être affichées automatiquement dans l’éditeur.","Afficher la barre d’outils de suggestion en ligne chaque fois qu’une suggestion inline est affichée.","Afficher la barre d’outils de suggestion en ligne lorsque vous pointez sur une suggestion incluse.","N’affichez jamais la barre d’outils de suggestion en ligne.","Contrôle quand afficher la barre d’outils de suggestion incluse.","Contrôle la façon dont les suggestions inline interagissent avec le widget de suggestion. Si cette option est activée, le widget de suggestion n’est pas affiché automatiquement lorsque des suggestions inline sont disponibles.","Contrôle la famille de polices des suggestions inlined.",null,null,null,null,null,null,"Contrôle si la colorisation des paires de crochets est activée ou non. Utilisez {0} pour remplacer les couleurs de surbrillance des crochets.","Contrôle si chaque type de crochet possède son propre pool de couleurs indépendant.","Désactive les repères de paire de crochets.","Active les repères de paire de crochets uniquement pour la paire de crochets actifs.","Désactive les repères de paire de crochets.","Contrôle si les guides de la paire de crochets sont activés ou non.","Active les repères horizontaux en plus des repères de paire de crochets verticaux.","Active les repères horizontaux uniquement pour la paire de crochets actifs.","Désactive les repères de paire de crochets horizontaux.","Contrôle si les guides de la paire de crochets horizontaux sont activés ou non.","Contrôle si l’éditeur doit mettre en surbrillance la paire de crochets actifs.","Contrôle si l’éditeur doit afficher les guides de mise en retrait.","Met en surbrillance le guide de retrait actif.","Met en surbrillance le repère de retrait actif même si les repères de crochet sont mis en surbrillance.","Ne mettez pas en surbrillance le repère de retrait actif.","Contrôle si l’éditeur doit mettre en surbrillance le guide de mise en retrait actif.","Insérez une suggestion sans remplacer le texte à droite du curseur.","Insérez une suggestion et remplacez le texte à droite du curseur.","Contrôle si les mots sont remplacés en cas d'acceptation de la saisie semi-automatique. Notez que cela dépend des extensions adhérant à cette fonctionnalité.","Détermine si le filtre et le tri des suggestions doivent prendre en compte les fautes de frappes mineures.","Contrôle si le tri favorise les mots qui apparaissent à proximité du curseur.","Contrôle si les sélections de suggestion mémorisées sont partagées entre plusieurs espaces de travail et fenêtres (nécessite '#editor.suggestSelection#').","Toujours sélectionner une suggestion lors du déclenchement automatique d’IntelliSense.","Ne jamais sélectionner une suggestion lors du déclenchement automatique d’IntelliSense.","Sélectionnez une suggestion uniquement lors du déclenchement d’IntelliSense à partir d’un caractère déclencheur.","Sélectionnez une suggestion uniquement lors du déclenchement d’IntelliSense au cours de la frappe.","Contrôle si une suggestion est sélectionnée lorsque le widget s’affiche. Notez que cela ne s’applique qu’aux suggestions déclenchées automatiquement ({0} et {1}) et qu’une suggestion est toujours sélectionnée lorsqu’elle est explicitement invoquée, par exemple via `Ctrl+Espace`.","Contrôle si un extrait de code actif empêche les suggestions rapides.","Contrôle s'il faut montrer ou masquer les icônes dans les suggestions.","Contrôle la visibilité de la barre d'état en bas du widget de suggestion.","Contrôle si la sortie de la suggestion doit être affichée en aperçu dans l’éditeur.","Détermine si les détails du widget de suggestion sont inclus dans l’étiquette ou uniquement dans le widget de détails.","Ce paramètre est déprécié. Le widget de suggestion peut désormais être redimensionné.","Ce paramètre est déprécié, veuillez utiliser des paramètres distincts comme 'editor.suggest.showKeywords' ou 'editor.suggest.showSnippets' à la place.","Si activé, IntelliSense montre des suggestions de type 'method'.","Si activé, IntelliSense montre des suggestions de type 'function'.","Si activé, IntelliSense montre des suggestions de type 'constructor'.","Si cette option est activée, IntelliSense montre des suggestions `dépréciées`.","Quand le filtrage IntelliSense est activé, le premier caractère correspond à un début de mot, par exemple 'c' sur 'Console' ou 'WebContext', mais _not_ sur 'description'. Si désactivé, IntelliSense affiche plus de résultats, mais les trie toujours par qualité de correspondance.","Si activé, IntelliSense montre des suggestions de type 'field'.","Si activé, IntelliSense montre des suggestions de type 'variable'.","Si activé, IntelliSense montre des suggestions de type 'class'.","Si activé, IntelliSense montre des suggestions de type 'struct'.","Si activé, IntelliSense montre des suggestions de type 'interface'.","Si activé, IntelliSense montre des suggestions de type 'module'.","Si activé, IntelliSense montre des suggestions de type 'property'.","Si activé, IntelliSense montre des suggestions de type 'event'.","Si activé, IntelliSense montre des suggestions de type 'operator'.","Si activé, IntelliSense montre des suggestions de type 'unit'.","Si activé, IntelliSense montre des suggestions de type 'value'.","Si activé, IntelliSense montre des suggestions de type 'constant'.","Si activé, IntelliSense montre des suggestions de type 'enum'.","Si activé, IntelliSense montre des suggestions de type 'enumMember'.","Si activé, IntelliSense montre des suggestions de type 'keyword'.","Si activé, IntelliSense montre des suggestions de type 'text'.","Si activé, IntelliSense montre des suggestions de type 'color'.","Si activé, IntelliSense montre des suggestions de type 'file'.","Si activé, IntelliSense montre des suggestions de type 'reference'.","Si activé, IntelliSense montre des suggestions de type 'customcolor'.","Si activé, IntelliSense montre des suggestions de type 'folder'.","Si activé, IntelliSense montre des suggestions de type 'typeParameter'.","Si activé, IntelliSense montre des suggestions de type 'snippet'.","Si activé, IntelliSense montre des suggestions de type 'utilisateur'.","Si activé, IntelliSense montre des suggestions de type 'problèmes'.","Indique si les espaces blancs de début et de fin doivent toujours être sélectionnés.","Indique si les sous-mots (tels que « foo » dans « fooBar » ou « foo_bar ») doivent être sélectionnés.","Paramètres régionaux à utiliser pour la segmentation de mots lors de navigations ou d’opérations liées à un mot. Spécifiez la balise de langue BCP 47 du mot que vous souhaitez reconnaître (par exemple, ja, zh-CN, zh-Hant-TW, etc.).","Paramètres régionaux à utiliser pour la segmentation de mots lors de navigations ou d’opérations liées à un mot. Spécifiez la balise de langue BCP 47 du mot que vous souhaitez reconnaître (par exemple, ja, zh-CN, zh-Hant-TW, etc.).","Aucune mise en retrait. Les lignes enveloppées commencent à la colonne 1.","Les lignes enveloppées obtiennent la même mise en retrait que le parent.","Les lignes justifiées obtiennent une mise en retrait +1 vers le parent.","Les lignes justifiées obtiennent une mise en retrait +2 vers le parent. ","Contrôle la mise en retrait des lignes justifiées.","Contrôle si vous pouvez glisser et déposer un fichier dans un éditeur de texte en maintenant la touche « Maj » enfoncée (au lieu d’ouvrir le fichier dans un éditeur).","Contrôle si un widget est affiché lors de l’annulation de fichiers dans l’éditeur. Ce widget vous permet de contrôler la façon dont le fichier est annulé.","Afficher le widget du sélecteur de dépôt après la suppression d’un fichier dans l’éditeur.","Ne jamais afficher le widget du sélecteur de dépôt. À la place, le fournisseur de dépôt par défaut est toujours utilisé.","Contrôle si vous pouvez coller le contenu de différentes manières.","Contrôle l’affichage d’un widget lors du collage de contenu dans l’éditeur. Ce widget vous permet de contrôler la manière dont le fichier est collé.","Afficher le widget du sélecteur de collage une fois le contenu collé dans l’éditeur.","Ne jamais afficher le widget de sélection de collage. Au lieu de cela, le comportement de collage par défaut est toujours utilisé.","Contrôle si les suggestions doivent être acceptées sur les caractères de validation. Par exemple, en JavaScript, le point-virgule (`;`) peut être un caractère de validation qui accepte une suggestion et tape ce caractère.","Accepter uniquement une suggestion avec 'Entrée' quand elle effectue une modification textuelle.","Contrôle si les suggestions sont acceptées après appui sur 'Entrée', en plus de 'Tab'. Permet d’éviter toute ambiguïté entre l’insertion de nouvelles lignes et l'acceptation de suggestions.","Contrôle le nombre de lignes de l’éditeur qu’un lecteur d’écran peut lire en une seule fois. Quand nous détectons un lecteur d’écran, nous définissons automatiquement la valeur par défaut à 500. Attention : Les valeurs supérieures à la valeur par défaut peuvent avoir un impact important sur les performances.","Contenu de l'éditeur","Contrôlez si les suggestions incluses sont annoncées par un lecteur d’écran.","Utilisez les configurations de langage pour déterminer quand fermer automatiquement les parenthèses.","Fermer automatiquement les parenthèses uniquement lorsque le curseur est à gauche de l’espace.","Contrôle si l’éditeur doit fermer automatiquement les parenthèses quand l’utilisateur ajoute une parenthèse ouvrante.","Utilisez les configurations de langage pour déterminer quand fermer automatiquement les commentaires.","Fermez automatiquement les commentaires seulement si le curseur est à gauche de l'espace.","Contrôle si l'éditeur doit fermer automatiquement les commentaires quand l'utilisateur ajoute un commentaire ouvrant.","Supprimez les guillemets ou crochets fermants adjacents uniquement s'ils ont été insérés automatiquement.","Contrôle si l'éditeur doit supprimer les guillemets ou crochets fermants adjacents au moment de la suppression.","Tapez avant les guillemets ou les crochets fermants uniquement s'ils sont automatiquement insérés.","Contrôle si l'éditeur doit taper avant les guillemets ou crochets fermants.","Utilisez les configurations de langage pour déterminer quand fermer automatiquement les guillemets.","Fermer automatiquement les guillemets uniquement lorsque le curseur est à gauche de l’espace.","Contrôle si l’éditeur doit fermer automatiquement les guillemets après que l’utilisateur ajoute un guillemet ouvrant.","L'éditeur n'insère pas de retrait automatiquement.","L'éditeur conserve le retrait de la ligne actuelle.","L'éditeur conserve le retrait de la ligne actuelle et honore les crochets définis par le langage.","L'éditeur conserve le retrait de la ligne actuelle, honore les crochets définis par le langage et appelle des objets onEnterRules spéciaux définis par les langages.","L'éditeur conserve le retrait de la ligne actuelle, honore les crochets définis par le langage, appelle des objets onEnterRules spéciaux définis par les langages et honore les objets indentationRules définis par les langages.","Contrôle si l'éditeur doit ajuster automatiquement le retrait quand les utilisateurs tapent, collent, déplacent ou mettent en retrait des lignes.","Utilisez les configurations de langage pour déterminer quand entourer automatiquement les sélections.","Entourez avec des guillemets et non des crochets.","Entourez avec des crochets et non des guillemets.","Contrôle si l'éditeur doit automatiquement entourer les sélections quand l'utilisateur tape des guillemets ou des crochets.","Émule le comportement des tabulations pour la sélection quand des espaces sont utilisés à des fins de mise en retrait. La sélection respecte les taquets de tabulation.","Contrôle si l'éditeur affiche CodeLens.","Contrôle la famille de polices pour CodeLens.","Contrôle la taille de police en pixels pour CodeLens. Quand la valeur est 0, 90 % de '#editor.fontSize#' est utilisé.","Contrôle si l'éditeur doit afficher les éléments décoratifs de couleurs inline et le sélecteur de couleurs.","Faire apparaître le sélecteur de couleurs au clic et au pointage de l’élément décoratif de couleurs","Faire apparaître le sélecteur de couleurs en survolant l’élément décoratif de couleurs","Faire apparaître le sélecteur de couleurs en cliquant sur l’élément décoratif de couleurs","Contrôle la condition pour faire apparaître un sélecteur de couleurs à partir d’un élément décoratif de couleurs","Contrôle le nombre maximal d’éléments décoratifs de couleur qui peuvent être rendus simultanément dans un éditeur.","Autoriser l'utilisation de la souris et des touches pour sélectionner des colonnes.","Contrôle si la coloration syntaxique doit être copiée dans le presse-papiers.","Contrôler le style d’animation du curseur.","L’animation de caret fluide est désactivée.","L’animation de caret fluide est activée uniquement lorsque l’utilisateur déplace le curseur avec un mouvement explicite.","L’animation de caret fluide est toujours activée.","Contrôle si l'animation du point d'insertion doit être activée.","Contrôle le style du curseur en mode d’entrée d’insertion.","Contrôle le nombre minimal de lignes de début (0 minimum) et de fin (1 minimum) visibles autour du curseur. Également appelé « scrollOff » ou « scrollOffset » dans d'autres éditeurs.","'cursorSurroundingLines' est appliqué seulement s'il est déclenché via le clavier ou une API.","'cursorSurroundingLines' est toujours appliqué.","Contrôle le moment où #editor.cursorSurroundingLines# doit être appliqué.","Détermine la largeur du curseur lorsque `#editor.cursorStyle#` est à `line`.","Contrôle si l’éditeur autorise le déplacement de sélections par glisser-déplacer.","Utilisez une nouvelle méthode de rendu avec des SVG.","Utilisez une nouvelle méthode de rendu avec des caractères de police.","Utilisez la méthode de rendu stable.","Contrôle si les espaces blancs sont rendus avec une nouvelle méthode expérimentale.","Multiplicateur de vitesse de défilement quand vous appuyez sur 'Alt'.","Contrôle si l'éditeur a le pliage de code activé.","Utilisez une stratégie de pliage propre au langage, si disponible, sinon utilisez la stratégie basée sur le retrait.","Utilisez la stratégie de pliage basée sur le retrait.","Contrôle la stratégie de calcul des plages de pliage.","Contrôle si l'éditeur doit mettre en évidence les plages pliées.","Contrôle si l’éditeur réduit automatiquement les plages d’importation.","Nombre maximal de régions pliables. L’augmentation de cette valeur peut réduire la réactivité de l’éditeur lorsque la source actuelle comprend un grand nombre de régions pliables.","Contrôle si le fait de cliquer sur le contenu vide après une ligne pliée déplie la ligne.","Contrôle la famille de polices.","Détermine si l’éditeur doit automatiquement mettre en forme le contenu collé. Un formateur doit être disponible et être capable de mettre en forme une plage dans un document.","Contrôle si l’éditeur doit mettre automatiquement en forme la ligne après la saisie.","Contrôle si l'éditeur doit afficher la marge de glyphes verticale. La marge de glyphes sert principalement au débogage.","Contrôle si le curseur doit être masqué dans la règle de la vue d’ensemble.","Contrôle l'espacement des lettres en pixels.","Contrôle si la modification liée est activée dans l’éditeur. En fonction du langage, les symboles associés, par exemple les balises HTML, sont mis à jour durant le processus de modification.","Contrôle si l’éditeur doit détecter les liens et les rendre cliquables.","Mettez en surbrillance les crochets correspondants.","Un multiplicateur à utiliser sur les `deltaX` et `deltaY` des événements de défilement de roulette de souris.","Faites un zoom sur la police de l’éditeur quand l’utilisateur fait tourner la roulette de la souris tout en maintenant la touche « Cmd » enfoncée.","Faire un zoom sur la police de l'éditeur quand l'utilisateur fait tourner la roulette de la souris tout en maintenant la touche 'Ctrl' enfoncée.","Fusionnez plusieurs curseurs quand ils se chevauchent.","Mappe vers 'Contrôle' dans Windows et Linux, et vers 'Commande' dans macOS.","Mappe vers 'Alt' dans Windows et Linux, et vers 'Option' dans macOS.","Modificateur à utiliser pour ajouter plusieurs curseurs avec la souris. Les mouvements de la souris Atteindre la définition et Ouvrir le lien s’adaptent afin qu’ils ne soient pas en conflit avec le [modificateur multicurseur](https://code.visualstudio.com/docs/editor/codebasics#_multicursor-modificateur).","Chaque curseur colle une seule ligne de texte.","Chaque curseur colle le texte en entier.","Contrôle le collage quand le nombre de lignes du texte collé correspond au nombre de curseurs.","Contrôle le nombre maximal de curseurs pouvant se trouver dans un éditeur actif à la fois.","Ne met pas en surbrillance les occurrences.","Met en surbrillance les occurrences uniquement dans le fichier actif.","Expérimental : met en évidence les occurrences dans tous les fichiers ouverts valides.","Contrôle si les occurrences doivent être mises en évidence dans les fichiers ouverts.","Contrôle si une bordure doit être dessinée autour de la règle de la vue d'ensemble.","Focus sur l'arborescence à l'ouverture de l'aperçu","Placer le focus sur l'éditeur à l'ouverture de l'aperçu","Contrôle s'il faut mettre le focus sur l'éditeur inline ou sur l'arborescence dans le widget d'aperçu.","Contrôle si le geste de souris Accéder à la définition ouvre toujours le widget d'aperçu.","Contrôle le délai en millisecondes après lequel des suggestions rapides sont affichées.","Contrôle si l'éditeur renomme automatiquement selon le type.","Déprécié. Utilisez 'editor.linkedEditing' à la place.","Contrôle si l’éditeur doit afficher les caractères de contrôle.","Affichez le dernier numéro de ligne quand le fichier se termine par un saut de ligne.","Met en surbrillance la gouttière et la ligne actuelle.","Contrôle la façon dont l’éditeur doit afficher la mise en surbrillance de la ligne actuelle.","Contrôle si l'éditeur doit afficher la mise en surbrillance de la ligne actuelle uniquement quand il a le focus.","Affiche les espaces blancs à l'exception des espaces uniques entre les mots.","Afficher les espaces blancs uniquement sur le texte sélectionné.","Affiche uniquement les caractères correspondant aux espaces blancs de fin.","Contrôle la façon dont l’éditeur doit restituer les caractères espaces.","Contrôle si les sélections doivent avoir des angles arrondis.","Contrôle le nombre de caractères supplémentaires, au-delà duquel l’éditeur défile horizontalement.","Contrôle si l’éditeur défile au-delà de la dernière ligne.","Faites défiler uniquement le long de l'axe prédominant quand le défilement est à la fois vertical et horizontal. Empêche la dérive horizontale en cas de défilement vertical sur un pavé tactile.","Contrôle si le presse-papiers principal Linux doit être pris en charge.","Contrôle si l'éditeur doit mettre en surbrillance les correspondances similaires à la sélection.","Affichez toujours les contrôles de pliage.","N’affichez jamais les contrôles de pliage et réduisez la taille de la marge.","Affichez uniquement les contrôles de pliage quand la souris est au-dessus de la reliure.","Contrôle quand afficher les contrôles de pliage sur la reliure.","Contrôle la disparition du code inutile.","Contrôle les variables dépréciées barrées.","Afficher des suggestions d’extraits au-dessus d’autres suggestions.","Afficher des suggestions d’extraits en-dessous d’autres suggestions.","Afficher des suggestions d’extraits avec d’autres suggestions.","Ne pas afficher de suggestions d’extrait de code.","Contrôle si les extraits de code s'affichent en même temps que d'autres suggestions, ainsi que leur mode de tri.","Contrôle si l'éditeur défile en utilisant une animation.","Contrôle si l'indicateur d'accessibilité doit être fourni aux utilisateurs du lecteur d'écran lorsqu'une complétion inline est affichée.","Taille de police pour le widget suggest. Lorsqu’elle est définie sur {0}, la valeur de {1} est utilisée.","Hauteur de ligne pour le widget suggest. Lorsqu’elle est définie sur {0}, la valeur de {1} est utilisée. La valeur minimale est 8.","Contrôle si les suggestions devraient automatiquement s’afficher lorsque vous tapez les caractères de déclencheur.","Sélectionnez toujours la première suggestion.","Sélectionnez les suggestions récentes sauf si une entrée ultérieure en a sélectionné une, par ex., 'console.| -> console.log', car 'log' a été effectué récemment.","Sélectionnez des suggestions en fonction des préfixes précédents qui ont complété ces suggestions, par ex., 'co -> console' et 'con -> const'.","Contrôle comment les suggestions sont pré-sélectionnés lors de l’affichage de la liste de suggestion.","La complétion par tabulation insérera la meilleure suggestion lorsque vous appuyez sur tab.","Désactiver les complétions par tabulation.","Compléter les extraits de code par tabulation lorsque leur préfixe correspond. Fonctionne mieux quand les 'quickSuggestions' ne sont pas activées.","Active les complétions par tabulation","Les marques de fin de ligne inhabituelles sont automatiquement supprimées.","Les marques de fin de ligne inhabituelles sont ignorées.","Les marques de fin de ligne inhabituelles demandent à être supprimées.","Supprimez les marques de fin de ligne inhabituelles susceptibles de causer des problèmes.","Les espaces et les onglets sont insérés et supprimés en alignement avec les taquets de tabulation.","Utilisez la règle de saut de ligne par défaut.","Les sauts de mots ne doivent pas être utilisés pour le texte chinois/japonais/coréen (CJC). Le comportement du texte non CJC est identique à celui du texte normal.","Contrôle les règles de séparateur de mots utilisées pour le texte chinois/japonais/coréen (CJC).","Caractères utilisés comme séparateurs de mots durant la navigation ou les opérations basées sur les mots","Le retour automatique à la ligne n'est jamais effectué.","Le retour automatique à la ligne s'effectue en fonction de la largeur de la fenêtre d'affichage.","Les lignes seront terminées à `#editor.wordWrapColumn#`.","Les lignes seront terminées au minimum du viewport et `#editor.wordWrapColumn#`.","Contrôle comment les lignes doivent être limitées.","Contrôle la colonne de terminaison de l’éditeur lorsque `#editor.wordWrap#` est à `wordWrapColumn` ou `bounded`.","Contrôle si les décorations de couleur inline doivent être affichées à l’aide du fournisseur de couleurs de document par défaut","Contrôle si l’éditeur reçoit des onglets ou les reporte au banc d’essai pour la navigation.","Couleur d'arrière-plan de la mise en surbrillance de la ligne à la position du curseur.","Couleur d'arrière-plan de la bordure autour de la ligne à la position du curseur.","Couleur d'arrière-plan des plages mises en surbrillance, comme par les fonctionnalités de recherche et Quick Open. La couleur ne doit pas être opaque pour ne pas masquer les ornements sous-jacents.","Couleur d'arrière-plan de la bordure autour des plages mises en surbrillance.","Couleur d'arrière-plan du symbole mis en surbrillance, comme le symbole Atteindre la définition ou Suivant/Précédent. La couleur ne doit pas être opaque pour ne pas masquer les décorations sous-jacentes.","Couleur d'arrière-plan de la bordure autour des symboles mis en surbrillance.","Couleur du curseur de l'éditeur.","La couleur de fond du curseur de l'éditeur. Permet de personnaliser la couleur d'un caractère survolé par un curseur de bloc.","Couleur du curseur de l’éditeur principal lorsque plusieurs curseurs sont présents.","Couleur d’arrière-plan du curseur de l’éditeur principal lorsque plusieurs curseurs sont présents. Permet de personnaliser la couleur d'un caractère survolé par un curseur de bloc.","Couleur des curseurs de l’éditeur secondaire lorsque plusieurs curseurs sont présents.","Couleur d’arrière-plan des curseurs de l’éditeur secondaire lorsque plusieurs curseurs sont présents. Permet de personnaliser la couleur d'un caractère survolé par un curseur de bloc.","Couleur des espaces blancs dans l'éditeur.","Couleur des numéros de ligne de l'éditeur.","Couleur des repères de retrait de l'éditeur.","'editorIndentGuide.background' est déconseillé. Utilisez 'editorIndentGuide.background1' à la place.","Couleur des guides d'indentation de l'éditeur actif","'editorIndentGuide.activeBackground' est déconseillé. Utilisez 'editorIndentGuide.activeBackground1' à la place.","Couleur des repères de retrait de l'éditeur (1).","Couleur des repères de retrait de l'éditeur (2).","Couleur des repères de retrait de l'éditeur (3).","Couleur des repères de retrait de l'éditeur (4).","Couleur des repères de retrait de l'éditeur (5).","Couleur des repères de retrait de l'éditeur (6).","Couleur des repaires de retrait de l'éditeur actifs (1).","Couleur des repaires de retrait de l'éditeur actifs (2).","Couleur des repaires de retrait de l'éditeur actifs (3).","Couleur des repaires de retrait de l'éditeur actifs (4).","Couleur des repaires de retrait de l'éditeur actifs (5).","Couleur des repaires de retrait de l'éditeur actifs (6).","Couleur des numéros de lignes actives de l'éditeur","L’ID est déprécié. Utilisez à la place 'editorLineNumber.activeForeground'.","Couleur des numéros de lignes actives de l'éditeur","Couleur de la ligne finale de l’éditeur lorsque editor.renderFinalNewline est défini sur grisé.","Couleur des règles de l'éditeur","Couleur pour les indicateurs CodeLens","Couleur d'arrière-plan pour les accolades associées","Couleur pour le contour des accolades associées","Couleur de la bordure de la règle d'aperçu.","Couleur d’arrière-plan de la règle de vue d’ensemble de l’éditeur.","Couleur de fond pour la bordure de l'éditeur. La bordure contient les marges pour les symboles et les numéros de ligne.","Couleur de bordure du code source inutile (non utilisé) dans l'éditeur.","Opacité du code source inutile (non utilisé) dans l'éditeur. Par exemple, '#000000c0' affiche le code avec une opacité de 75 %. Pour les thèmes à fort contraste, utilisez la couleur de thème 'editorUnnecessaryCode.border' pour souligner le code inutile au lieu d'utiliser la transparence.","Couleur de bordure du texte fantôme dans l’éditeur.","Couleur de premier plan du texte fantôme dans l’éditeur.","Couleur de l’arrière-plan du texte fantôme dans l’éditeur","Couleur de marqueur de la règle d'aperçu pour la mise en surbrillance des plages. La couleur ne doit pas être opaque pour ne pas masquer les ornements sous-jacents.","Couleur du marqueur de la règle d'aperçu pour les erreurs.","Couleur du marqueur de la règle d'aperçu pour les avertissements.","Couleur du marqueur de la règle d'aperçu pour les informations.","Couleur de premier plan des crochets (1). Nécessite l’activation de la coloration de la paire de crochets.","Couleur de premier plan des crochets (2). Nécessite l’activation de la coloration de la paire de crochets.","Couleur de premier plan des crochets (3). Nécessite l’activation de la coloration de la paire de crochets.","Couleur de premier plan des crochets (4). Nécessite l’activation de la coloration de la paire de crochets.","Couleur de premier plan des crochets (5). Nécessite l’activation de la coloration de la paire de crochets.","Couleur de premier plan des crochets (6). Nécessite l’activation de la coloration de la paire de crochets.","Couleur de premier plan des parenthèses inattendues","Couleur d’arrière-plan des repères de paire de crochets inactifs (1). Nécessite l’activation des repères de paire de crochets.","Couleur d’arrière-plan des repères de paire de crochets inactifs (2). Nécessite l’activation des repères de paire de crochets.","Couleur d’arrière-plan des repères de paire de crochets inactifs (3). Nécessite l’activation des repères de paire de crochets.","Couleur d’arrière-plan des repères de paire de crochets inactifs (4). Nécessite l’activation des repères de paire de crochets.","Couleur d’arrière-plan des repères de paire de crochets inactifs (5). Nécessite l’activation des repères de paire de crochets.","Couleur d’arrière-plan des repères de paire de crochets inactifs (6). Nécessite l’activation des repères de paire de crochets.","Couleur d’arrière-plan des repères de paire de crochets actifs (1). Nécessite l’activation des repères de paire de crochets.","Couleur d’arrière-plan des repères de paire de crochets actifs (2). Nécessite l’activation des repères de paire de crochets.","Couleur d’arrière-plan des repères de paire de crochets actifs (3). Nécessite l’activation des repères de paire de crochets.","Couleur d’arrière-plan des repères de paire de crochets actifs (4). Nécessite l’activation des repères de paire de crochets.","Couleur d’arrière-plan des repères de paire de crochets actifs (5). Nécessite l’activation des repères de paire de crochets.","Couleur d’arrière-plan des repères de paire de crochets actifs (6). Nécessite l’activation des repères de paire de crochets.","Couleur de bordure utilisée pour mettre en surbrillance les caractères Unicode","Couleur de fond utilisée pour mettre en évidence les caractères unicode","Indique si le texte de l'éditeur a le focus (le curseur clignote)","Indique si l'éditeur ou un widget de l'éditeur a le focus (par exemple, le focus se trouve sur le widget de recherche)","Indique si un éditeur ou une entrée de texte mis en forme a le focus (le curseur clignote)","Indique si l’éditeur est en lecture seule","Indique si le contexte est celui d'un éditeur de différences","Indique si le contexte est celui d’un éditeur de différences intégré",null,"Indique si tous les fichiers de l’éditeur de différences sont réduits","Indique si l’éditeur de différences a des modifications","Indique si un bloc de code déplacé est sélectionné pour être comparé","Indique si la visionneuse diff accessible est visible","Indique si le point d'arrêt Render Side by Side ou inline de l'éditeur de différences est atteint","Indique si le mode inline est actif","Indique si la modification est accessible en écriture dans l’éditeur de différences","Indique si la modification est accessible en écriture dans l’éditeur de différences","URI du document d’origine","URI du document modifié","Indique si 'editor.columnSelection' est activé","Indique si du texte est sélectionné dans l'éditeur","Indique si l'éditeur a plusieurs sélections","Indique si la touche Tab permet de déplacer le focus hors de l'éditeur","Indique si le pointage de l'éditeur est visible","Indique si le pointage de l’éditeur est ciblé","Indique si le défilement du pense-bête a le focus","Indique si le défilement du pense-bête est visible","Indique si le sélecteur de couleurs autonome est visible","Indique si le sélecteur de couleurs autonome est prioritaire","Indique si l'éditeur fait partie d'un éditeur plus important (par exemple Notebooks)","Identificateur de langage de l'éditeur","Indique si l'éditeur a un fournisseur d'éléments de complétion","Indique si l'éditeur a un fournisseur d'actions de code","Indique si l'éditeur a un fournisseur d'informations CodeLens","Indique si l'éditeur a un fournisseur de définitions","Indique si l'éditeur a un fournisseur de déclarations","Indique si l'éditeur a un fournisseur d'implémentation","Indique si l'éditeur a un fournisseur de définitions de type","Indique si l'éditeur a un fournisseur de pointage","Indique si l'éditeur a un fournisseur de mise en surbrillance pour les documents","Indique si l'éditeur a un fournisseur de symboles pour les documents","Indique si l'éditeur a un fournisseur de référence","Indique si l'éditeur a un fournisseur de renommage","Indique si l'éditeur a un fournisseur d'aide sur les signatures","Indique si l'éditeur a un fournisseur d'indicateurs inline","Indique si l'éditeur a un fournisseur de mise en forme pour les documents","Indique si l'éditeur a un fournisseur de mise en forme de sélection pour les documents","Indique si l'éditeur a plusieurs fournisseurs de mise en forme pour les documents","Indique si l'éditeur a plusieurs fournisseurs de mise en forme de sélection pour les documents","tableau","booléen","classe","constante","constructeur","énumération","membre d'énumération","événement","champ","fichier","fonction","interface","clé","méthode","module","espace de noms","NULL","nombre","objet","opérateur","package","propriété","chaîne","struct","paramètre de type","variable","{0} ({1})","Texte brut","Frappe en cours","Développeur : Inspecter les jetons","Accéder à la ligne/colonne...","Afficher tous les fournisseurs d'accès rapide","Palette de commandes","Commandes d'affichage et d'exécution","Accéder au symbole...","Accéder au symbole par catégorie...","Contenu de l'éditeur","Activer/désactiver le thème à contraste élevé","{0} modifications dans {1} fichiers","Afficher plus ({0})","{0} caractères","Ancre de sélection","Ancre définie sur {0}:{1}","Définir l'ancre de sélection","Atteindre l'ancre de sélection","Sélectionner de l'ancre au curseur","Annuler l'ancre de sélection","Couleur du marqueur de la règle d'aperçu pour rechercher des parenthèses.","Atteindre le crochet","Sélectionner jusqu'au crochet","Supprimer les crochets","Accéder au &&crochet","Sélectionner le texte à l’intérieur et inclure les crochets ou accolades","Déplacer le texte sélectionné à gauche","Déplacer le texte sélectionné à droite","Transposer les lettres","Co&&uper","Couper","Couper","Couper","&&Copier","Copier","Copier","Copier","Co&&ller","Coller","Coller","Coller","Copier avec la coloration syntaxique","Copier en tant que","Copier en tant que","Partager","Partager","Une erreur inconnue s'est produite à l'application de l'action du code","Type d'action de code à exécuter.","Contrôle quand les actions retournées sont appliquées.","Appliquez toujours la première action de code retournée.","Appliquez la première action de code retournée si elle est la seule.","N'appliquez pas les actions de code retournées.","Contrôle si seules les actions de code par défaut doivent être retournées.","Correction rapide...","Aucune action de code disponible","Aucune action de code préférée n'est disponible pour '{0}'","Aucune action de code disponible pour '{0}'","Aucune action de code par défaut disponible","Aucune action de code disponible","Remanier...","Aucune refactorisation par défaut disponible pour '{0}'","Aucune refactorisation disponible pour '{0}'","Aucune refactorisation par défaut disponible","Aucune refactorisation disponible","Action de la source","Aucune action source par défaut disponible pour '{0}'","Aucune action source disponible pour '{0}'","Aucune action source par défaut disponible","Aucune action n'est disponible","Organiser les importations","Aucune action organiser les imports disponible","Tout corriger","Aucune action Tout corriger disponible","Corriger automatiquement...","Aucun correctif automatique disponible","Activez/désactivez l’affichage des en-têtes de groupe dans le menu d’action du code.","Activer/désactiver l'affichage du correctif rapide le plus proche dans une ligne lorsque vous n'êtes pas actuellement en cours de diagnostic.","Activez le déclenchement de {0} lorsque {1} est défini sur {2}. Les actions de code doivent être définies sur {3} pour être déclenchées pour les modifications de fenêtre et de focus.","Contexte : {0} à la ligne {1} et à la colonne {2}.","Masquer désactivé","Afficher les éléments désactivés","Plus d’actions...","Correctif rapide","Extraire","Inline","Réécrire","Déplacer","Entourer de","Action source","Icône qui génère le menu Actions de code à partir de la reliure quand il n’y a pas d’espace dans l’éditeur.","Icône qui génère le menu actions de code à partir de la reliure quand l’éditeur n’a pas d’espace et qu’un correctif rapide est disponible.","Icône qui génère le menu actions de code à partir de la reliure quand il n’y a pas d’espace dans l’éditeur et qu’un correctif IA est disponible.","Icône qui génère le menu actions de code à partir de la reliure quand il n’y a pas d’espace dans l’éditeur et qu’un correctif IA et un correctif rapide sont disponibles.","Icône qui génère le menu actions de code à partir de la reliure quand il n’y a pas d’espace dans l’éditeur et qu’un correctif IA et un correctif rapide sont disponibles.","Exécuter : {0}","Afficher les actions de code. Correctif rapide disponible par défaut ({0})","Afficher les actions de code ({0})","Afficher les actions de code","Afficher les commandes Code Lens de la ligne actuelle","Sélectionner une commande",null,null,null,null,null,null,null,null,null,"Activer/désactiver le commentaire de ligne","Afficher/masquer le commen&&taire de ligne","Ajouter le commentaire de ligne","Supprimer le commentaire de ligne","Activer/désactiver le commentaire de bloc","Afficher/masquer le commentaire de &&bloc","Minimap","Afficher les caractères","Taille verticale","Proportionnel","Remplissage","Ajuster","Curseur","Pointer la souris","Toujours","Afficher le menu contextuel de l'éditeur","Annulation du curseur","Restauration du curseur",`Type de la modification de collage à essayer pour les opérations de collage.\r
S’il existe plusieurs modifications de ce type, l’éditeur affiche un sélecteur. S’il n’existe aucune modification de ce type, l’éditeur affiche un message d’erreur.`,"Coller en tant que...","Coller au format texte","Si le widget de collage est affiché","Afficher les options de collage...","Nous n’avons trouvé aucune modification de collage n’a été trouvée pour « {0} »","Résolution de la modification du collage. Cliquez ici pour annuler","Exécution des gestionnaires de collage. Cliquez pour annuler et effectuer un collage de base","Sélectionner l’action Coller","Exécution des gestionnaires de collage","Insérer du texte brut","Insérer des URI","Insérer un URI","Insérer des chemins d’accès","Insérer un chemin d’accès","Insérer des chemins d’accès relatifs","Insérer un chemin d’accès relatif","Insérer du code HTML",null,"Indique si le widget de suppression s’affiche","Afficher les options de suppression...","Exécution des gestionnaires de dépôt. Cliquez pour annuler",`Erreur lors de la résolution de la modification «{0}» :\r
{1}`,`Erreur lors de l’application de la modification «{0}» :\r
{1}`,"Indique si l'éditeur exécute une opération annulable, par exemple 'Avoir un aperçu des références'","Le fichier est trop volumineux pour effectuer une opération Tout remplacer.","Rechercher","&&Rechercher","Trouver avec des arguments","Rechercher dans la sélection","Rechercher suivant","Rechercher précédent","Accéder à la correspondance...","Aucune correspondance. Essayez de rechercher autre chose.","Tapez un nombre pour accéder à une correspondance spécifique (entre 1 et {0})","Veuillez entrer un nombre compris entre 1 et {0}","Veuillez entrer un nombre compris entre 1 et {0}","Sélection suivante","Sélection précédente","Remplacer","&&Remplacer","Icône permettant d'indiquer que le widget de recherche de l'éditeur est réduit.","Icône permettant d'indiquer que le widget de recherche de l'éditeur est développé.","Icône de l'option Rechercher dans la sélection dans le widget de recherche de l'éditeur.","Icône de l'option Remplacer dans le widget de recherche de l'éditeur.","Icône de l'option Tout remplacer dans le widget de recherche de l'éditeur.","Icône de l'option Rechercher précédent dans le widget de recherche de l'éditeur.","Icône de l'option Rechercher suivant dans le widget de recherche de l'éditeur.","Rechercher/remplacer","Rechercher","Rechercher","Correspondance précédente","Correspondance suivante","Rechercher dans la sélection","Fermer","Remplacer","Remplacer","Remplacer","Tout remplacer","Activer/désactiver le remplacement","Seuls les {0} premiers résultats sont mis en évidence, mais toutes les opérations de recherche fonctionnent sur l’ensemble du texte.","{0} sur {1}","Aucun résultat","{0} trouvé(s)","{0} trouvé pour '{1}'","{0} trouvé pour '{1}', sur {2}","{0} trouvé pour '{1}'","La combinaison Ctrl+Entrée permet désormais d'ajouter un saut de ligne au lieu de tout remplacer. Vous pouvez modifier le raccourci clavier de editor.action.replaceAll pour redéfinir le comportement.","Déplier","Déplier de manière récursive","Plier","Activer/désactiver le pliage","Plier de manière récursive","Activer/désactiver le repli récursif","Replier tous les commentaires de bloc","Replier toutes les régions","Déplier toutes les régions","Plier tout, sauf les éléments sélectionnés","Déplier tout, sauf les éléments sélectionnés","Plier tout","Déplier tout","Atteindre le pli parent","Accéder à la plage de pliage précédente","Accéder à la plage de pliage suivante","Créer une plage de pliage à partir de la sélection","Supprimer les plages de pliage manuelles","Niveau de pliage {0}","Couleur d'arrière-plan des gammes pliées. La couleur ne doit pas être opaque pour ne pas cacher les décorations sous-jacentes.","Couleur du texte réduit après la première ligne d’une plage pliée.","Couleur du contrôle de pliage dans la marge de l'éditeur.","Icône des plages développées dans la marge de glyphes de l'éditeur.","Icône des plages réduites dans la marge de glyphes de l'éditeur.","Icône pour les plages réduites manuellement dans la marge de glyphe de l’éditeur.","Icône pour les plages développées manuellement dans la marge de glyphe de l’éditeur.","Cliquez pour développer la plage.","Cliquez pour réduire la plage.","Augmenter la taille de police de l’éditeur","Diminuer la taille de police de l’éditeur","Réinitialiser la taille de police de l’éditeur","Mettre le document en forme","Mettre la sélection en forme","Aller au problème suivant (Erreur, Avertissement, Info)","Icône du prochain marqueur goto.","Aller au problème précédent (Erreur, Avertissement, Info)","Icône du précédent marqueur goto.","Aller au problème suivant dans Fichiers (Erreur, Avertissement, Info)","&&Problème suivant","Aller au problème précédent dans Fichiers (Erreur, Avertissement, Info)","&&Problème précédent","Erreur","Avertissement","Info","Conseil","{0} à {1}. ","{0} problèmes sur {1}","{0} problème(s) sur {1}","Couleur d'erreur du widget de navigation dans les marqueurs de l'éditeur.","Arrière-plan du titre d’erreur du widget de navigation dans les marqueurs de l’éditeur.","Couleur d'avertissement du widget de navigation dans les marqueurs de l'éditeur.","Arrière-plan du titre d’erreur du widget de navigation dans les marqueurs de l’éditeur.","Couleur d’information du widget de navigation du marqueur de l'éditeur.","Arrière-plan du titre des informations du widget de navigation dans les marqueurs de l’éditeur.","Arrière-plan du widget de navigation dans les marqueurs de l'éditeur.","Aperçu","Définitions","Définition introuvable pour '{0}'","Définition introuvable","Atteindre la &&définition","Déclarations","Aucune déclaration pour '{0}'","Aucune déclaration","Atteindre la &&déclaration","Aucune déclaration pour '{0}'","Aucune déclaration","Définitions de type","Définition de type introuvable pour '{0}'","Définition de type introuvable","Accéder à la définition de &&type","Implémentations","Implémentation introuvable pour '{0}'","Implémentation introuvable","Atteindre les &&implémentations","Aucune référence pour '{0}'","Aucune référence","Atteindre les &&références","Références","Références","Emplacements","Aucun résultat pour « {0} »","Références","Atteindre la définition","Ouvrir la définition sur le côté","Aperçu de la définition","Accéder à la déclaration","Aperçu de la déclaration","Atteindre la définition du type","Aperçu de la définition du type","Atteindre les implémentations","Aperçu des implémentations","Atteindre les références","Aperçu des références","Atteindre un symbole","Cliquez pour afficher {0} définitions.","Indique si l'aperçu des références est visible, par exemple via 'Avoir un aperçu des références' ou 'Faire un peek de la définition'","Chargement en cours...","{0} ({1})","{0} références","{0} référence","Références","aperçu non disponible","Aucun résultat","Références","dans {0} à la ligne {1} à la colonne {2}","{0}dans {1} à la ligne {2} à la colonne {3}","1 symbole dans {0}, chemin complet {1}","{0} symboles dans {1}, chemin complet {2}","Résultats introuvables","1 symbole dans {0}","{0} symboles dans {1}","{0} symboles dans {1} fichiers","Indique s'il existe des emplacements de symboles que vous pouvez parcourir à l'aide du clavier uniquement.","Symbole {0} sur {1}, {2} pour le suivant","Symbole {0} sur {1}","Augmenter le niveau de verbosité par pointage","Diminuer le niveau de détail du pointage","Afficher ou focus sur pointer","Le pointage ne prend pas automatiquement le focus.","Le pointage prend le focus uniquement s’il est déjà visible.","Le pointage prend automatiquement le focus lorsqu’il apparaît.","Afficher le pointeur de l'aperçu de définition","Faire défiler le pointage vers le haut","Faire défiler le pointage vers le bas","Faire défiler vers la gauche au pointage","Faire défiler le pointage vers la droite","Pointer vers le haut de la page","Pointer vers le bas de la page","Atteindre le pointage supérieur","Pointer vers le bas","Affichez ou concentrez le pointeur de l’éditeur qui affiche la documentation, les références et d’autres contenus pour un symbole à la position actuelle du curseur.","Afficher l’aperçu de définition survolé dans l’éditeur.","Faites défiler vers le haut le pointage de l’éditeur.","Faites défiler le pointeur de l’éditeur vers le bas.","Faites défiler vers la gauche le pointeur de l’éditeur.","Faites défiler vers la droite le pointeur de l’éditeur.","Page précédente le pointage de l’éditeur.","Page suivante le pointage de l’éditeur.","Accédez au haut du pointage de l’éditeur.","Atteindre le bas du pointage de l’éditeur.","Icône permettant d’augmenter le niveau de détail du pointage.","Icône permettant de réduire le niveau de détail du pointage.","Chargement en cours...","Rendu suspendu pour une longue ligne pour des raisons de performances. Cela peut être configuré via 'editor.stopRenderingLineAfter'.","La tokenisation des lignes longues est ignorée pour des raisons de performances. Cela peut être configurée via 'editor.maxTokenizationLineLength'.","Augmenter la verbosité par pointage ({0})","Augmenter la verbosité par pointage","Diminuer la verbosité par pointage ({0})","Diminuer la verbosité par pointage","Voir le problème","Aucune solution disponible dans l'immédiat","Recherche de correctifs rapides...","Aucune solution disponible dans l'immédiat","Correction rapide...","Convertir les retraits en espaces","Convertir les retraits en tabulations","Taille des tabulations configurée","Taille des tabulations par défaut","Taille actuelle des tabulations","Sélectionner la taille des tabulations pour le fichier actuel","Mettre en retrait avec des tabulations","Mettre en retrait avec des espaces","Modifier la taille d’affichage des tabulations","Détecter la mise en retrait à partir du contenu","Remettre en retrait les lignes","Réindenter les lignes sélectionnées","Convertir la mise en retrait de l’onglet en espaces.","Convertissez la mise en retrait des espaces en onglets.","Utilisez une mise en retrait avec des onglets.","Utilisez la mise en retrait avec des espaces.","Modifier la taille d’espace équivalente à l’onglet.","Détectez la mise en retrait du contenu.","Réinsérez les lignes de l’éditeur.","Réinsérez les lignes sélectionnées de l’éditeur.","Double-cliquer pour insérer","cmd + clic","ctrl + clic","option + clic","alt + clic","Accédez à Définition ({0}), cliquez avec le bouton droit pour en savoir plus.","Accéder à Définition ({0})","Exécuter la commande","Afficher la suggestion en ligne suivante","Afficher la suggestion en ligne précédente","Déclencher une suggestion en ligne","Accepter le mot suivant de la suggestion inline","Accepter Word","Accepter la ligne suivante d’une suggestion en ligne","Accepter la ligne","Accepter la suggestion en ligne","Accepter","Masquer la suggestion en ligne","Toujours afficher la barre d'outils","Indique si une suggestion en ligne est visible","Indique si la suggestion en ligne commence par un espace blanc","Indique si la suggestion incluse commence par un espace blanc inférieur à ce qui serait inséré par l’onglet.","Indique si les suggestions doivent être supprimées pour la suggestion actuelle","Inspecter ceci dans l’affichage accessible ({0})","Suggestion :","Icône d'affichage du prochain conseil de paramètre.","Icône d'affichage du précédent conseil de paramètre.","{0} ({1})","Précédent","Suivant",null,null,null,null,null,null,null,null,"Remplacer par la valeur précédente","Remplacer par la valeur suivante","Développer la sélection de ligne","Copier la ligne en haut","&&Copier la ligne en haut","Copier la ligne en bas","Co&&pier la ligne en bas","Dupliquer la sélection","&&Dupliquer la sélection","Déplacer la ligne vers le haut","Déplacer la ligne &&vers le haut","Déplacer la ligne vers le bas","Déplacer la &&ligne vers le bas","Trier les lignes dans l'ordre croissant","Trier les lignes dans l'ordre décroissant","Supprimer les lignes dupliquées","Découper l'espace blanc de fin","Supprimer la ligne","Mettre en retrait la ligne","Ajouter un retrait négatif à la ligne","Insérer une ligne au-dessus","Insérer une ligne sous","Supprimer tout ce qui est à gauche","Supprimer tout ce qui est à droite","Joindre les lignes","Transposer des caractères autour du curseur","Transformer en majuscule","Transformer en minuscule",'Appliquer la casse "1re lettre des mots en majuscule"',"Transformer en snake case","Transformer en casse mixte","Transformer en casse Pascal","Transformer en kebab case","Démarrer la modification liée","Couleur d'arrière-plan quand l'éditeur renomme automatiquement le type.","Échec de l'ouverture de ce lien, car il n'est pas bien formé : {0}","Échec de l'ouverture de ce lien, car sa cible est manquante.","Exécuter la commande","suivre le lien","cmd + clic","ctrl + clic","option + clic","alt + clic","Exécuter la commande {0}","Ouvrir le lien","Indique si l'éditeur affiche un message inline","Curseur ajouté : {0}","Curseurs ajoutés : {0}","Ajouter un curseur au-dessus","&&Ajouter un curseur au-dessus","Ajouter un curseur en dessous","Aj&&outer un curseur en dessous","Ajouter des curseurs à la fin des lignes","Ajouter des c&&urseurs à la fin des lignes","Ajouter des curseurs en bas","Ajouter des curseurs en haut","Ajouter la sélection à la correspondance de recherche suivante","Ajouter l'occurrence suiva&&nte","Ajouter la sélection à la correspondance de recherche précédente","Ajouter l'occurrence p&&récédente","Déplacer la dernière sélection vers la correspondance de recherche suivante","Déplacer la dernière sélection à la correspondance de recherche précédente","Sélectionner toutes les occurrences des correspondances de la recherche","Sélectionner toutes les &&occurrences","Modifier toutes les occurrences","Focus sur le curseur suivant","Concentre le curseur suivant","Focus sur le curseur précédent","Concentre le curseur précédent","Indicateurs des paramètres Trigger","Icône d'affichage du prochain conseil de paramètre.","Icône d'affichage du précédent conseil de paramètre.","{0}, conseil","Couleur de premier plan de l’élément actif dans l’indicateur de paramètre.","Indique si l'éditeur de code actuel est intégré à l'aperçu","Fermer","Couleur d'arrière-plan de la zone de titre de l'affichage d'aperçu.","Couleur du titre de l'affichage d'aperçu.","Couleur des informations sur le titre de l'affichage d'aperçu.","Couleur des bordures et de la flèche de l'affichage d'aperçu.","Couleur d'arrière-plan de la liste des résultats de l'affichage d'aperçu.","Couleur de premier plan des noeuds de lignes dans la liste des résultats de l'affichage d'aperçu.","Couleur de premier plan des noeuds de fichiers dans la liste des résultats de l'affichage d'aperçu.","Couleur d'arrière-plan de l'entrée sélectionnée dans la liste des résultats de l'affichage d'aperçu.","Couleur de premier plan de l'entrée sélectionnée dans la liste des résultats de l'affichage d'aperçu.","Couleur d'arrière-plan de l'éditeur d'affichage d'aperçu.","Couleur d'arrière-plan de la bordure de l'éditeur d'affichage d'aperçu.","Couleur d’arrière-plan du défilement rémanent dans l’éditeur d’affichage d’aperçu.","Couleur de mise en surbrillance d'une correspondance dans la liste des résultats de l'affichage d'aperçu.","Couleur de mise en surbrillance d'une correspondance dans l'éditeur de l'affichage d'aperçu.","Bordure de mise en surbrillance d'une correspondance dans l'éditeur de l'affichage d'aperçu.","Couleur de premier plan du texte texte de l’espace réservé dans l’éditeur.","Ouvrez d'abord un éditeur de texte pour accéder à une ligne.","Atteindre la ligne {0} et le caractère {1}.","Accédez à la ligne {0}.","Ligne actuelle : {0}, caractère : {1}. Tapez un numéro de ligne entre 1 et {2} auquel accéder.","Ligne actuelle : {0}, caractère : {1}. Tapez un numéro de ligne auquel accéder.","Pour accéder à un symbole, ouvrez d'abord un éditeur de texte avec des informations de symbole.","L'éditeur de texte actif ne fournit pas les informations de symbole.","Aucun symbole d'éditeur correspondant","Aucun symbole d'éditeur","Ouvrir sur le côté","Ouvrir en bas","symboles ({0})","propriétés ({0})","méthodes ({0})","fonctions ({0})","constructeurs ({0})","variables ({0})","classes ({0})","structs ({0})","événements ({0})","opérateurs ({0})","interfaces ({0})","espaces de noms ({0})","packages ({0})","paramètres de type ({0})","modules ({0})","propriétés ({0})","énumérations ({0})","membres d'énumération ({0})","chaînes ({0})","fichiers ({0})","tableaux ({0})","nombres ({0})","booléens ({0})","objets ({0})","clés ({0})","champs ({0})","constantes ({0})","Impossible de modifier dans l’entrée en lecture seule","Impossible de modifier dans l’éditeur en lecture seule","Aucun résultat.","Une erreur inconnue s'est produite lors de la résolution de l'emplacement de renommage","Renommage de '{0}' en '{1}'","Changement du nom de {0} en {1}","'{0}' renommé en '{1}'. Récapitulatif : {2}","Le renommage n'a pas pu appliquer les modifications","Le renommage n'a pas pu calculer les modifications","Renommer le symbole","Activer/désactiver la possibilité d'afficher un aperçu des changements avant le renommage","Prioriser la prochaine suggestion de changement de nom","Prioriser la suggestion de changement de nom précédente","Indique si le widget de renommage d'entrée est visible","Indique si le widget de renommage d'entrée est prioritaire","{0} pour renommer, {1} pour afficher un aperçu","{0} suggestions de changement de nom reçues","Renommez l'entrée. Tapez le nouveau nom et appuyez sur Entrée pour valider.","Générer des suggestions de nom","Annuler","Étendre la sélection","Dév&&elopper la sélection","Réduire la sélection","&&Réduire la sélection","Indique si l'éditeur est actualisé en mode extrait","Indique s'il existe un taquet de tabulation suivant en mode extrait","Indique s'il existe un taquet de tabulation précédent en mode extrait","Accéder à l’espace réservé suivant...","Dimanche","Lundi","Mardi","Mercredi","Jeudi","Vendredi","Samedi","Dim","Lun","Mar","Mer","Jeu","Ven","Sam","Janvier","Février","Mars","Avril","Mai","Juin","Juillet","Août","Septembre","Octobre","Novembre","Décembre","Jan","Fév","Mar","Avr","Mai","Juin","Jul","Aoû","Sept","Oct","Nov","Déc","&&Activer/désactiver le défilement épinglé de l’éditeur","Défilement épinglé","&&Défilement épinglé","&&Focus sur le défilement du pense-bête","Activer/désactiver le défilement épinglé de l’éditeur","Basculer/activer le défilement rémanent de l’éditeur qui affiche les étendues imbriqués en haut de la fenêtre d’affichage","Focus sur le défilement de l’éditeur","Sélectionner la ligne de défilement collante de l’éditeur suivant","Sélectionner la ligne de défilement du pense-bête précédente","Atteindre la ligne de défilement pense-bête prioritaire","Sélectionner l'éditeur","Indique si une suggestion a le focus","Indique si les détails des suggestions sont visibles","Indique s'il existe plusieurs suggestions au choix","Indique si l'insertion de la suggestion actuelle entraîne un changement ou si tout a déjà été tapé","Indique si les suggestions sont insérées quand vous appuyez sur Entrée","Indique si la suggestion actuelle a un comportement d'insertion et de remplacement","Indique si le comportement par défaut consiste à insérer ou à remplacer","Indique si la suggestion actuelle prend en charge la résolution des détails supplémentaires","L'acceptation de '{0}' a entraîné {1} modifications supplémentaires","Suggestions pour Trigger","Insérer","Insérer","Remplacer","Remplacer","Insérer","Afficher moins","Afficher plus","Réinitialiser la taille du widget de suggestion","Couleur d'arrière-plan du widget de suggestion.","Couleur de bordure du widget de suggestion.","Couleur de premier plan du widget de suggestion.","Couleur de premier plan de l’entrée sélectionnée dans le widget de suggestion.","Couleur de premier plan de l’icône de l’entrée sélectionnée dans le widget de suggestion.","Couleur d'arrière-plan de l'entrée sélectionnée dans le widget de suggestion.","Couleur de la surbrillance des correspondances dans le widget de suggestion.","Couleur des mises en surbrillance dans le widget de suggestion lorsqu’un élément a le focus.","Couleur de premier plan du statut du widget de suggestion.","Chargement en cours...","Pas de suggestions.","Suggérer","{0} {1}, {2}","{0} {1}","{0}, {1}","{0}, documents : {1}","Fermer","Chargement en cours...","Icône d'affichage d'informations supplémentaires dans le widget de suggestion.","Lire la suite","Couleur de premier plan des symboles de tableau. Ces symboles apparaissent dans le plan, la barre de navigation et le widget de suggestion.","Couleur de premier plan des symboles booléens. Ces symboles apparaissent dans le plan, la barre de navigation et le widget de suggestion.","Couleur de premier plan des symboles de classe. Ces symboles apparaissent dans le plan, la barre de navigation et le widget de suggestion.","Couleur de premier plan des symboles de couleur. Ces symboles apparaissent dans le plan, la barre de navigation et le widget de suggestion.","Couleur de premier plan pour les symboles de constante. Ces symboles apparaissent dans le plan, la barre de navigation et le widget de suggestion.","Couleur de premier plan des symboles de constructeur. Ces symboles apparaissent dans le plan, la barre de navigation et le widget de suggestion.","Couleur de premier plan des symboles d'énumérateur. Ces symboles apparaissent dans le plan, la barre de navigation et le widget de suggestion.","Couleur de premier plan des symboles de membre d'énumérateur. Ces symboles apparaissent dans le plan, la barre de navigation et le widget de suggestion.","Couleur de premier plan des symboles d'événement. Ces symboles apparaissent dans le plan, la barre de navigation et le widget de suggestion.","Couleur de premier plan des symboles de champ. Ces symboles apparaissent dans le plan, la barre de navigation et le widget de suggestion.","Couleur de premier plan des symboles de fichier. Ces symboles apparaissent dans le plan, la barre de navigation et le widget de suggestion.","Couleur de premier plan des symboles de dossier. Ces symboles apparaissent dans le plan, la barre de navigation et le widget de suggestion.","Couleur de premier plan des symboles de fonction. Ces symboles apparaissent dans le plan, la barre de navigation et le widget de suggestion.","Couleur de premier plan des symboles d'interface. Ces symboles apparaissent dans le plan, la barre de navigation et le widget de suggestion.","Couleur de premier plan des symboles de clé. Ces symboles apparaissent dans le plan, la barre de navigation et le widget de suggestion.","Couleur de premier plan des symboles de mot clé. Ces symboles apparaissent dans le plan, la barre de navigation et le widget de suggestion.","Couleur de premier plan des symboles de méthode. Ces symboles apparaissent dans le plan, la barre de navigation et le widget de suggestion.","Couleur de premier plan des symboles de module. Ces symboles apparaissent dans le plan, la barre de navigation et le widget de suggestion.","Couleur de premier plan des symboles d'espace de noms. Ces symboles apparaissent dans le plan, la barre de navigation et le widget de suggestion.","Couleur de premier plan des symboles null. Ces symboles apparaissent dans le plan, la barre de navigation et le widget de suggestion.","Couleur de premier plan des symboles de nombre. Ces symboles apparaissent dans le plan, la barre de navigation et le widget de suggestion.","Couleur de premier plan des symboles d'objet. Ces symboles apparaissent dans le plan, la barre de navigation et le widget de suggestion.","Couleur de premier plan des symboles d'opérateur. Ces symboles apparaissent dans le plan, la barre de navigation et le widget de suggestion.","Couleur de premier plan des symboles de package. Ces symboles apparaissent dans le plan, la barre de navigation et le widget de suggestion.","Couleur de premier plan des symboles de propriété. Ces symboles apparaissent dans le plan, la barre de navigation et le widget de suggestion.","Couleur de premier plan des symboles de référence. Ces symboles apparaissent dans le plan, la barre de navigation et le widget de suggestion.","Couleur de premier plan des symboles d'extrait de code. Ces symboles apparaissent dans le plan, la barre de navigation et le widget de suggestion.","Couleur de premier plan des symboles de chaîne. Ces symboles apparaissent dans le plan, la barre de navigation et le widget de suggestion.","Couleur de premier plan des symboles de struct. Ces symboles apparaissent dans le plan, la barre de navigation et le widget de suggestion.","Couleur de premier plan des symboles de texte. Ces symboles apparaissent dans le plan, la barre de navigation et le widget de suggestion.","Couleur de premier plan des symboles de paramètre de type. Ces symboles apparaissent dans le plan, la barre de navigation et le widget de suggestion.","Couleur de premier plan des symboles d'unité. Ces symboles apparaissent dans le plan, la barre de navigation et le widget de suggestion.","Couleur de premier plan des symboles de variable. Ces symboles apparaissent dans le plan, la barre de navigation et le widget de suggestion.","Appuyer sur Tab déplacera le focus vers le prochain élément pouvant être désigné comme élément actif","Appuyer sur Tab insérera le caractère de tabulation","Activer/désactiver l'utilisation de la touche Tab pour déplacer le focus","Détermine si la touche d’onglet déplace le focus autour du workbench ou insère le caractère d’onglet dans l’éditeur actuel. Il s’agit également du verrouillage des onglets, de la navigation dans les onglets ou du mode focus des onglets.","Développeur : forcer la retokenisation","Icône affichée avec un message d'avertissement dans l'éditeur d'extensions.","Ce document contient de nombreux caractères Unicode ASCII non basiques.","Ce document contient de nombreux caractères Unicode ambigus.","Ce document contient de nombreux caractères Unicode invisibles.","Configurer les options de surlignage Unicode","Le caractère {0} peut être confondu avec le caractère ASCII {1}, qui est plus courant dans le code source.","Le caractère {0} peut être confus avec le caractère {1}, ce qui est plus courant dans le code source.","Le caractère {0} est invisible.","Le caractère {0} n’est pas un caractère ASCII de base.","Ajuster les paramètres","Désactiver la mise en surbrillance dans les commentaires","Désactiver la mise en surbrillance des caractères dans les commentaires","Désactiver la mise en surbrillance dans les chaînes","Désactiver la mise en surbrillance des caractères dans les chaînes","Désactiver la mise en surbrillance ambiguë","Désactiver la mise en surbrillance des caractères ambigus","Désactiver le surlignage invisible","Désactiver la mise en surbrillance des caractères invisibles","Désactiver la mise en surbrillance non ASCII","Désactiver la mise en surbrillance des caractères ASCII non de base","Afficher les options d’exclusion","Exclure la mise en surbrillance des {0} (caractère invisible)","Exclure {0} de la mise en surbrillance",'Autoriser les caractères Unicode plus courants dans le langage "{0}"',"Marques de fin de ligne inhabituelles","Marques de fin de ligne inhabituelles détectées","Le fichier « {0} »contient un ou plusieurs caractères de fin de ligne inhabituels, par exemple le séparateur de ligne (LS) ou le séparateur de paragraphe (PS).\r\n\r\nIl est recommandé de les supprimer du fichier. Vous pouvez configurer ce comportement par le biais de `editor.unusualLineTerminators`.","&&Supprimer les marques de fin de ligne inhabituelles","Ignorer","Couleur d'arrière-plan d'un symbole pendant l'accès en lecture, comme la lecture d'une variable. La couleur ne doit pas être opaque pour ne pas masquer les ornements sous-jacents.","Couleur d'arrière-plan d'un symbole pendant l'accès en écriture, comme l'écriture d'une variable. La couleur ne doit pas être opaque pour ne pas masquer les ornements sous-jacents.","Couleur d’arrière-plan d’une occurrence textuelle d’un symbole. La couleur ne doit pas être opaque afin de ne pas masquer les décorations sous-jacentes.","Couleur de bordure d'un symbole durant l'accès en lecture, par exemple la lecture d'une variable.","Couleur de bordure d'un symbole durant l'accès en écriture, par exemple l'écriture dans une variable.","Couleur de bordure d’une occurrence textuelle pour un symbole.","Couleur de marqueur de la règle d'aperçu pour la mise en surbrillance des symboles. La couleur ne doit pas être opaque pour ne pas masquer les ornements sous-jacents.","Couleur de marqueur de la règle d'aperçu pour la mise en surbrillance des symboles d'accès en écriture. La couleur ne doit pas être opaque pour ne pas masquer les ornements sous-jacents.","Couleur de marqueur de règle d’aperçu d’une occurrence textuelle pour un symbole. La couleur ne doit pas être opaque afin de ne pas masquer les décorations sous-jacentes.","Aller à la prochaine mise en évidence de symbole","Aller à la mise en évidence de symbole précédente","Déclencher la mise en évidence de symbole","Supprimer le mot","Erreur sur la position","Erreur","Avertissement à la position","Avertissement","Erreur sur la ligne","Erreur sur la ligne","Avertissement sur la ligne","Avertissement sur la ligne","Zone pliée sur la ligne","Replié","Point d’arrêt sur ligne","Point d’arrêt","Suggestion inline sur la ligne","Correctif rapide de terminal","Correctif rapide","Débogueur arrêté sur le point d’arrêt","Point d’arrêt","Aucun indicateur d’inlay sur la ligne","Aucun conseil Inlay","Tâche terminée","Tâche terminée","Échec de la tâche","Échec de la tâche","Échec de la commande de terminal","Échec de la commande","Commande terminal réussie","Commande réussie","Cloche de terminal","Cloche de terminal","Cellule de bloc-notes terminée","Cellule de bloc-notes terminée","Échec de la cellule de bloc-notes","Échec de la cellule de bloc-notes","Ligne de diffusion insérée","Ligne de diffusion supprimée","Ligne diff modifiée","Demande de conversation envoyée","Requête de conversation envoyée","Réponse de conversation reçue","Progression","Progression","Effacer","Effacer","Enregistrer","Enregistrer","Format","Format","Enregistrement vocal commencé","Enregistrement vocal arrêté","Afficher","Aide","Test","fichier","Préférences","Développeur","{0} ({1})","{0} ({1})",`{0}\r
[{1}] {2}`,"Du {1} au {0}","{0} ({1})","Masquer","Réinitialiser le menu","Masquer «{0}»","Configurer la combinaison de touches","{0} à Appliquer, {1} à la Préversion","{0} pour appliquer","{0}, raison désactivée : {1}","Widget d’action","Couleur d'arrière-plan des éléments d'action activés dans la barre d'action.","Indique si la liste des widgets d’action est visible","Masquer le widget d’action","Sélectionner l’action précédente","Sélectionner l’action suivante","Accepter l’action sélectionnée","Aperçu de l’action sélectionnée","Substitutions de configuration du langage par défaut","Configurez les paramètres à remplacer pour le langage {0}.","Configurez les paramètres d'éditeur à remplacer pour un langage.","Ce paramètre ne prend pas en charge la configuration par langage.","Configurez les paramètres d'éditeur à remplacer pour un langage.","Ce paramètre ne prend pas en charge la configuration par langage.","Impossible d'inscrire une propriété vide","Impossible d'inscrire '{0}'. Ceci correspond au modèle de propriété '\\\\[.*\\\\]$' permettant de décrire les paramètres d'éditeur spécifiques à un langage. Utilisez la contribution 'configurationDefaults'.","Impossible d'inscrire '{0}'. Cette propriété est déjà inscrite.","Impossible d’inscrire '{0}'. Le {1} de stratégie associé est déjà inscrit auprès de {2}.","Commande qui retourne des informations sur les clés de contexte","Expression de clé de contexte vide","Avez-vous oublié d’écrire une expression ? Vous pouvez également placer 'false' ou 'true' pour toujours donner la valeur false ou true, respectivement.","'in' après 'not'.","parenthèse fermante ')'","Jeton inattendu","Avez-vous oublié de placer && ou || avant le jeton ?","Fin d’expression inattendue","Avez-vous oublié de placer une clé de contexte ?",`Attendu : {0}\r
Reçu : '{1}'.`,"Indique si le système d'exploitation est macOS","Indique si le système d'exploitation est Linux","Indique si le système d'exploitation est Windows","Indique si la plateforme est un navigateur web","Indique si le système d'exploitation est macOS sur une plateforme qui n'est pas un navigateur","Indique si le système d’exploitation est Linux","Indique si la plateforme est un navigateur web mobile","Type de qualité de VS Code","Indique si le focus clavier se trouve dans une zone d'entrée","Voulez-vous dire {0}?","Voulez-vous dire {0} ou {1}?","Voulez-vous dire {0}, {1} ou {2}?","Avez-vous oublié d’ouvrir ou de fermer le devis ?","Avez-vous oublié d’échapper le caractère « / » (barre oblique) ? Placez deux barre obliques inverses avant d’y échapper, par ex., « \\\\/ ».","Indique si les suggestions sont visibles","Touche ({0}) utilisée. En attente d'une seconde touche...","({0}) a été enfoncé. En attente de la touche suivante de la pression...","La combinaison de touches ({0}, {1}) n’est pas une commande.","La combinaison de touches ({0}, {1}) n’est pas une commande.","Banc d'essai","Mappe vers 'Contrôle' dans Windows et Linux, et vers 'Commande' dans macOS.","Mappe vers 'Alt' dans Windows et Linux, et vers 'Option' dans macOS.","Le modificateur à utiliser pour ajouter un élément dans les arbres et listes pour une sélection multiple avec la souris (par exemple dans l’Explorateur, les éditeurs ouverts et la vue scm). Les mouvements de la souris 'Ouvrir à côté' (si pris en charge) s'adapteront tels qu’ils n'entrent pas en conflit avec le modificateur multiselect.","Contrôle l'ouverture des éléments dans les arborescences et les listes à l'aide de la souris (si cela est pris en charge). Notez que certaines arborescences et listes peuvent choisir d'ignorer ce paramètre, s'il est non applicable.","Contrôle si les listes et les arborescences prennent en charge le défilement horizontal dans le banc d'essai. Avertissement : L'activation de ce paramètre a un impact sur les performances.","Contrôle si les clics dans la barre de défilement page par page.","Contrôle la mise en retrait de l'arborescence, en pixels.","Contrôle si l'arborescence doit afficher les repères de mise en retrait.","Détermine si les listes et les arborescences ont un défilement fluide.","Un multiplicateur à utiliser sur les `deltaX` et `deltaY` des événements de défilement de roulette de souris.","Multiplicateur de vitesse de défilement quand vous appuyez sur 'Alt'.","Mettez en surbrillance les éléments lors de la recherche. La navigation vers le haut et le bas traverse uniquement les éléments en surbrillance.","Filtrez des éléments lors de la recherche.","Contrôle le mode de recherche par défaut pour les listes et les arborescences dans Workbench.","La navigation au clavier Simple place le focus sur les éléments qui correspondent à l'entrée de clavier. La mise en correspondance est effectuée sur les préfixes uniquement.","La navigation de mise en surbrillance au clavier met en surbrillance les éléments qui correspondent à l'entrée de clavier. La navigation ultérieure vers le haut ou vers le bas parcourt uniquement les éléments mis en surbrillance.","La navigation au clavier Filtrer filtre et masque tous les éléments qui ne correspondent pas à l'entrée de clavier.","Contrôle le style de navigation au clavier pour les listes et les arborescences dans le banc d'essai. Les options sont Simple, Mise en surbrillance et Filtrer.","Utilisez 'workbench.list.defaultFindMode' et 'workbench.list.typeNavigationMode' à la place.","Utilisez la correspondance approximative lors de la recherche.","Utilisez des correspondances contiguës lors de la recherche.","Contrôle le type de correspondance utilisé lors de la recherche de listes et d’arborescences dans le banc d’essai.","Contrôle la façon dont les dossiers de l'arborescence sont développés quand vous cliquez sur les noms de dossiers. Notez que certaines arborescences et listes peuvent choisir d'ignorer ce paramètre, s'il est non applicable.","Contrôle si le défilement rémanent est activé dans les arborescences.","Contrôle le nombre d’éléments rémanents affichés dans l’arborescence lorsque {0} est activé.","Contrôle le fonctionnement de la navigation de type dans les listes et les arborescences du banc d'essai. Quand la valeur est 'trigger', la navigation de type commence une fois que la commande 'list.triggerTypeNavigation' est exécutée.","Erreur","Avertissement","Info","récemment utilisées","commandes similaires","utilisés le plus souvent","autres commandes","commandes similaires","{0}, {1}","La commande « {0} » a entraîné une erreur","{0}, {1}","Indique si le focus clavier se trouve à l’intérieur du contrôle d’entrée rapide","Type de l’entrée rapide actuellement visible","Indique si le curseur de l’entrée rapide se trouve à la fin de la zone d’entrée","Précédent","Appuyez sur 'Entrée' pour confirmer votre saisie, ou sur 'Échap' pour l'annuler","{0}/{1}","Taper pour affiner les résultats.","Utilisé dans le contexte de la sélection rapide. Si vous modifiez une combinaison de touches pour cette commande, vous devez également modifier toutes les autres combinaisons de touches (variantes de modification) de cette commande.","Si nous sommes en mode d’accès rapide, vous accédez à l’élément suivant. Si nous ne sommes pas en mode d’accès rapide, cela permet d’accéder au séparateur suivant.","Si nous sommes en mode d’accès rapide, vous accédez à l’élément précédent. Si nous ne sommes pas en mode d’accès rapide, cela permet d’accéder au séparateur précédent.","Activer/désactiver toutes les cases à cocher","{0} résultats","{0} Sélectionnés","OK","Personnalisé","Précédent ({0})","Retour","Entrée rapide","Cliquer pour exécuter la commande '{0}'","Couleur de premier plan globale. Cette couleur est utilisée si elle n'est pas remplacée par un composant.","Premier plan globale pour les éléments désactivés. Cette couleur est utilisée si elle n'est pas remplacée par un composant.","Couleur principale de premier plan pour les messages d'erreur. Cette couleur est utilisée uniquement si elle n'est pas redéfinie par un composant.","Couleur de premier plan du texte descriptif fournissant des informations supplémentaires, par exemple pour un label.","Couleur par défaut des icônes du banc d'essai.","Couleur de bordure globale des éléments ayant le focus. Cette couleur est utilisée si elle n'est pas remplacée par un composant.","Bordure supplémentaire autour des éléments pour les séparer des autres et obtenir un meilleur contraste.","Bordure supplémentaire autour des éléments actifs pour les séparer des autres et obtenir un meilleur contraste.","La couleur d'arrière-plan des sélections de texte dans le banc d'essai (par ex., pour les champs d'entrée ou les zones de texte). Notez que cette couleur ne s'applique pas aux sélections dans l'éditeur et le terminal.","Couleur des liens dans le texte.","Couleur de premier plan pour les liens dans le texte lorsqu'ils sont cliqués ou survolés.","Couleur pour les séparateurs de texte.","Couleur des segments de texte préformatés.","Couleur d'arrière-plan pour les segments de texte préformatés.","Couleur d'arrière-plan des citations dans le texte.","Couleur de bordure des citations dans le texte.","Couleur d'arrière-plan des blocs de code dans le texte.","Couleur de premier plan utilisée dans les graphiques.","Couleur utilisée pour les lignes horizontales dans les graphiques.","Couleur rouge utilisée dans les visualisations de graphiques.","Couleur bleue utilisée dans les visualisations de graphiques.","Couleur jaune utilisée dans les visualisations de graphiques.","Couleur orange utilisée dans les visualisations de graphiques.","Couleur verte utilisée dans les visualisations de graphiques.","Couleur violette utilisée dans les visualisations de graphiques.","Couleur d'arrière-plan de l'éditeur.","Couleur de premier plan par défaut de l'éditeur.","Couleur d’arrière-plan du défilement épinglé dans l’éditeur","Couleur d’arrière-plan du défilement épinglé lors du pointage pour l’éditeur","Couleur de bordure du défilement épinglé dans l’éditeur"," Couleur d’ombre du défilement épinglé dans l’éditeur","Couleur d'arrière-plan des gadgets de l'éditeur tels que rechercher/remplacer.","Couleur de premier plan des widgets de l'éditeur, notamment Rechercher/remplacer.","Couleur de bordure des widgets de l'éditeur. La couleur est utilisée uniquement si le widget choisit d'avoir une bordure et si la couleur n'est pas remplacée par un widget.","Couleur de bordure de la barre de redimensionnement des widgets de l'éditeur. La couleur est utilisée uniquement si le widget choisit une bordure de redimensionnement et si la couleur n'est pas remplacée par un widget.","Couleur d'arrière-plan du texte d'erreur dans l'éditeur. La couleur ne doit pas être opaque pour ne pas masquer les décorations sous-jacentes.","Couleur de premier plan de la ligne ondulée marquant les erreurs dans l'éditeur.","Si cette option est définie, couleur des doubles soulignements pour les erreurs dans l’éditeur.","Couleur d'arrière-plan du texte d'avertissement dans l'éditeur. La couleur ne doit pas être opaque pour ne pas masquer les décorations sous-jacentes.","Couleur de premier plan de la ligne ondulée marquant les avertissements dans l'éditeur.","Si cette option est définie, couleur des doubles soulignements pour les avertissements dans l’éditeur.","Couleur d'arrière-plan du texte d'information dans l'éditeur. La couleur ne doit pas être opaque pour ne pas masquer les décorations sous-jacentes.","Couleur de premier plan de la ligne ondulée marquant les informations dans l'éditeur.","Si cette option est définie, couleur des doubles soulignements pour les informations dans l’éditeur.","Couleur de premier plan de la ligne ondulée d'indication dans l'éditeur.","Si cette option est définie, couleur des doubles soulignements pour les conseils dans l’éditeur.","Couleur des liens actifs.","Couleur de la sélection de l'éditeur.","Couleur du texte sélectionné pour le contraste élevé.","Couleur de la sélection dans un éditeur inactif. La couleur ne doit pas être opaque pour ne pas masquer les ornements sous-jacents.","Couleur des régions dont le contenu est le même que celui de la sélection. La couleur ne doit pas être opaque pour ne pas masquer les ornements sous-jacents.","Couleur de bordure des régions dont le contenu est identique à la sélection.","Couleur du résultat de recherche actif.","Couleur du texte du résultat de recherche actif.","Couleur des autres correspondances de recherche. La couleur ne doit pas être opaque pour ne pas masquer les ornements sous-jacents.","Couleur de premier plan des autres résultats de recherche.","Couleur de la plage limitant la recherche. La couleur ne doit pas être opaque pour ne pas masquer les ornements sous-jacents.","Couleur de bordure du résultat de recherche actif.","Couleur de bordure des autres résultats de recherche.","Couleur de bordure de la plage limitant la recherche. La couleur ne doit pas être opaque pour ne pas masquer les ornements sous-jacents.","Surlignage sous le mot sélectionné par pointage. La couleur ne doit pas être opaque pour ne pas masquer les ornements sous-jacents.","Couleur d'arrière-plan du pointage de l'éditeur.","Couleur de premier plan du pointage de l'éditeur.","Couleur de bordure du pointage de l'éditeur.","Couleur d'arrière-plan de la barre d'état du pointage de l'éditeur.","Couleur de premier plan des indicateurs inline","Couleur d'arrière-plan des indicateurs inline","Couleur de premier plan des indicateurs inline pour les types","Couleur d'arrière-plan des indicateurs inline pour les types","Couleur de premier plan des indicateurs inline pour les paramètres","Couleur d'arrière-plan des indicateurs inline pour les paramètres","Couleur utilisée pour l'icône d'ampoule suggérant des actions.","Couleur utilisée pour l'icône d'ampoule suggérant des actions de correction automatique.","La couleur utilisée pour l’icône AI de l’ampoule.","Couleur d’arrière-plan de mise en surbrillance d’un extrait tabstop.","Couleur de bordure de mise en surbrillance d’un extrait tabstop.","Couleur d’arrière-plan de mise en surbrillance du tabstop final d’un extrait.","Mettez en surbrillance la couleur de bordure du dernier taquet de tabulation d'un extrait de code.","Couleur d'arrière-plan du texte inséré. La couleur ne doit pas être opaque pour ne pas masquer les ornements sous-jacents.","Couleur d'arrière-plan du texte supprimé. La couleur ne doit pas être opaque pour ne pas masquer les ornements sous-jacents.","Couleur d'arrière-plan des lignes insérées. La couleur ne doit pas être opaque pour ne pas masquer les ornements sous-jacents.","Couleur d'arrière-plan des lignes supprimées. La couleur ne doit pas être opaque pour ne pas masquer les ornements sous-jacents.","Couleur d’arrière-plan de la marge où les lignes ont été insérées","Couleur d’arrière-plan de la marge où les lignes ont été supprimées","Premier plan de la règle de vue d’ensemble des différences pour le contenu inséré","Premier plan de la règle de vue d’ensemble des différences pour le contenu supprimé","Couleur de contour du texte inséré.","Couleur de contour du texte supprimé.","Couleur de bordure entre les deux éditeurs de texte.","Couleur du remplissage diagonal de l'éditeur de différences. Le remplissage diagonal est utilisé dans les vues de différences côte à côte.","Couleur d’arrière-plan des blocs inchangés dans l’éditeur de différences.","Couleur de premier plan des blocs inchangés dans l’éditeur de différences.","Couleur d’arrière-plan du code inchangé dans l’éditeur de différences.","Couleur de l'ombre des widgets, comme rechercher/remplacer, au sein de l'éditeur.","Couleur de bordure des widgets, comme rechercher/remplacer au sein de l'éditeur.","Arrière-plan de la barre d’outils lors du survol des actions à l’aide de la souris","Contour de la barre d’outils lors du survol des actions à l’aide de la souris","Arrière-plan de la barre d’outils quand la souris est maintenue sur des actions","Couleur des éléments de navigation avec le focus.","Couleur de fond des éléments de navigation.","Couleur des éléments de navigation avec le focus.","Couleur des éléments de navigation sélectionnés.","Couleur de fond du sélecteur d’élément de navigation.","Arrière-plan d'en-tête actuel dans les conflits de fusion inline. La couleur ne doit pas être opaque pour ne pas masquer les ornements sous-jacents.","Arrière-plan de contenu actuel dans les conflits de fusion inline. La couleur ne doit pas être opaque pour ne pas masquer les ornements sous-jacents.","Arrière-plan d'en-tête entrant dans les conflits de fusion inline. La couleur ne doit pas être opaque pour ne pas masquer les ornements sous-jacents.","Arrière-plan de contenu entrant dans les conflits de fusion inline. La couleur ne doit pas être opaque pour ne pas masquer les ornements sous-jacents.","Arrière-plan d'en-tête de l'ancêtre commun dans les conflits de fusion inline. La couleur ne doit pas être opaque pour ne pas masquer les ornements sous-jacents.","Arrière-plan de contenu de l'ancêtre commun dans les conflits de fusion inline. La couleur ne doit pas être opaque pour ne pas masquer les ornements sous-jacents.","Couleur de bordure des en-têtes et du séparateur dans les conflits de fusion inline.","Premier plan de la règle d'aperçu actuelle pour les conflits de fusion inline.","Premier plan de la règle d'aperçu entrante pour les conflits de fusion inline.","Arrière-plan de la règle d'aperçu de l'ancêtre commun dans les conflits de fusion inline.","Couleur de marqueur de la règle d'aperçu pour rechercher les correspondances. La couleur ne doit pas être opaque pour ne pas masquer les ornements sous-jacents.","Couleur de marqueur de la règle d'aperçu pour la mise en surbrillance des sélections. La couleur ne doit pas être opaque pour ne pas masquer les ornements sous-jacents.","Couleur utilisée pour l'icône d'erreur des problèmes.","Couleur utilisée pour l'icône d'avertissement des problèmes.","Couleur utilisée pour l'icône d'informations des problèmes.","Arrière-plan de la zone d'entrée.","Premier plan de la zone d'entrée.","Bordure de la zone d'entrée.","Couleur de la bordure des options activées dans les champs d'entrée.","Couleur d'arrière-plan des options activées dans les champs d'entrée.","Couleur de pointage d’arrière-plan des options dans les champs d’entrée.","Couleur de premier plan des options activées dans les champs d'entrée.","Couleur de premier plan de la zone d'entrée pour le texte d'espace réservé.","Couleur d'arrière-plan de la validation d'entrée pour la gravité des informations.","Couleur de premier plan de validation de saisie pour la sévérité Information.","Couleur de bordure de la validation d'entrée pour la gravité des informations.","Couleur d'arrière-plan de la validation d'entrée pour la gravité de l'avertissement.","Couleur de premier plan de la validation de la saisie pour la sévérité Avertissement.","Couleur de bordure de la validation d'entrée pour la gravité de l'avertissement.","Couleur d'arrière-plan de la validation d'entrée pour la gravité de l'erreur.","Couleur de premier plan de la validation de saisie pour la sévérité Erreur.","Couleur de bordure de la validation d'entrée pour la gravité de l'erreur. ","Arrière-plan de la liste déroulante.","Arrière-plan de la liste déroulante.","Premier plan de la liste déroulante.","Bordure de la liste déroulante.","Couleur de premier plan du bouton.","Couleur du séparateur de boutons.","Couleur d'arrière-plan du bouton.","Couleur d'arrière-plan du bouton pendant le pointage.","Couleur de bordure du bouton.","Couleur de premier plan du bouton secondaire.","Couleur d'arrière-plan du bouton secondaire.","Couleur d'arrière-plan du bouton secondaire au moment du pointage.","Couleur de premier plan de l’option radio active.","Couleur d’arrière-plan de l’option radio active.","Couleur de bordure de l’option radio active.","Couleur de premier plan de l’option radio inactive.","Couleur d’arrière-plan de l’option radio inactive.","Couleur de bordure de l’option radio inactive.","Couleur d’arrière-plan de l’option radio active inactive lors du pointage.","Couleur de fond du widget Case à cocher.","Couleur d’arrière-plan du widget de case à cocher lorsque l’élément dans lequel il se trouve est sélectionné.","Couleur de premier plan du widget Case à cocher.","Couleur de bordure du widget Case à cocher.","Couleur de bordure du widget de case à cocher lorsque l’élément dans lequel il se trouve est sélectionné.","Couleur d’arrière-plan d’étiquette de combinaison de touches. L’étiquette est utilisée pour représenter un raccourci clavier.","Couleur de premier plan d’étiquette de combinaison de touches. L’étiquette est utilisée pour représenter un raccourci clavier.","Couleur de bordure de la combinaison de touches. L’étiquette est utilisée pour représenter un raccourci clavier.","Couleur de bordure du bas d’étiquette de combinaison de touches. L’étiquette est utilisée pour représenter un raccourci clavier.","Couleur d'arrière-plan de la liste/l'arborescence pour l'élément ayant le focus quand la liste/l'arborescence est active. Une liste/arborescence active peut être sélectionnée au clavier, elle ne l'est pas quand elle est inactive.","Couleur de premier plan de la liste/l'arborescence pour l'élément ayant le focus quand la liste/l'arborescence est active. Une liste/arborescence active peut être sélectionnée au clavier, elle ne l'est pas quand elle est inactive.","Couleur de contour de la liste/l'arborescence pour l'élément ayant le focus quand la liste/l'arborescence est active. Une liste/arborescence active a le focus clavier, contrairement à une liste/arborescence inactive.","Couleur de contour de liste/arborescence pour l’élément ciblé lorsque la liste/l’arborescence est active et sélectionnée. Une liste/arborescence active dispose d’un focus clavier, ce qui n’est pas le cas d’une arborescence inactive.","Couleur d'arrière-plan de la liste/l'arborescence de l'élément sélectionné quand la liste/l'arborescence est active. Une liste/arborescence active peut être sélectionnée au clavier, elle ne l'est pas quand elle est inactive.","Couleur de premier plan de la liste/l'arborescence pour l'élément sélectionné quand la liste/l'arborescence est active. Une liste/arborescence active peut être sélectionnée au clavier, elle ne l'est pas quand elle est inactive.","Couleur de premier plan de l’icône Liste/l'arborescence pour l'élément sélectionné quand la liste/l'arborescence est active. Une liste/arborescence active peut être sélectionnée au clavier, elle ne l'est pas quand elle est inactive.","Couleur d'arrière-plan de la liste/l'arborescence pour l'élément sélectionné quand la liste/l'arborescence est inactive. Une liste/arborescence active peut être sélectionnée au clavier, elle ne l'est pas quand elle est inactive.","Couleur de premier plan de la liste/l'arborescence pour l'élément sélectionné quand la liste/l'arborescence est inactive. Une liste/arborescence active peut être sélectionnée au clavier, elle ne l'est pas quand elle est inactive.","Couleur de premier plan de l’icône Liste/l'arborescence pour l'élément sélectionné quand la liste/l'arborescence est inactive. Une liste/arborescence active peut être sélectionnée au clavier, elle ne l'est pas quand elle est inactive.","Couleur d'arrière-plan de la liste/l'arborescence pour l'élément ayant le focus quand la liste/l'arborescence est active. Une liste/arborescence active peut être sélectionnée au clavier (elle ne l'est pas quand elle est inactive).","Couleur de contour de la liste/l'arborescence pour l'élément ayant le focus quand la liste/l'arborescence est inactive. Une liste/arborescence active a le focus clavier, contrairement à une liste/arborescence inactive.","Arrière-plan de la liste/l'arborescence pendant le pointage sur des éléments avec la souris.","Premier plan de la liste/l'arborescence pendant le pointage sur des éléments avec la souris.","Arrière-plan de l'opération de glisser-déplacer dans une liste/arborescence pendant le déplacement d’éléments sur d’autres éléments avec la souris.","Couleur de bordure glisser-déplacer de la liste/de l’arborescence lors du déplacement d’éléments entre des éléments lors de l’utilisation de la souris.","Couleur de premier plan dans la liste/l'arborescence pour la surbrillance des correspondances pendant la recherche dans une liste/arborescence.","Couleur de premier plan de la liste ou l’arborescence pour la surbrillance des correspondances sur les éléments ayant le focus pendant la recherche dans une liste/arborescence.","Couleur de premier plan de liste/arbre pour les éléments non valides, par exemple une racine non résolue dans l’Explorateur.","Couleur de premier plan des éléments de la liste contenant des erreurs.","Couleur de premier plan des éléments de liste contenant des avertissements.","Couleur d'arrière-plan du widget de filtre de type dans les listes et les arborescences.","Couleur de contour du widget de filtre de type dans les listes et les arborescences.","Couleur de contour du widget de filtre de type dans les listes et les arborescences, en l'absence de correspondance.","Appliquez une ombre à la couleur du widget filtre de type dans les listes et les arborescences.","Couleur d'arrière-plan de la correspondance filtrée.","Couleur de bordure de la correspondance filtrée.","Couleur de premier plan de la liste/l’arborescence des éléments atténués.","Couleur de trait de l'arborescence pour les repères de mise en retrait.","Couleur de trait d’arborescence pour les repères de mise en retrait qui ne sont pas actifs.","Couleur de la bordure du tableau entre les colonnes.","Couleur d'arrière-plan pour les lignes de tableau impaires.","Couleur d'arrière-plan de la liste d'actions.","Couleur de premier plan de la liste d'actions.","Couleur de premier plan de la liste d’actions pour l’élément focalisé.","Couleur d'arrière-plan de la liste d'actions pour l'élément focalisé.","Couleur de bordure des menus.","Couleur de premier plan des éléments de menu.","Couleur d'arrière-plan des éléments de menu.","Couleur de premier plan de l'élément de menu sélectionné dans les menus.","Couleur d'arrière-plan de l'élément de menu sélectionné dans les menus.","Couleur de bordure de l'élément de menu sélectionné dans les menus.","Couleur d'un élément de menu séparateur dans les menus.","Couleur de marqueur de la minimap pour les correspondances.","Couleur de marqueur minimap pour les sélections répétées de l’éditeur.","Couleur de marqueur du minimap pour la sélection de l'éditeur.","Couleur de marqueur de minimap pour les informations.","Couleur de marqueur de minimap pour les avertissements.","Couleur de marqueur de minimap pour les erreurs.","Couleur d'arrière-plan du minimap.","Opacité des éléments de premier plan rendus dans la minimap. Par exemple, « #000000c0 » affiche les éléments avec une opacité de 75 %.","Couleur d'arrière-plan du curseur de minimap.","Couleur d'arrière-plan du curseur de minimap pendant le survol.","Couleur d'arrière-plan du curseur de minimap pendant un clic.","Couleur de bordure des fenêtres coulissantes.","Couleur de fond des badges. Les badges sont de courts libellés d'information, ex. le nombre de résultats de recherche.","Couleur des badges. Les badges sont de courts libellés d'information, ex. le nombre de résultats de recherche.","Ombre de la barre de défilement pour indiquer que la vue défile.","Couleur de fond du curseur de la barre de défilement.","Couleur de fond du curseur de la barre de défilement lors du survol.","Couleur d’arrière-plan de la barre de défilement lorsqu'on clique dessus.","Couleur de fond pour la barre de progression qui peut s'afficher lors d'opérations longues.","Couleur d'arrière-plan du sélecteur rapide. Le widget de sélecteur rapide est le conteneur de sélecteurs comme la palette de commandes.","Couleur de premier plan du sélecteur rapide. Le widget de sélecteur rapide est le conteneur de sélecteurs comme la palette de commandes.","Couleur d'arrière-plan du titre du sélecteur rapide. Le widget de sélecteur rapide est le conteneur de sélecteurs comme la palette de commandes.","Couleur du sélecteur rapide pour les étiquettes de regroupement.","Couleur du sélecteur rapide pour les bordures de regroupement.","Utilisez quickInputList.focusBackground à la place","Couleur de premier plan du sélecteur rapide pour l’élément ayant le focus.","Couleur de premier plan de l’icône du sélecteur rapide pour l’élément ayant le focus.","Couleur d'arrière-plan du sélecteur rapide pour l'élément ayant le focus.","Couleur du texte dans le message d’achèvement de la viewlet de recherche.","Couleur des correspondances de requête de l'éditeur de recherche.","Couleur de bordure des correspondances de requête de l'éditeur de recherche.","Cette couleur doit être transparente ou son contenu sera masqué","Utiliser la couleur par défaut.","ID de la police à utiliser. Si aucune valeur n'est définie, la police définie en premier est utilisée.","Caractère de police associé à la définition d'icône.","Icône de l'action de fermeture dans les widgets.","Icône d'accès à l'emplacement précédent de l'éditeur.","Icône d'accès à l'emplacement suivant de l'éditeur.","Les fichiers suivants ont été fermés et modifiés sur le disque : {0}.","Les fichiers suivants ont été modifiés de manière incompatible : {0}.","Impossible d'annuler '{0}' dans tous les fichiers. {1}","Impossible d'annuler '{0}' dans tous les fichiers. {1}","Impossible d'annuler '{0}' dans tous les fichiers, car des modifications ont été apportées à {1}","Impossible d'annuler '{0}' dans tous les fichiers, car une opération d'annulation ou de rétablissement est déjà en cours d'exécution sur {1}","Impossible d'annuler '{0}' dans tous les fichiers, car une opération d'annulation ou de rétablissement s'est produite dans l'intervalle","Souhaitez-vous annuler '{0}' dans tous les fichiers ?","&&Annuler dans {0} fichiers","Annuler ce &&fichier","Impossible d'annuler '{0}', car une opération d'annulation ou de rétablissement est déjà en cours d'exécution.","Voulez-vous annuler '{0}' ?","&&Oui","Non","Impossible de répéter '{0}' dans tous les fichiers. {1}","Impossible de répéter '{0}' dans tous les fichiers. {1}","Impossible de répéter '{0}' dans tous les fichiers, car des modifications ont été apportées à {1}","Impossible de rétablir '{0}' dans tous les fichiers, car une opération d'annulation ou de rétablissement est déjà en cours d'exécution pour {1}","Impossible de rétablir '{0}' dans tous les fichiers, car une opération d'annulation ou de rétablissement s'est produite dans l'intervalle","Impossible de rétablir '{0}', car une opération d'annulation ou de rétablissement est déjà en cours d'exécution.","Espace de travail de code"],globalThis._VSCODE_NLS_LANGUAGE="fr"});