using Microsoft.AspNetCore.Mvc;
using Microsoft.AspNetCore.Authorization;
using Elsa.Workflows.Runtime;
using Harmoni360.Application.Features.WorkflowManagement.IncidentWorkflow.Models;
using Harmoni360.Application.Features.WorkflowManagement.IncidentWorkflow.Workflows;
using Harmoni360.Web.Authorization;
using System.ComponentModel.DataAnnotations;
using Harmoni360.Domain.Enums;

namespace Harmoni360.Web.Controllers;

/// <summary>
/// Controller for managing incident workflows
/// </summary>
[ApiController]
[Route("api/workflow/incidents")]
[Authorize]
public class IncidentWorkflowController : ControllerBase
{
    private readonly IWorkflowRuntime _workflowRuntime;
    private readonly ILogger<IncidentWorkflowController> _logger;
    
    public IncidentWorkflowController(
        IWorkflowRuntime workflowRuntime,
        ILogger<IncidentWorkflowController> logger)
    {
        _workflowRuntime = workflowRuntime;
        _logger = logger;
    }
    
    /// <summary>
    /// Starts a new incident management workflow
    /// </summary>
    /// <param name="request">The incident report data</param>
    /// <returns>The workflow instance information</returns>
    [HttpPost("start")]
    [RequireModulePermission(ModuleType.IncidentManagement, PermissionType.Create)]
    public async Task<IActionResult> StartIncidentWorkflow([FromBody] StartIncidentWorkflowRequest request)
    {
        try
        {
            _logger.LogInformation("Starting incident workflow for report from user: {UserId}", User.Identity?.Name);
            
            // Validate the request
            if (!ModelState.IsValid)
            {
                return BadRequest(ModelState);
            }
            
            // Create the incident report model
            var incidentReport = new IncidentReportModel
            {
                ReporterId = User.Identity?.Name ?? "unknown",
                Type = request.IncidentType,
                Description = request.Description,
                Location = request.Location,
                IncidentDateTime = request.IncidentDateTime,
                PerceivedSeverity = request.PerceivedSeverity,
                AffectedPersonnel = request.AffectedPersonnel ?? new List<string>(),
                WitnessStatements = request.WitnessStatements ?? new List<string>(),
                Attachments = request.Attachments ?? new List<AttachmentWorkflowModel>()
            };
            
            // Start the workflow
            // Note: For now, we're starting the workflow without input parameters
            // The workflow will need to be modified to receive input through a different mechanism
            // or we'll need to implement a trigger-based approach
            var workflowResult = await _workflowRuntime.StartWorkflowAsync(
                nameof(IncidentManagementWorkflow));
            
            // TODO: Implement proper input passing mechanism once Elsa configuration is finalized
            
            // Check if workflow has any incidents (errors)
            if (workflowResult.Incidents.Any())
            {
                var errors = workflowResult.Incidents.Select(i => i.Exception?.Message ?? "Unknown error").ToList();
                _logger.LogError("Failed to start incident workflow: {Errors}", 
                    string.Join(", ", errors));
                
                return BadRequest(new
                {
                    Message = "Failed to start incident workflow",
                    Errors = errors
                });
            }
            
            _logger.LogInformation("Incident workflow started successfully - Instance ID: {InstanceId}, Status: {Status}", 
                workflowResult.WorkflowInstanceId, workflowResult.Status);
            
            return Ok(new StartWorkflowResponse
            {
                WorkflowInstanceId = workflowResult.WorkflowInstanceId,
                Status = workflowResult.Status.ToString(),
                Message = "Incident workflow has been started successfully"
            });
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error starting incident workflow");
            return StatusCode(500, new { Message = "Internal server error" });
        }
    }
    
    /// <summary>
    /// Gets the status of an incident workflow
    /// </summary>
    /// <param name="workflowInstanceId">The workflow instance ID</param>
    /// <returns>The workflow status information</returns>
    [HttpGet("{workflowInstanceId}/status")]
    [RequireModulePermission(ModuleType.IncidentManagement, PermissionType.Read)]
    public async Task<IActionResult> GetWorkflowStatus(string workflowInstanceId)
    {
        try
        {
            // In a real implementation, you would query the workflow instance
            // For now, return a basic response
            return Ok(new WorkflowStatusResponse
            {
                WorkflowInstanceId = workflowInstanceId,
                Status = "Running",
                CurrentActivity = "Processing",
                StartedAt = DateTime.UtcNow.AddMinutes(-5),
                LastUpdated = DateTime.UtcNow
            });
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error retrieving workflow status for instance: {InstanceId}", workflowInstanceId);
            return StatusCode(500, new { Message = "Internal server error" });
        }
    }
    
    /// <summary>
    /// Cancels an incident workflow
    /// </summary>
    /// <param name="workflowInstanceId">The workflow instance ID</param>
    /// <returns>The cancellation result</returns>
    [HttpPost("{workflowInstanceId}/cancel")]
    [RequireModulePermission(ModuleType.IncidentManagement, PermissionType.Delete)]
    public async Task<IActionResult> CancelWorkflow(string workflowInstanceId)
    {
        try
        {
            _logger.LogInformation("Cancelling workflow instance: {InstanceId} by user: {UserId}", 
                workflowInstanceId, User.Identity?.Name);
            
            // Cancel the workflow
            await _workflowRuntime.CancelWorkflowAsync(workflowInstanceId, HttpContext.RequestAborted);
            
            return Ok(new { Message = "Workflow cancelled successfully" });
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error cancelling workflow instance: {InstanceId}", workflowInstanceId);
            return StatusCode(500, new { Message = "Internal server error" });
        }
    }
}

/// <summary>
/// Request model for starting an incident workflow
/// </summary>
public class StartIncidentWorkflowRequest
{
    [Required]
    [StringLength(50)]
    public string IncidentType { get; set; } = string.Empty; // "Accident" or "NearMiss"
    
    [Required]
    [StringLength(2000)]
    public string Description { get; set; } = string.Empty;
    
    [Required]
    [StringLength(200)]
    public string Location { get; set; } = string.Empty;
    
    public DateTime IncidentDateTime { get; set; } = DateTime.UtcNow;
    
    [StringLength(50)]
    public string PerceivedSeverity { get; set; } = string.Empty;
    
    public List<string>? AffectedPersonnel { get; set; }
    
    public List<string>? WitnessStatements { get; set; }
    
    public List<AttachmentWorkflowModel>? Attachments { get; set; }
}

/// <summary>
/// Response model for workflow start operation
/// </summary>
public class StartWorkflowResponse
{
    public string WorkflowInstanceId { get; set; } = string.Empty;
    public string Status { get; set; } = string.Empty;
    public string Message { get; set; } = string.Empty;
}

/// <summary>
/// Response model for workflow status
/// </summary>
public class WorkflowStatusResponse
{
    public string WorkflowInstanceId { get; set; } = string.Empty;
    public string Status { get; set; } = string.Empty;
    public string CurrentActivity { get; set; } = string.Empty;
    public DateTime StartedAt { get; set; }
    public DateTime LastUpdated { get; set; }
}