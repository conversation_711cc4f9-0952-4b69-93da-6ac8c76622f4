define("vs/basic-languages/azcli/azcli",["require","require"],e=>{"use strict";var t,n=Object.defineProperty,o=Object.getOwnPropertyDescriptor,r=Object.getOwnPropertyNames,i=Object.prototype.hasOwnProperty,s={},a=s,l={conf:()=>c,language:()=>f};for(t in l)n(a,t,{get:l[t],enumerable:!0});var c={comments:{lineComment:"#"}},f={defaultToken:"keyword",ignoreCase:!0,tokenPostfix:".azcli",str:/[^#\s]/,tokenizer:{root:[{include:"@comment"},[/\s-+@str*\s*/,{cases:{"@eos":{token:"key.identifier",next:"@popall"},"@default":{token:"key.identifier",next:"@type"}}}],[/^-+@str*\s*/,{cases:{"@eos":{token:"key.identifier",next:"@popall"},"@default":{token:"key.identifier",next:"@type"}}}]],type:[{include:"@comment"},[/-+@str*\s*/,{cases:{"@eos":{token:"key.identifier",next:"@popall"},"@default":"key.identifier"}}],[/@str+\s*/,{cases:{"@eos":{token:"string",next:"@popall"},"@default":"string"}}]],comment:[[/#.*$/,{cases:{"@eos":{token:"comment",next:"@popall"}}}]]}},p=n({},"__esModule",{value:!0}),d=s,u=void 0,k=void 0;if(d&&"object"==typeof d||"function"==typeof d)for(let e of r(d))i.call(p,e)||e===u||n(p,e,{get:()=>d[e],enumerable:!(k=o(d,e))||k.enumerable});return p});