<Project Sdk="Microsoft.NET.Sdk.BlazorWebAssembly">

  <PropertyGroup>
    <TargetFramework>net8.0</TargetFramework>
    <Nullable>enable</Nullable>
    <ImplicitUsings>enable</ImplicitUsings>
    <RootNamespace>Harmoni360.ElsaStudio</RootNamespace>
    <!-- Disable PDB generation for <PERSON><PERSON>zor WASM to avoid authentication/SRI issues -->
    <DebugType>None</DebugType>
    <DebugSymbols>false</DebugSymbols>
    <!-- Disable problematic package folder resolution on Linux -->
    <RestoreFallbackFolders></RestoreFallbackFolders>
    <!-- Enable NullabilityInfoContext support to fix NullabilityInfoContext_NotSupported error in WebAssembly -->
    <NullabilityInfoContextSupport>true</NullabilityInfoContextSupport>
  </PropertyGroup>

  <ItemGroup>
    <PackageReference Include="Elsa.Api.Client" Version="3.4.2" />
    <PackageReference Include="Elsa.Studio" Version="3.4.0" />
    <PackageReference Include="Elsa.Studio.Core.BlazorWasm" Version="3.4.0" />
    <PackageReference Include="Elsa.Studio.Dashboard" Version="3.4.0" />
    <PackageReference Include="Elsa.Studio.DomInterop" Version="3.4.0" />
    <PackageReference Include="Elsa.Studio.Login.BlazorWasm" Version="3.4.0" />
    <PackageReference Include="Elsa.Studio.Shell" Version="3.4.0" />
    <PackageReference Include="Elsa.Studio.Workflows" Version="3.4.0" />
    <PackageReference Include="Elsa.Studio.Workflows.Designer" Version="3.4.0" />
    <PackageReference Include="Microsoft.AspNetCore.Components.WebAssembly" Version="8.0.17" />
    <PackageReference Include="Microsoft.AspNetCore.Components.WebAssembly.DevServer" Version="8.0.17" PrivateAssets="all" />
    <PackageReference Include="Microsoft.IdentityModel.JsonWebTokens" Version="8.0.2" />
  </ItemGroup>


</Project>
