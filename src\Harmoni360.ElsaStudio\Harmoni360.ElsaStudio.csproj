<Project Sdk="Microsoft.NET.Sdk.BlazorWebAssembly">

  <PropertyGroup>
    <TargetFramework>net8.0</TargetFramework>
    <Nullable>enable</Nullable>
    <ImplicitUsings>enable</ImplicitUsings>
    <RootNamespace>Harmoni360.ElsaStudio</RootNamespace>
    <!-- Disable PDB generation for <PERSON><PERSON><PERSON> WASM to avoid authentication/SRI issues -->
    <DebugType>None</DebugType>
    <DebugSymbols>false</DebugSymbols>
  </PropertyGroup>

  <ItemGroup>
    <PackageReference Include="Elsa.Api.Client" Version="3.4.2" />
    <PackageReference Include="Elsa.Studio" Version="3.4.0" />
    <PackageReference Include="Elsa.Studio.Core.BlazorWasm" Version="3.4.0" />
    <PackageReference Include="Elsa.Studio.Dashboard" Version="3.4.0" />
    <PackageReference Include="Elsa.Studio.Login.BlazorWasm" Version="3.4.0" />
    <PackageReference Include="Elsa.Studio.Shell" Version="3.4.0" />
    <PackageReference Include="Elsa.Studio.Workflows" Version="3.4.0" />
    <PackageReference Include="Elsa.Studio.Workflows.Designer" Version="3.4.0" />
    <PackageReference Include="Microsoft.AspNetCore.Components.WebAssembly" Version="8.0.17" />
    <PackageReference Include="Microsoft.AspNetCore.Components.WebAssembly.DevServer" Version="8.0.17" PrivateAssets="all" />
  </ItemGroup>

  <!-- Copy static assets to main web project after build -->
  <Target Name="CopyElsaStudioAssets" AfterTargets="Build">
    <ItemGroup>
      <ElsaStudioAssets Include="$(OutputPath)wwwroot\**\*" />
      <ElsaStudioStaticAssets Include="wwwroot\**\*" />
      <ElsaScopedCss Include="$(IntermediateOutputPath)scopedcss\bundle\*.css" />
    </ItemGroup>
    
    <!-- Copy built assets from bin/Debug/net8.0/wwwroot -->
    <Copy SourceFiles="@(ElsaStudioAssets)" DestinationFolder="..\Harmoni360.Web\wwwroot\elsa-studio\%(RecursiveDir)" SkipUnchangedFiles="true" />
    
    <!-- Copy static assets from source wwwroot -->
    <Copy SourceFiles="@(ElsaStudioStaticAssets)" DestinationFolder="..\Harmoni360.Web\wwwroot\elsa-studio\%(RecursiveDir)" SkipUnchangedFiles="true" />
    
    <!-- Copy scoped CSS files -->
    <Copy SourceFiles="@(ElsaScopedCss)" DestinationFolder="..\Harmoni360.Web\wwwroot\elsa-studio\" SkipUnchangedFiles="true" />
  </Target>

</Project>
