using Microsoft.Extensions.Diagnostics.HealthChecks;
using Microsoft.Extensions.Logging;
using Microsoft.Extensions.Options;
using System.Net;
using System.Text.Json;

namespace Harmoni360.Infrastructure.Health;

public class ElsaStudioHealthCheck : IHealthCheck
{
    private readonly HttpClient _httpClient;
    private readonly ElsaStudioSettings _settings;
    private readonly ILogger<ElsaStudioHealthCheck> _logger;

    public ElsaStudioHealthCheck(
        HttpClient httpClient,
        IOptions<ElsaStudioSettings> settings,
        ILogger<ElsaStudioHealthCheck> logger)
    {
        _httpClient = httpClient;
        _settings = settings.Value;
        _logger = logger;
    }

    public async Task<HealthCheckResult> CheckHealthAsync(
        HealthCheckContext context,
        CancellationToken cancellationToken = default)
    {
        try
        {
            var healthData = new Dictionary<string, object>();
            var startTime = DateTime.UtcNow;

            // Check if Elsa Studio is accessible
            var isAccessible = await CheckElsaStudioAccessibility(healthData, cancellationToken);
            
            var responseTime = (DateTime.UtcNow - startTime).TotalMilliseconds;
            healthData["response_time_ms"] = responseTime;
            healthData["elsa_studio_url"] = _settings.BaseUrl;

            if (!isAccessible)
            {
                return HealthCheckResult.Unhealthy(
                    "Elsa Studio is not accessible", 
                    data: healthData);
            }

            // Check if response time is concerning
            if (responseTime > 10000) // 10 seconds
            {
                healthData["warning"] = "Slow response time detected";
                _logger.LogWarning("Elsa Studio response time is slow: {ResponseTime}ms", responseTime);
                
                return HealthCheckResult.Degraded(
                    "Elsa Studio is accessible but responding slowly",
                    data: healthData);
            }

            return HealthCheckResult.Healthy(
                "Elsa Studio is accessible and responding normally",
                data: healthData);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Elsa Studio health check failed");
            return HealthCheckResult.Unhealthy(
                "Elsa Studio health check failed",
                ex,
                new Dictionary<string, object> 
                { 
                    ["error"] = ex.Message,
                    ["elsa_studio_url"] = _settings.BaseUrl
                });
        }
    }

    private async Task<bool> CheckElsaStudioAccessibility(
        Dictionary<string, object> healthData,
        CancellationToken cancellationToken)
    {
        try
        {
            // Create a timeout for the health check
            using var timeoutCts = CancellationTokenSource.CreateLinkedTokenSource(cancellationToken);
            timeoutCts.CancelAfter(TimeSpan.FromSeconds(30));

            // Try to access Elsa Studio health endpoint or main page
            var healthEndpoint = $"{_settings.BaseUrl.TrimEnd('/')}/health";
            var fallbackEndpoint = $"{_settings.BaseUrl.TrimEnd('/')}/";

            HttpResponseMessage? response = null;
            
            try
            {
                // First try the health endpoint
                response = await _httpClient.GetAsync(healthEndpoint, timeoutCts.Token);
                healthData["endpoint_used"] = "health";
            }
            catch (HttpRequestException)
            {
                // If health endpoint doesn't exist, try the main page
                try
                {
                    response = await _httpClient.GetAsync(fallbackEndpoint, timeoutCts.Token);
                    healthData["endpoint_used"] = "main";
                }
                catch (HttpRequestException ex)
                {
                    healthData["connection_error"] = ex.Message;
                    _logger.LogWarning(ex, "Failed to connect to Elsa Studio at {Url}", _settings.BaseUrl);
                    return false;
                }
            }

            if (response != null)
            {
                healthData["status_code"] = (int)response.StatusCode;
                healthData["status_description"] = response.StatusCode.ToString();

                // Check if the response indicates the service is available
                var isHealthy = response.StatusCode == HttpStatusCode.OK || 
                               response.StatusCode == HttpStatusCode.Redirect ||
                               response.StatusCode == HttpStatusCode.MovedPermanently;

                if (!isHealthy)
                {
                    _logger.LogWarning("Elsa Studio returned status code: {StatusCode}", response.StatusCode);
                }

                // Try to read response content for additional information
                try
                {
                    var content = await response.Content.ReadAsStringAsync(timeoutCts.Token);
                    
                    // If it's a JSON health response, parse it
                    if (response.Content.Headers.ContentType?.MediaType?.Contains("json") == true && 
                        !string.IsNullOrWhiteSpace(content))
                    {
                        var healthResponse = JsonSerializer.Deserialize<Dictionary<string, object>>(content);
                        if (healthResponse?.ContainsKey("status") == true)
                        {
                            healthData["elsa_health_status"] = healthResponse["status"];
                        }
                    }
                    else if (!string.IsNullOrWhiteSpace(content))
                    {
                        // For HTML responses, check if it contains expected Elsa Studio content
                        var containsElsaContent = content.Contains("Elsa", StringComparison.OrdinalIgnoreCase) ||
                                                 content.Contains("workflow", StringComparison.OrdinalIgnoreCase);
                        
                        healthData["contains_elsa_content"] = containsElsaContent;
                        
                        if (!containsElsaContent && isHealthy)
                        {
                            _logger.LogWarning("Elsa Studio endpoint returned unexpected content");
                            healthData["warning"] = "Unexpected response content";
                        }
                    }
                }
                catch (Exception ex)
                {
                    _logger.LogDebug(ex, "Could not read Elsa Studio response content");
                    healthData["content_read_error"] = ex.Message;
                }

                return isHealthy;
            }

            return false;
        }
        catch (OperationCanceledException) when (cancellationToken.IsCancellationRequested)
        {
            healthData["error"] = "Health check was cancelled";
            return false;
        }
        catch (OperationCanceledException)
        {
            healthData["error"] = "Connection to Elsa Studio timed out";
            _logger.LogWarning("Elsa Studio health check timed out");
            return false;
        }
        catch (Exception ex)
        {
            healthData["error"] = ex.Message;
            _logger.LogError(ex, "Unexpected error checking Elsa Studio accessibility");
            return false;
        }
    }
}

public class ElsaStudioSettings
{
    public string BaseUrl { get; set; } = string.Empty;
    public bool RequireAuthentication { get; set; }
    public string[] AllowedRoles { get; set; } = Array.Empty<string>();
}