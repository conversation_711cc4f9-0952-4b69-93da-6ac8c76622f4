using Elsa.Workflows;
using Elsa.Workflows.Activities;
using Elsa.Workflows.Memory;
using Harmoni360.Application.Features.WorkflowManagement.IncidentWorkflow.Activities;
using Harmoni360.Application.Features.WorkflowManagement.IncidentWorkflow.Models;

namespace Harmoni360.Application.Features.WorkflowManagement.IncidentWorkflow.Workflows;

/// <summary>
/// Main incident management workflow orchestrating the complete incident lifecycle.
/// Comprehensive 12-step incident management process following HSE standards.
/// </summary>
public class IncidentManagementWorkflow : WorkflowBase
{
    /// <summary>
    /// The incident workflow context containing all incident data
    /// </summary>
    public static readonly Variable<IncidentWorkflowContext> Context = new();
    
    /// <summary>
    /// The incident number for tracking
    /// </summary>
    public static readonly Variable<string> IncidentNumber = new();
    
    /// <summary>
    /// Current incident status
    /// </summary>
    public static readonly Variable<string> IncidentStatus = new();
    
    /// <summary>
    /// Approval tracking variable
    /// </summary>
    public static readonly Variable<bool> IsApproved = new();
    
    protected override void Build(IWorkflowBuilder builder)
    {
        // Set workflow metadata
        builder.Name = "HSE Incident Management Workflow";
        builder.DefinitionId = GenerateDeterministicGuid("IncidentManagementWorkflow");
        builder.Description = "Approval-driven incident management workflow with HSE governance compliance. " +
                            "This workflow enforces role-based approvals at critical decision points.\n\n" +
                            "Approval Gates:\n" +
                            "• Gate 1: HSEManager/IncidentManager approves investigation start\n" +
                            "• Gate 2: Investigation Team approves control measures\n" +
                            "• Gate 3: Lead Investigator verifies action completion\n" +
                            "• Gate 4: HSEManager/DepartmentHead provides final approval\n\n" +
                            "Steps:\n" +
                            "1. Receive Incident Report\n" +
                            "2. Generate Incident Number (Format: Number/HSE-Type/MM/YYYY)\n" +
                            "3. Notify Management\n" +
                            "4. APPROVAL: Approve Investigation (Gate 1)\n" +
                            "5. Schedule HSE Investigation\n" +
                            "6. Assign Investigation Team\n" +
                            "7. Notify Investigation Team\n" +
                            "8. Conduct Investigation (HFACS/ICAM Analysis)\n" +
                            "9. APPROVAL: Approve Control Measures (Gate 2)\n" +
                            "10. Determine Control Measures\n" +
                            "11. Assign Control Measures with Due Dates\n" +
                            "12. Execute Actions\n" +
                            "13. APPROVAL: Verify Completion (Gate 3)\n" +
                            "14. Verify Completion\n" +
                            "15. APPROVAL: Final Approval (Gate 4)\n" +
                            "16. Close Incident & Send Final Report";
        
        // Add custom properties for additional metadata
        builder.WithCustomProperty("Category", "HSE Management");
        builder.WithCustomProperty("Icon", "shield-alert");
        builder.WithCustomProperty("Tags", new[] { "HSE", "Incident", "Safety", "Investigation", "HFACS", "ICAM" });
        builder.WithCustomProperty("Version", "1.0");
        builder.WithCustomProperty("Author", "Harmoni360 HSE Team");
        
        builder.Root = new Sequence
        {
            Activities =
            {
                // Initialize context with incident data
                new SetVariable
                {
                    Name = "InitializeIncidentContext",
                    Variable = IncidentStatus,
                    Value = new("Reported")
                },
                
                // Step 1: Receive Incident Report (Initial processing and validation)
                new SimpleReceiveIncidentActivity
                {
                    Name = "ReceiveIncidentReport",
                    Context = new(Context)
                },
                
                // Step 2: Generate Incident Number (Number/HSE-CategoryCode/MM/YYYY format)
                new SimpleGenerateIncidentNumberActivity
                {
                    Name = "GenerateIncidentNumber",
                    Context = new(Context)
                },
                
                // Step 3: Notify Management (Immediate alert to management)
                new SimpleNotifyManagementActivity
                {
                    Name = "NotifyManagement",
                    Context = new(Context)
                },
                
                // APPROVAL GATE 1: Approve Investigation Start
                // Required: SuperAdmin, Developer, HSEManager, IncidentManager
                // Status: Reported → Under Investigation
                // Timeout: 24 hours
                new SimpleApprovalActivity
                {
                    Name = "ApprovalGate1_ApproveInvestigation",
                    Context = new(Context),
                    FromStatus = new("Reported"),
                    ToStatus = new("Under Investigation"),
                    RequiredRoles = new("SuperAdmin, Developer, HSEManager, IncidentManager"),
                    TimeoutHours = new(24),
                    GateDescription = new("APPROVAL GATE 1: Approve Investigation Start")
                },
                
                // Update status after approval
                new SetVariable
                {
                    Name = "UpdateStatusToUnderInvestigation",
                    Variable = IncidentStatus,
                    Value = new("Under Investigation")
                },
                
                // Step 4: Schedule HSE Investigation (Calendar invites and timeline)
                new SimpleScheduleInvestigationActivity
                {
                    Name = "ScheduleInvestigation",
                    Context = new(Context)
                },
                
                // Step 5: Assign Investigation Team (Select team based on incident type)
                new SimpleAssignInvestigationActivity
                {
                    Name = "AssignInvestigationTeam",
                    Context = new(Context)
                },
                
                // Step 6: Notify Investigation Team (Email notifications with details)
                new SimpleNotifyInvestigationTeamActivity
                {
                    Name = "NotifyInvestigationTeam",
                    Context = new(Context)
                },
                
                // Step 7: Conduct Investigation (HFACS/ICAM analysis)
                new SimpleConductInvestigationActivity
                {
                    Name = "ConductInvestigation_HFACS_ICAM",
                    Context = new(Context)
                },
                
                // Step 8: Analyze the Problem (Root cause analysis)
                new SimpleAnalyzeProblemActivity
                {
                    Name = "AnalyzeProblem_RootCause",
                    Context = new(Context)
                },
                
                // APPROVAL GATE 2: Approve Control Measures
                // Required: SuperAdmin, Developer, HSEManager, SafetyOfficer, InvestigationTeam
                // Status: Under Investigation → Awaiting Action
                // Timeout: 48 hours
                new SimpleApprovalActivity
                {
                    Name = "ApprovalGate2_ApproveControlMeasures",
                    Context = new(Context),
                    FromStatus = new("Under Investigation"),
                    ToStatus = new("Awaiting Action"),
                    RequiredRoles = new("SuperAdmin, Developer, HSEManager, SafetyOfficer, InvestigationTeam"),
                    TimeoutHours = new(48),
                    GateDescription = new("APPROVAL GATE 2: Approve Control Measures")
                },
                
                // Update status after approval
                new SetVariable
                {
                    Name = "UpdateStatusToAwaitingAction",
                    Variable = IncidentStatus,
                    Value = new("Awaiting Action")
                },
                
                // Step 9: Determine Control Measures (Apply hierarchy of controls)
                new SimpleDetermineControlMeasuresActivity
                {
                    Name = "DetermineControlMeasures",
                    Context = new(Context)
                },
                
                // Step 10: Assign Control Measures (Lead investigator assigns with due dates)
                new Sequence
                {
                    Name = "AssignControlMeasures",
                    Activities =
                    {
                        new SimpleAssignControlMeasuresActivity
                        {
                            Context = new(Context)
                        },
                        new SetVariable
                        {
                            Variable = IncidentStatus,
                            Value = new("In Progress")
                        }
                    }
                },
                
                // Step 11: Action Execution (Assignees execute control measures)
                new SimpleExecuteActionsActivity
                {
                    Name = "ActionExecution",
                    Context = new(Context)
                },
                
                // APPROVAL GATE 3: Verify Completion
                // Required: SuperAdmin, Developer, LeadInvestigator
                // Status: In Progress → Pending Review
                // Timeout: 72 hours
                new SimpleApprovalActivity
                {
                    Name = "ApprovalGate3_VerifyCompletion",
                    Context = new(Context),
                    FromStatus = new("In Progress"),
                    ToStatus = new("Pending Review"),
                    RequiredRoles = new("SuperAdmin, Developer, LeadInvestigator"),
                    TimeoutHours = new(72),
                    GateDescription = new("APPROVAL GATE 3: Verify Completion")
                },
                
                // Update status after approval
                new SetVariable
                {
                    Name = "UpdateStatusToPendingReview",
                    Variable = IncidentStatus,
                    Value = new("Pending Review")
                },
                
                // Step 12: Verification (Lead investigator verifies all actions)
                new Sequence
                {
                    Name = "VerifyCompletedActions",
                    Activities =
                    {
                        new SimpleVerifyCompletedActionsActivity
                        {
                            Context = new(Context)
                        },
                        new SetVariable
                        {
                            Variable = IncidentStatus,
                            Value = new("Under Review")
                        }
                    }
                },
                
                // APPROVAL GATE 4: Final Approval
                // Required: SuperAdmin, Developer, HSEManager, DepartmentHead
                // Status: Under Review → Approved
                // Timeout: 24 hours
                new SimpleApprovalActivity
                {
                    Name = "ApprovalGate4_FinalApproval",
                    Context = new(Context),
                    FromStatus = new("Under Review"),
                    ToStatus = new("Approved"),
                    RequiredRoles = new("SuperAdmin, Developer, HSEManager, DepartmentHead"),
                    TimeoutHours = new(24),
                    GateDescription = new("APPROVAL GATE 4: Final Approval")
                },
                
                // Update status after approval
                new SetVariable
                {
                    Name = "UpdateStatusToApproved",
                    Variable = IncidentStatus,
                    Value = new("Approved")
                },
                
                // Step 13: Close Incident & Send Final Report
                new SimpleCloseIncidentActivity
                {
                    Name = "CloseIncident_FinalReport",
                    Context = new(Context)
                },
                
                // Final status update
                new SetVariable
                {
                    Name = "UpdateStatusToClosed",
                    Variable = IncidentStatus,
                    Value = new("Closed")
                }
            }
        };
    }
    
    private static string GenerateDeterministicGuid(string input)
    {
        using var md5 = System.Security.Cryptography.MD5.Create();
        var hash = md5.ComputeHash(System.Text.Encoding.UTF8.GetBytes(input));
        return new Guid(hash).ToString();
    }
}