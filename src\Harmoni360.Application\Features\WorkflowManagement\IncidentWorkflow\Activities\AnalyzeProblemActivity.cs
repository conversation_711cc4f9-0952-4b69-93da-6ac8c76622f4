using Elsa.Workflows;
using Elsa.Workflows.Attributes;
using Harmoni360.Application.Common.Interfaces;
using Harmoni360.Application.Features.WorkflowManagement.Common;
using Harmoni360.Application.Features.WorkflowManagement.IncidentWorkflow.Models;
using Harmoni360.Domain.Interfaces;
using Microsoft.Extensions.Logging;
using System.ComponentModel;
using Elsa.Extensions;
using Elsa.Workflows.Models;

namespace Harmoni360.Application.Features.WorkflowManagement.IncidentWorkflow.Activities;

/// <summary>
/// Activity that analyzes the incident problem using HFACS or ICAM methodology
/// </summary>
[Activity("Incident Management", "Analyze Problem", "Analyzes the incident using Human Factor Analysis Classification System (HFACS) or Incident Causative Analysis Method (ICAM)")]
public class AnalyzeProblemActivity : IncidentActivityBase<IncidentWorkflowContext>
{
    public AnalyzeProblemActivity(
        ILogger<AnalyzeProblemActivity> logger,
        IIncidentRepository incidentRepository,
        ICurrentUserService currentUserService)
        : base(logger, incidentRepository, currentUserService)
    {
    }
    
    /// <summary>
    /// The workflow context containing incident information
    /// </summary>
    [Input(
        Description = "The incident workflow context",
        DisplayName = "Incident Context"
    )]
    public Input<IncidentWorkflowContext> Context { get; set; } = default!;
    
    /// <summary>
    /// The analysis method to use (HFACS or ICAM)
    /// </summary>
    [Input(
        Description = "The analysis method to use",
        DisplayName = "Analysis Method"
    )]
    public Input<string> AnalysisMethod { get; set; } = default!;
    
    protected override async ValueTask ExecuteAsync(ActivityExecutionContext context)
    {
        const string activityName = nameof(AnalyzeProblemActivity);
        LogActivity(activityName, "Starting incident analysis");
        
        try
        {
            var workflowContext = Context.Get(context);
            var analysisMethod = AnalysisMethod.Get(context) ?? "HFACS";
            
            LogActivity(activityName, "Analyzing incident {IncidentId} using {AnalysisMethod}", 
                workflowContext.IncidentId, analysisMethod);
            
            // Perform analysis based on method
            if (analysisMethod == "HFACS")
            {
                // Human Factor Analysis Classification System
                workflowContext.AnalysisResults = new AnalysisResults
                {
                    Method = "HFACS",
                    UnsafeActs = new List<string> { "Procedural violation", "Skill-based error" },
                    Preconditions = new List<string> { "Inadequate supervision", "Environmental factors" },
                    UnsafeSupervision = new List<string> { "Failed to correct known problem" },
                    OrganizationalInfluences = new List<string> { "Resource management", "Organizational climate" },
                    RootCauses = new List<string> { "Lack of training", "Inadequate safety procedures" },
                    ContributingFactors = new List<string> { "Time pressure", "Equipment failure" }
                };
            }
            else if (analysisMethod == "ICAM")
            {
                // Incident Causative Analysis Method
                workflowContext.AnalysisResults = new AnalysisResults
                {
                    Method = "ICAM",
                    OrganizationalFactors = new List<string> { "Process deficiency", "Training gap" },
                    TaskEnvironmentalConditions = new List<string> { "Poor lighting", "Hazardous material" },
                    IndividualTeamActions = new List<string> { "Communication breakdown", "Decision error" },
                    AbsentFailedDefenses = new List<string> { "Missing safety barrier", "Ineffective controls" },
                    RootCauses = new List<string> { "Systemic failure", "Management oversight" },
                    ContributingFactors = new List<string> { "Workload", "Fatigue" }
                };
            }
            
            // Add analysis results to investigation
            if (workflowContext.Investigation != null)
            {
                workflowContext.Investigation.AnalysisMethod = analysisMethod;
                workflowContext.Investigation.RootCausesText = string.Join(", ", workflowContext.AnalysisResults.RootCauses);
                workflowContext.Investigation.ContributingFactors = string.Join(", ", workflowContext.AnalysisResults.ContributingFactors);
            }
            
            LogActivity(activityName, "Completed analysis for incident {IncidentId}. Root causes: {RootCauses}", 
                workflowContext.IncidentId, 
                string.Join(", ", workflowContext.AnalysisResults.RootCauses));
            
            // Set the output
            Result.Set(context, workflowContext);
        }
        catch (Exception ex)
        {
            LogActivityError(activityName, ex, "Failed to analyze incident");
            throw;
        }
    }
}