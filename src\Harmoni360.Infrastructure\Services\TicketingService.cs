using Harmoni360.Application.Common.Interfaces;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.Logging;
using System.Text.Json;

namespace Harmoni360.Infrastructure.Services;

public class TicketingService : ITicketingService
{
    private readonly ILogger<TicketingService> _logger;
    private readonly IConfiguration _configuration;
    private readonly HttpClient _httpClient;

    public TicketingService(
        ILogger<TicketingService> logger,
        IConfiguration configuration,
        HttpClient httpClient)
    {
        _logger = logger;
        _configuration = configuration;
        _httpClient = httpClient;
        ConfigureHttpClient();
    }

    private void ConfigureHttpClient()
    {
        var baseUrl = _configuration["Ticketing:BaseUrl"];
        var apiKey = _configuration["Ticketing:ApiKey"];
        var username = _configuration["Ticketing:Username"];
        var password = _configuration["Ticketing:Password"];

        if (!string.IsNullOrEmpty(baseUrl))
        {
            _httpClient.BaseAddress = new Uri(baseUrl);
        }

        if (!string.IsNullOrEmpty(apiKey))
        {
            _httpClient.DefaultRequestHeaders.Add("Authorization", $"Bearer {apiKey}");
        }
        else if (!string.IsNullOrEmpty(username) && !string.IsNullOrEmpty(password))
        {
            var authValue = Convert.ToBase64String(System.Text.Encoding.ASCII.GetBytes($"{username}:{password}"));
            _httpClient.DefaultRequestHeaders.Add("Authorization", $"Basic {authValue}");
        }

        _httpClient.DefaultRequestHeaders.Add("User-Agent", "Harmoni360-HSE/1.0");
        _httpClient.DefaultRequestHeaders.Add("Accept", "application/json");
    }

    public async Task<string> CreateTicketAsync(CreateTicketRequest request, CancellationToken cancellationToken = default)
    {
        try
        {
            _logger.LogInformation("Creating ticket: {Title}", request.Title);

            if (!await IsTicketingServiceAvailableAsync(cancellationToken))
            {
                return await CreateLocalTicketFallback(request);
            }

            var ticketingProvider = _configuration["Ticketing:Provider"];
            
            return ticketingProvider?.ToLower() switch
            {
                "jira" => await CreateJiraTicket(request, cancellationToken),
                "servicenow" => await CreateServiceNowTicket(request, cancellationToken),
                "linear" => await CreateLinearTicket(request, cancellationToken),
                _ => await CreateGenericTicket(request, cancellationToken)
            };
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to create ticket: {Title}", request.Title);
            return await CreateLocalTicketFallback(request);
        }
    }

    public async Task UpdateTicketAsync(string ticketId, UpdateTicketRequest request, CancellationToken cancellationToken = default)
    {
        try
        {
            _logger.LogInformation("Updating ticket: {TicketId}", ticketId);

            if (!await IsTicketingServiceAvailableAsync(cancellationToken))
            {
                await UpdateLocalTicketFallback(ticketId, request);
                return;
            }

            var ticketingProvider = _configuration["Ticketing:Provider"];
            
            switch (ticketingProvider?.ToLower())
            {
                case "jira":
                    await UpdateJiraTicket(ticketId, request, cancellationToken);
                    break;
                case "servicenow":
                    await UpdateServiceNowTicket(ticketId, request, cancellationToken);
                    break;
                case "linear":
                    await UpdateLinearTicket(ticketId, request, cancellationToken);
                    break;
                default:
                    await UpdateGenericTicket(ticketId, request, cancellationToken);
                    break;
            }
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to update ticket: {TicketId}", ticketId);
            await UpdateLocalTicketFallback(ticketId, request);
        }
    }

    public async Task CloseTicketAsync(string ticketId, string reason, CancellationToken cancellationToken = default)
    {
        try
        {
            _logger.LogInformation("Closing ticket: {TicketId} with reason: {Reason}", ticketId, reason);

            if (!await IsTicketingServiceAvailableAsync(cancellationToken))
            {
                _logger.LogWarning("Ticketing service not available. Ticket closure logged: {TicketId}", ticketId);
                return;
            }

            var updateRequest = new UpdateTicketRequest
            {
                Status = TicketStatus.Closed,
                Resolution = reason
            };

            await UpdateTicketAsync(ticketId, updateRequest, cancellationToken);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to close ticket: {TicketId}", ticketId);
        }
    }

    public async Task<TicketDetails?> GetTicketAsync(string ticketId, CancellationToken cancellationToken = default)
    {
        try
        {
            _logger.LogInformation("Retrieving ticket: {TicketId}", ticketId);

            if (!await IsTicketingServiceAvailableAsync(cancellationToken))
            {
                return GetLocalTicketFallback(ticketId);
            }

            var ticketingProvider = _configuration["Ticketing:Provider"];
            
            return ticketingProvider?.ToLower() switch
            {
                "jira" => await GetJiraTicket(ticketId, cancellationToken),
                "servicenow" => await GetServiceNowTicket(ticketId, cancellationToken),
                "linear" => await GetLinearTicket(ticketId, cancellationToken),
                _ => await GetGenericTicket(ticketId, cancellationToken)
            };
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to retrieve ticket: {TicketId}", ticketId);
            return GetLocalTicketFallback(ticketId);
        }
    }

    public async Task AddCommentAsync(string ticketId, string comment, string author, CancellationToken cancellationToken = default)
    {
        try
        {
            _logger.LogInformation("Adding comment to ticket: {TicketId} by {Author}", ticketId, author);

            if (!await IsTicketingServiceAvailableAsync(cancellationToken))
            {
                _logger.LogInformation("Ticketing service not available. Comment logged: Ticket={TicketId}, Author={Author}, Comment={Comment}",
                    ticketId, author, comment);
                return;
            }

            var payload = new
            {
                body = comment,
                author = author,
                visibility = new { type = "group", value = "harmoni360-users" }
            };

            var content = new StringContent(JsonSerializer.Serialize(payload),
                System.Text.Encoding.UTF8, "application/json");

            var response = await _httpClient.PostAsync($"rest/api/2/issue/{ticketId}/comment", content, cancellationToken);

            if (response.IsSuccessStatusCode)
            {
                _logger.LogInformation("Comment added successfully to ticket: {TicketId}", ticketId);
            }
            else
            {
                _logger.LogWarning("Failed to add comment to ticket: {TicketId}. Status: {StatusCode}",
                    ticketId, response.StatusCode);
            }
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to add comment to ticket: {TicketId}", ticketId);
        }
    }

    public async Task AssignTicketAsync(string ticketId, string assigneeId, CancellationToken cancellationToken = default)
    {
        try
        {
            _logger.LogInformation("Assigning ticket: {TicketId} to {AssigneeId}", ticketId, assigneeId);

            var updateRequest = new UpdateTicketRequest
            {
                AssigneeId = assigneeId
            };

            await UpdateTicketAsync(ticketId, updateRequest, cancellationToken);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to assign ticket: {TicketId}", ticketId);
        }
    }

    public async Task<bool> IsTicketingServiceAvailableAsync(CancellationToken cancellationToken = default)
    {
        try
        {
            var baseUrl = _configuration["Ticketing:BaseUrl"];
            if (string.IsNullOrEmpty(baseUrl))
            {
                _logger.LogDebug("Ticketing service not configured - BaseUrl is empty");
                return false;
            }

            var response = await _httpClient.GetAsync("rest/api/2/serverInfo", cancellationToken);
            var isAvailable = response.IsSuccessStatusCode;
            
            _logger.LogDebug("Ticketing service availability check: {IsAvailable}", isAvailable);
            return isAvailable;
        }
        catch (Exception ex)
        {
            _logger.LogDebug(ex, "Ticketing service not available");
            return false;
        }
    }

    #region Provider-Specific Implementations

    private async Task<string> CreateJiraTicket(CreateTicketRequest request, CancellationToken cancellationToken)
    {
        var projectKey = _configuration["Ticketing:Jira:ProjectKey"] ?? "HSE";
        var issueType = _configuration["Ticketing:Jira:IssueType"] ?? "Bug";

        var payload = new
        {
            fields = new
            {
                project = new { key = projectKey },
                summary = request.Title,
                description = request.Description,
                issuetype = new { name = issueType },
                priority = new { name = MapPriorityToJira(request.Priority) },
                reporter = new { name = request.ReporterId },
                assignee = request.AssigneeId != null ? new { name = request.AssigneeId } : null,
                labels = request.Labels.ToArray()
            }
        };

        var content = new StringContent(JsonSerializer.Serialize(payload),
            System.Text.Encoding.UTF8, "application/json");

        var response = await _httpClient.PostAsync("rest/api/2/issue", content, cancellationToken);
        
        if (response.IsSuccessStatusCode)
        {
            var responseContent = await response.Content.ReadAsStringAsync(cancellationToken);
            var result = JsonSerializer.Deserialize<JsonElement>(responseContent);
            var ticketId = result.GetProperty("key").GetString() ?? "";
            
            _logger.LogInformation("Jira ticket created successfully: {TicketId}", ticketId);
            return ticketId;
        }

        throw new InvalidOperationException($"Failed to create Jira ticket: {response.StatusCode}");
    }

    private async Task<string> CreateServiceNowTicket(CreateTicketRequest request, CancellationToken cancellationToken)
    {
        var payload = new
        {
            short_description = request.Title,
            description = request.Description,
            priority = MapPriorityToServiceNow(request.Priority),
            caller_id = request.ReporterId,
            assigned_to = request.AssigneeId,
            category = "HSE Management",
            subcategory = request.Type.ToString()
        };

        var content = new StringContent(JsonSerializer.Serialize(payload),
            System.Text.Encoding.UTF8, "application/json");

        var response = await _httpClient.PostAsync("api/now/table/incident", content, cancellationToken);
        
        if (response.IsSuccessStatusCode)
        {
            var responseContent = await response.Content.ReadAsStringAsync(cancellationToken);
            var result = JsonSerializer.Deserialize<JsonElement>(responseContent);
            var ticketId = result.GetProperty("result").GetProperty("number").GetString() ?? "";
            
            _logger.LogInformation("ServiceNow ticket created successfully: {TicketId}", ticketId);
            return ticketId;
        }

        throw new InvalidOperationException($"Failed to create ServiceNow ticket: {response.StatusCode}");
    }

    private async Task<string> CreateLinearTicket(CreateTicketRequest request, CancellationToken cancellationToken)
    {
        var teamId = _configuration["Ticketing:Linear:TeamId"];
        
        var query = @"
        mutation IssueCreate($input: IssueCreateInput!) {
            issueCreate(input: $input) {
                success
                issue {
                    id
                    identifier
                    title
                }
            }
        }";

        var variables = new
        {
            input = new
            {
                teamId = teamId,
                title = request.Title,
                description = request.Description,
                priority = MapPriorityToLinear(request.Priority),
                assigneeId = request.AssigneeId,
                labelIds = new string[0] // Labels would need to be pre-created in Linear
            }
        };

        var payload = new
        {
            query = query,
            variables = variables
        };

        var content = new StringContent(JsonSerializer.Serialize(payload),
            System.Text.Encoding.UTF8, "application/json");

        var response = await _httpClient.PostAsync("api/graphql", content, cancellationToken);
        
        if (response.IsSuccessStatusCode)
        {
            var responseContent = await response.Content.ReadAsStringAsync(cancellationToken);
            var result = JsonSerializer.Deserialize<JsonElement>(responseContent);
            var ticketId = result.GetProperty("data").GetProperty("issueCreate")
                .GetProperty("issue").GetProperty("identifier").GetString() ?? "";
            
            _logger.LogInformation("Linear ticket created successfully: {TicketId}", ticketId);
            return ticketId;
        }

        throw new InvalidOperationException($"Failed to create Linear ticket: {response.StatusCode}");
    }

    private async Task<string> CreateGenericTicket(CreateTicketRequest request, CancellationToken cancellationToken)
    {
        // Generic REST API implementation
        var payload = new
        {
            title = request.Title,
            description = request.Description,
            priority = request.Priority.ToString(),
            type = request.Type.ToString(),
            reporter = request.ReporterId,
            assignee = request.AssigneeId,
            labels = request.Labels
        };

        var content = new StringContent(JsonSerializer.Serialize(payload),
            System.Text.Encoding.UTF8, "application/json");

        var response = await _httpClient.PostAsync("tickets", content, cancellationToken);
        
        if (response.IsSuccessStatusCode)
        {
            var responseContent = await response.Content.ReadAsStringAsync(cancellationToken);
            var result = JsonSerializer.Deserialize<JsonElement>(responseContent);
            var ticketId = result.GetProperty("id").GetString() ?? "";
            
            _logger.LogInformation("Generic ticket created successfully: {TicketId}", ticketId);
            return ticketId;
        }

        throw new InvalidOperationException($"Failed to create generic ticket: {response.StatusCode}");
    }

    #endregion

    #region Fallback Methods

    private async Task<string> CreateLocalTicketFallback(CreateTicketRequest request)
    {
        var ticketId = $"HSE-{DateTime.UtcNow:yyyyMMdd}-{Guid.NewGuid().ToString("N")[..8]}";
        
        _logger.LogWarning("Ticketing service not available. Creating local ticket: {TicketId}", ticketId);
        _logger.LogInformation("Local Ticket Details: ID={TicketId}, Title={Title}, Priority={Priority}, Reporter={Reporter}",
            ticketId, request.Title, request.Priority, request.ReporterEmail);

        return await Task.FromResult(ticketId);
    }

    private async Task UpdateLocalTicketFallback(string ticketId, UpdateTicketRequest request)
    {
        _logger.LogWarning("Ticketing service not available. Local ticket update logged: {TicketId}", ticketId);
        _logger.LogInformation("Local Ticket Update: ID={TicketId}, Status={Status}, Priority={Priority}, Assignee={Assignee}",
            ticketId, request.Status, request.Priority, request.AssigneeId);

        await Task.CompletedTask;
    }

    private TicketDetails? GetLocalTicketFallback(string ticketId)
    {
        _logger.LogWarning("Ticketing service not available. Returning mock ticket details: {TicketId}", ticketId);
        
        return new TicketDetails
        {
            Id = ticketId,
            Title = "Mock Ticket (Service Unavailable)",
            Description = "This is a mock ticket created because the external ticketing service is not available.",
            Status = TicketStatus.Open,
            Priority = TicketPriority.Medium,
            Type = TicketType.Incident,
            CreatedAt = DateTime.UtcNow,
            ExternalUrl = $"#ticket-{ticketId}"
        };
    }

    #endregion

    #region Provider-Specific Update Methods (stub implementations)

    private async Task UpdateJiraTicket(string ticketId, UpdateTicketRequest request, CancellationToken cancellationToken)
    {
        // Implementation for updating Jira tickets
        await Task.CompletedTask;
    }

    private async Task UpdateServiceNowTicket(string ticketId, UpdateTicketRequest request, CancellationToken cancellationToken)
    {
        // Implementation for updating ServiceNow tickets
        await Task.CompletedTask;
    }

    private async Task UpdateLinearTicket(string ticketId, UpdateTicketRequest request, CancellationToken cancellationToken)
    {
        // Implementation for updating Linear tickets
        await Task.CompletedTask;
    }

    private async Task UpdateGenericTicket(string ticketId, UpdateTicketRequest request, CancellationToken cancellationToken)
    {
        // Implementation for updating generic tickets
        await Task.CompletedTask;
    }

    private async Task<TicketDetails?> GetJiraTicket(string ticketId, CancellationToken cancellationToken)
    {
        // Implementation for retrieving Jira tickets
        return await Task.FromResult<TicketDetails?>(null);
    }

    private async Task<TicketDetails?> GetServiceNowTicket(string ticketId, CancellationToken cancellationToken)
    {
        // Implementation for retrieving ServiceNow tickets
        return await Task.FromResult<TicketDetails?>(null);
    }

    private async Task<TicketDetails?> GetLinearTicket(string ticketId, CancellationToken cancellationToken)
    {
        // Implementation for retrieving Linear tickets
        return await Task.FromResult<TicketDetails?>(null);
    }

    private async Task<TicketDetails?> GetGenericTicket(string ticketId, CancellationToken cancellationToken)
    {
        // Implementation for retrieving generic tickets
        return await Task.FromResult<TicketDetails?>(null);
    }

    #endregion

    #region Priority Mapping

    private string MapPriorityToJira(TicketPriority priority) => priority switch
    {
        TicketPriority.Critical => "Critical",
        TicketPriority.High => "High",
        TicketPriority.Medium => "Medium",
        TicketPriority.Low => "Low",
        _ => "Medium"
    };

    private int MapPriorityToServiceNow(TicketPriority priority) => priority switch
    {
        TicketPriority.Critical => 1,
        TicketPriority.High => 2,
        TicketPriority.Medium => 3,
        TicketPriority.Low => 4,
        _ => 3
    };

    private int MapPriorityToLinear(TicketPriority priority) => priority switch
    {
        TicketPriority.Critical => 1,
        TicketPriority.High => 2,
        TicketPriority.Medium => 3,
        TicketPriority.Low => 4,
        _ => 3
    };

    #endregion
}