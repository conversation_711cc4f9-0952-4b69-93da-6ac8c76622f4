# HSE Business Work Flow - Incident Management Flow

## Overview
This workflow describes the process for managing incidents, from initial reporting through investigation, analysis, and implementation of control measures to prevent recurrence. The workflow includes approval gates at critical decision points to ensure proper oversight and compliance.

## Incident Classification

### Incident Types
The system supports five main types of incidents:
1. **Accident** - Events resulting in injury or damage
2. **Near Miss** - Events that could have resulted in injury or damage
3. **Environmental Incident** - Events affecting the environment (spills, emissions, etc.)
4. **Security Incident** - Breaches of security protocols or unauthorized access
5. **Other** - Incidents not fitting the above categories

### Incident Categories
Each incident is further classified into specific categories with unique codes:
- **STF** - Slip, Trip, Fall
- **EQUIP** - Equipment Malfunction
- **CHEM** - Chemical Exposure
- **FIRE** - Fire/Explosion
- **MED** - Medical Emergency
- **SEC** - Security Breach
- **VEH** - Vehicle Accident
- **VIO** - Workplace Violence
- **ENV** - Environmental
- **ELEC** - Electrical
- **STRUCT** - Structural
- **FOOD** - Food Safety
- **DATA** - Data Breach
- **NEAR** - Near Miss
- **PROP** - Property Damage

## Status Transitions
The incident progresses through the following statuses:
1. **Reported** - Initial report received
2. **Under Investigation** - Investigation approved and in progress
3. **Awaiting Action** - Control measures determined and awaiting assignment
4. **In Progress** - Control measures being executed
5. **Pending Review** - Actions completed, awaiting verification
6. **Approved** - All actions verified and approved
7. **Closed** - Incident closed with final report sent

## Workflow Steps

### 1. Incident Report Received
**Status:** Reported
**Process:**
- Report received by email or ticketing system or by phone
- Initial incident notification and documentation
- Incident record created in the system

### 2. Generate Incident Number
**Process:**
- Automatic generation of incident number using category codes
- Format: **Number/HSE-CategoryCode/Month/Year**
- Examples:
  - Slip/Trip/Fall: `001/HSE-STF/12/2024`
  - Equipment Malfunction: `002/HSE-EQUIP/12/2024`
  - Chemical Exposure: `003/HSE-CHEM/12/2024`
- Sequential numbering ensures unique identification
- Category code is used for more specific classification than incident type

**Accident Classification:**
- **Major:** If the accident causes a lost time injury or results in the partial or complete shutdown of school operations
- **Minor:** If the accident does not cause a lost time injury or the injury can be treated with first aid
- **Fatality:** If the accident results in the loss of life

### 3. Notify Management
**Process:**
- Immediate notification sent to management team
- High priority alert for awareness and initial response

### 4. Approval Gate 1: Approve Investigation
**Required Approvers:** SuperAdmin, Developer, HSE Manager, or Incident Manager
**Status Transition:** Reported → Under Investigation
**Process:**
- Approval required to proceed with formal investigation
- 24-hour timeout for approval response
- System tracks approver details and comments

### 5. Schedule HSE Investigation
**Actor:** HSE Team
**Status:** Under Investigation
**Process:**
- Schedule investigation within 2 days
- Create calendar invitations for investigation team
- Set investigation timeline and milestones

### 6. Assign Investigation Team
**Process:**
- Select appropriate team members based on incident type and severity
- Assign roles and responsibilities
- Document team composition

### 7. Notify Investigation Team
**Process:**
- Send email notifications to all team members
- Include investigation schedule and initial incident details
- Provide access to incident documentation

### 8. Conduct Investigation (Analyse the Problem)
**Process:**
- Using Human Factor Analysis Classification System (HFACS) or Incident Causative Analysis Method (ICAM)
- Systematic analysis of root causes and contributing factors
- Document findings and evidence

### 9. Approval Gate 2: Approve Control Measures
**Required Approvers:** SuperAdmin, Developer, HSE Manager, or Safety Officer
**Status Transition:** Under Investigation → Awaiting Action
**Process:**
- Investigation team presents findings and proposed control measures
- Approval required before proceeding with control measures
- 48-hour timeout for approval response

### 10. Determination of Control Measures
**Status:** Awaiting Action
**Process:**
- Develop specific control measures based on investigation findings
- Apply hierarchy of controls (Elimination, Substitution, Engineering, Administrative, PPE)
- Document rationale for each control measure

### 11. Assign Control Measures
**Actor:** The lead investigator
**Status Transition:** Awaiting Action → In Progress
**Process:**
- The lead investigator assigns control measures to specific individuals
- Set due dates for each control measure
- Clear assignment of responsibilities and timelines
- System tracks assignments and sends notifications

### 12. Action Execution
**Actor:** The assignee
**Status:** In Progress
**Process:**
- The assignee executes the assigned control measures
- Progress updates and status reports submitted
- Implementation of assigned control measures

### 13. Approval Gate 3: Verify Completion
**Required Approvers:** SuperAdmin, Developer, or Lead Investigator
**Status Transition:** In Progress → Pending Review
**Process:**
- Lead investigator reviews completed actions
- Verification that control measures have been properly implemented
- 72-hour timeout for approval response

### 14. Verification
**Actor:** The lead investigator
**Status:** Pending Review
**Process:**
- The lead investigator verifies all reported actions and control measures
- Ensures effectiveness of implemented controls
- Documents verification findings

### 15. Approval Gate 4: Final Approval
**Required Approvers:** SuperAdmin, Developer, HSE Manager, or Department Head
**Status Transition:** Pending Review → Approved
**Process:**
- Final review of entire incident management process
- Approval of all actions and outcomes
- 24-hour timeout for approval response

### 16. Close Incident & Final Result
**Status Transition:** Approved → Closed
**Process:**
- Final report generated and sent to all stakeholders
- Includes: assignee, investigation team, department heads, top management
- Lessons learned documented and shared
- Incident formally closed in the system

## Report Templates by Category

The incident number format follows: **Number/HSE-CategoryCode/Month/Year**

### Physical Safety Incidents

#### STF - Slip, Trip, Fall
- Location and surface conditions
- Footwear type
- Lighting conditions
- Injuries sustained
- Preventive measures

#### EQUIP - Equipment Malfunction
- Equipment identification
- Nature of malfunction
- Last maintenance date
- Impact on operations
- Repair/replacement actions

#### VEH - Vehicle Accident
- Vehicle details
- Driver information
- Road/weather conditions
- Damage assessment
- Insurance/police reports

### Chemical & Environmental Incidents

#### CHEM - Chemical Exposure
- Chemical identification (SDS)
- Exposure route and duration
- PPE worn
- Decontamination measures
- Medical treatment

#### ENV - Environmental
- Type of environmental impact
- Quantity released/affected
- Containment measures
- Regulatory notifications
- Remediation plan

### Emergency Response Incidents

#### FIRE - Fire/Explosion
- Source of ignition
- Materials involved
- Evacuation details
- Fire suppression used
- Property damage

#### MED - Medical Emergency
- Nature of medical issue
- First aid provided
- Emergency services response
- Hospital transport
- Follow-up care needed

#### ELEC - Electrical
- Voltage/equipment involved
- Arc flash/shock details
- Electrical safety measures
- Equipment isolation
- Repairs required

### Security & Safety Incidents

#### SEC - Security Breach
- Type of breach
- Areas/systems accessed
- Security measures bypassed
- Assets at risk
- Law enforcement involvement

#### DATA - Data Breach
- Systems affected
- Data types compromised
- Number of records
- Notification requirements
- Remediation steps

#### VIO - Workplace Violence
- Parties involved
- Nature of incident
- Witness statements
- Security response
- HR/Legal actions

### Property & Structural Incidents

#### STRUCT - Structural
- Building/structure affected
- Type of damage
- Safety risks
- Emergency stabilization
- Repair requirements

#### PROP - Property Damage
- Property description
- Cause of damage
- Estimated cost
- Insurance claim
- Replacement/repair plan

### Other Categories

#### FOOD - Food Safety
- Food items involved
- Contamination type
- Affected individuals
- Health department notification
- Disposal/recall actions

#### NEAR - Near Miss
- What almost happened
- Contributing factors
- Potential severity
- Preventive actions
- Lessons learned

## Analysis Methods

### Human Factor Analysis Classification System (HFACS)
- Systematic approach to identify human factors contributing to incidents
- Categories: Unsafe acts, preconditions, unsafe supervision, organizational influences

### Incident Causative Analysis Method (ICAM)
- Comprehensive investigation methodology
- Focus on organizational and systemic factors
- Identification of absent or failed defenses

## Investigation Team Composition

The investigation team may include:
- HSE personnel (lead investigator)
- Department representatives
- Subject matter experts
- Management representatives
- External specialists (if required)

## Control Measure Hierarchy

1. **Elimination** - Remove the hazard completely
2. **Substitution** - Replace with something less hazardous
3. **Engineering Controls** - Isolate people from the hazard
4. **Administrative Controls** - Change work practices
5. **Personal Protective Equipment** - Protect the individual

## Key Stakeholders
- Incident reporter
- HSE team
- Investigation team members
- Department heads and managers
- Top management
- Assignees responsible for control measures
- Witnesses and affected personnel

## Approval Roles
The following roles can approve workflow transitions:

### System Override Roles (Can approve at any gate)
- **SuperAdmin** - Full system administrative privileges
- **Developer** - Development team with bypass privileges

### Business Approval Roles
- **HSE Manager** - Approves investigation start (Gate 1), control measures (Gate 2), and final closure (Gate 4)
- **Incident Manager** - Approves investigation start (Gate 1)
- **Safety Officer** - Approves control measures (Gate 2)
- **Lead Investigator** - Verifies action completion (Gate 3)
- **Department Head** - Provides final approval (Gate 4)

## Deliverables
- Incident report with assigned number
- Investigation report
- Root cause analysis
- Control measures plan
- Implementation verification report
- Final incident closure report
- Lessons learned documentation
- Communication to relevant stakeholders

## Documentation Requirements
- All incidents must be documented with appropriate classification
- Investigation findings must be evidence-based
- Control measures must be specific, measurable, and time-bound
- Verification of implementation must be documented
- Regular review and follow-up until closure