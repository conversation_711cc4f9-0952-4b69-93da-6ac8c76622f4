using Harmoni360.Domain.Common;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;
using System.Text.Json;

namespace Harmoni360.Domain.Entities;

public class WorkflowBookmark : BaseEntity, IAuditableEntity
{

    [Required]
    public int WorkflowExecutionId { get; set; }

    [Required]
    [MaxLength(100)]
    public string ActivityId { get; set; } = string.Empty;

    [Required]
    [MaxLength(500)]
    public string ActivityName { get; set; } = string.Empty;

    [Required]
    [MaxLength(100)]
    public string ActivityType { get; set; } = string.Empty;

    [Required]
    [MaxLength(500)]
    public string BookmarkName { get; set; } = string.Empty;

    [MaxLength(1000)]
    public string? Stimulus { get; set; }

    public string? Payload { get; set; }

    public string? Metadata { get; set; }


    public DateTime? ResumedAt { get; set; }

    [MaxLength(100)]
    public string? ResumedBy { get; set; }

    [Required]
    public bool IsActive { get; set; } = true;

    [MaxLength(500)]
    public string? CallbackUrl { get; set; }

    [MaxLength(1000)]
    public string? Tag { get; set; }

    public int? Priority { get; set; } = 0;

    // Audit properties
    public DateTime CreatedAt { get; private set; }
    public string CreatedBy { get; private set; } = string.Empty;
    public DateTime? LastModifiedAt { get; private set; }
    public string? LastModifiedBy { get; private set; }

    // Navigation properties
    [ForeignKey(nameof(WorkflowExecutionId))]
    public virtual WorkflowExecution WorkflowExecution { get; set; } = null!;

    // Helper methods
    public T? GetPayloadData<T>() where T : class
    {
        if (string.IsNullOrEmpty(Payload))
            return null;

        try
        {
            return JsonSerializer.Deserialize<T>(Payload);
        }
        catch
        {
            return null;
        }
    }

    public void SetPayloadData<T>(T? data) where T : class
    {
        Payload = data != null ? JsonSerializer.Serialize(data) : null;
    }

    public Dictionary<string, object>? GetMetadata()
    {
        if (string.IsNullOrEmpty(Metadata))
            return null;

        try
        {
            return JsonSerializer.Deserialize<Dictionary<string, object>>(Metadata);
        }
        catch
        {
            return null;
        }
    }

    public void SetMetadata(Dictionary<string, object>? metadata)
    {
        Metadata = metadata != null ? JsonSerializer.Serialize(metadata) : null;
    }

    public void Resume(string resumedBy)
    {
        if (!IsActive)
            throw new InvalidOperationException("Cannot resume inactive bookmark");

        ResumedAt = DateTime.UtcNow;
        ResumedBy = resumedBy;
        IsActive = false;
    }
}