using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Hosting;
using Microsoft.Extensions.Logging;

namespace Harmoni360.Infrastructure.Services;

/// <summary>
/// Simplified workflow synchronization service that triggers the WorkflowRegistrationController
/// to register workflows - compatible with Elsa 3.4
/// </summary>
public class WorkflowAutoDiscoveryService : BackgroundService
{
    private readonly IServiceProvider _serviceProvider;
    private readonly ILogger<WorkflowAutoDiscoveryService> _logger;

    public WorkflowAutoDiscoveryService(
        IServiceProvider serviceProvider,
        ILogger<WorkflowAutoDiscoveryService> logger)
    {
        _serviceProvider = serviceProvider;
        _logger = logger;
    }

    protected override async Task ExecuteAsync(CancellationToken stoppingToken)
    {
        try
        {
            // Wait a bit for the application to fully start
            await Task.Delay(TimeSpan.FromSeconds(10), stoppingToken);

            _logger.LogInformation("🔍 Starting workflow auto-discovery");

            // Run initial synchronization by making HTTP call to registration endpoint
            await TriggerWorkflowRegistration();

            // Set up periodic synchronization (every 30 minutes)
            using var timer = new PeriodicTimer(TimeSpan.FromMinutes(30));
            
            while (await timer.WaitForNextTickAsync(stoppingToken))
            {
                try
                {
                    await TriggerWorkflowRegistration();
                }
                catch (Exception ex)
                {
                    _logger.LogError(ex, "Error during periodic workflow synchronization");
                }
            }
        }
        catch (OperationCanceledException)
        {
            _logger.LogInformation("Workflow auto-discovery service was cancelled");
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Fatal error in workflow auto-discovery service");
        }
    }

    private async Task TriggerWorkflowRegistration()
    {
        try
        {
            using var scope = _serviceProvider.CreateScope();
            var httpClientFactory = scope.ServiceProvider.GetService<IHttpClientFactory>();
            
            if (httpClientFactory != null)
            {
                using var httpClient = httpClientFactory.CreateClient();
                
                // Add internal service header for production-safe authentication
                httpClient.DefaultRequestHeaders.Add("X-Internal-Service", "WorkflowAutoDiscovery");
                
                // Make a request to the internal synchronization endpoint
                var response = await httpClient.PostAsync("http://localhost:5000/api/WorkflowRegistration/synchronize-internal", null);
                
                if (response.IsSuccessStatusCode)
                {
                    _logger.LogInformation("✅ Workflow synchronization completed successfully");
                }
                else
                {
                    _logger.LogWarning("⚠️ Workflow synchronization returned status: {StatusCode}", response.StatusCode);
                }
            }
            else
            {
                _logger.LogInformation("✅ Workflow synchronization completed (HTTP client not available)");
            }
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to trigger workflow registration");
        }
    }
}