using Elsa.Workflows;
using Elsa.Workflows.Attributes;
using Harmoni360.Application.Common.Interfaces;
using Harmoni360.Application.Features.WorkflowManagement.IncidentWorkflow.Models;
using Harmoni360.Domain.Interfaces;
using Microsoft.Extensions.Logging;
using System.ComponentModel;
using System.Text.Json.Serialization;
using Elsa.Extensions;
using Elsa.Workflows.Models;

namespace Harmoni360.Application.Features.WorkflowManagement.IncidentWorkflow.Activities;

/// <summary>
/// Activity that creates external tickets for incident tracking and integration
/// </summary>
[Activity("Incident Management", "Create Ticket", "Creates a ticket in external ticketing system for incident tracking")]
public class CreateTicketActivity : IncidentActivityBase
{
    private readonly ITicketingService _ticketingService;
    
    public CreateTicketActivity(
        ILogger<CreateTicketActivity> logger,
        IIncidentRepository incidentRepository,
        ICurrentUserService currentUserService,
        ITicketingService ticketingService)
        : base(logger, incidentRepository, currentUserService)
    {
        _ticketingService = ticketingService;
    }

    /// <summary>
    /// Ticket priority level
    /// </summary>
    [Input]
    [Description("Priority level for the ticket")]
    [JsonPropertyName("ticketPriority")]
    public TicketPriority TicketPriority { get; set; } = TicketPriority.Medium;

    /// <summary>
    /// Ticket type
    /// </summary>
    [Input]
    [Description("Type of ticket to create")]
    [JsonPropertyName("ticketType")]
    public TicketType TicketType { get; set; } = TicketType.Incident;

    /// <summary>
    /// Assignee ID for the ticket
    /// </summary>
    [Input]
    [Description("User ID to assign the ticket to")]
    [JsonPropertyName("assigneeId")]
    public string? AssigneeId { get; set; }

    /// <summary>
    /// Additional labels for the ticket
    /// </summary>
    [Input]
    [Description("Labels to add to the ticket")]
    [JsonPropertyName("labels")]
    public List<string> Labels { get; set; } = new();

    /// <summary>
    /// Custom fields for the ticket
    /// </summary>
    [Input]
    [Description("Custom fields for the ticket")]
    [JsonPropertyName("customFields")]
    public Dictionary<string, string> CustomFields { get; set; } = new();

    /// <summary>
    /// The incident workflow context
    /// </summary>
    [Input(Description = "The incident workflow context")]
    public Input<IncidentWorkflowContext> Context { get; set; } = default!;

    /// <summary>
    /// Created ticket ID
    /// </summary>
    public Output<string?> TicketId { get; set; } = new();

    /// <summary>
    /// External ticket URL
    /// </summary>
    public Output<string?> TicketUrl { get; set; } = new();

    /// <summary>
    /// Success indicator
    /// </summary>
    public Output<bool> Success { get; set; } = new();

    protected override async ValueTask ExecuteAsync(ActivityExecutionContext context)
    {
        var workflowContext = Context.Get(context);
        
        try
        {
            Logger.LogInformation("Creating external ticket for incident {IncidentId}", workflowContext.IncidentId);

            var incident = await IncidentRepository.GetByIdAsync(Guid.Parse(workflowContext.IncidentId));
            if (incident == null)
            {
                Logger.LogError("Incident not found: {IncidentId}", workflowContext.IncidentId);
                Success.Set(context, false);
                return;
            }

            // Create ticket request
            var ticketRequest = new CreateTicketRequest
            {
                Title = $"HSE Incident: {incident.Title}",
                Description = BuildTicketDescription(incident, workflowContext),
                Priority = TicketPriority,
                Type = TicketType,
                ReporterId = incident.ReporterEmail,
                ReporterEmail = incident.ReporterEmail,
                AssigneeId = AssigneeId,
                CustomFields = BuildCustomFields(incident, workflowContext),
                Labels = BuildTicketLabels(incident, workflowContext)
            };

            // Create the ticket
            var ticketId = await _ticketingService.CreateTicketAsync(ticketRequest);
            var success = !string.IsNullOrEmpty(ticketId);
            
            TicketId.Set(context, ticketId);
            Success.Set(context, success);
            
            if (success)
            {
                var ticketUrl = GenerateTicketUrl(ticketId);
                TicketUrl.Set(context, ticketUrl);
                
                Logger.LogInformation("External ticket created successfully: {TicketId} for incident {IncidentId}", 
                    ticketId, workflowContext.IncidentId);

                // Update workflow context with ticket information
                workflowContext.ExternalTicketId = ticketId;
                workflowContext.ExternalTicketUrl = ticketUrl;
                
                // Add to activity log
                LogActivity(nameof(CreateTicketActivity), 
                    "External ticket created - ID: {TicketId}, Priority: {Priority}", 
                    ticketId, TicketPriority.ToString());
            }
            else
            {
                Logger.LogWarning("Failed to create external ticket for incident {IncidentId}", workflowContext.IncidentId);
                
                LogActivity(nameof(CreateTicketActivity), 
                    "Failed to create external ticket - service may be unavailable - Priority: {Priority}", 
                    TicketPriority.ToString());
            }
        }
        catch (Exception ex)
        {
            Logger.LogError(ex, "Error creating external ticket for incident {IncidentId}", workflowContext.IncidentId);
            
            Success.Set(context, false);
            LogActivityError(nameof(CreateTicketActivity), ex, 
                "Error creating external ticket for incident: {IncidentId}", 
                workflowContext.IncidentId);
        }
    }

    private string BuildTicketDescription(Domain.Entities.Incident incident, IncidentWorkflowContext workflowContext)
    {
        var description = $@"HSE Incident Report

**Incident Details:**
- Incident Number: {workflowContext.IncidentNumber}
- Incident Date: {incident.IncidentDate:yyyy-MM-dd HH:mm}
- Location: {incident.Location}
- Severity: {incident.Severity}
- Status: {incident.Status}

**Description:**
{incident.Description}

**Reporter:**
- Name: {incident.ReporterName}
- Email: {incident.ReporterEmail}

**Workflow Information:**
- Created: {DateTime.UtcNow:yyyy-MM-dd HH:mm} UTC

This ticket was automatically created by the Harmoni360 HSE Management System for tracking and external integration purposes.";

        return description;
    }

    private Dictionary<string, string> BuildCustomFields(Domain.Entities.Incident incident, IncidentWorkflowContext workflowContext)
    {
        var customFields = new Dictionary<string, string>(CustomFields)
        {
            ["incident_id"] = workflowContext.IncidentId,
            ["incident_number"] = workflowContext.IncidentNumber,
            ["workflow_instance"] = "workflow-instance",
            ["harmoni360_source"] = "true",
            ["incident_severity"] = incident.Severity.ToString(),
            ["incident_date"] = incident.IncidentDate.ToString("yyyy-MM-dd"),
            ["reporter_email"] = incident.ReporterEmail
        };

        return customFields;
    }

    private List<string> BuildTicketLabels(Domain.Entities.Incident incident, IncidentWorkflowContext workflowContext)
    {
        var labels = new List<string>(Labels)
        {
            "hse",
            "incident",
            "harmoni360",
            incident.Severity.ToString().ToLower()
        };

        // Add location-based label if available
        if (!string.IsNullOrEmpty(incident.Location))
        {
            labels.Add($"location-{incident.Location.ToLower().Replace(" ", "-")}");
        }

        return labels.Distinct().ToList();
    }

    private string? GenerateTicketUrl(string ticketId)
    {
        // This would be configured based on the ticketing system
        var baseUrl = GetTicketingBaseUrl();
        if (string.IsNullOrEmpty(baseUrl))
            return null;

        // Generate appropriate URL based on the ticketing system
        var ticketingProvider = GetTicketingProvider();
        return ticketingProvider?.ToLower() switch
        {
            "jira" => $"{baseUrl}/browse/{ticketId}",
            "servicenow" => $"{baseUrl}/nav_to.do?uri=incident.do?sys_id={ticketId}",
            "linear" => $"{baseUrl}/issue/{ticketId}",
            _ => $"{baseUrl}/tickets/{ticketId}"
        };
    }

    private string? GetTicketingBaseUrl()
    {
        // This would typically come from configuration
        // For now, return a placeholder
        return "https://your-ticketing-system.com";
    }

    private string? GetTicketingProvider()
    {
        // This would typically come from configuration
        return "generic";
    }
}