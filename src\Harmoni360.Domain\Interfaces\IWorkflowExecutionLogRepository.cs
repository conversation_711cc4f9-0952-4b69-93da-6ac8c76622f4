using Harmoni360.Domain.Common;
using Harmoni360.Domain.Entities;
using Harmoni360.Domain.Enums;

namespace Harmoni360.Domain.Interfaces;

public interface IWorkflowExecutionLogRepository : IRepository<WorkflowExecutionLog>
{
    Task<IEnumerable<WorkflowExecutionLog>> GetByWorkflowExecutionIdAsync(
        int workflowExecutionId, 
        CancellationToken cancellationToken = default);
    
    Task<IEnumerable<WorkflowExecutionLog>> GetByActivityIdAsync(
        string activityId, 
        CancellationToken cancellationToken = default);
    
    Task<IEnumerable<WorkflowExecutionLog>> GetByEventTypeAsync(
        WorkflowLogEventType eventType, 
        CancellationToken cancellationToken = default);
    
    Task<IEnumerable<WorkflowExecutionLog>> GetByDateRangeAsync(
        DateTime startDate, 
        DateTime endDate, 
        CancellationToken cancellationToken = default);
    
    Task<IEnumerable<WorkflowExecutionLog>> GetLogsByWorkflowAndActivityAsync(
        int workflowExecutionId, 
        string activityId, 
        CancellationToken cancellationToken = default);

    Task<WorkflowExecutionLog?> GetLatestLogAsync(
        int workflowExecutionId, 
        CancellationToken cancellationToken = default);
    
    Task<IEnumerable<WorkflowExecutionLog>> GetErrorLogsAsync(
        int? workflowExecutionId = null, 
        CancellationToken cancellationToken = default);
    
    Task<int> GetLogCountAsync(
        int workflowExecutionId, 
        CancellationToken cancellationToken = default);
    
    Task DeleteOldLogsAsync(
        DateTime cutoffDate, 
        CancellationToken cancellationToken = default);
}