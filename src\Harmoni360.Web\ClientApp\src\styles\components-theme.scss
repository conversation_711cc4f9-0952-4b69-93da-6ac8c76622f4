// Theme-aware component styles for HarmoniHSE360
// This file contains dark mode updates for core UI components
// Created: June 2025

// ========================================
// NAVIGATION COMPONENTS
// ========================================

// Sidebar styling
.sidebar {
  background-color: var(--theme-bg-secondary);
  border-right: 1px solid var(--theme-border);
  transition: background-color 0.3s ease, border-color 0.3s ease;

  .sidebar-brand {
    background-color: var(--theme-bg-tertiary);
    border-bottom: 1px solid var(--theme-border);
    color: var(--theme-text-primary);
    
    &:hover {
      color: var(--harmoni-teal-500);
    }
  }

  .sidebar-nav {
    .nav-link {
      color: var(--theme-text-secondary);
      border-left: 3px solid transparent;
      transition: all 0.2s ease;

      &:hover {
        background-color: var(--theme-bg-tertiary);
        color: var(--theme-text-primary);
        border-left-color: var(--harmoni-teal-500);
      }

      &.active {
        background-color: var(--harmoni-teal-500);
        color: white;
        border-left-color: var(--harmoni-teal-600);

        .nav-icon {
          color: white;
        }
      }

      .nav-icon {
        color: var(--theme-text-muted);
        transition: color 0.2s ease;
      }
    }

    .nav-group {
      .nav-group-toggle {
        color: #b3b3b3;
        background-color: transparent;

        &:hover {
          background-color: var(--theme-bg-tertiary);
          color: var(--theme-text-primary);
        }

        &::after {
          border-color: var(--theme-text-muted);
        }
      }

      &.show .nav-group-toggle {
        background-color: var(--theme-bg-tertiary);
        color: var(--theme-text-primary);
      }
    }

    .nav-title {
      color: var(--theme-text-muted);
      font-size: 0.75rem;
      text-transform: uppercase;
      letter-spacing: 0.5px;
      padding: 1rem 1rem 0.5rem;
      margin-top: 1rem;
    }
  }

  // Sidebar footer (application settings)
  .sidebar-footer {
    border-top: 1px solid var(--theme-border);
    background-color: var(--theme-bg-tertiary);
  }
}

// Sidebar minimizer
.sidebar-toggler {
  background-color: var(--theme-bg-tertiary);
  border-color: var(--theme-border);
  color: var(--theme-text-muted);

  &:hover {
    background-color: var(--theme-bg-secondary);
    color: var(--theme-text-primary);
  }
}

// ========================================
// HEADER COMPONENTS
// ========================================

.header {
  background-color: var(--theme-bg-primary);
  border-bottom: 1px solid var(--theme-border);
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);

  .header-toggler {
    color: var(--theme-text-primary);
    
    &:hover {
      background-color: var(--theme-bg-secondary);
    }
  }

  .header-brand {
    color: var(--theme-text-primary);
  }

  .header-nav {
    .nav-link {
      color: var(--theme-text-secondary);

      &:hover {
        color: var(--theme-text-primary);
        background-color: var(--theme-bg-secondary);
      }
    }
  }
}

// ========================================
// CARD COMPONENTS
// ========================================

.card {
  background-color: var(--theme-bg-primary);
  border: 1px solid var(--theme-border);
  color: var(--theme-text-primary);
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.05);
  transition: all 0.3s ease;

  &:hover {
    box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
  }

  .card-header {
    background-color: var(--theme-bg-secondary);
    border-bottom: 1px solid var(--theme-border);
    color: var(--theme-text-primary);

    .card-header-actions {
      .btn-ghost {
        color: var(--theme-text-muted);

        &:hover {
          color: var(--theme-text-primary);
          background-color: var(--theme-bg-tertiary);
        }
      }
    }
  }

  .card-body {
    color: var(--theme-text-primary);

    .text-muted {
      color: var(--theme-text-muted) !important;
    }

    .text-secondary {
      color: var(--theme-text-secondary) !important;
    }
  }

  .card-footer {
    background-color: var(--theme-bg-secondary);
    border-top: 1px solid var(--theme-border);
    color: var(--theme-text-secondary);
  }

  // Stat cards
  &.stat-card {
    .stat-value {
      color: var(--theme-text-primary);
    }

    .stat-label {
      color: var(--theme-text-muted);
    }

    .stat-icon {
      background-color: var(--theme-bg-tertiary);
      color: var(--harmoni-teal-500);
    }
  }
}

// ========================================
// FORM COMPONENTS
// ========================================

.form-control,
.form-select {
  background-color: var(--theme-bg-primary);
  border: 1px solid var(--theme-border);
  color: var(--theme-text-primary);
  transition: all 0.2s ease;

  &::placeholder {
    color: var(--theme-text-muted);
    opacity: 0.7;
  }

  &:focus {
    background-color: var(--theme-bg-primary);
    border-color: var(--harmoni-teal-500);
    color: var(--theme-text-primary);
    box-shadow: 0 0 0 0.2rem rgba(0, 151, 167, 0.25);
  }

  &:disabled,
  &[readonly] {
    background-color: var(--theme-bg-tertiary);
    color: var(--theme-text-disabled);
    opacity: 0.6;
  }

  &.is-invalid {
    border-color: var(--theme-risk-critical);

    &:focus {
      border-color: var(--theme-risk-critical);
      box-shadow: 0 0 0 0.2rem rgba(220, 53, 69, 0.25);
    }
  }

  &.is-valid {
    border-color: var(--theme-risk-low);

    &:focus {
      border-color: var(--theme-risk-low);
      box-shadow: 0 0 0 0.2rem rgba(40, 167, 69, 0.25);
    }
  }
}

// Textarea specific
textarea.form-control {
  min-height: 100px;
  resize: vertical;
}

// Form labels
.form-label {
  color: var(--theme-text-primary);
  font-weight: 500;
  margin-bottom: 0.5rem;
}

// Form text helpers
.form-text {
  color: var(--theme-text-muted);
  font-size: 0.875rem;
}

// Input groups
.input-group {
  .input-group-text {
    background-color: var(--theme-bg-secondary);
    border: 1px solid var(--theme-border);
    color: var(--theme-text-secondary);
  }

  .form-control:focus ~ .input-group-text {
    border-color: var(--harmoni-teal-500);
  }
}

// Custom checkboxes and radios
.form-check-input {
  background-color: var(--theme-bg-primary);
  border: 1px solid var(--theme-border);

  &:checked {
    background-color: var(--harmoni-teal-500);
    border-color: var(--harmoni-teal-500);
  }

  &:focus {
    border-color: var(--harmoni-teal-500);
    box-shadow: 0 0 0 0.2rem rgba(0, 151, 167, 0.25);
  }

  &:disabled {
    background-color: var(--theme-bg-tertiary);
    opacity: 0.5;
  }
}

.form-check-label {
  color: var(--theme-text-primary);
}

// Custom switches
.form-switch {
  .form-check-input {
    background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='-4 -4 8 8'%3e%3ccircle r='3' fill='%23ffffff'/%3e%3c/svg%3e");
    
    &:checked {
      background-color: var(--harmoni-teal-500);
    }
  }
}

// ========================================
// BUTTON COMPONENTS
// ========================================

.btn {
  transition: all 0.2s ease;
  border: 1px solid transparent;

  // Primary button
  &.btn-primary {
    background-color: var(--harmoni-teal-500);
    border-color: var(--harmoni-teal-500);
    color: white;

    &:hover {
      background-color: var(--harmoni-teal-600);
      border-color: var(--harmoni-teal-600);
    }

    &:focus,
    &.focus {
      box-shadow: 0 0 0 0.2rem rgba(0, 151, 167, 0.25);
    }

    &:disabled,
    &.disabled {
      background-color: var(--harmoni-teal-300);
      border-color: var(--harmoni-teal-300);
      opacity: 0.6;
    }
  }

  // Secondary button
  &.btn-secondary {
    background-color: var(--theme-bg-secondary);
    border-color: var(--theme-border);
    color: var(--theme-text-primary);

    &:hover {
      background-color: var(--theme-bg-tertiary);
      border-color: var(--theme-border-strong);
      color: var(--theme-text-primary);
    }

    &:focus,
    &.focus {
      box-shadow: 0 0 0 0.2rem rgba(108, 117, 125, 0.25);
    }
  }

  // Ghost button
  &.btn-ghost {
    background-color: transparent;
    border-color: transparent;
    color: var(--theme-text-secondary);

    &:hover {
      background-color: var(--theme-bg-secondary);
      color: var(--theme-text-primary);
    }
  }

  // Danger button
  &.btn-danger {
    background-color: var(--theme-risk-critical);
    border-color: var(--theme-risk-critical);
    color: white;

    &:hover {
      background-color: var(--risk-critical-dark);
      border-color: var(--risk-critical-dark);
    }
  }

  // Success button
  &.btn-success {
    background-color: var(--theme-risk-low);
    border-color: var(--theme-risk-low);
    color: white;

    &:hover {
      background-color: var(--risk-low-dark);
      border-color: var(--risk-low-dark);
    }
  }

  // Warning button
  &.btn-warning {
    background-color: var(--theme-risk-medium);
    border-color: var(--theme-risk-medium);
    color: var(--theme-warning-text);

    &:hover {
      background-color: var(--risk-medium-dark);
      border-color: var(--risk-medium-dark);
    }
  }

  // Info button
  &.btn-info {
    background-color: var(--theme-info-bg);
    border-color: var(--theme-info-bg);
    color: var(--theme-info-text);

    &:hover {
      background-color: var(--status-draft-dark);
      border-color: var(--status-draft-dark);
    }
  }
}

// Button groups
.btn-group {
  .btn {
    border-color: var(--theme-border);

    &:not(:last-child) {
      border-right: 0;
    }

    &.active {
      background-color: var(--harmoni-teal-500);
      border-color: var(--harmoni-teal-500);
      color: white;
    }
  }
}

// ========================================
// DROPDOWN COMPONENTS
// ========================================

.dropdown-menu {
  background-color: var(--theme-bg-primary);
  border: 1px solid var(--theme-border);
  box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);

  .dropdown-header {
    color: var(--theme-text-muted);
    background-color: var(--theme-bg-secondary);
  }

  .dropdown-item {
    color: var(--theme-text-primary);
    transition: all 0.2s ease;

    &:hover,
    &:focus {
      background-color: var(--theme-bg-tertiary);
      color: var(--theme-text-primary);
    }

    &.active,
    &:active {
      background-color: var(--harmoni-teal-500);
      color: white;
    }

    &.disabled,
    &:disabled {
      color: var(--theme-text-disabled);
      opacity: 0.5;
    }
  }

  .dropdown-divider {
    border-top-color: var(--theme-border);
    margin: 0.5rem 0;
  }
}

// ========================================
// MODAL COMPONENTS
// ========================================

.modal-content {
  background-color: var(--theme-bg-primary);
  border: 1px solid var(--theme-border);
  color: var(--theme-text-primary);

  .modal-header {
    background-color: var(--theme-bg-secondary);
    border-bottom: 1px solid var(--theme-border);
    color: var(--theme-text-primary);

    .btn-close {
      filter: var(--theme-close-button-filter, none);
    }
  }

  .modal-body {
    color: var(--theme-text-primary);
  }

  .modal-footer {
    background-color: var(--theme-bg-secondary);
    border-top: 1px solid var(--theme-border);
  }
}

.modal-backdrop {
  background-color: var(--theme-bg-overlay);
}

// ========================================
// PAGINATION COMPONENTS
// ========================================

.pagination {
  .page-link {
    background-color: var(--theme-bg-primary);
    border-color: var(--theme-border);
    color: var(--theme-text-primary);

    &:hover {
      background-color: var(--theme-bg-secondary);
      border-color: var(--theme-border);
      color: var(--theme-text-primary);
    }

    &:focus {
      box-shadow: 0 0 0 0.2rem rgba(0, 151, 167, 0.25);
    }
  }

  .page-item {
    &.active .page-link {
      background-color: var(--harmoni-teal-500);
      border-color: var(--harmoni-teal-500);
      color: white;
    }

    &.disabled .page-link {
      background-color: var(--theme-bg-tertiary);
      border-color: var(--theme-border);
      color: var(--theme-text-disabled);
    }
  }
}

// ========================================
// BREADCRUMB COMPONENTS
// ========================================

.breadcrumb {
  background-color: var(--theme-bg-secondary);
  border: 1px solid var(--theme-border);

  .breadcrumb-item {
    color: var(--theme-text-secondary);

    a {
      color: var(--harmoni-teal-500);
      text-decoration: none;

      &:hover {
        color: var(--harmoni-teal-600);
        text-decoration: underline;
      }
    }

    &.active {
      color: var(--theme-text-primary);
    }

    + .breadcrumb-item::before {
      color: var(--theme-text-muted);
    }
  }
}

// ========================================
// PROGRESS COMPONENTS
// ========================================

.progress {
  background-color: var(--theme-bg-tertiary);
  border: 1px solid var(--theme-border);

  .progress-bar {
    background-color: var(--harmoni-teal-500);
    color: white;
    font-weight: 500;
  }

  .progress-bar-success {
    background-color: var(--theme-risk-low);
  }

  .progress-bar-warning {
    background-color: var(--theme-risk-medium);
  }

  .progress-bar-danger {
    background-color: var(--theme-risk-critical);
  }
}

// ========================================
// TABLE COMPONENTS
// ========================================

.table {
  color: var(--theme-text-primary);
  border-color: var(--theme-border);

  th {
    background-color: var(--theme-bg-secondary);
    border-color: var(--theme-border);
    color: var(--theme-text-primary);
    font-weight: 600;
  }

  td {
    border-color: var(--theme-border);
    color: var(--theme-text-primary);
  }

  &.table-striped {
    tbody tr:nth-of-type(odd) {
      background-color: var(--theme-bg-tertiary);
      
      td {
        background-color: transparent;
      }
    }
  }

  &.table-hover {
    tbody tr:hover {
      background-color: var(--theme-bg-tertiary);
      color: var(--theme-text-primary);
      
      td {
        background-color: transparent;
      }
    }
  }

  &.table-bordered {
    border: 1px solid var(--theme-border);
    
    th,
    td {
      border: 1px solid var(--theme-border);
    }
  }

  // Dark table variant
  &.table-dark {
    background-color: var(--theme-bg-secondary);
    color: var(--theme-text-primary);

    th {
      background-color: var(--theme-bg-tertiary);
    }

    &.table-striped tbody tr:nth-of-type(odd) {
      background-color: var(--theme-bg-primary);
    }

    &.table-hover tbody tr:hover {
      background-color: var(--theme-bg-primary);
    }
  }

  // Responsive table wrapper
  .table-responsive {
    border: 1px solid var(--theme-border);
    border-radius: 0.375rem;
  }
}

// ========================================
// BADGE COMPONENTS
// ========================================

.badge {
  font-weight: 500;
  font-size: 0.75rem;
  border-radius: 0.375rem;
  padding: 0.25rem 0.5rem;
  transition: all 0.2s ease;

  // Primary badge
  &.badge-primary {
    background-color: var(--harmoni-teal-500);
    color: white;
  }

  // Secondary badge
  &.badge-secondary {
    background-color: var(--theme-risk-none);
    color: white;
  }

  // Success badge (using theme-aware colors)
  &.badge-success {
    background-color: var(--theme-risk-low);
    color: white;
  }

  // Danger badge
  &.badge-danger {
    background-color: var(--theme-risk-critical);
    color: white;
  }

  // Warning badge
  &.badge-warning {
    background-color: var(--theme-risk-medium);
    color: var(--theme-warning-text);
  }

  // Info badge (FIXED - now WCAG compliant)
  &.badge-info {
    background-color: var(--theme-info-bg);
    color: var(--theme-info-text);
  }

  // Light badge
  &.badge-light {
    background-color: var(--theme-bg-secondary);
    color: var(--theme-text-primary);
    border: 1px solid var(--theme-border);
  }

  // Dark badge
  &.badge-dark {
    background-color: var(--theme-text-primary);
    color: var(--theme-bg-primary);
  }

  // HSSE-specific status badges
  &.badge-critical {
    background-color: var(--theme-risk-critical);
    color: white;
    position: relative;

    &::before {
      content: "⚠️";
      margin-right: 0.25rem;
    }

    // Add pattern for color-blind users
    background-image: repeating-linear-gradient(
      45deg,
      transparent,
      transparent 2px,
      rgba(255, 255, 255, 0.1) 2px,
      rgba(255, 255, 255, 0.1) 4px
    );
  }

  &.badge-high-risk {
    background-color: var(--theme-risk-high);
    color: white;

    &::before {
      content: "🔶";
      margin-right: 0.25rem;
    }
  }

  &.badge-medium-risk {
    background-color: var(--theme-risk-medium);
    color: var(--theme-warning-text);

    &::before {
      content: "⚡";
      margin-right: 0.25rem;
    }
  }

  &.badge-low-risk {
    background-color: var(--theme-risk-low);
    color: white;

    &::before {
      content: "✅";
      margin-right: 0.25rem;
    }
  }

  // Status badges
  &.badge-draft {
    background-color: var(--theme-status-draft);
    color: white;
  }

  &.badge-in-progress {
    background-color: var(--theme-status-progress);
    color: var(--theme-warning-text);
  }

  &.badge-under-review {
    background-color: var(--theme-status-review);
    color: white;
  }

  &.badge-completed {
    background-color: var(--theme-status-complete);
    color: white;
  }

  &.badge-overdue {
    background-color: var(--theme-status-overdue);
    color: white;
    animation: pulse 2s infinite;
  }

  &.badge-cancelled {
    background-color: var(--theme-status-cancelled);
    color: white;
    opacity: 0.7;
  }

  // Outline badges
  &.badge-outline-primary {
    background-color: transparent;
    color: var(--harmoni-teal-500);
    border: 1px solid var(--harmoni-teal-500);
  }

  &.badge-outline-secondary {
    background-color: transparent;
    color: var(--theme-risk-none);
    border: 1px solid var(--theme-risk-none);
  }

  &.badge-outline-success {
    background-color: transparent;
    color: var(--theme-risk-low);
    border: 1px solid var(--theme-risk-low);
  }

  &.badge-outline-danger {
    background-color: transparent;
    color: var(--theme-risk-critical);
    border: 1px solid var(--theme-risk-critical);
  }

  &.badge-outline-warning {
    background-color: transparent;
    color: var(--theme-risk-medium);
    border: 1px solid var(--theme-risk-medium);
  }

  &.badge-outline-info {
    background-color: transparent;
    color: var(--theme-info-bg);
    border: 1px solid var(--theme-info-bg);
  }

  // Pill badges
  &.badge-pill {
    border-radius: 10rem;
  }

  // Large badges
  &.badge-lg {
    font-size: 0.875rem;
    padding: 0.375rem 0.75rem;
  }

  // Small badges
  &.badge-sm {
    font-size: 0.625rem;
    padding: 0.125rem 0.375rem;
  }
}

// Badge animations
@keyframes pulse {
  0% {
    opacity: 1;
  }
  50% {
    opacity: 0.7;
  }
  100% {
    opacity: 1;
  }
}

// ========================================
// ALERT COMPONENTS
// ========================================

.alert {
  border: 1px solid transparent;
  border-radius: 0.375rem;
  padding: 0.75rem 1rem;
  margin-bottom: 1rem;
  position: relative;

  &.alert-primary {
    background-color: rgba(0, 151, 167, 0.1);
    border-color: var(--harmoni-teal-500);
    color: var(--harmoni-teal-600);
  }

  &.alert-secondary {
    background-color: var(--theme-risk-none-bg);
    border-color: var(--theme-risk-none);
    color: var(--theme-risk-none);
  }

  &.alert-success {
    background-color: var(--theme-risk-low-bg);
    border-color: var(--theme-risk-low);
    color: var(--theme-risk-low);
  }

  &.alert-danger {
    background-color: var(--theme-risk-critical-bg);
    border-color: var(--theme-risk-critical);
    color: var(--theme-risk-critical);
  }

  &.alert-warning {
    background-color: var(--theme-risk-medium-bg);
    border-color: var(--theme-risk-medium);
    color: var(--theme-risk-medium);
  }

  &.alert-info {
    background-color: var(--theme-status-draft-bg);
    border-color: var(--theme-status-draft);
    color: var(--theme-status-draft);
  }

  &.alert-light {
    background-color: var(--theme-bg-secondary);
    border-color: var(--theme-border);
    color: var(--theme-text-primary);
  }

  &.alert-dark {
    background-color: var(--theme-bg-tertiary);
    border-color: var(--theme-text-primary);
    color: var(--theme-text-primary);
  }

  // Dismissible alerts
  .btn-close {
    position: absolute;
    top: 0.5rem;
    right: 0.5rem;
    padding: 0.375rem;
    background: none;
    border: none;
    opacity: 0.5;

    &:hover {
      opacity: 1;
    }
  }

  // Alert links
  .alert-link {
    font-weight: 600;
    text-decoration: underline;
  }
}

// ========================================
// SPINNER COMPONENTS
// ========================================

.spinner-border,
.spinner-grow {
  color: var(--harmoni-teal-500);

  &.text-primary {
    color: var(--harmoni-teal-500) !important;
  }

  &.text-secondary {
    color: var(--theme-text-secondary) !important;
  }

  &.text-light {
    color: var(--theme-text-muted) !important;
  }

  &.text-success {
    color: var(--theme-risk-low) !important;
  }

  &.text-danger {
    color: var(--theme-risk-critical) !important;
  }

  &.text-warning {
    color: var(--theme-risk-medium) !important;
  }

  &.text-info {
    color: var(--theme-status-draft) !important;
  }
}