using Elsa.Workflows;
using Elsa.Workflows.Attributes;
using Harmoni360.Application.Common.Interfaces;
using Harmoni360.Application.Features.WorkflowManagement.IncidentWorkflow.Models;
using Harmoni360.Domain.Interfaces;
using Microsoft.Extensions.Logging;
using System.ComponentModel;
using Elsa.Extensions;
using Elsa.Workflows.Models;

namespace Harmoni360.Application.Features.WorkflowManagement.IncidentWorkflow.Activities;

/// <summary>
/// Activity that assigns investigation team members based on incident severity and type
/// </summary>
[Activity("Incident Management", "Assign Investigation", "Assigns appropriate investigation team members based on incident characteristics")]
public class AssignInvestigationActivity : IncidentActivityBase<IncidentWorkflowContext>
{
    private readonly IUserManagementService _userService;
    
    public AssignInvestigationActivity(
        ILogger<AssignInvestigationActivity> logger,
        IIncidentRepository incidentRepository,
        ICurrentUserService currentUserService,
        IUserManagementService userService)
        : base(logger, incidentRepository, currentUserService)
    {
        _userService = userService;
    }
    
    /// <summary>
    /// The incident workflow context
    /// </summary>
    [Input(
        Description = "The incident workflow context",
        DisplayName = "Incident Context"
    )]
    public Input<IncidentWorkflowContext> Context { get; set; } = default!;
    
    protected override async ValueTask ExecuteAsync(ActivityExecutionContext context)
    {
        const string activityName = nameof(AssignInvestigationActivity);
        LogActivity(activityName, "Starting investigation team assignment");
        
        try
        {
            var workflowContext = Context.Get(context);
            
            // Determine required investigation team based on incident characteristics
            var teamAssignment = await DetermineInvestigationTeam(workflowContext);
            
            // Update workflow context
            workflowContext.InvestigationTeam = teamAssignment.TeamMembers;
            workflowContext.Status = "Investigation Assigned";
            
            // Create investigation record in database
            var investigationId = await CreateInvestigationRecord(workflowContext, teamAssignment);
            
            // Update context with investigation data
            workflowContext.InvestigationData = new InvestigationWorkflowModel
            {
                Id = investigationId,
                TeamMembers = teamAssignment.TeamMembers,
                LeadInvestigator = teamAssignment.LeadInvestigator,
                AnalysisMethod = teamAssignment.RecommendedMethod,
                StartedAt = DateTime.UtcNow,
                Status = "Assigned"
            };
            
            LogActivity(activityName, 
                "Investigation assigned - Lead: {LeadInvestigator}, Team: {TeamCount} members, Method: {Method}",
                teamAssignment.LeadInvestigator, 
                teamAssignment.TeamMembers.Count,
                teamAssignment.RecommendedMethod);
            
            // Set the output
            Result.Set(context, workflowContext);
        }
        catch (Exception ex)
        {
            LogActivityError(activityName, ex, "Failed to assign investigation team");
            
            // Return context with minimal investigation assignment
            var workflowContext = Context.Get(context);
            workflowContext.InvestigationTeam = new List<string> { GetCurrentUserId() };
            workflowContext.Status = "Assignment Failed";
            Result.Set(context, workflowContext);
        }
    }
    
    private async Task<InvestigationTeamAssignment> DetermineInvestigationTeam(IncidentWorkflowContext context)
    {
        var assignment = new InvestigationTeamAssignment();
        
        // Determine team composition based on severity
        switch (context.Severity.ToLowerInvariant())
        {
            case "fatality":
                assignment = await AssignFatalityInvestigationTeam(context);
                break;
                
            case "major":
                assignment = await AssignMajorIncidentTeam(context);
                break;
                
            case "minor":
                assignment = await AssignMinorIncidentTeam(context);
                break;
                
            default:
                assignment = await AssignDefaultTeam(context);
                break;
        }
        
        // Ensure minimum team composition
        if (!assignment.TeamMembers.Any())
        {
            assignment.TeamMembers.Add(GetCurrentUserId());
        }
        
        if (string.IsNullOrEmpty(assignment.LeadInvestigator))
        {
            assignment.LeadInvestigator = assignment.TeamMembers.First();
        }
        
        return assignment;
    }
    
    private async Task<InvestigationTeamAssignment> AssignFatalityInvestigationTeam(IncidentWorkflowContext context)
    {
        var assignment = new InvestigationTeamAssignment
        {
            RecommendedMethod = "ICAM" // More comprehensive for fatalities
        };
        
        // Get senior HSE management
        var hseManagers = await _userService.GetUsersByRole("HSE_Manager");
        if (hseManagers.Any())
        {
            assignment.LeadInvestigator = hseManagers.First().Id;
            assignment.TeamMembers.Add(hseManagers.First().Id);
        }
        
        // Add HSE officers
        var hseOfficers = await _userService.GetUsersByRole("HSE_Officer");
        assignment.TeamMembers.AddRange(hseOfficers.Take(2).Select(u => u.Id));
        
        // Add external expert if available
        var externalExperts = await _userService.GetUsersByRole("External_Expert");
        if (externalExperts.Any())
        {
            assignment.TeamMembers.Add(externalExperts.First().Id);
        }
        
        // Add department representative
        var departmentReps = await _userService.GetUsersByDepartment(GetDepartmentFromLocation(context.Location));
        if (departmentReps.Any())
        {
            assignment.TeamMembers.Add(departmentReps.First().Id);
        }
        
        return assignment;
    }
    
    private async Task<InvestigationTeamAssignment> AssignMajorIncidentTeam(IncidentWorkflowContext context)
    {
        var assignment = new InvestigationTeamAssignment
        {
            RecommendedMethod = "HFACS" // Standard for major incidents
        };
        
        // Get HSE officer as lead
        var hseOfficers = await _userService.GetUsersByRole("HSE_Officer");
        if (hseOfficers.Any())
        {
            assignment.LeadInvestigator = hseOfficers.First().Id;
            assignment.TeamMembers.Add(hseOfficers.First().Id);
        }
        
        // Add another HSE team member
        if (hseOfficers.Count > 1)
        {
            assignment.TeamMembers.Add(hseOfficers.Skip(1).First().Id);
        }
        
        // Add department supervisor
        var supervisors = await _userService.GetSupervisorsByDepartment(GetDepartmentFromLocation(context.Location));
        if (supervisors.Any())
        {
            assignment.TeamMembers.Add(supervisors.First().Id);
        }
        
        return assignment;
    }
    
    private async Task<InvestigationTeamAssignment> AssignMinorIncidentTeam(IncidentWorkflowContext context)
    {
        var assignment = new InvestigationTeamAssignment
        {
            RecommendedMethod = "Simple Root Cause" // Simplified for minor incidents
        };
        
        // For minor incidents, department supervisor can lead
        var supervisors = await _userService.GetSupervisorsByDepartment(GetDepartmentFromLocation(context.Location));
        if (supervisors.Any())
        {
            assignment.LeadInvestigator = supervisors.First().Id;
            assignment.TeamMembers.Add(supervisors.First().Id);
        }
        
        // Add HSE representative
        var hseOfficers = await _userService.GetUsersByRole("HSE_Officer");
        if (hseOfficers.Any())
        {
            assignment.TeamMembers.Add(hseOfficers.First().Id);
        }
        
        return assignment;
    }
    
    private async Task<InvestigationTeamAssignment> AssignDefaultTeam(IncidentWorkflowContext context)
    {
        var assignment = new InvestigationTeamAssignment
        {
            RecommendedMethod = "HFACS"
        };
        
        // Default to HSE officer lead
        var hseOfficers = await _userService.GetUsersByRole("HSE_Officer");
        if (hseOfficers.Any())
        {
            assignment.LeadInvestigator = hseOfficers.First().Id;
            assignment.TeamMembers.Add(hseOfficers.First().Id);
        }
        
        return assignment;
    }
    
    private string GetDepartmentFromLocation(string location)
    {
        // Simple mapping - in real implementation, this would be more sophisticated
        return location.ToLowerInvariant() switch
        {
            var l when l.Contains("warehouse") => "Operations",
            var l when l.Contains("office") => "Administration",
            var l when l.Contains("lab") => "Research",
            var l when l.Contains("workshop") => "Maintenance",
            _ => "General"
        };
    }
    
    private async Task<string> CreateInvestigationRecord(
        IncidentWorkflowContext context, 
        InvestigationTeamAssignment assignment)
    {
        // In a real implementation, this would create an Investigation entity
        // For now, return a generated ID
        var investigationId = Guid.NewGuid().ToString();
        
        LogActivity(nameof(AssignInvestigationActivity), 
            "Created investigation record: {InvestigationId} for incident: {IncidentId}",
            investigationId, context.IncidentId);
        
        return investigationId;
    }
}

/// <summary>
/// Represents the assignment of an investigation team
/// </summary>
public class InvestigationTeamAssignment
{
    public List<string> TeamMembers { get; set; } = new();
    public string LeadInvestigator { get; set; } = string.Empty;
    public string RecommendedMethod { get; set; } = "HFACS";
}

/// <summary>
/// Interface for user management operations
/// </summary>
public interface IUserManagementService
{
    Task<List<UserInfo>> GetUsersByRole(string role);
    Task<List<UserInfo>> GetUsersByDepartment(string department);
    Task<List<UserInfo>> GetSupervisorsByDepartment(string department);
}

/// <summary>
/// Basic user information
/// </summary>
public class UserInfo
{
    public string Id { get; set; } = string.Empty;
    public string Name { get; set; } = string.Empty;
    public string Email { get; set; } = string.Empty;
    public string Department { get; set; } = string.Empty;
    public List<string> Roles { get; set; } = new();
}