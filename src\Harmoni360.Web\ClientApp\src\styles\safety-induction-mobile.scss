/* Mobile-specific optimizations for Safety Induction Video components */

/* Work Permit Settings Mobile Styles */
.work-permit-settings {
  /* Mobile navigation tabs */
  @media (max-width: 767.98px) {
    .nav-responsive {
      .nav-item {
        flex: 1;
        
        .nav-link {
          border-radius: 0;
          border-left: none;
          border-right: none;
          
          &:first-child {
            border-left: 1px solid var(--cui-nav-tabs-border-color);
          }
          
          &:last-child {
            border-right: 1px solid var(--cui-nav-tabs-border-color);
          }
        }
      }
    }
  }
}

/* Form Configuration Mobile Styles */
.form-configuration-tab {
  /* Mobile form inputs */
  @media (max-width: 575.98px) {
    .form-control,
    .form-select {
      font-size: 16px; /* Prevent zoom on iOS */
    }
    
    /* Mobile-friendly buttons */
    .btn {
      min-height: 44px; /* Touch target size */
      padding: 0.75rem 1rem;
    }
    
    /* Stacked form layout */
    .row > .col-lg-6 {
      margin-bottom: 1rem !important;
    }
  }
}

/* Video Upload Mobile Styles */
.video-upload-component {
  @media (max-width: 767.98px) {
    /* File selection button */
    .btn {
      min-height: 48px;
      font-size: 16px;
    }
    
    /* Video preview responsive */
    .video-preview-container {
      video {
        border-radius: 8px;
        box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
      }
    }
    
    /* Progress bar visibility */
    .progress {
      height: 8px;
      border-radius: 4px;
    }
  }
}

/* Safety Induction Video Mobile Styles */
.safety-induction-video {
  @media (max-width: 767.98px) {
    /* Video playlist items */
    .video-item {
      padding: 0.75rem !important;
      border-radius: 8px;
      
      /* Touch feedback */
      &:active {
        transform: scale(0.98);
        transition: transform 0.1s ease;
      }
      
      /* Better text handling */
      .text-truncate {
        max-width: 180px;
      }
    }
    
    /* Video player container */
    .video-player-container {
      border-radius: 8px;
      overflow: hidden;
      box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
      
      /* Custom controls */
      .bg-gradient {
        backdrop-filter: blur(4px);
      }
      
      /* Touch-friendly buttons */
      .btn {
        border-radius: 50%;
        display: flex;
        align-items: center;
        justify-content: center;
        
        &:focus {
          box-shadow: 0 0 0 3px rgba(255, 255, 255, 0.3);
        }
      }
    }
  }
  
  /* Extra small devices */
  @media (max-width: 575.98px) {
    .video-player-container {
      margin: -0.5rem -0.5rem 1rem -0.5rem;
      border-radius: 0;
    }
    
    .video-item {
      margin-left: -0.5rem;
      margin-right: -0.5rem;
      border-radius: 0;
      border-left: none;
      border-right: none;
    }
  }
}

/* Video List Mobile Styles */
.video-list {
  @media (max-width: 767.98px) {
    .card {
      border-radius: 8px;
      box-shadow: 0 2px 8px rgba(0, 0, 0, 0.08);
      
      .card-body {
        padding: 1rem;
      }
    }
    
    /* Video metadata badges */
    .badge {
      font-size: 0.75rem;
      padding: 0.375rem 0.5rem;
    }
    
    /* Action buttons */
    .btn-ghost {
      min-height: 40px;
      min-width: 40px;
    }
  }
}

/* Mobile-specific utilities */
@media (max-width: 767.98px) {
  /* Touch-friendly clickable areas */
  .cursor-pointer {
    cursor: pointer;
    -webkit-tap-highlight-color: rgba(0, 0, 0, 0.1);
  }
  
  /* Prevent text selection on interactive elements */
  .video-item,
  .nav-link,
  .btn {
    -webkit-user-select: none;
    -moz-user-select: none;
    -ms-user-select: none;
    user-select: none;
  }
  
  /* Smooth scrolling for video playlist */
  .video-playlist {
    -webkit-overflow-scrolling: touch;
  }
  
  /* Improved focus states for accessibility */
  .btn:focus,
  .form-control:focus,
  .form-select:focus {
    box-shadow: 0 0 0 3px rgba(var(--cui-primary-rgb), 0.25);
    outline: none;
  }
  
  /* Alert spacing */
  .alert {
    margin-bottom: 1rem;
    border-radius: 8px;
  }
}

/* Video player responsive aspect ratio */
.video-player-container {
  position: relative;
  width: 100%;
  height: 0;
  
  /* Standard 16:9 aspect ratio */
  padding-bottom: 56.25%;
  
  /* Adjust for mobile landscape */
  @media (max-width: 767.98px) and (orientation: landscape) {
    padding-bottom: 50%;
  }
  
  video {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    object-fit: contain;
    background-color: #000;
  }
}

/* Progress bar enhancements */
.progress {
  background-color: rgba(255, 255, 255, 0.2);
  
  .progress-bar {
    transition: width 0.3s ease;
  }
}

/* Loading states */
.video-upload-component {
  .progress {
    &.uploading {
      animation: pulse 1.5s ease-in-out infinite alternate;
    }
  }
}

@keyframes pulse {
  0% {
    opacity: 0.6;
  }
  100% {
    opacity: 1;
  }
}

/* Dark mode support */
@media (prefers-color-scheme: dark) {
  .video-item {
    &.bg-light {
      background-color: var(--cui-gray-800) !important;
      color: var(--cui-gray-100) !important;
    }
  }
  
  .video-player-container {
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.4);
  }
}