<Project Sdk="Microsoft.NET.Sdk.Web">

  <PropertyGroup>
    <TargetFramework>net8.0</TargetFramework>
    <Nullable>enable</Nullable>
    <ImplicitUsings>enable</ImplicitUsings>
    <SpaRoot>ClientApp\</SpaRoot>
    <DefaultItemExcludes>$(DefaultItemExcludes);$(SpaRoot)node_modules\**</DefaultItemExcludes>
    <SpaProxyServerUrl>http://localhost:5173</SpaProxyServerUrl>
    <UserSecretsId>27956cca-ed46-4f3f-b393-cd88f0c18d1a</UserSecretsId>
    <SkipClientBuild>false</SkipClientBuild>
  </PropertyGroup>

  <ItemGroup>
    <!-- Elsa Studio (frontend) -->
    <PackageReference Include="Elsa.Studio" Version="3.4.0" />
    
    <!-- Elsa Server packages (backend API) -->
    <PackageReference Include="Elsa" Version="3.4.2" />
    <PackageReference Include="Elsa.EntityFrameworkCore.SqlServer" Version="3.4.2" />
    <PackageReference Include="Elsa.Http" Version="3.4.2" />
    <PackageReference Include="Elsa.Identity" Version="3.4.2" />
    <PackageReference Include="Elsa.Studio.Dashboard" Version="3.4.0" />
    <PackageReference Include="Elsa.Workflows.Api" Version="3.4.2" />
    <PackageReference Include="Elsa.Scheduling" Version="3.4.2" />
    <PackageReference Include="Elsa.Email" Version="3.4.2" />
    <!-- FastEndpoints is included via Elsa packages, removing explicit reference to avoid conflicts -->
    <PackageReference Include="Microsoft.AspNetCore.Components.WebAssembly.Server" Version="8.0.16" />
    <PackageReference Include="Microsoft.AspNetCore.OpenApi" Version="8.0.16" />
    <PackageReference Include="Microsoft.AspNetCore.SpaServices.Extensions" Version="8.0.0" />
    <PackageReference Include="Microsoft.AspNetCore.Authentication.JwtBearer" Version="8.0.13" />
    <PackageReference Include="Microsoft.CodeAnalysis.Common" Version="4.13.0" />
    <PackageReference Include="Microsoft.EntityFrameworkCore.Design" Version="9.0.1">
      <PrivateAssets>all</PrivateAssets>
      <IncludeAssets>runtime; build; native; contentfiles; analyzers; buildtransitive</IncludeAssets>
    </PackageReference>
    <!-- Removed Newtonsoft.Json - using System.Text.Json to match Elsa -->
    <PackageReference Include="Swashbuckle.AspNetCore" Version="8.1.4" />
    <PackageReference Include="MediatR" Version="12.5.0" />
    <PackageReference Include="FluentValidation.AspNetCore" Version="11.3.1" />
    <PackageReference Include="Serilog.AspNetCore" Version="9.0.0" />
    <PackageReference Include="Serilog.Sinks.Seq" Version="9.0.0" />
    <PackageReference Include="System.Text.Json" Version="9.0.6" />
  </ItemGroup>

  <ItemGroup>
    <ProjectReference Include="..\Harmoni360.Infrastructure\Harmoni360.Infrastructure.csproj" />
    <ProjectReference Include="..\Harmoni360.ElsaStudio\Harmoni360.ElsaStudio.csproj" />
  </ItemGroup>

  <ItemGroup>
    <!-- Don't publish the SPA source files, but do show them in the project files list -->
    <Content Remove="$(SpaRoot)**" />
    <None Remove="$(SpaRoot)**" />
    <None Include="$(SpaRoot)**" Exclude="$(SpaRoot)node_modules\**" />
  </ItemGroup>

  <Target Name="DebugEnsureNodeEnv" BeforeTargets="Build" Condition=" '$(Configuration)' == 'Debug' And !Exists('$(SpaRoot)node_modules') ">
    <Exec Command="node --version" ContinueOnError="true">
      <Output TaskParameter="ExitCode" PropertyName="ErrorCode" />
    </Exec>
    <Error Condition="'$(ErrorCode)' != '0'" Text="Node.js is required to build and run this project." />
    <Message Importance="high" Text="Restoring dependencies using 'npm'. This may take several minutes..." />
    <Exec WorkingDirectory="$(SpaRoot)" Command="npm install" />
  </Target>

  <Target Name="PublishRunWebpack" AfterTargets="ComputeFilesToPublish">
    <Exec WorkingDirectory="$(SpaRoot)" Command="npm install" Condition="'$(SkipClientBuild)' != 'true'" />
    <Exec WorkingDirectory="$(SpaRoot)" Command="npm run build" Condition="'$(SkipClientBuild)' != 'true'" />
    <ItemGroup>
      <DistFiles Include="$(SpaRoot)dist\**" />
      <ResolvedFileToPublish Include="@(DistFiles->'%(FullPath)')" Exclude="@(ResolvedFileToPublish)">
        <RelativePath>%(DistFiles.Identity)</RelativePath>
        <CopyToPublishDirectory>PreserveNewest</CopyToPublishDirectory>
        <ExcludeFromSingleFile>true</ExcludeFromSingleFile>
      </ResolvedFileToPublish>
    </ItemGroup>
  </Target>

  <!-- Copy Elsa Studio WASM files to wwwroot for direct loading -->
  <Target Name="CopyElsaStudioFiles" AfterTargets="Build">
    <!-- Clean existing Elsa Studio directory to ensure no stale files -->
    <RemoveDir Directories="wwwroot/elsa-studio" Condition="Exists('wwwroot/elsa-studio')" />
    <MakeDir Directories="wwwroot/elsa-studio" />
    <ItemGroup>
      <ElsaStudioFiles Include="../Harmoni360.ElsaStudio/bin/$(Configuration)/net8.0/wwwroot/**/*" />
      <ElsaStudioStaticAssets Include="../Harmoni360.ElsaStudio/wwwroot/**/*" />
      <ElsaScopedCssFiles Include="../Harmoni360.ElsaStudio/obj/$(Configuration)/net8.0/scopedcss/bundle/*.css" />
    </ItemGroup>
    <Copy SourceFiles="@(ElsaStudioFiles)" DestinationFiles="@(ElsaStudioFiles->'wwwroot/elsa-studio/%(RecursiveDir)%(Filename)%(Extension)')" SkipUnchangedFiles="true" />
    
    <!-- Copy static assets from Elsa Studio source (CSS, favicon, etc.) -->
    <Copy SourceFiles="@(ElsaStudioStaticAssets)" DestinationFiles="@(ElsaStudioStaticAssets->'wwwroot/elsa-studio/%(RecursiveDir)%(Filename)%(Extension)')" SkipUnchangedFiles="true" />
    
    <!-- Copy scoped CSS files from Elsa Studio -->
    <Copy SourceFiles="@(ElsaScopedCssFiles)" DestinationFolder="wwwroot/elsa-studio/" SkipUnchangedFiles="true" Condition="'@(ElsaScopedCssFiles)' != ''" />
    
    <!-- Copy static web assets from NuGet packages for Elsa Studio -->
    <ItemGroup>
      <CustomElementsFile Include="$(NuGetPackageRoot)microsoft.aspnetcore.components.customelements/8.0.*/staticwebassets/Microsoft.AspNetCore.Components.CustomElements.lib.module.js" />
      <!-- Get all static web assets from Elsa packages -->
      <ElsaStaticAssets Include="$(NuGetPackageRoot)elsa.studio.*/*/staticwebassets/**/*" />
      <ElsaLoginStaticAssets Include="$(NuGetPackageRoot)elsa.studio.login*/*/staticwebassets/**/*" />
      <MudBlazorStaticAssets Include="$(NuGetPackageRoot)mudblazor/*/staticwebassets/**/*" />
    </ItemGroup>
    
    <!-- Create directories -->
    <MakeDir Directories="wwwroot/elsa-studio/_content/Microsoft.AspNetCore.Components.CustomElements" />
    <MakeDir Directories="wwwroot/elsa-studio/_content/Elsa.Studio.Login" />
    <MakeDir Directories="wwwroot/elsa-studio/_content/MudBlazor" />
    
    <Copy SourceFiles="@(CustomElementsFile)" DestinationFolder="wwwroot/elsa-studio/_content/Microsoft.AspNetCore.Components.CustomElements" SkipUnchangedFiles="true" Condition="'@(CustomElementsFile)' != ''" />
    
    <!-- Copy Elsa static assets -->
    <Copy SourceFiles="@(ElsaStaticAssets)" DestinationFiles="@(ElsaStaticAssets->'wwwroot/elsa-studio/_content/%(RecursiveDir)%(Filename)%(Extension)')" SkipUnchangedFiles="true" Condition="'@(ElsaStaticAssets)' != ''" />
    
    <Copy SourceFiles="@(ElsaLoginStaticAssets)" DestinationFiles="@(ElsaLoginStaticAssets->'wwwroot/elsa-studio/_content/%(RecursiveDir)%(Filename)%(Extension)')" SkipUnchangedFiles="true" Condition="'@(ElsaLoginStaticAssets)' != ''" />
    
    <!-- Copy versioned static assets to root _content directory for direct access -->
    <ItemGroup>
      <VersionedElsaAssets Include="wwwroot/elsa-studio/_content/Elsa.Studio.Login/*/staticwebassets/**/*" />
    </ItemGroup>
    <Copy SourceFiles="@(VersionedElsaAssets)" DestinationFiles="@(VersionedElsaAssets->'wwwroot/elsa-studio/_content/Elsa.Studio.Login/%(Filename)%(Extension)')" SkipUnchangedFiles="true" Condition="'@(VersionedElsaAssets)' != ''" />
    
    <!-- Copy _content directory to _framework/_content for Blazor library initializers -->
    <MakeDir Directories="wwwroot/elsa-studio/_framework/_content" />
    <ItemGroup>
      <ContentAssets Include="wwwroot/elsa-studio/_content/**/*" />
    </ItemGroup>
    <Copy SourceFiles="@(ContentAssets)" DestinationFiles="@(ContentAssets->'wwwroot/elsa-studio/_framework/_content/%(RecursiveDir)%(Filename)%(Extension)')" SkipUnchangedFiles="true" Condition="'@(ContentAssets)' != ''" />
    
    <!-- Copy MudBlazor static assets -->
    <Copy SourceFiles="@(MudBlazorStaticAssets)" DestinationFiles="@(MudBlazorStaticAssets->'wwwroot/elsa-studio/_content/%(RecursiveDir)%(Filename)%(Extension)')" SkipUnchangedFiles="true" Condition="'@(MudBlazorStaticAssets)' != ''" />
    
    <!-- Copy main application icon as Elsa Studio icon -->
    <MakeDir Directories="wwwroot/elsa-studio/_content/Elsa.Studio.Shell/img" />
    <Copy SourceFiles="wwwroot/Harmoni_360_Icon.png" DestinationFiles="wwwroot/elsa-studio/_content/Elsa.Studio.Shell/img/icon.png" SkipUnchangedFiles="true" Condition="Exists('wwwroot/Harmoni_360_Icon.png')" />
    
    <!-- Also copy assets to Vite public directory for development -->
    <MakeDir Directories="ClientApp/public/elsa-studio" />
    <ItemGroup>
      <ElsaStudioAssetsForVite Include="wwwroot/elsa-studio/**/*" />
    </ItemGroup>
    <Copy SourceFiles="@(ElsaStudioAssetsForVite)" DestinationFiles="@(ElsaStudioAssetsForVite->'ClientApp/public/elsa-studio/%(RecursiveDir)%(Filename)%(Extension)')" SkipUnchangedFiles="true" />
    
    <!-- Update the index.html to have correct base href and fix blazor.boot.json paths -->
    <ItemGroup>
      <IndexContent Include="wwwroot/elsa-studio/index.html" />
    </ItemGroup>
    <WriteLinesToFile File="wwwroot/elsa-studio/index.html" Lines="$([System.IO.File]::ReadAllText('wwwroot/elsa-studio/index.html').Replace('&lt;base href=&quot;/&quot; /&gt;', '&lt;base href=&quot;/elsa-studio/&quot; /&gt;').Replace('base href=&quot;/&quot;', 'base href=&quot;/elsa-studio/&quot;'))" Overwrite="true" Condition="Exists('wwwroot/elsa-studio/index.html')" />
    
    <!-- Also update index.html in Vite public directory -->
    <WriteLinesToFile File="ClientApp/public/elsa-studio/index.html" Lines="$([System.IO.File]::ReadAllText('ClientApp/public/elsa-studio/index.html').Replace('&lt;base href=&quot;/&quot; /&gt;', '&lt;base href=&quot;/elsa-studio/&quot; /&gt;').Replace('base href=&quot;/&quot;', 'base href=&quot;/elsa-studio/&quot;'))" Overwrite="true" Condition="Exists('ClientApp/public/elsa-studio/index.html')" />
    
    <!-- Fix _content paths in blazor.boot.json for library initializers -->
    <WriteLinesToFile File="wwwroot/elsa-studio/_framework/blazor.boot.json" Lines="$([System.IO.File]::ReadAllText('wwwroot/elsa-studio/_framework/blazor.boot.json').Replace('&quot;../../elsa-studio/_content/', '&quot;_content/').Replace('&quot;../_content/', '&quot;_content/').Replace('&quot;appsettings.json&quot;', '&quot;../appsettings.json&quot;'))" Overwrite="true" Condition="Exists('wwwroot/elsa-studio/_framework/blazor.boot.json')" />
                      
    <!-- Also fix blazor.boot.json in Vite public directory -->
    <WriteLinesToFile File="ClientApp/public/elsa-studio/_framework/blazor.boot.json" Lines="$([System.IO.File]::ReadAllText('ClientApp/public/elsa-studio/_framework/blazor.boot.json').Replace('&quot;../../elsa-studio/_content/', '&quot;_content/').Replace('&quot;../_content/', '&quot;_content/').Replace('&quot;appsettings.json&quot;', '&quot;../appsettings.json&quot;'))" Overwrite="true" Condition="Exists('ClientApp/public/elsa-studio/_framework/blazor.boot.json')" />
  </Target>

</Project>
