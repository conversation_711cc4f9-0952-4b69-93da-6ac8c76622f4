import React, { useEffect, useRef, useState } from 'react';
import { useAuth } from '../../hooks/useAuth';

interface ElsaStudioDirectProps {
  height?: string;
  className?: string;
}

declare global {
  interface Window {
    Blazor?: any;
    DotNet?: any;
    ElsaComponentBridge?: {
      bridgeReference: any;
      initialize: (dotNetRef: any) => void;
      renderWorkflowDesigner: (containerId: string, workflowDefinitionId: string) => Promise<any>;
      renderWorkflowInstances: (containerId: string) => Promise<any>;
      renderWorkflowDashboard: (containerId: string) => Promise<any>;
      setAuthToken: (token: string) => Promise<any>;
      disposeComponent: (containerId: string) => Promise<any>;
    };
  }
}

export const ElsaStudioDirect: React.FC<ElsaStudioDirectProps> = ({
  height = 'calc(100vh - 120px)',
  className = ''
}) => {
  const containerRef = useRef<HTMLDivElement>(null);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [isInitialized, setIsInitialized] = useState(false);
  const { token } = useAuth();

  useEffect(() => {
    let cancelled = false;
    
    if (isInitialized) return;

    const loadElsaStudio = async () => {
      if (cancelled) return;
      
      try {
        setIsLoading(true);
        setError(null);

        // Create container for Blazor app with proper loading UI
        if (containerRef.current && !cancelled) {
          containerRef.current.innerHTML = `
            <div id="app">
              <svg class="loading-progress" style="width: 100px; height: 100px; position: relative; margin: 100px auto;">
                <circle r="40%" cx="50%" cy="50%" style="fill: none; stroke: #e0e0e0; stroke-width: 6;" />
                <circle r="40%" cx="50%" cy="50%" style="fill: none; stroke: #1976d2; stroke-width: 6;" />
              </svg>
              <div class="loading-progress-text" style="text-align: center; margin-top: 20px;">Loading Elsa Studio...</div>
            </div>`;
        }

        // Initialize MudBlazor JavaScript stubs to prevent errors
        if (!(window as any).mudElementRef) {
          (window as any).mudElementRef = {
            getBoundingClientRect: function() {
              return { width: 0, height: 0, top: 0, left: 0, right: 0, bottom: 0, x: 0, y: 0 };
            },
            saveFocus: function() { },
            addOnBlurEvent: function() { },
            restoreFocus: function() { },
            focus: function() { },
            select: function() { },
            selectRange: function() { }
          };
          
          (window as any).mudKeyInterceptor = {
            connect: function() { },
            disconnect: function() { }
          };
          
          (window as any).mudScrollManager = {
            lockScroll: function() { },
            unlockScroll: function() { }
          };
          
          // Add MudBlazor window functions
          (window as any).mudWindow = {
            copyToClipboard: function(text: string) {
              return navigator.clipboard.writeText(text);
            },
            getBoundingClientRect: function(selector: string) {
              const element = document.querySelector(selector);
              return element ? element.getBoundingClientRect() : null;
            }
          };
          
          (window as any).mudDragAndDrop = {
            initDropZone: function() { },
            dispose: function() { }
          };
          
          (window as any).mudResizeObserver = {
            connect: function() { },
            disconnect: function() { },
            observe: function() { },
            unobserve: function() { }
          };
          
          (window as any).mudEventProjections = {
            subscribe: function() { },
            unsubscribe: function() { }
          };
        }

        // Initialize ElsaComponentBridge before loading Blazor
        if (!window.ElsaComponentBridge) {
          window.ElsaComponentBridge = {
            bridgeReference: null,
            
            initialize: function(dotNetRef: any) {
              this.bridgeReference = dotNetRef;
              console.log('Elsa Component Bridge initialized');
              
              // Notify parent window (React) that Elsa is ready
              if (window.parent !== window) {
                window.parent.postMessage({ type: 'ELSA_READY' }, '*');
              }
            },
            
            renderWorkflowDesigner: function(containerId: string, workflowDefinitionId: string) {
              if (this.bridgeReference) {
                return this.bridgeReference.invokeMethodAsync('renderWorkflowDesigner', containerId, workflowDefinitionId);
              }
              console.error('Bridge not initialized');
            },
            
            renderWorkflowInstances: function(containerId: string) {
              if (this.bridgeReference) {
                return this.bridgeReference.invokeMethodAsync('renderWorkflowInstances', containerId);
              }
              console.error('Bridge not initialized');
            },
            
            renderWorkflowDashboard: function(containerId: string) {
              if (this.bridgeReference) {
                return this.bridgeReference.invokeMethodAsync('renderWorkflowDashboard', containerId);
              }
              console.error('Bridge not initialized');
            },
            
            setAuthToken: function(token: string) {
              if (this.bridgeReference) {
                return this.bridgeReference.invokeMethodAsync('setAuthToken', token);
              }
              console.error('Bridge not initialized');
            },
            
            disposeComponent: function(containerId: string) {
              if (this.bridgeReference) {
                return this.bridgeReference.invokeMethodAsync('disposeComponent', containerId);
              }
              console.error('Bridge not initialized');
            }
          };
        }

        const serverUrl = process.env.NODE_ENV === 'development' ? '' : '';
        
        // Load required CSS files
        const loadCSS = (href: string) => {
          if (!document.querySelector(`link[href="${href}"]`)) {
            const link = document.createElement('link');
            link.rel = 'stylesheet';
            link.href = href;
            document.head.appendChild(link);
          }
        };
        
        // Load Elsa Studio CSS files
        loadCSS('/elsa-studio/css/app.css');
        loadCSS('/elsa-studio/Harmoni360.ElsaStudio.styles.css');
        loadCSS('/elsa-studio/css/bootstrap/bootstrap.min.css');
        
        // Load MudBlazor CSS - use the actual path
        loadCSS('/elsa-studio/_content/8.5.0/staticwebassets/MudBlazor.min.css');

        // Check if Blazor is already initialized globally
        const existingScript = document.querySelector('script[src*="blazor.webassembly.js"]');
        
        if (existingScript && window.Blazor && (window.Blazor as any)._internal) {
          // Blazor is already initialized, just use it
          console.log('Using existing Blazor instance');
          if (!cancelled) {
            setIsLoading(false);
            setIsInitialized(true);
          }
          return;
        }

        // Create a single Blazor initialization promise to prevent multiple attempts
        if (!(window as any)._blazorInitPromise) {
          (window as any)._blazorInitPromise = new Promise(async (resolve, reject) => {
            try {
              // Remove any existing scripts first
              const scripts = document.querySelectorAll('script[src*="blazor.webassembly.js"]');
              scripts.forEach(script => script.remove());

              // Reset Blazor state
              delete window.Blazor;

              // Load Blazor script with proper resource redirection
              const script = document.createElement('script');
              script.src = `${serverUrl}/elsa-studio/_framework/blazor.webassembly.js`;
              script.setAttribute('autostart', 'false');
              
              script.onload = async () => {
                try {
                  // Wait for Blazor to be available
                  let attempts = 0;
                  while (!window.Blazor && attempts < 50) {
                    await new Promise(resolve => setTimeout(resolve, 100));
                    attempts++;
                  }

                  if (!window.Blazor) {
                    throw new Error('Blazor not available after loading script');
                  }

                  // Start Blazor with proper resource redirection
                  await window.Blazor.start({
                    loadBootResource: function(type: string, name: string, defaultUri: string, integrity: string) {
                      console.log(`LoadBootResource: type=${type}, name=${name}, defaultUri=${defaultUri}`);
                      
                      // Skip PDB files
                      if (type === 'pdb' || name.endsWith('.pdb')) {
                        return 'data:application/octet-stream;base64,';
                      }
                      
                      // Handle configuration files
                      if (type === 'configuration' || name.includes('appsettings.json')) {
                        return `${serverUrl}/elsa-studio/appsettings.json`;
                      }
                      
                      // Handle _content static assets
                      if (defaultUri.includes('_content/')) {
                        return `${serverUrl}/elsa-studio/${defaultUri}`;
                      }
                      
                      // Handle full URLs
                      if (defaultUri.startsWith('http://') || defaultUri.startsWith('https://')) {
                        const url = new URL(defaultUri);
                        const filename = url.pathname.split('/').pop() || name;
                        return `${serverUrl}/elsa-studio/_framework/${filename}`;
                      }
                      
                      // Handle relative paths
                      if (defaultUri.startsWith('./')) {
                        return `${serverUrl}/elsa-studio/_framework/${defaultUri.substring(2)}`;
                      }
                      
                      // Handle _framework paths
                      if (defaultUri.startsWith('_framework/')) {
                        return `${serverUrl}/elsa-studio/${defaultUri}`;
                      }
                      
                      // Default
                      return `${serverUrl}/elsa-studio/_framework/${defaultUri}`;
                    },
                    reconnectionHandler: {
                      onConnectionDown: () => { console.log('Connection down'); },
                      onConnectionUp: () => { console.log('Connection up'); }
                    }
                  });
                  
                  // Wait for Blazor app to be fully loaded
                  let appStarted = false;
                  const startTimeout = setTimeout(() => {
                    if (!appStarted) {
                      console.warn('Blazor app did not start within timeout, forcing completion');
                      resolve(window.Blazor);
                    }
                  }, 10000); // 10 second timeout
                  
                  // Check if the app element has been replaced (indicating Blazor has started)
                  const checkAppStarted = setInterval(() => {
                    const appElement = document.querySelector('#app');
                    if (appElement && !appElement.querySelector('.loading-progress')) {
                      clearInterval(checkAppStarted);
                      clearTimeout(startTimeout);
                      appStarted = true;
                      console.log('Blazor app has started');
                      resolve(window.Blazor);
                    }
                  }, 100);
                } catch (err) {
                  reject(err);
                }
              };
              
              script.onerror = () => reject(new Error('Failed to load Blazor script'));
              document.head.appendChild(script);
            } catch (err) {
              reject(err);
            }
          });
        }

        // Wait for Blazor initialization
        await (window as any)._blazorInitPromise;
        
        if (!cancelled) {
          console.log('Blazor started successfully');
          setIsLoading(false);
          setIsInitialized(true);
        }

      } catch (err) {
        if (!cancelled) {
          console.error('Error loading Elsa Studio:', err);
          setError('Failed to load Elsa Studio: ' + (err as Error).message);
          setIsLoading(false);
        }
      }
    };

    loadElsaStudio();

    return () => {
      cancelled = true;
    };
  }, [isInitialized]);

  // Handle auth token
  useEffect(() => {
    if (token && isInitialized && window.ElsaComponentBridge?.bridgeReference) {
      console.log('Setting auth token in Elsa Studio');
      window.ElsaComponentBridge.setAuthToken(token)
        .then(() => console.log('Auth token set successfully'))
        .catch(err => console.warn('Failed to set auth token:', err));
    }
  }, [token, isInitialized]);

  if (error) {
    return (
      <div className="flex items-center justify-center h-96 bg-red-50 border border-red-200 rounded-lg">
        <div className="text-center">
          <div className="text-red-600 mb-2">⚠️ Elsa Studio Error</div>
          <div className="text-red-700 font-medium">{error}</div>
          <div className="text-sm text-red-600 mt-2">
            Please ensure Elsa Studio WASM files are properly deployed.
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className={`elsa-studio-direct ${className}`}>
      {isLoading && (
        <div className="flex items-center justify-center h-96 bg-gray-50">
          <div className="text-center">
            <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto"></div>
            <p className="mt-4 text-gray-600">Loading Elsa Studio WASM...</p>
            <p className="mt-2 text-sm text-gray-500">This may take a moment on first load</p>
          </div>
        </div>
      )}
      
      <div 
        ref={containerRef}
        style={{ 
          height,
          width: '100%',
          display: isLoading ? 'none' : 'block'
        }}
        className="elsa-studio-container"
      />
    </div>
  );
};

export default ElsaStudioDirect;