# Token Storage Architecture Analysis & Recommendations

## Current State Problems

### Multiple Token Storage Locations
1. **Redux Store + localStorage (`token`)** - Main React app
2. **localStorage (`harmoni360_token`)** - Elsa Studio handoff
3. **sessionStorage (`harmoni360_token`)** - Fallback storage
4. **Cookies (`harmoni360_token`)** - Server-side access
5. **HTTP Headers** - API authentication

### Root Causes
1. **Multi-Technology Integration**: React SPA + Blazor WebAssembly + ASP.NET Core
2. **Elsa Studio Isolation**: Blazor runs in separate context, can't access Redux
3. **Historical Debt**: Evolved organically without unified strategy
4. **Cross-Context Communication**: Different apps need different access patterns

## Recommended Solution: Unified Token Service

```typescript
// src/services/TokenService.ts
export class TokenService {
  private static readonly TOKEN_KEY = 'harmoni360_token';
  private static readonly REFRESH_TOKEN_KEY = 'harmoni360_refresh_token';
  
  static setTokens(token: string, refreshToken: string): void {
    localStorage.setItem(this.TOKEN_KEY, token);
    localStorage.setItem(this.REFRESH_TOKEN_KEY, refreshToken);
    this.setCookie(token);
    // Notify Redux store
    store.dispatch(authActions.setTokens({ token, refreshToken }));
  }
  
  static getToken(): string | null {
    return localStorage.getItem(this.TOKEN_KEY);
  }
  
  static clearTokens(): void {
    localStorage.removeItem(this.TOKEN_KEY);
    localStorage.removeItem(this.REFRESH_TOKEN_KEY);
    this.clearCookie();
    store.dispatch(authActions.clearAuth());
  }
  
  private static setCookie(token: string): void {
    const isHttps = window.location.protocol === 'https:';
    const secureFlag = isHttps ? '; secure' : '';
    document.cookie = `${this.TOKEN_KEY}=${token}; path=/${secureFlag}; samesite=strict; max-age=28800`;
  }
  
  private static clearCookie(): void {
    document.cookie = `${this.TOKEN_KEY}=; expires=Thu, 01 Jan 1970 00:00:00 UTC; path=/;`;
  }
}
```

## Implementation Plan

### Phase 1: Immediate Fix (Current Sprint)
- [ ] Standardize token key to `harmoni360_token` everywhere
- [ ] Update authSlice to use `harmoni360_token`
- [ ] Fix ElsaStudioGuard token detection
- [ ] Ensure WorkflowManagement uses consistent key

### Phase 2: Unified Service (Next Sprint)
- [ ] Create TokenService class
- [ ] Update all token operations to use service
- [ ] Add token expiration handling
- [ ] Implement automatic refresh logic

### Phase 3: Security Hardening (Future Sprint)
- [ ] Evaluate HttpOnly cookie approach
- [ ] Implement token rotation
- [ ] Add CSRF protection
- [ ] Audit all token access points

## Benefits of Single Source of Truth

1. **Consistency**: One token, one location, one truth
2. **Reliability**: No race conditions or sync issues
3. **Security**: Centralized security policies
4. **Maintainability**: Single place to modify auth logic
5. **Debugging**: Clear token lifecycle

## Current User Context Flow (After Fix)

```mermaid
graph TD
    A[User Login] --> B[TokenService.setTokens]
    B --> C[localStorage: harmoni360_token]
    B --> D[Redux Store Update]
    B --> E[Cookie: harmoni360_token]
    
    F[React Components] --> D
    G[Elsa Studio] --> C
    H[Server APIs] --> E
    
    I[User Logout] --> J[TokenService.clearTokens]
    J --> K[Clear All Storage]
```

This ensures proper user context is maintained across all parts of the application while eliminating authentication issues.