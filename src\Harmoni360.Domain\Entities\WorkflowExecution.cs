using Harmoni360.Domain.Common;
using Harmoni360.Domain.Enums;
using System.ComponentModel.DataAnnotations;
using System.Text.Json;
using System.Text.Json.Serialization;

namespace Harmoni360.Domain.Entities;

public class WorkflowExecution : BaseEntity, IAuditableEntity
{

    [Required]
    [MaxLength(100)]
    public string WorkflowDefinitionId { get; set; } = string.Empty;

    [Required]
    [MaxLength(200)]
    public string WorkflowName { get; set; } = string.Empty;

    [Required]
    [MaxLength(50)]
    public string WorkflowVersion { get; set; } = "1.0";

    [Required]
    [MaxLength(100)]
    public string InstanceId { get; set; } = string.Empty;

    [Required]
    public WorkflowStatus Status { get; set; } = WorkflowStatus.Idle;

    [Required]
    public WorkflowSubStatus SubStatus { get; set; } = WorkflowSubStatus.Executing;

    [Required]
    public DateTime StartedAt { get; set; }

    public DateTime? CompletedAt { get; set; }

    public DateTime? FaultedAt { get; set; }

    public DateTime? CancelledAt { get; set; }

    public DateTime? FinishedAt { get; set; }

    [MaxLength(100)]
    public string? CorrelationId { get; set; }

    [MaxLength(1000)]
    public string? ContextType { get; set; }

    [MaxLength(500)]
    public string? Name { get; set; }

    public string? Input { get; set; }

    public string? Output { get; set; }

    [MaxLength(2000)]
    public string? FaultMessage { get; set; }

    public string? Exception { get; set; }

    [Required]
    [MaxLength(100)]
    public string InitiatedBy { get; set; } = string.Empty;

    [MaxLength(100)]
    public string? CurrentActivityId { get; set; }

    [MaxLength(500)]
    public string? CurrentActivityName { get; set; }

    public string? Variables { get; set; }

    public string? Metadata { get; set; }

    public string? Tags { get; set; }

    // Audit properties
    public DateTime CreatedAt { get; private set; }
    public string CreatedBy { get; private set; } = string.Empty;
    public DateTime? LastModifiedAt { get; private set; }
    public string? LastModifiedBy { get; private set; }

    // Navigation properties
    public virtual ICollection<WorkflowExecutionLog> ExecutionLogs { get; set; } = new List<WorkflowExecutionLog>();
    public virtual ICollection<WorkflowBookmark> Bookmarks { get; set; } = new List<WorkflowBookmark>();
    public virtual ICollection<WorkflowExecutionContext> ExecutionContexts { get; set; } = new List<WorkflowExecutionContext>();

    // Helper methods for JSON serialization
    public T? GetInputData<T>() where T : class
    {
        if (string.IsNullOrEmpty(Input))
            return null;

        try
        {
            return JsonSerializer.Deserialize<T>(Input);
        }
        catch
        {
            return null;
        }
    }

    public void SetInputData<T>(T? data) where T : class
    {
        Input = data != null ? JsonSerializer.Serialize(data) : null;
    }

    public T? GetOutputData<T>() where T : class
    {
        if (string.IsNullOrEmpty(Output))
            return null;

        try
        {
            return JsonSerializer.Deserialize<T>(Output);
        }
        catch
        {
            return null;
        }
    }

    public void SetOutputData<T>(T? data) where T : class
    {
        Output = data != null ? JsonSerializer.Serialize(data) : null;
    }

    public Dictionary<string, object>? GetVariables()
    {
        if (string.IsNullOrEmpty(Variables))
            return null;

        try
        {
            return JsonSerializer.Deserialize<Dictionary<string, object>>(Variables);
        }
        catch
        {
            return null;
        }
    }

    public void SetVariables(Dictionary<string, object>? variables)
    {
        Variables = variables != null ? JsonSerializer.Serialize(variables) : null;
    }
}