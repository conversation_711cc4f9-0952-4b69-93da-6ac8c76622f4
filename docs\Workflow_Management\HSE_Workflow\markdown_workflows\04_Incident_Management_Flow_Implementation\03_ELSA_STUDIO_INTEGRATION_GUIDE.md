# Elsa Studio Integration Guide for Incident Management Workflow

## 1. Introduction

This guide provides comprehensive instructions for integrating the Incident Management workflow with Elsa Studio in the Harmoni360 HSSE platform. It covers visual workflow design, activity configuration, testing procedures, and deployment strategies.

## 2. Elsa Studio Setup and Configuration

### 2.1 Initial Studio Configuration

```json
// appsettings.json
{
  "Elsa": {
    "Studio": {
      "Enabled": true,
      "Path": "/elsa-studio",
      "RequireAuthentication": true,
      "DefaultCulture": "en-US",
      "Features": {
        "WorkflowDesigner": true,
        "WorkflowInstances": true,
        "ActivityLibrary": true,
        "WorkflowRegistry": true,
        "WorkflowSettings": true
      }
    },
    "Server": {
      "BaseUrl": "https://harmoni360.com",
      "Tenancy": {
        "Enabled": true,
        "DefaultTenant": "HSSE"
      }
    }
  }
}
```

### 2.2 Studio Authentication Integration

```csharp
// Startup.cs configuration
public void ConfigureServices(IServiceCollection services)
{
    // Configure Elsa with Studio
    services
        .AddElsa(elsa => elsa
            .UseEntityFrameworkPersistence(ef => ef.UseSqlServer(connectionString))
            .AddActivitiesFrom<IncidentManagementActivities>()
            .AddWorkflowsFrom<IncidentManagementWorkflow>()
            .UseDefaultWorkflowChannels()
            .ConfigureWorkflowChannels(channels => channels
                .WithChannel("IncidentReporting")
                .WithChannel("InvestigationUpdates")
                .WithChannel("ActionTracking"))
        )
        .AddElsaStudio(studio => studio
            .ConfigureAuthentication(auth => auth
                .UseHarmoni360Authentication()
                .RequireRole("WorkflowDesigner", "HSE_Manager")
            )
            .ConfigureAuthorization(authz => authz
                .AddPolicy("DesignWorkflows", policy => 
                    policy.RequireRole("WorkflowDesigner"))
                .AddPolicy("ManageInstances", policy => 
                    policy.RequireRole("HSE_Manager", "HSE_Officer"))
            )
        );
}
```

## 3. Visual Workflow Design in Elsa Studio

### 3.1 Creating the Incident Management Workflow

#### Step 1: Access Elsa Studio
1. Navigate to `https://harmoni360.com/elsa-studio`
2. Authenticate with Harmoni360 credentials
3. Click "New Workflow" from the dashboard

#### Step 2: Configure Workflow Properties
```yaml
Workflow Properties:
  Name: IncidentManagementWorkflow
  Display Name: Incident Management Workflow
  Description: Manages HSE incidents from reporting through closure
  Version: 1.0.0
  Tag: Production
  Persistence: Enabled
  Singleton: false
  Context Type: IncidentWorkflowContext
```

#### Step 3: Design the Workflow Visually

![Workflow Designer View]
```
┌─────────────────┐
│ Start: Receive  │
│ Incident Report │
└────────┬────────┘
         │
         ▼
┌─────────────────┐
│Generate Incident│
│     Number      │
└────────┬────────┘
         │
         ▼
┌─────────────────┐
│   Classify      │
│   Severity      │
└────────┬────────┘
         │
         ▼
    ◆ Decision ◆
    │         │
Minor│         │Major/Fatal
    │         │
    │    ┌────▼─────────┐
    │    │ Fork: Notify │
    │    │& Schedule    │
    │    └──┬───────┬───┘
    │       │       │
    │  ┌────▼──┐ ┌─▼────────┐
    │  │ Email │ │ Calendar │
    │  │ Alert │ │  Event   │
    │  └───────┘ └──────────┘
    │       │       │
    │    └──┬───────┘
    │       │
    │  ┌────▼──────────┐
    │  │    Assign     │
    │  │ Investigation │
    │  └───────────────┘
    │       │
    │  ┌────▼──────────┐
    │  │   Conduct     │
    │  │Investigation  │
    │  └───────────────┘
    │       │
    │  ┌────▼──────────┐
    │  │  Determine    │
    │  │   Actions     │
    │  └───────────────┘
    │       │
    │  ┌────▼──────────┐
    │  │ ForEach:      │
    │  │Execute Actions│
    │  └───────────────┘
    │       │
    └───────┤
            │
      ┌─────▼────────┐
      │    Close     │
      │   Incident   │
      └──────────────┘
            │
      ┌─────▼────────┐
      │   Generate   │
      │Final Report  │
      └──────────────┘
            │
            ▼
         ◆ End ◆
```

### 3.2 Activity Configuration in Studio

#### 3.2.1 Receive Incident Report Activity

```yaml
Activity Type: HTTP Endpoint
Properties:
  Path: /api/workflow/incident/report
  Method: POST
  Authorize: true
  Policy: CanReportIncident
  
Output:
  Name: IncidentData
  Type: IncidentReportModel
  
Validation:
  Required Fields:
    - Description
    - Location
    - ReporterId
    - IncidentDateTime
```

#### 3.2.2 Generate Incident Number Activity

```yaml
Activity Type: Custom Activity
Class: GenerateIncidentNumber
Properties:
  Sequence Provider: DatabaseSequence
  Format: "{0:000}/HSE-{1}/{2:MM}/{3:yyyy}"
  
Input:
  IncidentType: "= IncidentData.Type"
  
Output:
  IncidentNumber: String
  
Error Handling:
  Retry: 3 times
  Delay: 1 second
```

#### 3.2.3 Decision Activity Configuration

```yaml
Activity Type: If/Else
Condition: "IncidentData.Severity != 'Minor'"
True Branch: Investigation Flow
False Branch: Direct to Close
```

#### 3.2.4 Fork Activity for Parallel Execution

```yaml
Activity Type: Fork
Branches:
  - Branch 1: Email Notification
    Join Mode: WaitAll
  - Branch 2: Calendar Event Creation
    Join Mode: WaitAll
```

#### 3.2.5 Human Task Activity

```yaml
Activity Type: Human Task
Properties:
  Task Name: Investigation Review
  Assigned To: "= InvestigationTeam"
  Due Date: "= CurrentDateTime.AddDays(5)"
  Form Schema:
    Fields:
      - Name: Findings
        Type: RichText
        Required: true
      - Name: RootCauses
        Type: MultiSelect
        Options: [Human Error, Equipment Failure, Process Gap]
      - Name: Recommendations
        Type: TextArea
        Required: true
```

### 3.3 Workflow Variables Configuration

```javascript
// In Elsa Studio Variables Panel
{
  "variables": [
    {
      "name": "IncidentData",
      "type": "Harmoni360.Models.IncidentData",
      "storageMode": "WorkflowInstance"
    },
    {
      "name": "IncidentNumber",
      "type": "System.String",
      "storageMode": "WorkflowInstance"
    },
    {
      "name": "InvestigationTeam",
      "type": "System.Collections.Generic.List<System.String>",
      "storageMode": "WorkflowInstance"
    },
    {
      "name": "CorrectiveActions",
      "type": "System.Collections.Generic.List<Harmoni360.Models.CorrectiveAction>",
      "storageMode": "WorkflowInstance"
    },
    {
      "name": "CurrentUser",
      "type": "System.String",
      "storageMode": "Activity"
    }
  ]
}
```

## 4. Custom Activity Development

### 4.1 Creating Custom Activities for Elsa Studio

```csharp
// Custom Activity Base Class
[Activity(
    Category = "Incident Management",
    DisplayName = "Incident Activity Base",
    Description = "Base class for incident management activities"
)]
public abstract class IncidentActivityBase : Activity
{
    [ActivityInput(
        Label = "Incident Context",
        Hint = "The incident workflow context",
        SupportedSyntaxes = new[] { SyntaxNames.JavaScript, SyntaxNames.Liquid }
    )]
    public IncidentWorkflowContext Context { get; set; }
    
    protected IIncidentService IncidentService { get; }
    protected ILogger Logger { get; }
    
    protected IncidentActivityBase(
        IIncidentService incidentService,
        ILogger logger)
    {
        IncidentService = incidentService;
        Logger = logger;
    }
}
```

### 4.2 Investigation Activity with UI Designer Support

```csharp
[Activity(
    Category = "Incident Management",
    DisplayName = "Conduct Investigation",
    Description = "Manages the investigation process for an incident",
    Icon = "fas fa-search"
)]
public class ConductInvestigationActivity : IncidentActivityBase
{
    [ActivityInput(
        Label = "Analysis Method",
        Hint = "Select the analysis method to use",
        UIHint = ActivityInputUIHints.Dropdown,
        Options = new[] { "HFACS", "ICAM" },
        DefaultValue = "HFACS"
    )]
    public string AnalysisMethod { get; set; }
    
    [ActivityInput(
        Label = "Investigation Team",
        Hint = "Select team members for the investigation",
        UIHint = ActivityInputUIHints.MultiSelect,
        OptionsProvider = typeof(UserOptionsProvider)
    )]
    public List<string> TeamMembers { get; set; }
    
    [ActivityOutput(
        Hint = "The completed investigation result"
    )]
    public InvestigationResult Result { get; set; }
    
    protected override async ValueTask<IActivityExecutionResult> OnExecuteAsync(
        ActivityExecutionContext context)
    {
        // Create investigation record
        var investigation = await IncidentService.CreateInvestigation(
            Context.IncidentId,
            TeamMembers,
            AnalysisMethod);
        
        // Create human task for investigation
        var taskId = Guid.NewGuid().ToString();
        context.SetVariable("InvestigationTaskId", taskId);
        
        // Suspend workflow until investigation is complete
        return Suspend();
    }
    
    protected override async ValueTask<IActivityExecutionResult> OnResumeAsync(
        ActivityExecutionContext context)
    {
        var taskId = context.GetVariable<string>("InvestigationTaskId");
        var result = await IncidentService.GetInvestigationResult(taskId);
        
        if (result.Status != "Completed")
        {
            return Suspend();
        }
        
        Result = result;
        return Done();
    }
}
```

### 4.3 Activity Designer Metadata

```csharp
public class IncidentActivityDesigner : IActivityTypeDescriber
{
    public ActivityDescriptor Describe(Type activityType)
    {
        if (!typeof(IncidentActivityBase).IsAssignableFrom(activityType))
            return null;
            
        return new ActivityDescriptor
        {
            Type = activityType.Name,
            DisplayName = GetDisplayName(activityType),
            Description = GetDescription(activityType),
            Category = "Incident Management",
            Icon = GetIcon(activityType),
            Outcomes = GetOutcomes(activityType),
            InputProperties = GetInputProperties(activityType),
            OutputProperties = GetOutputProperties(activityType)
        };
    }
}
```

## 5. Testing Workflows in Elsa Studio

### 5.1 Test Mode Configuration

```csharp
// Enable test mode in development
services.AddElsaStudio(studio => studio
    .EnableTestMode(test => test
        .WithMockServices()
        .WithTestDataGenerator()
        .WithSimulatedDelays()
        .RecordAllActivities()
    )
);
```

### 5.2 Creating Test Scenarios

```yaml
Test Scenario: Minor Incident Flow
Steps:
  1. Trigger workflow with test data:
     - Type: NearMiss
     - Severity: Minor
     - Description: "Test incident"
  
  2. Verify execution path:
     - Incident number generated
     - No investigation triggered
     - Direct to closure
     
  3. Check outputs:
     - Final report generated
     - Status = "Closed"
```

### 5.3 Studio Testing Interface

```javascript
// Test Data Generator in Studio
{
  "testScenarios": [
    {
      "name": "Major Accident with Investigation",
      "input": {
        "incidentReport": {
          "type": "Accident",
          "severity": "Major",
          "description": "Employee injured in warehouse",
          "location": "Warehouse B",
          "reportedBy": "<EMAIL>",
          "affectedPersonnel": ["emp001"],
          "witnessStatements": ["Saw employee slip on wet floor"]
        }
      },
      "expectedPath": [
        "ReceiveReport",
        "GenerateNumber",
        "ClassifySeverity",
        "NotifyManagement",
        "ScheduleInvestigation",
        "AssignInvestigator",
        "ConductInvestigation",
        "DetermineActions",
        "ExecuteActions",
        "VerifyCompletion",
        "CloseIncident",
        "GenerateReport"
      ],
      "assertions": {
        "incidentNumber": "regex:^\\d{3}/HSE-Accident/\\d{2}/\\d{4}$",
        "finalStatus": "Closed",
        "investigationRequired": true
      }
    }
  ]
}
```

## 6. Debugging and Monitoring

### 6.1 Studio Debug Mode

```yaml
Debug Features:
  - Step-by-step execution
  - Variable inspection
  - Activity input/output monitoring
  - Execution timeline
  - Error stack traces
  
Breakpoint Support:
  - Activity breakpoints
  - Conditional breakpoints
  - Log points
```

### 6.2 Workflow Instance Viewer

```javascript
// Instance Details View Configuration
{
  "instanceViewer": {
    "layout": "vertical",
    "panels": [
      {
        "type": "workflow-diagram",
        "showExecutionPath": true,
        "highlightCurrentActivity": true
      },
      {
        "type": "variables",
        "filter": ["IncidentData", "IncidentNumber", "Status"]
      },
      {
        "type": "activity-log",
        "showTimestamps": true,
        "showInputOutput": true
      },
      {
        "type": "journal",
        "showSystemEvents": false
      }
    ]
  }
}
```

### 6.3 Performance Monitoring in Studio

```yaml
Performance Metrics Display:
  - Workflow execution time
  - Activity duration breakdown
  - Database query count
  - External API call latency
  - Memory usage
  
Alerts Configuration:
  - Slow activity execution (> 5s)
  - Failed activities
  - Stuck workflows (> 1 hour)
  - High memory usage (> 500MB)
```

## 7. Deployment from Elsa Studio

### 7.1 Export Workflow Definition

```json
// Exported Workflow Definition
{
  "id": "incident-management-workflow",
  "definitionId": "IncidentManagementWorkflow",
  "version": 1,
  "name": "Incident Management Workflow",
  "displayName": "Incident Management Workflow",
  "description": "Manages HSE incidents from reporting through closure",
  "isPublished": true,
  "isLatest": true,
  "variables": [...],
  "activities": [...],
  "connections": [...],
  "metadata": {
    "designer": "Elsa Studio 3.1",
    "created": "2024-01-15T10:00:00Z",
    "lastModified": "2024-01-20T15:30:00Z",
    "author": "<EMAIL>"
  }
}
```

### 7.2 Deployment Pipeline Integration

```yaml
# Azure DevOps Pipeline
trigger:
  branches:
    include:
    - main
  paths:
    include:
    - workflows/*

stages:
- stage: ValidateWorkflows
  jobs:
  - job: Validate
    steps:
    - task: ElsaWorkflowValidator@1
      inputs:
        workflowPath: '$(Build.SourcesDirectory)/workflows'
        schemaValidation: true
        activityValidation: true

- stage: Deploy
  jobs:
  - deployment: DeployWorkflows
    environment: production
    strategy:
      runOnce:
        deploy:
          steps:
          - task: ElsaWorkflowDeploy@1
            inputs:
              connectionString: $(ElsaConnectionString)
              workflowPath: '$(Build.SourcesDirectory)/workflows'
              strategy: 'blue-green'
```

## 8. Best Practices for Elsa Studio

### 8.1 Workflow Design Best Practices

```yaml
Design Guidelines:
  1. Naming Conventions:
     - Activities: VerbNoun (e.g., SendEmail, CreateTicket)
     - Variables: PascalCase for complex types, camelCase for simple
     - Workflows: DomainWorkflow (e.g., IncidentManagementWorkflow)
  
  2. Activity Organization:
     - Group related activities in categories
     - Use sub-workflows for complex processes
     - Limit workflow size to 50 activities
  
  3. Error Handling:
     - Always include compensation activities
     - Use try-catch patterns for external calls
     - Implement circuit breakers for integrations
  
  4. Performance:
     - Avoid long-running synchronous activities
     - Use caching for frequently accessed data
     - Implement pagination for large datasets
```

### 8.2 Security Considerations

```csharp
// Secure Activity Implementation
[Activity]
public class SecureDataActivity : Activity
{
    protected override IActivityExecutionResult Execute(ActivityExecutionContext context)
    {
        // Always validate user permissions
        var user = context.GetVariable<ClaimsPrincipal>("CurrentUser");
        if (!user.IsInRole("HSE_Officer"))
        {
            return Fault("Unauthorized access");
        }
        
        // Encrypt sensitive data
        var sensitiveData = context.GetVariable<string>("SensitiveInfo");
        var encrypted = _encryptionService.Encrypt(sensitiveData);
        
        // Audit access
        _auditService.LogDataAccess(user.Identity.Name, "SecureDataActivity");
        
        return Done();
    }
}
```

### 8.3 Version Management

```yaml
Versioning Strategy:
  - Major versions for breaking changes
  - Minor versions for new features
  - Patch versions for bug fixes
  
Migration Process:
  1. Test new version in staging
  2. Run parallel with old version
  3. Gradual migration of instances
  4. Deprecate old version after 30 days
```

## 9. Troubleshooting Common Issues

### 9.1 Common Issues and Solutions

| Issue | Symptoms | Solution |
|-------|----------|----------|
| Workflow not triggering | HTTP endpoint returns 404 | Check route registration and authentication |
| Activity timeout | Workflow stuck at activity | Increase timeout or implement async pattern |
| Variable not found | Null reference exception | Verify variable initialization and scope |
| Performance degradation | Slow workflow execution | Enable caching, optimize queries |
| Memory leaks | High memory usage | Dispose resources properly, limit concurrent workflows |

### 9.2 Diagnostic Tools

```csharp
// Diagnostic Endpoint
[ApiController]
[Route("api/workflow/diagnostics")]
public class WorkflowDiagnosticsController : ControllerBase
{
    [HttpGet("health")]
    public async Task<IActionResult> CheckHealth()
    {
        var diagnostics = new
        {
            WorkflowEngine = await CheckWorkflowEngine(),
            Database = await CheckDatabase(),
            Redis = await CheckRedis(),
            ExternalServices = await CheckExternalServices()
        };
        
        return Ok(diagnostics);
    }
    
    [HttpGet("stuck-workflows")]
    public async Task<IActionResult> GetStuckWorkflows()
    {
        var stuck = await _workflowStore.FindManyAsync(
            new WorkflowInstanceIsStuckSpecification(TimeSpan.FromHours(1)));
            
        return Ok(stuck.Select(w => new
        {
            w.Id,
            w.DefinitionId,
            w.CurrentActivity,
            w.CreatedAt,
            StuckDuration = DateTime.UtcNow - w.LastExecutedAt
        }));
    }
}
```

## 10. Advanced Features

### 10.1 Custom UI Extensions

```javascript
// Custom Activity Editor
Elsa.Studio.Extensions.register({
  activityEditors: [{
    activityType: 'InvestigationActivity',
    component: {
      template: `
        <div class="investigation-editor">
          <el-select v-model="activity.analysisMethod" 
                     placeholder="Select analysis method">
            <el-option label="HFACS" value="HFACS"></el-option>
            <el-option label="ICAM" value="ICAM"></el-option>
          </el-select>
          
          <user-picker v-model="activity.teamMembers" 
                       :multiple="true"
                       :roles="['Investigator', 'HSE_Officer']">
          </user-picker>
        </div>
      `,
      props: ['activity', 'workflow'],
      methods: {
        validate() {
          return this.activity.teamMembers?.length > 0;
        }
      }
    }
  }]
});
```

### 10.2 Workflow Templates

```json
{
  "templates": [
    {
      "id": "incident-template",
      "name": "Standard Incident Workflow",
      "description": "Template for incident management workflows",
      "category": "HSE",
      "parameters": [
        {
          "name": "IncidentTypes",
          "type": "array",
          "default": ["Accident", "NearMiss"]
        },
        {
          "name": "RequireInvestigation",
          "type": "boolean",
          "default": true
        }
      ],
      "workflow": {
        // Template workflow definition
      }
    }
  ]
}
```

This comprehensive guide provides all the necessary information to successfully integrate the Incident Management workflow with Elsa Studio in the Harmoni360 HSSE platform. Following these guidelines will ensure a robust, maintainable, and user-friendly workflow implementation.