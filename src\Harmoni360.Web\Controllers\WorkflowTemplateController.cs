using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using Harmoni360.Application.Common.Interfaces;

namespace Harmoni360.Web.Controllers;

[ApiController]
[Route("api/[controller]")]
[Authorize(Roles = "SuperAdmin,HSEManager,WorkflowAdmin")]
public class WorkflowTemplateController : ControllerBase
{
    private readonly IWorkflowTemplateService _templateService;
    private readonly ILogger<WorkflowTemplateController> _logger;

    public WorkflowTemplateController(
        IWorkflowTemplateService templateService,
        ILogger<WorkflowTemplateController> logger)
    {
        _templateService = templateService;
        _logger = logger;
    }

    [HttpGet("{workflowName}/customizations")]
    public async Task<IActionResult> CheckCustomizations(string workflowName)
    {
        try
        {
            var hasCustomizations = await _templateService.HasUserCustomizationsAsync(workflowName);
            return Ok(new { workflowName, hasCustomizations });
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to check customizations for workflow {WorkflowName}", workflowName);
            return StatusCode(500, new { error = "Failed to check customizations", details = ex.Message });
        }
    }

    [HttpGet("{workflowName}/update-notification")]
    public async Task<IActionResult> GetUpdateNotification(string workflowName)
    {
        try
        {
            var notification = await _templateService.GetUpdateNotificationAsync(workflowName);
            return Ok(notification);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to get update notification for workflow {WorkflowName}", workflowName);
            return StatusCode(500, new { error = "Failed to get update notification", details = ex.Message });
        }
    }

    [HttpPost("{workflowName}/reset-to-template")]
    public async Task<IActionResult> ResetToTemplate(string workflowName)
    {
        try
        {
            // Get current user ID from claims
            var userId = User.FindFirst("sub")?.Value ?? User.FindFirst("id")?.Value ?? "Unknown";
            
            var result = await _templateService.ResetToTemplateAsync(workflowName, userId);
            
            if (result.Success)
            {
                return Ok(result);
            }
            else
            {
                return BadRequest(new { error = "Failed to reset workflow to template", result });
            }
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to reset workflow {WorkflowName} to template", workflowName);
            return StatusCode(500, new { error = "Failed to reset workflow to template", details = ex.Message });
        }
    }

    [HttpPost("{workflowName}/preserve-customizations")]
    public async Task<IActionResult> PreserveCustomizations(string workflowName, [FromBody] PreserveCustomizationsRequest request)
    {
        try
        {
            var result = await _templateService.PreserveUserCustomizationsAsync(
                workflowName, 
                request.NewTemplateVersion);
            
            return Ok(result);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to preserve customizations for workflow {WorkflowName}", workflowName);
            return StatusCode(500, new { error = "Failed to preserve customizations", details = ex.Message });
        }
    }

    [HttpPost("create-template")]
    public async Task<IActionResult> CreateTemplate([FromBody] CreateTemplateRequest request)
    {
        try
        {
            // Map workflow name to type - only include existing workflows
            var workflowType = request.WorkflowName switch
            {
                "IncidentManagementWorkflow" => typeof(Harmoni360.Application.Features.WorkflowManagement.IncidentWorkflow.Workflows.IncidentManagementWorkflow),
                // Note: Other workflow types will be added when implemented
                // "IncidentEscalationWorkflow" => typeof(IncidentEscalationWorkflow),
                // "IncidentInvestigationWorkflow" => typeof(IncidentInvestigationWorkflow),
                // "IncidentReviewWorkflow" => typeof(IncidentReviewWorkflow),
                _ => null
            };

            if (workflowType == null)
            {
                return BadRequest(new { error = $"Unknown workflow type: {request.WorkflowName}" });
            }

            var result = await _templateService.CreateOrUpdateTemplateAsync(workflowType);
            return Ok(result);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to create template for workflow {WorkflowName}", request.WorkflowName);
            return StatusCode(500, new { error = "Failed to create template", details = ex.Message });
        }
    }

    [HttpGet("info")]
    public IActionResult GetTemplateInfo()
    {
        return Ok(new
        {
            message = "Workflow Template Pattern Information",
            features = new[]
            {
                "🔒 Creates readonly templates from code-based workflows",
                "✏️ Generates editable user versions for customization",
                "🛡️ Preserves user customizations during code updates",
                "🔄 Automatic synchronization on startup and periodically",
                "📊 Tracks customization history and audit trail",
                "🚨 Notifications when templates are updated"
            },
            availableWorkflows = new[]
            {
                "IncidentProcessWorkflow",
                "IncidentEscalationWorkflow", 
                "IncidentInvestigationWorkflow",
                "IncidentReviewWorkflow"
            },
            apiEndpoints = new
            {
                checkCustomizations = "GET /api/WorkflowTemplate/{workflowName}/customizations",
                getUpdateNotification = "GET /api/WorkflowTemplate/{workflowName}/update-notification",
                resetToTemplate = "POST /api/WorkflowTemplate/{workflowName}/reset-to-template",
                preserveCustomizations = "POST /api/WorkflowTemplate/{workflowName}/preserve-customizations",
                createTemplate = "POST /api/WorkflowTemplate/create-template"
            }
        });
    }
}

public class PreserveCustomizationsRequest
{
    public int NewTemplateVersion { get; set; }
}

public class CreateTemplateRequest
{
    public string WorkflowName { get; set; } = string.Empty;
}