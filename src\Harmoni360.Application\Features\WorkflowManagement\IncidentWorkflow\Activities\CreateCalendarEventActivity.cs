using Elsa.Workflows;
using Elsa.Workflows.Attributes;
using Harmoni360.Application.Common.Interfaces;
using Harmoni360.Application.Features.WorkflowManagement.IncidentWorkflow.Models;
using Harmoni360.Domain.Interfaces;
using Microsoft.Extensions.Logging;
using Elsa.Extensions;
using Elsa.Workflows.Models;

namespace Harmoni360.Application.Features.WorkflowManagement.IncidentWorkflow.Activities;

/// <summary>
/// Activity that creates calendar events for incident-related activities
/// </summary>
[Activity("Incident Management", "Create Calendar Event", "Creates calendar events for investigations and meetings")]
public class CreateCalendarEventActivity : IncidentActivityBase
{
    private readonly ICalendarService _calendarService;
    
    public CreateCalendarEventActivity(
        ILogger<CreateCalendarEventActivity> logger,
        IIncidentRepository incidentRepository,
        ICurrentUserService currentUserService,
        ICalendarService calendarService)
        : base(logger, incidentRepository, currentUserService)
    {
        _calendarService = calendarService;
    }
    
    /// <summary>
    /// The incident workflow context
    /// </summary>
    [Input(
        Description = "The incident workflow context",
        DisplayName = "Incident Context"
    )]
    public Input<IncidentWorkflowContext> Context { get; set; } = default!;
    
    /// <summary>
    /// The type of event to create (investigation, meeting, follow-up)
    /// </summary>
    [Input(
        Description = "Type of calendar event: 'investigation', 'meeting', 'follow-up'",
        DisplayName = "Event Type"
    )]
    public Input<string> EventType { get; set; } = default!;
    
    protected override async ValueTask ExecuteAsync(ActivityExecutionContext context)
    {
        const string activityName = nameof(CreateCalendarEventActivity);
        LogActivity(activityName, "Starting calendar event creation");
        
        try
        {
            var workflowContext = Context.Get(context);
            var eventType = EventType.Get(context);
            
            // Create calendar event based on type
            var calendarEvent = CreateCalendarEvent(workflowContext, eventType);
            
            // Create the event in the calendar system
            var eventId = await _calendarService.CreateEventAsync(calendarEvent);
            
            LogActivity(activityName, 
                "Calendar event created - ID: {EventId}, Type: {EventType}, Title: {Title}",
                eventId, eventType, calendarEvent.Title);
        }
        catch (Exception ex)
        {
            LogActivityError(activityName, ex, "Failed to create calendar event");
        }
    }
    
    private WorkflowCalendarEvent CreateCalendarEvent(IncidentWorkflowContext context, string eventType)
    {
        var baseEvent = new WorkflowCalendarEvent
        {
            Location = context.Location,
            SendInvitations = true
        };
        
        switch (eventType.ToLowerInvariant())
        {
            case "investigation":
                return CreateInvestigationEvent(context, baseEvent);
                
            case "meeting":
                return CreateMeetingEvent(context, baseEvent);
                
            case "follow-up":
                return CreateFollowUpEvent(context, baseEvent);
                
            default:
                return CreateDefaultEvent(context, baseEvent);
        }
    }
    
    private WorkflowCalendarEvent CreateInvestigationEvent(IncidentWorkflowContext context, WorkflowCalendarEvent baseEvent)
    {
        baseEvent.Title = $"Incident Investigation - {context.IncidentNumber}";
        baseEvent.Description = $"Investigation meeting for incident: {context.Description}\n\n" +
                               $"Incident Details:\n" +
                               $"- Number: {context.IncidentNumber}\n" +
                               $"- Type: {context.IncidentType}\n" +
                               $"- Severity: {context.Severity}\n" +
                               $"- Location: {context.Location}\n" +
                               $"- Reported: {context.ReportedAt:yyyy-MM-dd HH:mm}";
        
        // Schedule for next business day, 9 AM
        var nextBusinessDay = GetNextBusinessDay(DateTime.Now);
        baseEvent.Start = nextBusinessDay.Date.AddHours(9);
        baseEvent.End = baseEvent.Start.AddHours(2); // 2-hour meeting
        
        // Add investigation team as attendees
        if (context.InvestigationTeam?.Any() == true)
        {
            baseEvent.Attendees.AddRange(context.InvestigationTeam.Select(userId => $"user.{userId}@company.com"));
        }
        
        return baseEvent;
    }
    
    private WorkflowCalendarEvent CreateMeetingEvent(IncidentWorkflowContext context, WorkflowCalendarEvent baseEvent)
    {
        baseEvent.Title = $"Incident Review Meeting - {context.IncidentNumber}";
        baseEvent.Description = $"Review meeting for incident {context.IncidentNumber}";
        
        var nextBusinessDay = GetNextBusinessDay(DateTime.Now);
        baseEvent.Start = nextBusinessDay.Date.AddHours(14); // 2 PM
        baseEvent.End = baseEvent.Start.AddHours(1); // 1-hour meeting
        
        return baseEvent;
    }
    
    private WorkflowCalendarEvent CreateFollowUpEvent(IncidentWorkflowContext context, WorkflowCalendarEvent baseEvent)
    {
        baseEvent.Title = $"Incident Follow-up - {context.IncidentNumber}";
        baseEvent.Description = $"Follow-up review for incident {context.IncidentNumber}";
        
        // Schedule follow-up for one week from now
        baseEvent.Start = DateTime.Now.AddDays(7).Date.AddHours(10); // 10 AM
        baseEvent.End = baseEvent.Start.AddHours(1); // 1-hour meeting
        
        return baseEvent;
    }
    
    private WorkflowCalendarEvent CreateDefaultEvent(IncidentWorkflowContext context, WorkflowCalendarEvent baseEvent)
    {
        baseEvent.Title = $"Incident Activity - {context.IncidentNumber}";
        baseEvent.Description = $"Incident-related activity for {context.IncidentNumber}";
        
        var nextBusinessDay = GetNextBusinessDay(DateTime.Now);
        baseEvent.Start = nextBusinessDay.Date.AddHours(9);
        baseEvent.End = baseEvent.Start.AddHours(1);
        
        return baseEvent;
    }
    
    private static DateTime GetNextBusinessDay(DateTime date)
    {
        var nextDay = date.Date.AddDays(1);
        
        // Skip weekends
        while (nextDay.DayOfWeek == DayOfWeek.Saturday || nextDay.DayOfWeek == DayOfWeek.Sunday)
        {
            nextDay = nextDay.AddDays(1);
        }
        
        return nextDay;
    }
}

/// <summary>
/// Interface for calendar service operations
/// </summary>
public interface ICalendarService
{
    Task<string> CreateEventAsync(WorkflowCalendarEvent calendarEvent);
    Task UpdateEventAsync(string eventId, WorkflowCalendarEvent calendarEvent);
    Task DeleteEventAsync(string eventId);
}