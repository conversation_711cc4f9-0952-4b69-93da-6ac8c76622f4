{"version": 1, "dependencies": {"net8.0": {"Elsa": {"type": "Direct", "requested": "[3.4.2, )", "resolved": "3.4.2", "contentHash": "XRLUspT+UawJiOfnl+gN948y4wiDRfSURsr98btmt2h1nQWMaqRw5IkpW3qgPaLVycDDIuGJ6v0StYLmRcN2lA==", "dependencies": {"Antlr4.Runtime.Standard": "4.13.1", "Cronos": "0.9.0", "DistributedLock.Core": "1.0.8", "DistributedLock.FileSystem": "1.0.3", "Elsa.Api.Common": "3.4.2", "Elsa.Workflows.Core": "3.4.2", "Elsa.Workflows.Management": "3.4.2", "Elsa.Workflows.Runtime": "3.4.2", "FastEndpoints": "5.35.0", "FastEndpoints.Security": "5.35.0", "FastEndpoints.Swagger": "5.35.0", "Humanizer.Core": "2.14.1", "IronCompress": "1.6.3", "LinqKit.Core": "1.2.8", "Microsoft.Extensions.Caching.Abstractions": "9.0.6", "Microsoft.Extensions.Caching.Memory": "9.0.6", "Microsoft.Extensions.Configuration": "9.0.6", "Microsoft.Extensions.Configuration.Abstractions": "9.0.6", "Microsoft.Extensions.Configuration.Json": "9.0.6", "Microsoft.Extensions.DependencyInjection": "9.0.6", "Microsoft.Extensions.DependencyInjection.Abstractions": "9.0.6", "Microsoft.Extensions.DependencyModel": "9.0.6", "Microsoft.Extensions.Hosting.Abstractions": "9.0.6", "Microsoft.Extensions.Logging": "9.0.6", "Microsoft.Extensions.Logging.Abstractions": "9.0.6", "Microsoft.Extensions.Options": "9.0.6", "Microsoft.Extensions.Options.ConfigurationExtensions": "9.0.6", "Newtonsoft.Json": "13.0.3", "Open.Linq.AsyncExtensions": "1.2.0", "Scrutor": "6.0.1", "ShortGuid": "2.0.1", "System.Linq.Async": "6.0.1", "System.Linq.Dynamic.Core": "*******", "System.Text.Json": "9.0.6", "ThrottleDebounce": "2.0.0"}}, "Elsa.Email": {"type": "Direct", "requested": "[3.4.2, )", "resolved": "3.4.2", "contentHash": "YAB+12zHjbfnWvjMI4VOjrzsDN1yxwZ9EPwECY/3ID4+C728VoGXztrd/+AMCITCjvd55fk6DXKXkOwOHsoRJQ==", "dependencies": {"Antlr4.Runtime.Standard": "4.13.1", "Cronos": "0.9.0", "DistributedLock.Core": "1.0.8", "Elsa.Workflows.Core": "3.4.2", "Elsa.Workflows.Management": "3.4.2", "FastEndpoints": "5.35.0", "FastEndpoints.Security": "5.35.0", "FastEndpoints.Swagger": "5.35.0", "Humanizer.Core": "2.14.1", "IronCompress": "1.6.3", "LinqKit.Core": "1.2.8", "MailKit": "4.11.0", "Microsoft.Extensions.Caching.Abstractions": "9.0.6", "Microsoft.Extensions.Caching.Memory": "9.0.6", "Microsoft.Extensions.Configuration": "9.0.6", "Microsoft.Extensions.Configuration.Abstractions": "9.0.6", "Microsoft.Extensions.Configuration.Json": "9.0.6", "Microsoft.Extensions.DependencyInjection": "9.0.6", "Microsoft.Extensions.DependencyInjection.Abstractions": "9.0.6", "Microsoft.Extensions.DependencyModel": "9.0.6", "Microsoft.Extensions.Hosting.Abstractions": "9.0.6", "Microsoft.Extensions.Http": "9.0.6", "Microsoft.Extensions.Logging": "9.0.6", "Microsoft.Extensions.Logging.Abstractions": "9.0.6", "Microsoft.Extensions.Options": "9.0.6", "Microsoft.Extensions.Options.ConfigurationExtensions": "9.0.6", "Newtonsoft.Json": "13.0.3", "Scrutor": "6.0.1", "ShortGuid": "2.0.1", "System.Formats.Asn1": "9.0.6", "System.Linq.Async": "6.0.1", "System.Linq.Dynamic.Core": "*******", "System.Text.Json": "9.0.6"}}, "Elsa.EntityFrameworkCore.SqlServer": {"type": "Direct", "requested": "[3.4.2, )", "resolved": "3.4.2", "contentHash": "V4b3JQTTuiCqIVuun4ux6pPJ7KTOM4E/TRMDKmOwmVDvj6pUAWksgdCMlHMw2EiMq7ct7yjnyy+04upIvPJD3w==", "dependencies": {"Antlr4.Runtime.Standard": "4.13.1", "AspNetCore.Authentication.ApiKey": "8.0.1", "Azure.Identity": "1.13.2", "Cronos": "0.9.0", "DistributedLock.Core": "1.0.8", "DistributedLock.FileSystem": "1.0.3", "Elsa.EntityFrameworkCore": "3.4.2", "FastEndpoints": "5.35.0", "FastEndpoints.Security": "5.35.0", "FastEndpoints.Swagger": "5.35.0", "Humanizer.Core": "2.14.1", "IronCompress": "1.6.3", "LinqKit.Core": "1.2.8", "Microsoft.Data.SqlClient": "6.0.1", "Microsoft.EntityFrameworkCore": "9.0.6", "Microsoft.EntityFrameworkCore.Relational": "9.0.6", "Microsoft.EntityFrameworkCore.SqlServer": "9.0.6", "Microsoft.Extensions.Caching.Abstractions": "9.0.6", "Microsoft.Extensions.Caching.Memory": "9.0.6", "Microsoft.Extensions.Configuration": "9.0.6", "Microsoft.Extensions.Configuration.Abstractions": "9.0.6", "Microsoft.Extensions.Configuration.Json": "9.0.6", "Microsoft.Extensions.DependencyInjection": "9.0.6", "Microsoft.Extensions.DependencyInjection.Abstractions": "9.0.6", "Microsoft.Extensions.DependencyModel": "9.0.6", "Microsoft.Extensions.Hosting.Abstractions": "9.0.6", "Microsoft.Extensions.Logging": "9.0.6", "Microsoft.Extensions.Logging.Abstractions": "9.0.6", "Microsoft.Extensions.Options": "9.0.6", "Microsoft.Extensions.Options.ConfigurationExtensions": "9.0.6", "Microsoft.Identity.Client": "4.69.1", "Newtonsoft.Json": "13.0.3", "Open.Linq.AsyncExtensions": "1.2.0", "Scrutor": "6.0.1", "ShortGuid": "2.0.1", "System.CommandLine": "2.0.0-beta4.22272.1", "System.Formats.Asn1": "9.0.6", "System.Linq.Async": "6.0.1", "System.Linq.Dynamic.Core": "*******", "System.Text.Json": "9.0.6", "ThrottleDebounce": "2.0.0"}}, "Elsa.Http": {"type": "Direct", "requested": "[3.4.2, )", "resolved": "3.4.2", "contentHash": "nnqrwefQkkwlHWjL/75jo54Yq3AHNAPCb0nZ/uKni04FUKNQ9SKeyCj2iUh/co87UbHGsv2ainI8yOLEvPB16g==", "dependencies": {"Antlr4.Runtime.Standard": "4.13.1", "Cronos": "0.9.0", "DistributedLock.Core": "1.0.8", "DistributedLock.FileSystem": "1.0.3", "Elsa.JavaScript": "3.4.2", "Elsa.Liquid": "3.4.2", "Elsa.SasTokens": "3.4.2", "Elsa.Workflows.Core": "3.4.2", "Elsa.Workflows.Management": "3.4.2", "Elsa.Workflows.Runtime": "3.4.2", "FastEndpoints": "5.35.0", "FastEndpoints.Security": "5.35.0", "FastEndpoints.Swagger": "5.35.0", "FluentStorage": "5.6.0", "Fluid.Core": "2.19.0", "Humanizer.Core": "2.14.1", "IronCompress": "1.6.3", "Jint": "4.2.1", "LinqKit.Core": "1.2.8", "Microsoft.Extensions.Caching.Abstractions": "9.0.6", "Microsoft.Extensions.Caching.Memory": "9.0.6", "Microsoft.Extensions.Configuration": "9.0.6", "Microsoft.Extensions.Configuration.Abstractions": "9.0.6", "Microsoft.Extensions.Configuration.Json": "9.0.6", "Microsoft.Extensions.DependencyInjection": "9.0.6", "Microsoft.Extensions.DependencyInjection.Abstractions": "9.0.6", "Microsoft.Extensions.DependencyModel": "9.0.6", "Microsoft.Extensions.Hosting.Abstractions": "9.0.6", "Microsoft.Extensions.Http": "9.0.6", "Microsoft.Extensions.Http.Resilience": "9.6.0", "Microsoft.Extensions.Logging": "9.0.6", "Microsoft.Extensions.Logging.Abstractions": "9.0.6", "Microsoft.Extensions.Options": "9.0.6", "Microsoft.Extensions.Options.ConfigurationExtensions": "9.0.6", "Microsoft.Extensions.Resilience": "9.6.0", "Newtonsoft.Json": "13.0.3", "Open.Linq.AsyncExtensions": "1.2.0", "Scrutor": "6.0.1", "ShortGuid": "2.0.1", "System.Linq.Async": "6.0.1", "System.Linq.Dynamic.Core": "*******", "System.Text.Json": "9.0.6", "ThrottleDebounce": "2.0.0"}}, "Elsa.Identity": {"type": "Direct", "requested": "[3.4.2, )", "resolved": "3.4.2", "contentHash": "nnlWQaDUxn9ZnRWIYJNZVQrH0BV9pVLsiqvPaZRqev7pRAY/WWjrZMMkBQy1Kdi/el5JNrAiNEwsn4liZCZY6A==", "dependencies": {"AspNetCore.Authentication.ApiKey": "8.0.1", "Cronos": "0.9.0", "DistributedLock.Core": "1.0.8", "Elsa.Api.Common": "3.4.2", "FastEndpoints": "5.35.0", "FastEndpoints.Security": "5.35.0", "FastEndpoints.Swagger": "5.35.0", "Humanizer.Core": "2.14.1", "IronCompress": "1.6.3", "LinqKit.Core": "1.2.8", "Microsoft.Extensions.Configuration": "9.0.6", "Microsoft.Extensions.Configuration.Abstractions": "9.0.6", "Microsoft.Extensions.Configuration.Json": "9.0.6", "Microsoft.Extensions.DependencyInjection": "9.0.6", "Microsoft.Extensions.DependencyInjection.Abstractions": "9.0.6", "Microsoft.Extensions.Hosting.Abstractions": "9.0.6", "Microsoft.Extensions.Logging": "9.0.6", "Microsoft.Extensions.Logging.Abstractions": "9.0.6", "Microsoft.Extensions.Options": "9.0.6", "Microsoft.Extensions.Options.ConfigurationExtensions": "9.0.6", "Newtonsoft.Json": "13.0.3", "ShortGuid": "2.0.1", "System.Linq.Async": "6.0.1", "System.Text.Json": "9.0.6"}}, "Elsa.Scheduling": {"type": "Direct", "requested": "[3.4.2, )", "resolved": "3.4.2", "contentHash": "LhjwiHjqfyJkfgnfP2RI2oXJdWP2kqqucEeqsgP9WuJN/tsSRyuJqHQA4tguKrXrDg6w0shZMDyuB//60XGgkA==", "dependencies": {"Antlr4.Runtime.Standard": "4.13.1", "Cronos": "0.9.0", "DistributedLock.Core": "1.0.8", "DistributedLock.FileSystem": "1.0.3", "Elsa.Workflows.Runtime": "3.4.2", "FastEndpoints": "5.35.0", "FastEndpoints.Security": "5.35.0", "FastEndpoints.Swagger": "5.35.0", "Humanizer.Core": "2.14.1", "IronCompress": "1.6.3", "LinqKit.Core": "1.2.8", "Microsoft.Extensions.Caching.Abstractions": "9.0.6", "Microsoft.Extensions.Caching.Memory": "9.0.6", "Microsoft.Extensions.Configuration": "9.0.6", "Microsoft.Extensions.Configuration.Abstractions": "9.0.6", "Microsoft.Extensions.Configuration.Json": "9.0.6", "Microsoft.Extensions.DependencyInjection": "9.0.6", "Microsoft.Extensions.DependencyInjection.Abstractions": "9.0.6", "Microsoft.Extensions.DependencyModel": "9.0.6", "Microsoft.Extensions.Hosting.Abstractions": "9.0.6", "Microsoft.Extensions.Logging": "9.0.6", "Microsoft.Extensions.Logging.Abstractions": "9.0.6", "Microsoft.Extensions.Options": "9.0.6", "Microsoft.Extensions.Options.ConfigurationExtensions": "9.0.6", "Newtonsoft.Json": "13.0.3", "Open.Linq.AsyncExtensions": "1.2.0", "Scrutor": "6.0.1", "ShortGuid": "2.0.1", "System.Linq.Async": "6.0.1", "System.Linq.Dynamic.Core": "*******", "System.Text.Json": "9.0.6", "ThrottleDebounce": "2.0.0"}}, "Elsa.Studio": {"type": "Direct", "requested": "[3.4.0, )", "resolved": "3.4.0", "contentHash": "EPk3aa5vl+nfXyj6FYJ/yHE+qLFUWoj0v6Mh7wg+Y9HIV5gCdk+E0f0kwdRXD+YxjMSw8I45EnyFsd6xJYZ6cg==", "dependencies": {"BlazorMonaco": "3.3.0", "Blazored.FluentValidation": "2.2.0", "Blazored.LocalStorage": "4.5.0", "CodeBeam.MudBlazor.Extensions": "8.0.0", "Elsa.Api.Client": "3.4.0", "Elsa.Studio.Dashboard": "3.4.0", "Elsa.Studio.Shell": "3.4.0", "Elsa.Studio.Workflows": "3.4.0", "FluentValidation": "11.11.0", "Microsoft.AspNetCore.Components": "8.0.14", "Microsoft.AspNetCore.Components.Authorization": "8.0.14", "Microsoft.AspNetCore.Components.CustomElements": "8.0.14", "Microsoft.AspNetCore.Components.Web": "8.0.14", "Microsoft.AspNetCore.SignalR.Client": "8.0.14", "Microsoft.Extensions.DependencyInjection.Abstractions": "9.0.3", "Microsoft.JSInterop": "8.0.14", "MudBlazor": "8.5.0", "Radzen.Blazor": "6.2.9", "Refit": "8.0.0", "Refit.HttpClientFactory": "8.0.0", "ShortGuid": "2.0.1", "ThrottleDebounce": "2.0.0"}}, "Elsa.Studio.Dashboard": {"type": "Direct", "requested": "[3.4.0, )", "resolved": "3.4.0", "contentHash": "+oULwKgvkxHnQ0dE1DhTY84bU9zCgCl9/vrsiY12hL3epWOCHBnh/iAeM0k+qfqQYKezZ/XtnKhs1xyzPF6DYQ==", "dependencies": {"BlazorMonaco": "3.3.0", "Blazored.FluentValidation": "2.2.0", "Blazored.LocalStorage": "4.5.0", "CodeBeam.MudBlazor.Extensions": "8.0.0", "Elsa.Api.Client": "3.4.0", "Elsa.Studio.Shared": "3.4.0", "Elsa.Studio.Workflows": "3.4.0", "FluentValidation": "11.11.0", "Microsoft.AspNetCore.Components": "8.0.14", "Microsoft.AspNetCore.Components.Authorization": "8.0.14", "Microsoft.AspNetCore.Components.CustomElements": "8.0.14", "Microsoft.AspNetCore.Components.Web": "8.0.14", "Microsoft.AspNetCore.SignalR.Client": "8.0.14", "Microsoft.Extensions.DependencyInjection.Abstractions": "9.0.3", "Microsoft.JSInterop": "8.0.14", "MudBlazor": "8.5.0", "Radzen.Blazor": "6.2.9", "Refit": "8.0.0", "Refit.HttpClientFactory": "8.0.0", "ShortGuid": "2.0.1", "ThrottleDebounce": "2.0.0"}}, "Elsa.Workflows.Api": {"type": "Direct", "requested": "[3.4.2, )", "resolved": "3.4.2", "contentHash": "iWi3LFnryJMq/5vbQsTjWd5UOey8LwopUCn6+h5cgR1h6hZ0a1BZkQ94yfKSxuquvj4OtGa9qzQ3iaxvh2bqsg==", "dependencies": {"Antlr4.Runtime.Standard": "4.13.1", "Cronos": "0.9.0", "DistributedLock.Core": "1.0.8", "DistributedLock.FileSystem": "1.0.3", "Elsa.Api.Common": "3.4.2", "Elsa.Http": "3.4.2", "Elsa.JavaScript": "3.4.2", "Elsa.Workflows.Management": "3.4.2", "Elsa.Workflows.Runtime": "3.4.2", "FastEndpoints": "5.35.0", "FastEndpoints.Security": "5.35.0", "FastEndpoints.Swagger": "5.35.0", "FluentStorage": "5.6.0", "Fluid.Core": "2.19.0", "Humanizer.Core": "2.14.1", "IronCompress": "1.6.3", "Jint": "4.2.1", "LinqKit.Core": "1.2.8", "Microsoft.Extensions.Caching.Abstractions": "9.0.6", "Microsoft.Extensions.Caching.Memory": "9.0.6", "Microsoft.Extensions.Configuration": "9.0.6", "Microsoft.Extensions.Configuration.Abstractions": "9.0.6", "Microsoft.Extensions.Configuration.Json": "9.0.6", "Microsoft.Extensions.DependencyInjection": "9.0.6", "Microsoft.Extensions.DependencyInjection.Abstractions": "9.0.6", "Microsoft.Extensions.DependencyModel": "9.0.6", "Microsoft.Extensions.Hosting.Abstractions": "9.0.6", "Microsoft.Extensions.Http": "9.0.6", "Microsoft.Extensions.Http.Resilience": "9.6.0", "Microsoft.Extensions.Logging": "9.0.6", "Microsoft.Extensions.Logging.Abstractions": "9.0.6", "Microsoft.Extensions.Options": "9.0.6", "Microsoft.Extensions.Options.ConfigurationExtensions": "9.0.6", "Microsoft.Extensions.Resilience": "9.6.0", "Newtonsoft.Json": "13.0.3", "Open.Linq.AsyncExtensions": "1.2.0", "Scrutor": "6.0.1", "ShortGuid": "2.0.1", "System.Linq.Async": "6.0.1", "System.Linq.Dynamic.Core": "*******", "System.Text.Json": "9.0.6", "ThrottleDebounce": "2.0.0"}}, "FluentValidation.AspNetCore": {"type": "Direct", "requested": "[11.3.1, )", "resolved": "11.3.1", "contentHash": "FjkfrGGwC+25oH8QpX1ti3gxkFjDDtotkAQWyQz2CkwQwEfRKTANjhh77I79kY8dov2ydK1SY+e4jiOWsbbDdQ==", "dependencies": {"FluentValidation": "11.11.0", "FluentValidation.DependencyInjectionExtensions": "11.11.0"}}, "MediatR": {"type": "Direct", "requested": "[12.5.0, )", "resolved": "12.5.0", "contentHash": "vqm2H8/nqL5NAJHPhsG1JOPwfkmbVrPyh4svdoRzu+uZh6Ex7PRoHBGsLYC0/RWCEJFqD1ohHNpteQvql9OktA==", "dependencies": {"MediatR.Contracts": "[2.0.1, 3.0.0)", "Microsoft.Extensions.DependencyInjection.Abstractions": "8.0.0"}}, "Microsoft.AspNetCore.Authentication.JwtBearer": {"type": "Direct", "requested": "[8.0.13, )", "resolved": "8.0.13", "contentHash": "EUNaX3F4fALAfvp7wsCqjqziu1lTNwYRFbIcVJEt6vInWxEpscAM/pcG6GBOx3WcmSRdB7sqtKAKSkyY6XhTig==", "dependencies": {"Microsoft.IdentityModel.Protocols.OpenIdConnect": "7.1.2"}}, "Microsoft.AspNetCore.Components.WebAssembly.Server": {"type": "Direct", "requested": "[8.0.16, )", "resolved": "8.0.16", "contentHash": "surVTYWJbVkdAP+gnPsrPmVOJrrJfSPk6DNTpNPwA+sbRR3d0oIez1xWqdnfvAPFL7qcp6mfZgHhRhg47orBcQ=="}, "Microsoft.AspNetCore.OpenApi": {"type": "Direct", "requested": "[8.0.16, )", "resolved": "8.0.16", "contentHash": "jeZBYi62BKGRZXEkXAr9hj1L6u71HRKE7EPaZBouF1xmKdQIX7GO5oSRLTQLQmmST0y/aaI+Mr4OzyyRjmBFog==", "dependencies": {"Microsoft.OpenApi": "1.4.3"}}, "Microsoft.AspNetCore.SpaServices.Extensions": {"type": "Direct", "requested": "[8.0.0, )", "resolved": "8.0.0", "contentHash": "kWGOh9wI0eT/n98VSVQJnLAaLCCDHkvYjFp6t1LUf+eIaBwHAMFLsjbS6ILqFYu12XxQdSvFMvVVfhW7S4Pu3g==", "dependencies": {"Microsoft.Extensions.FileProviders.Physical": "8.0.0"}}, "Microsoft.CodeAnalysis.Common": {"type": "Direct", "requested": "[4.13.0, )", "resolved": "4.13.0", "contentHash": "T8nRl4mAUY4mhdYM4U2ra2vP2EL+ol8Yqwo0gwC/V55vmlXq9NxdIkZJynTpTL1uX/jHijJ90AeOEx4lf7OwzQ==", "dependencies": {"Microsoft.CodeAnalysis.Analyzers": "3.11.0", "System.Collections.Immutable": "8.0.0", "System.Reflection.Metadata": "8.0.0"}}, "Microsoft.EntityFrameworkCore.Design": {"type": "Direct", "requested": "[9.0.1, )", "resolved": "9.0.1", "contentHash": "/pchcadGU57ChRYH0/bvLTeU/n1mpWO+0pVK7pUzzuwRu5SIQb8dVMZVPhzvEI2VO5rP1yricSQBBnOmDqQhvg==", "dependencies": {"Humanizer.Core": "2.14.1", "Microsoft.Build.Framework": "17.8.3", "Microsoft.Build.Locator": "1.7.8", "Microsoft.CodeAnalysis.CSharp": "4.8.0", "Microsoft.CodeAnalysis.CSharp.Workspaces": "4.8.0", "Microsoft.CodeAnalysis.Workspaces.MSBuild": "4.8.0", "Microsoft.EntityFrameworkCore.Relational": "9.0.1", "Microsoft.Extensions.Caching.Memory": "9.0.1", "Microsoft.Extensions.Configuration.Abstractions": "9.0.1", "Microsoft.Extensions.DependencyModel": "9.0.1", "Microsoft.Extensions.Logging": "9.0.1", "Mono.TextTemplating": "3.0.0", "System.Text.Json": "9.0.1"}}, "Serilog.AspNetCore": {"type": "Direct", "requested": "[9.0.0, )", "resolved": "9.0.0", "contentHash": "JslDajPlBsn3Pww1554flJFTqROvK9zz9jONNQgn0D8Lx2Trw8L0A8/n6zEQK1DAZWXrJwiVLw8cnTR3YFuYsg==", "dependencies": {"Serilog": "4.2.0", "Serilog.Extensions.Hosting": "9.0.0", "Serilog.Formatting.Compact": "3.0.0", "Serilog.Settings.Configuration": "9.0.0", "Serilog.Sinks.Console": "6.0.0", "Serilog.Sinks.Debug": "3.0.0", "Serilog.Sinks.File": "6.0.0"}}, "Serilog.Sinks.Seq": {"type": "Direct", "requested": "[9.0.0, )", "resolved": "9.0.0", "contentHash": "aNU8A0K322q7+voPNmp1/qNPH+9QK8xvM1p72sMmCG0wGlshFzmtDW9QnVSoSYCj0MgQKcMOlgooovtBhRlNHw==", "dependencies": {"Serilog": "4.2.0", "Serilog.Sinks.File": "6.0.0"}}, "Swashbuckle.AspNetCore": {"type": "Direct", "requested": "[8.1.4, )", "resolved": "8.1.4", "contentHash": "qYk8VHyvs6wML+KXtjyCgS9Aj18mcm0ZtnJeNCTlj/DYQ7A3pfLIztQgLuZS/LEMYsrTo1lSKR3IIZ5/HzVCWA==", "dependencies": {"Microsoft.Extensions.ApiDescription.Server": "6.0.5", "Swashbuckle.AspNetCore.Swagger": "8.1.4", "Swashbuckle.AspNetCore.SwaggerGen": "8.1.4", "Swashbuckle.AspNetCore.SwaggerUI": "8.1.4"}}, "System.Text.Json": {"type": "Direct", "requested": "[9.0.6, )", "resolved": "9.0.6", "contentHash": "h+ZtYTyTnTh5Ju6mHCKb3FPGx4ylJZgm9W7Y2psUnkhQRPMOIxX+TCN0ZgaR/+Yea+93XHWAaMzYTar1/EHIPg==", "dependencies": {"System.IO.Pipelines": "9.0.6", "System.Text.Encodings.Web": "9.0.6"}}, "Acornima": {"type": "Transitive", "resolved": "1.1.1", "contentHash": "+ul3wnGmz+e845O8pKzA4NVuRiYvPXlVn+5vgQTN7zmBi/VAgV6aAyBtjBkv1tLKUUFCZS6cPJtRPLOQ1D9XHw=="}, "Antlr4.Runtime.Standard": {"type": "Transitive", "resolved": "4.13.1", "contentHash": "Da5+i4kFHUseJRZGcBG5fmZGpA/Ns180ibrQMxgZzjpQOnENVvSL5gi5HZ8Ncz8/AR2WsKbOg2lMBzjz0HUQcA=="}, "AspNetCore.Authentication.ApiKey": {"type": "Transitive", "resolved": "8.0.1", "contentHash": "u/WTgciHLgthMdBnOUsijh9HGxMW/6rLcEU/9DEAoS3L9DtqA8PVwmkD+XuZFVd8q3QkJapDaEkXe6fxHR6fcw=="}, "AutoMapper": {"type": "Transitive", "resolved": "12.0.1", "contentHash": "hvV62vl6Hp/WfQ24yzo3Co9+OPl8wH8hApwVtgWpiAynVJkUcs7xvehnSftawL8Pe8FrPffBRM3hwzLQqWDNjA==", "dependencies": {"Microsoft.CSharp": "4.7.0"}}, "AutoMapper.Extensions.Microsoft.DependencyInjection": {"type": "Transitive", "resolved": "12.0.1", "contentHash": "+g/K+Vpe3gGMKGzjslMOdqNlkikScDjWfVvmWTayrDHaG/n2pPmFBMa+jKX1r/h6BDGFdkyRjAuhFE3ykW+r1g==", "dependencies": {"AutoMapper": "[12.0.1]", "Microsoft.Extensions.Options": "6.0.0"}}, "Azure.Core": {"type": "Transitive", "resolved": "1.44.1", "contentHash": "YyznXLQZCregzHvioip07/BkzjuWNXogJEVz9T5W6TwjNr17ax41YGzYMptlo2G10oLCuVPoyva62y0SIRDixg==", "dependencies": {"Microsoft.Bcl.AsyncInterfaces": "6.0.0", "System.ClientModel": "1.1.0", "System.Diagnostics.DiagnosticSource": "6.0.1", "System.Memory.Data": "6.0.0", "System.Numerics.Vectors": "4.5.0", "System.Text.Encodings.Web": "6.0.0", "System.Text.Json": "6.0.10", "System.Threading.Tasks.Extensions": "4.5.4"}}, "Azure.Identity": {"type": "Transitive", "resolved": "1.13.2", "contentHash": "CngQVQELdzFmsGSWyGIPIUOCrII7nApMVWxVmJCKQQrWxRXcNquCsZ+njRJRnhFUfD+KMAhpjyRCaceE4EOL6A==", "dependencies": {"Azure.Core": "1.44.1", "Microsoft.Identity.Client": "4.67.2", "Microsoft.Identity.Client.Extensions.Msal": "4.67.2", "System.Memory": "4.5.5", "System.Threading.Tasks.Extensions": "4.5.4"}}, "Blazored.FluentValidation": {"type": "Transitive", "resolved": "2.2.0", "contentHash": "lBSHW7faCJqwn7KtZt6vqHSiyGvTL5McpS4sQUW3RLu2R5eMmf4oolFeufZfcAB8TV8iI+/PYouz7sn773E8Og==", "dependencies": {"FluentValidation": "11.9.1", "Microsoft.AspNetCore.Components.Web": "8.0.6"}}, "Blazored.LocalStorage": {"type": "Transitive", "resolved": "4.5.0", "contentHash": "6nZuJwA7zNIKx83IsObiHXZb09ponJOpCClU3en+hI8ZFvrOKXeOw+H7TegQZQrvdR1n9fkrVkEBQZg8vx6ZTw==", "dependencies": {"Microsoft.AspNetCore.Components.Web": "8.0.0"}}, "BlazorMonaco": {"type": "Transitive", "resolved": "3.3.0", "contentHash": "ywtUCZMfmaNBadhQbZEGPerdptHUcyVbPB2Ug1HTNzarJlMCJFSBRvQZefS383h/vDhFgHUrR7/pE6scIc6lJg==", "dependencies": {"Microsoft.AspNetCore.Components": "8.0.0", "Microsoft.AspNetCore.Components.Web": "8.0.0"}}, "BouncyCastle.Cryptography": {"type": "Transitive", "resolved": "2.5.1", "contentHash": "zy8TMeTP+1FH2NrLaNZtdRbBdq7u5MI+NFZQOBSM69u5RFkciinwzV2eveY6Kjf5MzgsYvvl6kTStsj3JrXqkg=="}, "BuildBundlerMinifier": {"type": "Transitive", "resolved": "3.2.449", "contentHash": "uA9sYDy4VepL3xwzBTLcP2LyuVYMt0ZIT3gaSiXvGoX15Ob+rOP+hGydhevlSVd+rFo+Y+VQFEHDuWU8HBW+XA=="}, "CodeBeam.MudBlazor.Extensions": {"type": "Transitive", "resolved": "8.0.0", "contentHash": "MNxReFDmME1OLhHsD7lrfQZ9cMu5X60PIvidyFueq0ddDKHlagHS6CuYBZRQ62rHqmocdHWOLyJ73t2Im0NnWw==", "dependencies": {"BuildBundlerMinifier": "3.2.449", "CsvHelper": "33.0.1", "Microsoft.AspNetCore.Components": "8.0.12", "Microsoft.AspNetCore.Components.Web": "8.0.12", "MudBlazor": "8.0.0", "ZXing.Net": "0.16.9"}}, "Cronos": {"type": "Transitive", "resolved": "0.9.0", "contentHash": "NBEeEWzI1bNiq2NMxoQh54qPbMtsIoJSO4cswRzNlB5iq7l21zEgCMFT86OzvVZc2eD1ArQ7kBo4FzjrNbM5Rw=="}, "CsvHelper": {"type": "Transitive", "resolved": "33.0.1", "contentHash": "fev4lynklAU2A9GVMLtwarkwaanjSYB4wUqO2nOJX5hnzObORzUqVLe+bDYCUyIIRQM4o5Bsq3CcyJR89iMmEQ=="}, "DistributedLock.Core": {"type": "Transitive", "resolved": "1.0.8", "contentHash": "LAOsY8WxX8JU/n3lfXFz+f2pfnv0+4bHkCrOO3bwa28u9HrS3DlxSG6jf+u76SqesKs+KehZi0CndkfaUXBKvg=="}, "DistributedLock.FileSystem": {"type": "Transitive", "resolved": "1.0.3", "contentHash": "Q88LTgHXzcQq9ATa6SZSqAE+VFyIapNGFs+XG7nHx+SpzUf1ACnZ8iyz6fAT/ojH+OLNnWwF3kCYh83s+vnCbA==", "dependencies": {"DistributedLock.Core": "[1.0.8, 1.1.0)"}}, "Elsa.Alterations": {"type": "Transitive", "resolved": "3.4.2", "contentHash": "jYjrxFSrL94RM6eoKAqwXx3DzoKg3ZrPNE2as1rNPnqFa9iDGFhxWoBQQGuICX02SUeWOXRV8dQU3cPH1DwZFw==", "dependencies": {"Antlr4.Runtime.Standard": "4.13.1", "Cronos": "0.9.0", "DistributedLock.Core": "1.0.8", "DistributedLock.FileSystem": "1.0.3", "Elsa.Alterations.Core": "3.4.2", "Elsa.Api.Common": "3.4.2", "FastEndpoints": "5.35.0", "FastEndpoints.Security": "5.35.0", "FastEndpoints.Swagger": "5.35.0", "Humanizer.Core": "2.14.1", "IronCompress": "1.6.3", "LinqKit.Core": "1.2.8", "Microsoft.Extensions.Caching.Abstractions": "9.0.6", "Microsoft.Extensions.Caching.Memory": "9.0.6", "Microsoft.Extensions.Configuration": "9.0.6", "Microsoft.Extensions.Configuration.Abstractions": "9.0.6", "Microsoft.Extensions.Configuration.Json": "9.0.6", "Microsoft.Extensions.DependencyInjection": "9.0.6", "Microsoft.Extensions.DependencyInjection.Abstractions": "9.0.6", "Microsoft.Extensions.DependencyModel": "9.0.6", "Microsoft.Extensions.Hosting.Abstractions": "9.0.6", "Microsoft.Extensions.Logging": "9.0.6", "Microsoft.Extensions.Logging.Abstractions": "9.0.6", "Microsoft.Extensions.Options": "9.0.6", "Microsoft.Extensions.Options.ConfigurationExtensions": "9.0.6", "Newtonsoft.Json": "13.0.3", "Open.Linq.AsyncExtensions": "1.2.0", "Scrutor": "6.0.1", "ShortGuid": "2.0.1", "System.Linq.Async": "6.0.1", "System.Linq.Dynamic.Core": "*******", "System.Text.Json": "9.0.6", "ThrottleDebounce": "2.0.0"}}, "Elsa.Alterations.Core": {"type": "Transitive", "resolved": "3.4.2", "contentHash": "/OwsFaX2pC8DTd+ftnuOTsn85ikLFuJ+0zwExmSWp6yXYmTVzMV2yNSwibVaVth9svAk5QkToqB1W16E5Z7N2w==", "dependencies": {"Antlr4.Runtime.Standard": "4.13.1", "Cronos": "0.9.0", "DistributedLock.Core": "1.0.8", "DistributedLock.FileSystem": "1.0.3", "Elsa.Workflows.Core": "3.4.2", "Elsa.Workflows.Management": "3.4.2", "Elsa.Workflows.Runtime": "3.4.2", "FastEndpoints": "5.35.0", "FastEndpoints.Security": "5.35.0", "FastEndpoints.Swagger": "5.35.0", "Humanizer.Core": "2.14.1", "IronCompress": "1.6.3", "LinqKit.Core": "1.2.8", "Microsoft.Extensions.Caching.Abstractions": "9.0.6", "Microsoft.Extensions.Caching.Memory": "9.0.6", "Microsoft.Extensions.Configuration": "9.0.6", "Microsoft.Extensions.Configuration.Abstractions": "9.0.6", "Microsoft.Extensions.Configuration.Json": "9.0.6", "Microsoft.Extensions.DependencyInjection": "9.0.6", "Microsoft.Extensions.DependencyInjection.Abstractions": "9.0.6", "Microsoft.Extensions.DependencyModel": "9.0.6", "Microsoft.Extensions.Hosting.Abstractions": "9.0.6", "Microsoft.Extensions.Logging": "9.0.6", "Microsoft.Extensions.Logging.Abstractions": "9.0.6", "Microsoft.Extensions.Options": "9.0.6", "Microsoft.Extensions.Options.ConfigurationExtensions": "9.0.6", "Newtonsoft.Json": "13.0.3", "Open.Linq.AsyncExtensions": "1.2.0", "Scrutor": "6.0.1", "ShortGuid": "2.0.1", "System.Linq.Async": "6.0.1", "System.Linq.Dynamic.Core": "*******", "System.Text.Json": "9.0.6", "ThrottleDebounce": "2.0.0"}}, "Elsa.Api.Client": {"type": "Transitive", "resolved": "3.4.2", "contentHash": "FqGk+EKyZD3quWLFvDRChf0vKaT8OCuLULN7MA0VtnPO7qMW8ZES5pBdmnrn3uDbXaQlUNAaeaeAmSCToR6XUg==", "dependencies": {"Cronos": "0.9.0", "DistributedLock.Core": "1.0.8", "Elsa.Expressions": "3.4.2", "Humanizer.Core": "2.14.1", "IronCompress": "1.6.3", "LinqKit.Core": "1.2.8", "Microsoft.Extensions.Configuration": "9.0.6", "Microsoft.Extensions.Configuration.Abstractions": "9.0.6", "Microsoft.Extensions.Configuration.Json": "9.0.6", "Microsoft.Extensions.DependencyInjection": "9.0.6", "Microsoft.Extensions.DependencyInjection.Abstractions": "9.0.6", "Microsoft.Extensions.Hosting.Abstractions": "9.0.6", "Microsoft.Extensions.Http": "9.0.6", "Microsoft.Extensions.Http.Polly": "9.0.6", "Microsoft.Extensions.Logging": "9.0.6", "Microsoft.Extensions.Logging.Abstractions": "9.0.6", "Microsoft.Extensions.Options": "9.0.6", "Microsoft.Extensions.Options.ConfigurationExtensions": "9.0.6", "Polly": "8.6.0", "Polly.Extensions.Http": "3.0.0", "Refit": "8.0.0", "Refit.HttpClientFactory": "8.0.0", "System.Text.Json": "9.0.6"}}, "Elsa.Api.Common": {"type": "Transitive", "resolved": "3.4.2", "contentHash": "f/ppRXJHJ1wDECwkA0q7UVNlQeepjA8oqW0vqIVJZ1K7fg6Uh7/aIvs3oh7UB0m+TJx3AVDKq19WfNUdGEVcMQ==", "dependencies": {"Cronos": "0.9.0", "DistributedLock.Core": "1.0.8", "Elsa.Features": "3.4.2", "Elsa.Workflows.Core": "3.4.2", "FastEndpoints": "5.35.0", "FastEndpoints.Security": "5.35.0", "FastEndpoints.Swagger": "5.35.0", "Humanizer.Core": "2.14.1", "IronCompress": "1.6.3", "LinqKit.Core": "1.2.8", "Microsoft.Extensions.Configuration": "9.0.6", "Microsoft.Extensions.Configuration.Abstractions": "9.0.6", "Microsoft.Extensions.Configuration.Json": "9.0.6", "Microsoft.Extensions.DependencyInjection": "9.0.6", "Microsoft.Extensions.DependencyInjection.Abstractions": "9.0.6", "Microsoft.Extensions.Hosting.Abstractions": "9.0.6", "Microsoft.Extensions.Logging": "9.0.6", "Microsoft.Extensions.Logging.Abstractions": "9.0.6", "Microsoft.Extensions.Options": "9.0.6", "Microsoft.Extensions.Options.ConfigurationExtensions": "9.0.6", "Newtonsoft.Json": "13.0.3", "ShortGuid": "2.0.1", "System.Linq.Async": "6.0.1", "System.Text.Json": "9.0.6"}}, "Elsa.Caching": {"type": "Transitive", "resolved": "3.4.2", "contentHash": "AM4BMtWc1Sq6arHpFATv5fAtKAAOOcLbBxkGOGf+1TBXGbKe/WXJqjQ6+/ygIKlRls2tRS50z+oykSqqZNfTKA==", "dependencies": {"Elsa.Features": "3.4.2", "Microsoft.Extensions.Caching.Abstractions": "9.0.6", "Microsoft.Extensions.Caching.Memory": "9.0.6", "Microsoft.Extensions.Configuration.Abstractions": "9.0.6", "Microsoft.Extensions.DependencyInjection.Abstractions": "9.0.6", "Microsoft.Extensions.DependencyModel": "9.0.6", "Microsoft.Extensions.Hosting.Abstractions": "9.0.6", "Microsoft.Extensions.Logging.Abstractions": "9.0.6", "Microsoft.Extensions.Options": "9.0.6", "Scrutor": "6.0.1", "System.Text.Json": "9.0.6"}}, "Elsa.Common": {"type": "Transitive", "resolved": "3.4.2", "contentHash": "1Zr2T4IxCMLl4KlgRMiFJ/p/atSumRVnazRj+9pKmc6qx8smq3gssBrL4rakJ7rl/GRfTHPuvdv3OUWywbYomg==", "dependencies": {"Cronos": "0.9.0", "DistributedLock.Core": "1.0.8", "Elsa.Features": "3.4.2", "Elsa.Mediator": "3.4.2", "IronCompress": "1.6.3", "LinqKit.Core": "1.2.8", "Microsoft.Extensions.Configuration": "9.0.6", "Microsoft.Extensions.Configuration.Abstractions": "9.0.6", "Microsoft.Extensions.Configuration.Json": "9.0.6", "Microsoft.Extensions.DependencyInjection.Abstractions": "9.0.6", "Microsoft.Extensions.Hosting.Abstractions": "9.0.6", "Microsoft.Extensions.Logging.Abstractions": "9.0.6", "Microsoft.Extensions.Options": "9.0.6", "Microsoft.Extensions.Options.ConfigurationExtensions": "9.0.6", "System.Text.Json": "9.0.6"}}, "Elsa.CSharp": {"type": "Transitive", "resolved": "3.4.2", "contentHash": "J9k1wsjZY2MEZKL0fcxTbOSbftMs+LlJTvOO3kbpwD/efSk8Dt+Vm/lKBHMJhTf/hx3iL1lDN8MINuNaZabTRQ==", "dependencies": {"Antlr4.Runtime.Standard": "4.13.1", "Cronos": "0.9.0", "DistributedLock.Core": "1.0.8", "Elsa.Expressions": "3.4.2", "Elsa.Workflows.Core": "3.4.2", "Elsa.Workflows.Management": "3.4.2", "FastEndpoints": "5.35.0", "FastEndpoints.Security": "5.35.0", "FastEndpoints.Swagger": "5.35.0", "Humanizer.Core": "2.14.1", "IronCompress": "1.6.3", "LinqKit.Core": "1.2.8", "Microsoft.CodeAnalysis.CSharp.Scripting": "4.13.0", "Microsoft.Extensions.Caching.Abstractions": "9.0.6", "Microsoft.Extensions.Caching.Memory": "9.0.6", "Microsoft.Extensions.Configuration": "9.0.6", "Microsoft.Extensions.Configuration.Abstractions": "9.0.6", "Microsoft.Extensions.Configuration.Json": "9.0.6", "Microsoft.Extensions.DependencyInjection": "9.0.6", "Microsoft.Extensions.DependencyInjection.Abstractions": "9.0.6", "Microsoft.Extensions.DependencyModel": "9.0.6", "Microsoft.Extensions.Hosting.Abstractions": "9.0.6", "Microsoft.Extensions.Logging": "9.0.6", "Microsoft.Extensions.Logging.Abstractions": "9.0.6", "Microsoft.Extensions.Options": "9.0.6", "Microsoft.Extensions.Options.ConfigurationExtensions": "9.0.6", "Newtonsoft.Json": "13.0.3", "Scrutor": "6.0.1", "ShortGuid": "2.0.1", "System.Linq.Async": "6.0.1", "System.Linq.Dynamic.Core": "*******", "System.Text.Json": "9.0.6"}}, "Elsa.Dsl": {"type": "Transitive", "resolved": "3.4.2", "contentHash": "qhvFTe8LTmwksiBTxu9vJOUKNB9zvxfGC8gVNx8A0DJ/EY2A681iupmwIHNZRxmno0VHYQxkkrl5EjeAIYiPFw==", "dependencies": {"Antlr4.Runtime.Standard": "4.13.1", "Cronos": "0.9.0", "DistributedLock.Core": "1.0.8", "Elsa.Workflows.Core": "3.4.2", "Humanizer.Core": "2.14.1", "IronCompress": "1.6.3", "LinqKit.Core": "1.2.8", "Microsoft.Extensions.Configuration": "9.0.6", "Microsoft.Extensions.Configuration.Abstractions": "9.0.6", "Microsoft.Extensions.Configuration.Json": "9.0.6", "Microsoft.Extensions.DependencyInjection": "9.0.6", "Microsoft.Extensions.DependencyInjection.Abstractions": "9.0.6", "Microsoft.Extensions.Hosting.Abstractions": "9.0.6", "Microsoft.Extensions.Logging": "9.0.6", "Microsoft.Extensions.Logging.Abstractions": "9.0.6", "Microsoft.Extensions.Options": "9.0.6", "Microsoft.Extensions.Options.ConfigurationExtensions": "9.0.6", "Newtonsoft.Json": "13.0.3", "ShortGuid": "2.0.1", "System.Linq.Async": "6.0.1", "System.Text.Json": "9.0.6"}}, "Elsa.EntityFrameworkCore": {"type": "Transitive", "resolved": "3.4.2", "contentHash": "KnpoaZGJ2yP1aiCqaCkq4DPD4vlHf1XnznUpOq2p3NhKsjHkvPcAZjMMPArL3m7IRYs0q7Q1Mlzzk9foDA8ybg==", "dependencies": {"Antlr4.Runtime.Standard": "4.13.1", "AspNetCore.Authentication.ApiKey": "8.0.1", "Cronos": "0.9.0", "DistributedLock.Core": "1.0.8", "DistributedLock.FileSystem": "1.0.3", "Elsa.Alterations": "3.4.2", "Elsa.Alterations.Core": "3.4.2", "Elsa.EntityFrameworkCore.Common": "3.4.2", "Elsa.Identity": "3.4.2", "Elsa.Labels": "3.4.2", "Elsa.Workflows.Management": "3.4.2", "Elsa.Workflows.Runtime": "3.4.2", "FastEndpoints": "5.35.0", "FastEndpoints.Security": "5.35.0", "FastEndpoints.Swagger": "5.35.0", "Humanizer.Core": "2.14.1", "IronCompress": "1.6.3", "LinqKit.Core": "1.2.8", "Microsoft.EntityFrameworkCore": "9.0.6", "Microsoft.EntityFrameworkCore.Relational": "9.0.6", "Microsoft.Extensions.Caching.Abstractions": "9.0.6", "Microsoft.Extensions.Caching.Memory": "9.0.6", "Microsoft.Extensions.Configuration": "9.0.6", "Microsoft.Extensions.Configuration.Abstractions": "9.0.6", "Microsoft.Extensions.Configuration.Json": "9.0.6", "Microsoft.Extensions.DependencyInjection": "9.0.6", "Microsoft.Extensions.DependencyInjection.Abstractions": "9.0.6", "Microsoft.Extensions.DependencyModel": "9.0.6", "Microsoft.Extensions.Hosting.Abstractions": "9.0.6", "Microsoft.Extensions.Logging": "9.0.6", "Microsoft.Extensions.Logging.Abstractions": "9.0.6", "Microsoft.Extensions.Options": "9.0.6", "Microsoft.Extensions.Options.ConfigurationExtensions": "9.0.6", "Newtonsoft.Json": "13.0.3", "Open.Linq.AsyncExtensions": "1.2.0", "Scrutor": "6.0.1", "ShortGuid": "2.0.1", "System.CommandLine": "2.0.0-beta4.22272.1", "System.Linq.Async": "6.0.1", "System.Linq.Dynamic.Core": "*******", "System.Text.Json": "9.0.6", "ThrottleDebounce": "2.0.0"}}, "Elsa.EntityFrameworkCore.Common": {"type": "Transitive", "resolved": "3.4.2", "contentHash": "wwouZ/dUy+l9wKJCg2aqiZ4u8i5AoxI0Y8qvSlUEji1NPWcKLLgJ+vyGPp1ST2kD7IyyEzB2OR+SeEDAIiLrXQ==", "dependencies": {"Cronos": "0.9.0", "DistributedLock.Core": "1.0.8", "Elsa.Common": "3.4.2", "Elsa.Tenants": "3.4.2", "FastEndpoints": "5.35.0", "FastEndpoints.Security": "5.35.0", "FastEndpoints.Swagger": "5.35.0", "Humanizer.Core": "2.14.1", "IronCompress": "1.6.3", "LinqKit.Core": "1.2.8", "Microsoft.EntityFrameworkCore": "9.0.6", "Microsoft.EntityFrameworkCore.Relational": "9.0.6", "Microsoft.Extensions.Caching.Abstractions": "9.0.6", "Microsoft.Extensions.Caching.Memory": "9.0.6", "Microsoft.Extensions.Configuration": "9.0.6", "Microsoft.Extensions.Configuration.Abstractions": "9.0.6", "Microsoft.Extensions.Configuration.Json": "9.0.6", "Microsoft.Extensions.DependencyInjection": "9.0.6", "Microsoft.Extensions.DependencyInjection.Abstractions": "9.0.6", "Microsoft.Extensions.DependencyModel": "9.0.6", "Microsoft.Extensions.Hosting.Abstractions": "9.0.6", "Microsoft.Extensions.Logging": "9.0.6", "Microsoft.Extensions.Logging.Abstractions": "9.0.6", "Microsoft.Extensions.Options": "9.0.6", "Microsoft.Extensions.Options.ConfigurationExtensions": "9.0.6", "Newtonsoft.Json": "13.0.3", "Open.Linq.AsyncExtensions": "1.2.0", "Scrutor": "6.0.1", "ShortGuid": "2.0.1", "System.CommandLine": "2.0.0-beta4.22272.1", "System.Linq.Async": "6.0.1", "System.Text.Json": "9.0.6"}}, "Elsa.EntityFrameworkCore.PostgreSql": {"type": "Transitive", "resolved": "3.4.2", "contentHash": "0B0bLwc7nNGk0v3MBrw7E+trvniKbJgx37NEtE9Cx9i4CfSybTVk1tMliBysXJUpe3oCGkLTl8pfw5erUS6v+A==", "dependencies": {"Antlr4.Runtime.Standard": "4.13.1", "AspNetCore.Authentication.ApiKey": "8.0.1", "Cronos": "0.9.0", "DistributedLock.Core": "1.0.8", "DistributedLock.FileSystem": "1.0.3", "Elsa.EntityFrameworkCore": "3.4.2", "FastEndpoints": "5.35.0", "FastEndpoints.Security": "5.35.0", "FastEndpoints.Swagger": "5.35.0", "Humanizer.Core": "2.14.1", "IronCompress": "1.6.3", "LinqKit.Core": "1.2.8", "Microsoft.EntityFrameworkCore": "9.0.6", "Microsoft.EntityFrameworkCore.Relational": "9.0.6", "Microsoft.Extensions.Caching.Abstractions": "9.0.6", "Microsoft.Extensions.Caching.Memory": "9.0.6", "Microsoft.Extensions.Configuration": "9.0.6", "Microsoft.Extensions.Configuration.Abstractions": "9.0.6", "Microsoft.Extensions.Configuration.Json": "9.0.6", "Microsoft.Extensions.DependencyInjection": "9.0.6", "Microsoft.Extensions.DependencyInjection.Abstractions": "9.0.6", "Microsoft.Extensions.DependencyModel": "9.0.6", "Microsoft.Extensions.Hosting.Abstractions": "9.0.6", "Microsoft.Extensions.Logging": "9.0.6", "Microsoft.Extensions.Logging.Abstractions": "9.0.6", "Microsoft.Extensions.Options": "9.0.6", "Microsoft.Extensions.Options.ConfigurationExtensions": "9.0.6", "Newtonsoft.Json": "13.0.3", "Npgsql": "9.0.3", "Npgsql.EntityFrameworkCore.PostgreSQL": "9.0.4", "Open.Linq.AsyncExtensions": "1.2.0", "Scrutor": "6.0.1", "ShortGuid": "2.0.1", "System.CommandLine": "2.0.0-beta4.22272.1", "System.Linq.Async": "6.0.1", "System.Linq.Dynamic.Core": "*******", "System.Text.Json": "9.0.6", "ThrottleDebounce": "2.0.0"}}, "Elsa.Expressions": {"type": "Transitive", "resolved": "3.4.2", "contentHash": "eUKJ9sxyNwXdCQFMzqFe0/VNpa4FOv3bjvZAOiIvOkUvu2qzsuA0tKltY0mM5jzsIbOl6sdAd2gOxe1/Pbhspw==", "dependencies": {"Cronos": "0.9.0", "DistributedLock.Core": "1.0.8", "Elsa.Common": "3.4.2", "Elsa.Features": "3.4.2", "IronCompress": "1.6.3", "LinqKit.Core": "1.2.8", "Microsoft.Extensions.Configuration": "9.0.6", "Microsoft.Extensions.Configuration.Abstractions": "9.0.6", "Microsoft.Extensions.Configuration.Json": "9.0.6", "Microsoft.Extensions.DependencyInjection.Abstractions": "9.0.6", "Microsoft.Extensions.Hosting.Abstractions": "9.0.6", "Microsoft.Extensions.Logging.Abstractions": "9.0.6", "Microsoft.Extensions.Options": "9.0.6", "Microsoft.Extensions.Options.ConfigurationExtensions": "9.0.6", "System.Text.Json": "9.0.6"}}, "Elsa.Features": {"type": "Transitive", "resolved": "3.4.2", "contentHash": "64mqfmTST0VVhABkORqIza1NUBheKcRUqKs/iCPXOV1hyFzaESKVS4wvXceOVKhIcXiEGSxgUdyQ/GhJW7hYtQ==", "dependencies": {"Microsoft.Extensions.Configuration.Abstractions": "9.0.6", "Microsoft.Extensions.DependencyInjection.Abstractions": "9.0.6", "Microsoft.Extensions.Hosting.Abstractions": "9.0.6", "Microsoft.Extensions.Logging.Abstractions": "9.0.6", "Microsoft.Extensions.Options": "9.0.6"}}, "Elsa.JavaScript": {"type": "Transitive", "resolved": "3.4.2", "contentHash": "skhdtwaBCI0J7dd9OVo48nqJ/wLqKiomiff7shsZ0Ro47i6muLCqEMIaO2XHAewjkMqW85qZf0RUUzmq++uqFw==", "dependencies": {"Antlr4.Runtime.Standard": "4.13.1", "Cronos": "0.9.0", "DistributedLock.Core": "1.0.8", "Elsa.Api.Common": "3.4.2", "Elsa.Expressions": "3.4.2", "Elsa.Workflows.Management": "3.4.2", "FastEndpoints": "5.35.0", "FastEndpoints.Security": "5.35.0", "FastEndpoints.Swagger": "5.35.0", "Humanizer.Core": "2.14.1", "IronCompress": "1.6.3", "Jint": "4.2.1", "LinqKit.Core": "1.2.8", "Microsoft.Extensions.Caching.Abstractions": "9.0.6", "Microsoft.Extensions.Caching.Memory": "9.0.6", "Microsoft.Extensions.Configuration": "9.0.6", "Microsoft.Extensions.Configuration.Abstractions": "9.0.6", "Microsoft.Extensions.Configuration.Json": "9.0.6", "Microsoft.Extensions.DependencyInjection": "9.0.6", "Microsoft.Extensions.DependencyInjection.Abstractions": "9.0.6", "Microsoft.Extensions.DependencyModel": "9.0.6", "Microsoft.Extensions.Hosting.Abstractions": "9.0.6", "Microsoft.Extensions.Logging": "9.0.6", "Microsoft.Extensions.Logging.Abstractions": "9.0.6", "Microsoft.Extensions.Options": "9.0.6", "Microsoft.Extensions.Options.ConfigurationExtensions": "9.0.6", "Newtonsoft.Json": "13.0.3", "Scrutor": "6.0.1", "ShortGuid": "2.0.1", "System.Linq.Async": "6.0.1", "System.Linq.Dynamic.Core": "*******", "System.Text.Json": "9.0.6"}}, "Elsa.KeyValues": {"type": "Transitive", "resolved": "3.4.2", "contentHash": "B23vKbBuqAVxpwU5YZx49oElDEPR3C5wjs9t8shGZshPtRwAluGMWNTkFDCHd612YR18wHi6vUczwo6QCx2TPA==", "dependencies": {"Cronos": "0.9.0", "DistributedLock.Core": "1.0.8", "Elsa.Common": "3.4.2", "Elsa.Features": "3.4.2", "IronCompress": "1.6.3", "LinqKit.Core": "1.2.8", "Microsoft.Extensions.Configuration": "9.0.6", "Microsoft.Extensions.Configuration.Abstractions": "9.0.6", "Microsoft.Extensions.Configuration.Json": "9.0.6", "Microsoft.Extensions.DependencyInjection.Abstractions": "9.0.6", "Microsoft.Extensions.Hosting.Abstractions": "9.0.6", "Microsoft.Extensions.Logging.Abstractions": "9.0.6", "Microsoft.Extensions.Options": "9.0.6", "Microsoft.Extensions.Options.ConfigurationExtensions": "9.0.6", "System.Text.Json": "9.0.6"}}, "Elsa.Labels": {"type": "Transitive", "resolved": "3.4.2", "contentHash": "1QkGZ0HWlkTZ/P17yCk+hhorKu+yAH4TtSVhDF+EnvBhRu8xK4zo/IF2M29EuHi/GKjsvvAagj/7wFfQ9Z314w==", "dependencies": {"Antlr4.Runtime.Standard": "4.13.1", "Cronos": "0.9.0", "DistributedLock.Core": "1.0.8", "Elsa.Api.Common": "3.4.2", "Elsa.Workflows.Core": "3.4.2", "Elsa.Workflows.Management": "3.4.2", "FastEndpoints": "5.35.0", "FastEndpoints.Security": "5.35.0", "FastEndpoints.Swagger": "5.35.0", "Humanizer.Core": "2.14.1", "IronCompress": "1.6.3", "LinqKit.Core": "1.2.8", "Microsoft.Extensions.Caching.Abstractions": "9.0.6", "Microsoft.Extensions.Caching.Memory": "9.0.6", "Microsoft.Extensions.Configuration": "9.0.6", "Microsoft.Extensions.Configuration.Abstractions": "9.0.6", "Microsoft.Extensions.Configuration.Json": "9.0.6", "Microsoft.Extensions.DependencyInjection": "9.0.6", "Microsoft.Extensions.DependencyInjection.Abstractions": "9.0.6", "Microsoft.Extensions.DependencyModel": "9.0.6", "Microsoft.Extensions.Hosting.Abstractions": "9.0.6", "Microsoft.Extensions.Logging": "9.0.6", "Microsoft.Extensions.Logging.Abstractions": "9.0.6", "Microsoft.Extensions.Options": "9.0.6", "Microsoft.Extensions.Options.ConfigurationExtensions": "9.0.6", "Newtonsoft.Json": "13.0.3", "Open.Linq.AsyncExtensions": "1.2.0", "Scrutor": "6.0.1", "ShortGuid": "2.0.1", "System.Linq.Async": "6.0.1", "System.Linq.Dynamic.Core": "*******", "System.Text.Json": "9.0.6"}}, "Elsa.Liquid": {"type": "Transitive", "resolved": "3.4.2", "contentHash": "DNhNU5sPfZzs7+8eMbhR4/iTHcnbsSsgrS8PdTx2uvnpxNvtjx01JO0DKJ8akvLgpzaGvr1xIqb5vWo3GeY7LA==", "dependencies": {"Antlr4.Runtime.Standard": "4.13.1", "Cronos": "0.9.0", "DistributedLock.Core": "1.0.8", "Elsa.Expressions": "3.4.2", "Elsa.Workflows.Core": "3.4.2", "Elsa.Workflows.Management": "3.4.2", "FastEndpoints": "5.35.0", "FastEndpoints.Security": "5.35.0", "FastEndpoints.Swagger": "5.35.0", "Fluid.Core": "2.19.0", "Humanizer.Core": "2.14.1", "IronCompress": "1.6.3", "LinqKit.Core": "1.2.8", "Microsoft.Extensions.Caching.Abstractions": "9.0.6", "Microsoft.Extensions.Caching.Memory": "9.0.6", "Microsoft.Extensions.Configuration": "9.0.6", "Microsoft.Extensions.Configuration.Abstractions": "9.0.6", "Microsoft.Extensions.Configuration.Json": "9.0.6", "Microsoft.Extensions.DependencyInjection": "9.0.6", "Microsoft.Extensions.DependencyInjection.Abstractions": "9.0.6", "Microsoft.Extensions.DependencyModel": "9.0.6", "Microsoft.Extensions.Hosting.Abstractions": "9.0.6", "Microsoft.Extensions.Logging": "9.0.6", "Microsoft.Extensions.Logging.Abstractions": "9.0.6", "Microsoft.Extensions.Options": "9.0.6", "Microsoft.Extensions.Options.ConfigurationExtensions": "9.0.6", "Newtonsoft.Json": "13.0.3", "Scrutor": "6.0.1", "ShortGuid": "2.0.1", "System.Linq.Async": "6.0.1", "System.Linq.Dynamic.Core": "*******", "System.Text.Json": "9.0.6"}}, "Elsa.Mediator": {"type": "Transitive", "resolved": "3.4.2", "contentHash": "e18oitv1Q7u7mdmp5dXbUkvPMxeYNsRejU/lqkHUUs2kQbE4xss0zlgmKT6Mgz9r2uWerNZCWe1Roh9ysBkmVQ==", "dependencies": {"Microsoft.Extensions.Configuration.Abstractions": "9.0.6", "Microsoft.Extensions.DependencyInjection.Abstractions": "9.0.6", "Microsoft.Extensions.Hosting.Abstractions": "9.0.6", "Microsoft.Extensions.Logging.Abstractions": "9.0.6", "Microsoft.Extensions.Options": "9.0.6", "Microsoft.Extensions.Options.ConfigurationExtensions": "9.0.6"}}, "Elsa.SasTokens": {"type": "Transitive", "resolved": "3.4.2", "contentHash": "/Kkuz3a6ha1ToQsR0gWTW+9/BJzb98/LQyZkYgx5z/Sp+Mzatas4f/wgpbTucJndLxtGThFcirYqHo7Y++t1/g==", "dependencies": {"Elsa.Features": "3.4.2", "Microsoft.Extensions.Configuration.Abstractions": "9.0.6", "Microsoft.Extensions.DependencyInjection.Abstractions": "9.0.6", "Microsoft.Extensions.Hosting.Abstractions": "9.0.6", "Microsoft.Extensions.Logging.Abstractions": "9.0.6", "Microsoft.Extensions.Options": "9.0.6"}}, "Elsa.Studio.ActivityPortProviders": {"type": "Transitive", "resolved": "3.4.0", "contentHash": "eCJLsRqAZwAr1a+LCEDkzCDnGpSZx9dNPB0/LxyBc4/wsCOgXaJDLBVuiZZtjiVH2cpZCTFlkuKzGtc5Qa+7nw==", "dependencies": {"BlazorMonaco": "3.3.0", "Blazored.FluentValidation": "2.2.0", "Blazored.LocalStorage": "4.5.0", "CodeBeam.MudBlazor.Extensions": "8.0.0", "Elsa.Api.Client": "3.4.0", "Elsa.Studio.Core": "3.4.0", "Elsa.Studio.Workflows.Core": "3.4.0", "FluentValidation": "11.11.0", "Microsoft.AspNetCore.Components": "8.0.14", "Microsoft.AspNetCore.Components.Authorization": "8.0.14", "Microsoft.AspNetCore.Components.CustomElements": "8.0.14", "Microsoft.AspNetCore.Components.Web": "8.0.14", "Microsoft.Extensions.DependencyInjection.Abstractions": "9.0.3", "Microsoft.JSInterop": "8.0.14", "MudBlazor": "8.5.0", "Radzen.Blazor": "6.2.9", "Refit": "8.0.0", "Refit.HttpClientFactory": "8.0.0", "ShortGuid": "2.0.1", "ThrottleDebounce": "2.0.0"}}, "Elsa.Studio.Core": {"type": "Transitive", "resolved": "3.4.0", "contentHash": "jZEoGbk6aFyXR5y4GXZ8h4fjA/qChB8a+bRQk3GXF1H1lPMYOvl0u59T5jXfvBhEAmq4VnDoGDlr+s9iUi012Q==", "dependencies": {"Blazored.FluentValidation": "2.2.0", "Blazored.LocalStorage": "4.5.0", "CodeBeam.MudBlazor.Extensions": "8.0.0", "Elsa.Api.Client": "3.4.0", "FluentValidation": "11.11.0", "Microsoft.AspNetCore.Components": "8.0.14", "Microsoft.AspNetCore.Components.Authorization": "8.0.14", "Microsoft.AspNetCore.Components.CustomElements": "8.0.14", "Microsoft.AspNetCore.Components.Web": "8.0.14", "Microsoft.Extensions.DependencyInjection.Abstractions": "9.0.3", "Microsoft.JSInterop": "8.0.14", "MudBlazor": "8.5.0", "Radzen.Blazor": "6.2.9", "Refit": "8.0.0", "Refit.HttpClientFactory": "8.0.0", "ShortGuid": "2.0.1", "ThrottleDebounce": "2.0.0"}}, "Elsa.Studio.Core.BlazorWasm": {"type": "Transitive", "resolved": "3.4.0", "contentHash": "DEgwK0IV68h01f4y7Nzpg0FF7DZAb0vn78AG5+UUKLtXH9qLbmZfdGidSVj+3VyAwczbMGmUGFwoH7uYS469ag==", "dependencies": {"BlazorMonaco": "3.3.0", "Blazored.FluentValidation": "2.2.0", "Blazored.LocalStorage": "4.5.0", "CodeBeam.MudBlazor.Extensions": "8.0.0", "Elsa.Api.Client": "3.4.0", "Elsa.Studio.Core": "3.4.0", "Elsa.Studio.Shared": "3.4.0", "FluentValidation": "11.11.0", "Microsoft.AspNetCore.Components": "8.0.14", "Microsoft.AspNetCore.Components.Authorization": "8.0.14", "Microsoft.AspNetCore.Components.CustomElements": "8.0.14", "Microsoft.AspNetCore.Components.Web": "8.0.14", "Microsoft.Extensions.DependencyInjection.Abstractions": "9.0.3", "Microsoft.JSInterop": "8.0.14", "MudBlazor": "8.5.0", "Radzen.Blazor": "6.2.9", "Refit": "8.0.0", "Refit.HttpClientFactory": "8.0.0", "ShortGuid": "2.0.1", "ThrottleDebounce": "2.0.0"}}, "Elsa.Studio.DomInterop": {"type": "Transitive", "resolved": "3.4.0", "contentHash": "DK13qFss+gLljpNbyzOAoSfO1rQtGhLFVdCPZ3ZIYC0ZHaMeIfjuLhq3DGqreojtPk9EgkleJsrcWa8x1NCQug==", "dependencies": {"Microsoft.AspNetCore.Components": "8.0.14", "Microsoft.Extensions.DependencyInjection.Abstractions": "9.0.3", "Microsoft.JSInterop": "8.0.14"}}, "Elsa.Studio.Login": {"type": "Transitive", "resolved": "3.4.0", "contentHash": "0lKNphTGuquJeklLqtmj/6jdde7W4w2WhHYWvM5DK+rOAOBNwcCPq7/txyddnOFb4ur67/cmsMlok8++oY5NHA==", "dependencies": {"BlazorMonaco": "3.3.0", "Blazored.FluentValidation": "2.2.0", "Blazored.LocalStorage": "4.5.0", "CodeBeam.MudBlazor.Extensions": "8.0.0", "Elsa.Api.Client": "3.4.0", "Elsa.Studio.Core": "3.4.0", "Elsa.Studio.Shared": "3.4.0", "FluentValidation": "11.11.0", "Microsoft.AspNetCore.Components": "8.0.14", "Microsoft.AspNetCore.Components.Authorization": "8.0.14", "Microsoft.AspNetCore.Components.CustomElements": "8.0.14", "Microsoft.AspNetCore.Components.Web": "8.0.14", "Microsoft.AspNetCore.WebUtilities": "8.0.14", "Microsoft.Extensions.DependencyInjection.Abstractions": "9.0.3", "Microsoft.JSInterop": "8.0.14", "MudBlazor": "8.5.0", "Radzen.Blazor": "6.2.9", "Refit": "8.0.0", "Refit.HttpClientFactory": "8.0.0", "ShortGuid": "2.0.1", "ThrottleDebounce": "2.0.0"}}, "Elsa.Studio.Login.BlazorWasm": {"type": "Transitive", "resolved": "3.4.0", "contentHash": "ttRSFE0Xv9U+BDynEqNzaVexXOal2YplJ8LvEfy6giBQNtc6O8DQRTxNdg0fmtmHQnQnlZZET5IDDhfm8u6MOw==", "dependencies": {"BlazorMonaco": "3.3.0", "Blazored.FluentValidation": "2.2.0", "Blazored.LocalStorage": "4.5.0", "CodeBeam.MudBlazor.Extensions": "8.0.0", "Elsa.Api.Client": "3.4.0", "Elsa.Studio.Login": "3.4.0", "FluentValidation": "11.11.0", "Microsoft.AspNetCore.Components": "8.0.14", "Microsoft.AspNetCore.Components.Authorization": "8.0.14", "Microsoft.AspNetCore.Components.CustomElements": "8.0.14", "Microsoft.AspNetCore.Components.Web": "8.0.14", "Microsoft.AspNetCore.WebUtilities": "8.0.14", "Microsoft.Extensions.DependencyInjection.Abstractions": "9.0.3", "Microsoft.JSInterop": "8.0.14", "MudBlazor": "8.5.0", "Radzen.Blazor": "6.2.9", "Refit": "8.0.0", "Refit.HttpClientFactory": "8.0.0", "ShortGuid": "2.0.1", "ThrottleDebounce": "2.0.0"}}, "Elsa.Studio.Shared": {"type": "Transitive", "resolved": "3.4.0", "contentHash": "+Cp3QlYQ6LIo9z2pXP17Qjq1nlE08zck5XPQ4BYvdW2l4atEDPNzlI1jjLgli/EQ06MasdfZtqYincMa62mGcA==", "dependencies": {"BlazorMonaco": "3.3.0", "Blazored.FluentValidation": "2.2.0", "Blazored.LocalStorage": "4.5.0", "CodeBeam.MudBlazor.Extensions": "8.0.0", "Elsa.Api.Client": "3.4.0", "Elsa.Studio.Core": "3.4.0", "Elsa.Studio.DomInterop": "3.4.0", "FluentValidation": "11.11.0", "Microsoft.AspNetCore.Components": "8.0.14", "Microsoft.AspNetCore.Components.Authorization": "8.0.14", "Microsoft.AspNetCore.Components.CustomElements": "8.0.14", "Microsoft.AspNetCore.Components.Web": "8.0.14", "Microsoft.Extensions.DependencyInjection.Abstractions": "9.0.3", "Microsoft.JSInterop": "8.0.14", "MudBlazor": "8.5.0", "Radzen.Blazor": "6.2.9", "Refit": "8.0.0", "Refit.HttpClientFactory": "8.0.0", "ShortGuid": "2.0.1", "ThrottleDebounce": "2.0.0"}}, "Elsa.Studio.Shell": {"type": "Transitive", "resolved": "3.4.0", "contentHash": "2nt/fEefbnwuWxBp03kx0kPrJM8UvhR/IxnuOHNdzRVC9+1nq5f/nSdjBcKQeU1aA+lM+QCdGSGPMyEX6Brwdg==", "dependencies": {"BlazorMonaco": "3.3.0", "Blazored.FluentValidation": "2.2.0", "Blazored.LocalStorage": "4.5.0", "CodeBeam.MudBlazor.Extensions": "8.0.0", "Elsa.Api.Client": "3.4.0", "Elsa.Studio.Core": "3.4.0", "Elsa.Studio.Shared": "3.4.0", "FluentValidation": "11.11.0", "Microsoft.AspNetCore.Components": "8.0.14", "Microsoft.AspNetCore.Components.Authorization": "8.0.14", "Microsoft.AspNetCore.Components.CustomElements": "8.0.14", "Microsoft.AspNetCore.Components.Web": "8.0.14", "Microsoft.Extensions.DependencyInjection.Abstractions": "9.0.3", "Microsoft.JSInterop": "8.0.14", "MudBlazor": "8.5.0", "Radzen.Blazor": "6.2.9", "Refit": "8.0.0", "Refit.HttpClientFactory": "8.0.0", "ShortGuid": "2.0.1", "ThrottleDebounce": "2.0.0"}}, "Elsa.Studio.UIHints": {"type": "Transitive", "resolved": "3.4.0", "contentHash": "2f3nWC+7BHewN6ZJj8bK5XPGYoaH1oA9RzPLmasZ37maldv6GsJFEcgd6nDFRkEfNAnrQHZGZ86jyYzT3OKJmA==", "dependencies": {"BlazorMonaco": "3.3.0", "Blazored.FluentValidation": "2.2.0", "Blazored.LocalStorage": "4.5.0", "CodeBeam.MudBlazor.Extensions": "8.0.0", "Elsa.Api.Client": "3.4.0", "Elsa.Studio.Shared": "3.4.0", "Elsa.Studio.Workflows.Core": "3.4.0", "FluentValidation": "11.11.0", "Microsoft.AspNetCore.Components": "8.0.14", "Microsoft.AspNetCore.Components.Authorization": "8.0.14", "Microsoft.AspNetCore.Components.CustomElements": "8.0.14", "Microsoft.AspNetCore.Components.Web": "8.0.14", "Microsoft.Extensions.DependencyInjection.Abstractions": "9.0.3", "Microsoft.JSInterop": "8.0.14", "MudBlazor": "8.5.0", "Radzen.Blazor": "6.2.9", "Refit": "8.0.0", "Refit.HttpClientFactory": "8.0.0", "ShortGuid": "2.0.1", "ThrottleDebounce": "2.0.0"}}, "Elsa.Studio.Workflows": {"type": "Transitive", "resolved": "3.4.0", "contentHash": "WnZCz5auDtVuWwb/vcgvfAG2nX4sIR8bCvL2g/840lOV0sExoyCSDMFYGtGqrxyYdB05NunOuYI3gqbmDrxrmA==", "dependencies": {"BlazorMonaco": "3.3.0", "Blazored.FluentValidation": "2.2.0", "Blazored.LocalStorage": "4.5.0", "CodeBeam.MudBlazor.Extensions": "8.0.0", "Elsa.Api.Client": "3.4.0", "Elsa.Studio.ActivityPortProviders": "3.4.0", "Elsa.Studio.DomInterop": "3.4.0", "Elsa.Studio.Shared": "3.4.0", "Elsa.Studio.UIHints": "3.4.0", "Elsa.Studio.Workflows.Core": "3.4.0", "Elsa.Studio.Workflows.Designer": "3.4.0", "FluentValidation": "11.11.0", "Microsoft.AspNetCore.Components": "8.0.14", "Microsoft.AspNetCore.Components.Authorization": "8.0.14", "Microsoft.AspNetCore.Components.CustomElements": "8.0.14", "Microsoft.AspNetCore.Components.Web": "8.0.14", "Microsoft.AspNetCore.SignalR.Client": "8.0.14", "Microsoft.Extensions.DependencyInjection.Abstractions": "9.0.3", "Microsoft.JSInterop": "8.0.14", "MudBlazor": "8.5.0", "Radzen.Blazor": "6.2.9", "Refit": "8.0.0", "Refit.HttpClientFactory": "8.0.0", "ShortGuid": "2.0.1", "ThrottleDebounce": "2.0.0"}}, "Elsa.Studio.Workflows.Core": {"type": "Transitive", "resolved": "3.4.0", "contentHash": "tjKhn3mr9L7cd+RmPYWlnEwh2jqLRA2O9l4VhNc9X4tdXo0s41sWi9/Ux3OSSU5/HlMxSGl4mNzQNoG8NGhfTg==", "dependencies": {"BlazorMonaco": "3.3.0", "Blazored.FluentValidation": "2.2.0", "Blazored.LocalStorage": "4.5.0", "CodeBeam.MudBlazor.Extensions": "8.0.0", "Elsa.Api.Client": "3.4.0", "Elsa.Studio.Core": "3.4.0", "Elsa.Studio.Shared": "3.4.0", "FluentValidation": "11.11.0", "Microsoft.AspNetCore.Components": "8.0.14", "Microsoft.AspNetCore.Components.Authorization": "8.0.14", "Microsoft.AspNetCore.Components.CustomElements": "8.0.14", "Microsoft.AspNetCore.Components.Web": "8.0.14", "Microsoft.Extensions.DependencyInjection.Abstractions": "9.0.3", "Microsoft.JSInterop": "8.0.14", "MudBlazor": "8.5.0", "Radzen.Blazor": "6.2.9", "Refit": "8.0.0", "Refit.HttpClientFactory": "8.0.0", "ShortGuid": "2.0.1", "ThrottleDebounce": "2.0.0"}}, "Elsa.Studio.Workflows.Designer": {"type": "Transitive", "resolved": "3.4.0", "contentHash": "FG2FpHe2wErMIZVAk+tr92VYG4Ke4iOaOoBIGzUAP07wlcteUTstQeiPpC8lXjaoTF7i6mBaOvYGWQ/v1ju2rQ==", "dependencies": {"BlazorMonaco": "3.3.0", "Blazored.FluentValidation": "2.2.0", "Blazored.LocalStorage": "4.5.0", "CodeBeam.MudBlazor.Extensions": "8.0.0", "Elsa.Api.Client": "3.4.0", "Elsa.Studio.Core": "3.4.0", "Elsa.Studio.Workflows.Core": "3.4.0", "FluentValidation": "11.11.0", "Microsoft.AspNetCore.Components": "8.0.14", "Microsoft.AspNetCore.Components.Authorization": "8.0.14", "Microsoft.AspNetCore.Components.CustomElements": "8.0.14", "Microsoft.AspNetCore.Components.Web": "8.0.14", "Microsoft.Extensions.DependencyInjection.Abstractions": "9.0.3", "Microsoft.JSInterop": "8.0.14", "MudBlazor": "8.5.0", "Radzen.Blazor": "6.2.9", "Refit": "8.0.0", "Refit.HttpClientFactory": "8.0.0", "ShortGuid": "2.0.1", "ThrottleDebounce": "2.0.0"}}, "Elsa.Tenants": {"type": "Transitive", "resolved": "3.4.2", "contentHash": "e2gBupq1mAEcFEcfAUgbEwdM4cjAoHOqpb5ADRbdwy+weyh0cd/WecY/aLEpwD0Xsnvkwz+2+p1ltNNW1wFjvA==", "dependencies": {"Cronos": "0.9.0", "DistributedLock.Core": "1.0.8", "Elsa.Api.Common": "3.4.2", "Elsa.Common": "3.4.2", "Elsa.Features": "3.4.2", "FastEndpoints": "5.35.0", "FastEndpoints.Security": "5.35.0", "FastEndpoints.Swagger": "5.35.0", "Humanizer.Core": "2.14.1", "IronCompress": "1.6.3", "LinqKit.Core": "1.2.8", "Microsoft.Extensions.Configuration": "9.0.6", "Microsoft.Extensions.Configuration.Abstractions": "9.0.6", "Microsoft.Extensions.Configuration.Json": "9.0.6", "Microsoft.Extensions.DependencyInjection": "9.0.6", "Microsoft.Extensions.DependencyInjection.Abstractions": "9.0.6", "Microsoft.Extensions.Hosting.Abstractions": "9.0.6", "Microsoft.Extensions.Logging": "9.0.6", "Microsoft.Extensions.Logging.Abstractions": "9.0.6", "Microsoft.Extensions.Options": "9.0.6", "Microsoft.Extensions.Options.ConfigurationExtensions": "9.0.6", "Newtonsoft.Json": "13.0.3", "ShortGuid": "2.0.1", "System.Linq.Async": "6.0.1", "System.Text.Json": "9.0.6"}}, "Elsa.Workflows.Core": {"type": "Transitive", "resolved": "3.4.2", "contentHash": "dGd6HpMCXQ8S84eWC3vLvrEIXfQTzvxIPZwgHlDXuKxNXgpsOvuust9d7JshvnitdWAajweP4Lekx4wqPd18qw==", "dependencies": {"Cronos": "0.9.0", "DistributedLock.Core": "1.0.8", "Elsa.Common": "3.4.2", "Elsa.Expressions": "3.4.2", "Humanizer.Core": "2.14.1", "IronCompress": "1.6.3", "LinqKit.Core": "1.2.8", "Microsoft.Extensions.Configuration": "9.0.6", "Microsoft.Extensions.Configuration.Abstractions": "9.0.6", "Microsoft.Extensions.Configuration.Json": "9.0.6", "Microsoft.Extensions.DependencyInjection": "9.0.6", "Microsoft.Extensions.DependencyInjection.Abstractions": "9.0.6", "Microsoft.Extensions.Hosting.Abstractions": "9.0.6", "Microsoft.Extensions.Logging": "9.0.6", "Microsoft.Extensions.Logging.Abstractions": "9.0.6", "Microsoft.Extensions.Options": "9.0.6", "Microsoft.Extensions.Options.ConfigurationExtensions": "9.0.6", "Newtonsoft.Json": "13.0.3", "ShortGuid": "2.0.1", "System.Linq.Async": "6.0.1", "System.Text.Json": "9.0.6"}}, "Elsa.Workflows.Management": {"type": "Transitive", "resolved": "3.4.2", "contentHash": "4IVIUDuzrhqVNhj4gmHZSH7iWBY01s3vS8j3LjwvgOSSqJMANpHeqZPbUW9KYC0p7WxRPI2F/BLhOYBcKsjmlg==", "dependencies": {"Antlr4.Runtime.Standard": "4.13.1", "Cronos": "0.9.0", "DistributedLock.Core": "1.0.8", "Elsa.Api.Common": "3.4.2", "Elsa.Caching": "3.4.2", "Elsa.Dsl": "3.4.2", "Elsa.Workflows.Core": "3.4.2", "FastEndpoints": "5.35.0", "FastEndpoints.Security": "5.35.0", "FastEndpoints.Swagger": "5.35.0", "Humanizer.Core": "2.14.1", "IronCompress": "1.6.3", "LinqKit.Core": "1.2.8", "Microsoft.Extensions.Caching.Abstractions": "9.0.6", "Microsoft.Extensions.Caching.Memory": "9.0.6", "Microsoft.Extensions.Configuration": "9.0.6", "Microsoft.Extensions.Configuration.Abstractions": "9.0.6", "Microsoft.Extensions.Configuration.Json": "9.0.6", "Microsoft.Extensions.DependencyInjection": "9.0.6", "Microsoft.Extensions.DependencyInjection.Abstractions": "9.0.6", "Microsoft.Extensions.DependencyModel": "9.0.6", "Microsoft.Extensions.Hosting.Abstractions": "9.0.6", "Microsoft.Extensions.Logging": "9.0.6", "Microsoft.Extensions.Logging.Abstractions": "9.0.6", "Microsoft.Extensions.Options": "9.0.6", "Microsoft.Extensions.Options.ConfigurationExtensions": "9.0.6", "Newtonsoft.Json": "13.0.3", "Scrutor": "6.0.1", "ShortGuid": "2.0.1", "System.Linq.Async": "6.0.1", "System.Linq.Dynamic.Core": "*******", "System.Text.Json": "9.0.6"}}, "Elsa.Workflows.Runtime": {"type": "Transitive", "resolved": "3.4.2", "contentHash": "1RyJ8ZmiIrVjWgfBsG8rdE+l/yvbhu+BnIYkdnknRw8moQNtG8/azWt1tuEYDWw3ZFuVIjZYhbPpwiaH5/JdHw==", "dependencies": {"Antlr4.Runtime.Standard": "4.13.1", "Cronos": "0.9.0", "DistributedLock.Core": "1.0.8", "DistributedLock.FileSystem": "1.0.3", "Elsa.Caching": "3.4.2", "Elsa.KeyValues": "3.4.2", "Elsa.Tenants": "3.4.2", "Elsa.Workflows.Core": "3.4.2", "Elsa.Workflows.Management": "3.4.2", "FastEndpoints": "5.35.0", "FastEndpoints.Security": "5.35.0", "FastEndpoints.Swagger": "5.35.0", "Humanizer.Core": "2.14.1", "IronCompress": "1.6.3", "LinqKit.Core": "1.2.8", "Microsoft.Extensions.Caching.Abstractions": "9.0.6", "Microsoft.Extensions.Caching.Memory": "9.0.6", "Microsoft.Extensions.Configuration": "9.0.6", "Microsoft.Extensions.Configuration.Abstractions": "9.0.6", "Microsoft.Extensions.Configuration.Json": "9.0.6", "Microsoft.Extensions.DependencyInjection": "9.0.6", "Microsoft.Extensions.DependencyInjection.Abstractions": "9.0.6", "Microsoft.Extensions.DependencyModel": "9.0.6", "Microsoft.Extensions.Hosting.Abstractions": "9.0.6", "Microsoft.Extensions.Logging": "9.0.6", "Microsoft.Extensions.Logging.Abstractions": "9.0.6", "Microsoft.Extensions.Options": "9.0.6", "Microsoft.Extensions.Options.ConfigurationExtensions": "9.0.6", "Newtonsoft.Json": "13.0.3", "Open.Linq.AsyncExtensions": "1.2.0", "Scrutor": "6.0.1", "ShortGuid": "2.0.1", "System.Linq.Async": "6.0.1", "System.Linq.Dynamic.Core": "*******", "System.Text.Json": "9.0.6", "ThrottleDebounce": "2.0.0"}}, "FastEndpoints": {"type": "Transitive", "resolved": "5.35.0", "contentHash": "6pMv0i2AsPlwZES65qfVrwpjQDVIBDOFZ2/E7UqWbHNjpB8T5PpsbaFa8cLrcvkLzxijqzORDmDJ+wB6hck2rA==", "dependencies": {"FastEndpoints.Attributes": "5.35.0", "FastEndpoints.Messaging.Core": "5.35.0", "FluentValidation": "11.11.0"}}, "FastEndpoints.Attributes": {"type": "Transitive", "resolved": "5.35.0", "contentHash": "Fxxc1Bh6ItLQ1lJg4bEIg57AoRCibu69GCmCOe35vzAqq4nQVb9bGl5snoxJA3CNkC4XzIJLXzsKj/fJNtiHxQ==", "dependencies": {"Microsoft.Extensions.Primitives": "8.0.0"}}, "FastEndpoints.Messaging.Core": {"type": "Transitive", "resolved": "5.35.0", "contentHash": "gLvzZ0h7+/jWCfp9U9y04E1miy5WwMjnd1stxCh+nMTvxRfo4qtd93qIsTHScQr+r6pCS0LYjdyqGfLxzncHyg=="}, "FastEndpoints.Security": {"type": "Transitive", "resolved": "5.35.0", "contentHash": "xcgZWhRK7+SODPHYxmHJT7/da03Wa6+Dfqk/5+0HFooYzE4j5gCsNeR5R0RcEOZCvqdD6IJGDSSgqEldQJSFMA==", "dependencies": {"FastEndpoints": "5.35.0", "Microsoft.AspNetCore.Authentication.JwtBearer": "8.0.13"}}, "FastEndpoints.Swagger": {"type": "Transitive", "resolved": "5.35.0", "contentHash": "qasL4AwV7y50qTr/lskWN5zqNE2hWn10pVymegNt7W7ByGrciPB7OYa6XqwM+mZkI3NMvEBOpl2KUwOsSB1XKg==", "dependencies": {"FastEndpoints": "5.35.0", "NSwag.AspNetCore": "14.2.0"}}, "FluentStorage": {"type": "Transitive", "resolved": "5.6.0", "contentHash": "4iiV0vsR21rJOtVnIsMfrzQ6QLPUnPHhLwvTUtEUFESsf7+lXdSicUix4CszvNPEl3NdGF8mpD93OapDTohidg==", "dependencies": {"Microsoft.IO.RecyclableMemoryStream": "3.0.1", "System.Text.Json": "8.0.4", "System.Threading.Thread": "4.3.0", "TestableIO.System.IO.Abstractions.Wrappers": "21.0.22"}}, "FluentValidation": {"type": "Transitive", "resolved": "12.0.0", "contentHash": "8NVLxtMUXynRHJIX3Hn1ACovaqZIJASufXIIFkD0EUbcd5PmMsL1xUD5h548gCezJ5BzlITaR9CAMrGe29aWpA=="}, "FluentValidation.DependencyInjectionExtensions": {"type": "Transitive", "resolved": "12.0.0", "contentHash": "B28fBRL1UjhGsBC8fwV6YBZosh+SiU1FxdD7l7p5dGPgRlVI7UnM+Lgzmg+unZtV1Zxzpaw96UY2MYfMaAd8cg==", "dependencies": {"FluentValidation": "12.0.0", "Microsoft.Extensions.Dependencyinjection.Abstractions": "2.1.0"}}, "Fluid.Core": {"type": "Transitive", "resolved": "2.19.0", "contentHash": "NcJzwEePZjJRrLFRglFvdl84NGJtvqoEt/FK+gLnYkKwr6YKEN29XHEQYEh5murUzqN0xcnAseNspxU+uI2WTQ==", "dependencies": {"Microsoft.Extensions.FileProviders.Abstractions": "8.0.0", "Parlot": "1.3.2", "TimeZoneConverter": "6.1.0"}}, "Humanizer.Core": {"type": "Transitive", "resolved": "2.14.1", "contentHash": "lQKvtaTDOXnoVJ20ibTuSIOf2i0uO0MPbDhd1jm238I+U/2ZnRENj0cktKZhtchBMtCUSRQ5v4xBCUbKNmyVMw=="}, "IronCompress": {"type": "Transitive", "resolved": "1.6.3", "contentHash": "ygrMPrh20nVW8irdGZpMj9rXnz8WPDNN4fpq9JF1pGNL53Jg484OQOwaD4HaF6mxxIjhzKigRUFopjY/GdDfgg==", "dependencies": {"Snappier": "1.1.6", "ZstdSharp.Port": "0.8.1"}}, "Jint": {"type": "Transitive", "resolved": "4.2.1", "contentHash": "XFP2yGdYt9kp7HjycwXSLqHxV3JVPXH7qPNVSUfb3ABJ8zisy98DtQkR+PGRATWzZOISvtfszMzOCv0kS4+N+Q==", "dependencies": {"Acornima": "1.1.1"}}, "LinqKit.Core": {"type": "Transitive", "resolved": "1.2.8", "contentHash": "F/ZGutPdat2u+nevwACsp09fT1z+fazu0UlZGVeAPSaGCdf/PuOv4hAyJ+7zCmqpADQLtmw9jEfJVPmgQy/8Cg=="}, "MailKit": {"type": "Transitive", "resolved": "4.11.0", "contentHash": "JVoRxJ+QRqFMRtEM4veStj3pMLBPRulQGV+iZm6Tq1pnr66Dy6dFYOW9Uw02nxAVzdZAN8G+y3BsUPtgZcKXhA==", "dependencies": {"MimeKit": "4.11.0", "System.Formats.Asn1": "8.0.1"}}, "MediatR.Contracts": {"type": "Transitive", "resolved": "2.0.1", "contentHash": "FYv95bNT4UwcNA+G/J1oX5OpRiSUxteXaUt2BJbRSdRNiIUNbggJF69wy6mnk2wYToaanpdXZdCwVylt96MpwQ=="}, "Microsoft.AspNetCore.Authorization": {"type": "Transitive", "resolved": "8.0.17", "contentHash": "3trCSwZ919JFidsglUPnWANfEIACjGrQ1Vo82nu83p8WLhH6XMBOSI/GZIuscfcXJcYKlVPwlFNKhYbJxF2vZg==", "dependencies": {"Microsoft.AspNetCore.Metadata": "8.0.17", "Microsoft.Extensions.Logging.Abstractions": "8.0.3", "Microsoft.Extensions.Options": "8.0.2"}}, "Microsoft.AspNetCore.Components": {"type": "Transitive", "resolved": "8.0.17", "contentHash": "eHCM+/J/Ejpu6X0MWI0eXwqRAA4iaLhH6kiPkaiOPUtbABMSBJSnfSV7hW38PzkSrpdb+sTqmCEqusxJfjeljQ==", "dependencies": {"Microsoft.AspNetCore.Authorization": "8.0.17", "Microsoft.AspNetCore.Components.Analyzers": "8.0.17"}}, "Microsoft.AspNetCore.Components.Analyzers": {"type": "Transitive", "resolved": "8.0.17", "contentHash": "QHAATSz19jRVeDjvlauvhqrWwPnAOZ/VbkvcyisGe+NPTZO3+9UBV3b/Ro/DupTcqf/MThtQwHy7Bc8UGb0uAg=="}, "Microsoft.AspNetCore.Components.Authorization": {"type": "Transitive", "resolved": "8.0.14", "contentHash": "z6+aRIBaRqkKf4bnd/zzQK5EbflaRU1zVr1MyGHwlaux0ffDrMgtptnxC+QWspTclKA25be814AUFC2mpJc1NQ==", "dependencies": {"Microsoft.AspNetCore.Authorization": "8.0.14", "Microsoft.AspNetCore.Components": "8.0.14"}}, "Microsoft.AspNetCore.Components.CustomElements": {"type": "Transitive", "resolved": "8.0.14", "contentHash": "TEhlvfsjP3LmSyj10OgRfqyf5rdOjaluKniXuONz6wkOVH5nTxVEoMv9cFLDLUiLeygWZpzabqKf6PsPlIL93w==", "dependencies": {"Microsoft.AspNetCore.Components.Web": "8.0.14"}}, "Microsoft.AspNetCore.Components.Forms": {"type": "Transitive", "resolved": "8.0.17", "contentHash": "XjY/AJQ+L/c5BHIR8wLp7US/yNb+8kCGFhPIi65z64AV6DVQYCPOArKd1kWSoQrXU0cVugFsFWEJ0dta7bujRg==", "dependencies": {"Microsoft.AspNetCore.Components": "8.0.17"}}, "Microsoft.AspNetCore.Components.Web": {"type": "Transitive", "resolved": "8.0.17", "contentHash": "yLm+c5g+pK1oSQUPazQFSiPmPEY3ZbIDJZc9owN5DQ+UpNvC7fwjo/kIcYjfIhKKIiOI1hjTcIo5ccSp50L8JQ==", "dependencies": {"Microsoft.AspNetCore.Components": "8.0.17", "Microsoft.AspNetCore.Components.Forms": "8.0.17", "Microsoft.Extensions.DependencyInjection": "8.0.1", "Microsoft.Extensions.Primitives": "8.0.0", "Microsoft.JSInterop": "8.0.17", "System.IO.Pipelines": "8.0.0"}}, "Microsoft.AspNetCore.Components.WebAssembly": {"type": "Transitive", "resolved": "8.0.17", "contentHash": "QGk+Sp92NWAVXXxl9sj2OPZ7MddLYl8LFq2juEY+YZWMOjBhqv3/seWHMSSXGH5KzDlfXp6YZSaxn4hhGLmn8w==", "dependencies": {"Microsoft.AspNetCore.Components.Web": "8.0.17", "Microsoft.Extensions.Configuration.Binder": "8.0.2", "Microsoft.Extensions.Configuration.Json": "8.0.1", "Microsoft.Extensions.Logging": "8.0.1", "Microsoft.JSInterop.WebAssembly": "8.0.17"}}, "Microsoft.AspNetCore.Connections.Abstractions": {"type": "Transitive", "resolved": "8.0.14", "contentHash": "Z8V0PIPntcom1eqXg8Q8ocPDCc69JmIMDlUHV4qVGRc+Loxvjk3KigGPwqPvPcYj/zZldMm7jHY4Ev26mVwhzA==", "dependencies": {"Microsoft.Extensions.Features": "8.0.14", "System.IO.Pipelines": "8.0.0"}}, "Microsoft.AspNetCore.Cryptography.Internal": {"type": "Transitive", "resolved": "8.0.0", "contentHash": "buuMMCTxFcVkOkEftb2OafYxrveNGre9KJF4Oi1DkR4rxIj6oLam7Wq3g0Fp9hNVpJteKEPiupsxYnPrD/oUGA=="}, "Microsoft.AspNetCore.Cryptography.KeyDerivation": {"type": "Transitive", "resolved": "8.0.0", "contentHash": "65w93R5wqUUs35R9wjHHDf75GqAbxJsNByKZo5TbQOWSXcUbLWrDUWBQHv78iXIT0PL1pXNqKQz7OHiHMvo0/A==", "dependencies": {"Microsoft.AspNetCore.Cryptography.Internal": "8.0.0"}}, "Microsoft.AspNetCore.Http.Connections.Client": {"type": "Transitive", "resolved": "8.0.14", "contentHash": "QF+gHiC1xEY/7jynpKDOf493OUVyO34OS9SpWLbiQLUSnNooKwD89QO6xMGqCPtgfGVFPBIaH0XKuLGZwn17YA==", "dependencies": {"Microsoft.AspNetCore.Http.Connections.Common": "8.0.14", "Microsoft.Extensions.Logging.Abstractions": "8.0.3", "Microsoft.Extensions.Options": "8.0.2"}}, "Microsoft.AspNetCore.Http.Connections.Common": {"type": "Transitive", "resolved": "8.0.14", "contentHash": "kemIBUdSVAjAmwguT0M/9g4vHQDM1sIw8Gsavp6BAWkzVySzNbkarQkCfhs3IDX9ZL9ERLTeW+V9rdvdnftoyQ==", "dependencies": {"Microsoft.AspNetCore.Connections.Abstractions": "8.0.14"}}, "Microsoft.AspNetCore.Http.Features": {"type": "Transitive", "resolved": "5.0.17", "contentHash": "3jG2xS+dx8DDCGV/F+STdPTg89lX3ao3dF/VEPvJaz3wzBIjuadipTtYNEXDIVuOPZwb6jdmhrX9jkzOIBm5cw==", "dependencies": {"Microsoft.Extensions.Primitives": "5.0.1", "System.IO.Pipelines": "5.0.2"}}, "Microsoft.AspNetCore.Identity.EntityFrameworkCore": {"type": "Transitive", "resolved": "8.0.0", "contentHash": "ua2LSZY/f0BkNUUVPPm83eq4Xnt+FZYutiMimRrzSmv2K2t2Ia/PuP4CfibYNSwnKl6fbZ49Bwn2mQGWnmmvOA==", "dependencies": {"Microsoft.EntityFrameworkCore.Relational": "8.0.0", "Microsoft.Extensions.Identity.Stores": "8.0.0"}}, "Microsoft.AspNetCore.Metadata": {"type": "Transitive", "resolved": "8.0.17", "contentHash": "VN8osKYj9qgEYsUdO9HRqeP6nuQ13R92u+YtetNlj3I3ZzIvXm8xYFykcmqJr4EGBvKPeXCgdMQJ+7GIdqcrQg=="}, "Microsoft.AspNetCore.SignalR.Client": {"type": "Transitive", "resolved": "8.0.14", "contentHash": "R/Udux51i+SJSCx0sSZGj+dene9l1n+o1batE92I7LaEeiBjiPZlUh6uczEDUG0Nk6N33LnW5iXD3dvh6ThSXg==", "dependencies": {"Microsoft.AspNetCore.Http.Connections.Client": "8.0.14", "Microsoft.AspNetCore.SignalR.Client.Core": "8.0.14"}}, "Microsoft.AspNetCore.SignalR.Client.Core": {"type": "Transitive", "resolved": "8.0.14", "contentHash": "N6n8XPMsi5gKv+bOyWdt9/dsTDYysF6810sIk9c+tJGQ9bCvhRvRAKs6U+ys6IGdEbTpymDw9AfrDKns3AKVmg==", "dependencies": {"Microsoft.AspNetCore.SignalR.Common": "8.0.14", "Microsoft.AspNetCore.SignalR.Protocols.Json": "8.0.14", "Microsoft.Extensions.DependencyInjection": "8.0.1", "Microsoft.Extensions.Logging": "8.0.1", "System.Threading.Channels": "8.0.0"}}, "Microsoft.AspNetCore.SignalR.Common": {"type": "Transitive", "resolved": "8.0.14", "contentHash": "8vg1YEmChNBRKZWccbWtIH6hB4B+QY6u2RhJOKUlm4yDw2VvDu4hkcjxEqib1PYfEWEEyCwD0uH41NLI1eajeg==", "dependencies": {"Microsoft.AspNetCore.Connections.Abstractions": "8.0.14", "Microsoft.Extensions.Options": "8.0.2"}}, "Microsoft.AspNetCore.SignalR.Protocols.Json": {"type": "Transitive", "resolved": "8.0.14", "contentHash": "qYnq8VN0GfsrrGlntM2zeCPeXSH4MqpwfQ5wEJGO6z7d0GDyU2bFxi8HNEBDm9H2iLp68hCwnQo90jTCSdMzAA==", "dependencies": {"Microsoft.AspNetCore.SignalR.Common": "8.0.14"}}, "Microsoft.AspNetCore.WebUtilities": {"type": "Transitive", "resolved": "8.0.14", "contentHash": "CnD6y/y/Zp3uWCl/6NuZIsnltEsDPEjtVV3r5egL1/RvABdRduyrHbMFdrhoeVRzZkyhhIgO8erGWUXDtYIQBQ==", "dependencies": {"Microsoft.Net.Http.Headers": "8.0.14", "System.IO.Pipelines": "8.0.0"}}, "Microsoft.Bcl.AsyncInterfaces": {"type": "Transitive", "resolved": "7.0.0", "contentHash": "3aeMZ1N0lJoSyzqiP03hqemtb1BijhsJADdobn/4nsMJ8V1H+CrpuduUe4hlRdx+ikBQju1VGjMD1GJ3Sk05Eg=="}, "Microsoft.Bcl.Cryptography": {"type": "Transitive", "resolved": "8.0.0", "contentHash": "Y3t/c7C5XHJGFDnohjf1/9SYF3ZOfEU1fkNQuKg/dGf9hN18yrQj2owHITGfNS3+lKJdW6J4vY98jYu57jCO8A=="}, "Microsoft.Build.Framework": {"type": "Transitive", "resolved": "17.8.3", "contentHash": "NrQZJW8TlKVPx72yltGb8SVz3P5mNRk9fNiD/ao8jRSk48WqIIdCn99q4IjlVmPcruuQ+yLdjNQLL8Rb4c916g=="}, "Microsoft.Build.Locator": {"type": "Transitive", "resolved": "1.7.8", "contentHash": "sPy10x527Ph16S2u0yGME4S6ohBKJ69WfjeGG/bvELYeZVmJdKjxgnlL8cJJJLGV/cZIRqSfB12UDB8ICakOog=="}, "Microsoft.CodeAnalysis.Analyzers": {"type": "Transitive", "resolved": "3.11.0", "contentHash": "v/EW3UE8/lbEYHoC2Qq7AR/DnmvpgdtAMndfQNmpuIMx/Mto8L5JnuCfdBYtgvalQOtfNCnxFejxuRrryvUTsg=="}, "Microsoft.CodeAnalysis.CSharp": {"type": "Transitive", "resolved": "4.13.0", "contentHash": "BsH7Vijbj9IL7Fj4k/ysZSVyLGFqr75wmdFGwCKWJvSjnA1xwPaQ3hkB2BQdHOt5CpEYA6Q0I6Oo5sDTDHqHsg==", "dependencies": {"Microsoft.CodeAnalysis.Analyzers": "3.11.0", "Microsoft.CodeAnalysis.Common": "[4.13.0]", "System.Collections.Immutable": "8.0.0", "System.Reflection.Metadata": "8.0.0"}}, "Microsoft.CodeAnalysis.CSharp.Scripting": {"type": "Transitive", "resolved": "4.13.0", "contentHash": "wcgb4zpOikD4i4Opia8mgggrW7kBCBfn6NuF9tktjZl/RkYGLWMtGt8LAgDLKZ6xvaDwxUvHoP9YlE0VFKLcZg==", "dependencies": {"Microsoft.CSharp": "4.7.0", "Microsoft.CodeAnalysis.Analyzers": "3.11.0", "Microsoft.CodeAnalysis.CSharp": "[4.13.0]", "Microsoft.CodeAnalysis.Common": "[4.13.0]", "Microsoft.CodeAnalysis.Scripting.Common": "[4.13.0]", "System.Collections.Immutable": "8.0.0", "System.Reflection.Metadata": "8.0.0"}}, "Microsoft.CodeAnalysis.CSharp.Workspaces": {"type": "Transitive", "resolved": "4.8.0", "contentHash": "3amm4tq4Lo8/BGvg9p3BJh3S9nKq2wqCXfS7138i69TUpo/bD+XvD0hNurpEBtcNZhi1FyutiomKJqVF39ugYA==", "dependencies": {"Humanizer.Core": "2.14.1", "Microsoft.CodeAnalysis.CSharp": "[4.8.0]", "Microsoft.CodeAnalysis.Common": "[4.8.0]", "Microsoft.CodeAnalysis.Workspaces.Common": "[4.8.0]"}}, "Microsoft.CodeAnalysis.Scripting.Common": {"type": "Transitive", "resolved": "4.13.0", "contentHash": "E5t8Kkb9tn7U8BUMnTdb01wkY/4bsMdzHj5ObPgTv5k2oYIhTKlfZdfuaeQq5B40gzV4AwgCmyRbyh1nYifOwA==", "dependencies": {"Microsoft.CodeAnalysis.Analyzers": "3.11.0", "Microsoft.CodeAnalysis.Common": "[4.13.0]", "System.Collections.Immutable": "8.0.0", "System.Reflection.Metadata": "8.0.0"}}, "Microsoft.CodeAnalysis.Workspaces.Common": {"type": "Transitive", "resolved": "4.8.0", "contentHash": "LXyV+MJKsKRu3FGJA3OmSk40OUIa/dQCFLOnm5X8MNcujx7hzGu8o+zjXlb/cy5xUdZK2UKYb9YaQ2E8m9QehQ==", "dependencies": {"Humanizer.Core": "2.14.1", "Microsoft.Bcl.AsyncInterfaces": "7.0.0", "Microsoft.CodeAnalysis.Common": "[4.8.0]", "System.Composition": "7.0.0", "System.IO.Pipelines": "7.0.0", "System.Threading.Channels": "7.0.0"}}, "Microsoft.CodeAnalysis.Workspaces.MSBuild": {"type": "Transitive", "resolved": "4.8.0", "contentHash": "IEYreI82QZKklp54yPHxZNG9EKSK6nHEkeuf+0Asie9llgS1gp0V1hw7ODG+QyoB7MuAnNQHmeV1Per/ECpv6A==", "dependencies": {"Microsoft.Build.Framework": "16.10.0", "Microsoft.CodeAnalysis.Common": "[4.8.0]", "Microsoft.CodeAnalysis.Workspaces.Common": "[4.8.0]", "System.Text.Json": "7.0.3"}}, "Microsoft.CSharp": {"type": "Transitive", "resolved": "4.7.0", "contentHash": "pTj+D3uJWyN3My70i2Hqo+OXixq3Os2D1nJ2x92FFo6sk8fYS1m1WLNTs0Dc1uPaViH0YvEEwvzddQ7y4rhXmA=="}, "Microsoft.Data.SqlClient": {"type": "Transitive", "resolved": "6.0.1", "contentHash": "v7HxnYYXGGCJilxeQ4Pdks+popVuGajBpHmau0RU4ACIcbfs5qCNUnCogGpZ+CJ//8Qafhxq7vc5a8L9d6O8Eg==", "dependencies": {"Azure.Identity": "1.11.4", "Microsoft.Bcl.Cryptography": "8.0.0", "Microsoft.Data.SqlClient.SNI.runtime": "6.0.2", "Microsoft.Extensions.Caching.Memory": "8.0.1", "Microsoft.IdentityModel.JsonWebTokens": "7.5.0", "Microsoft.IdentityModel.Protocols.OpenIdConnect": "7.5.0", "Microsoft.SqlServer.Server": "1.0.0", "System.Configuration.ConfigurationManager": "8.0.1", "System.Security.Cryptography.Pkcs": "8.0.1"}}, "Microsoft.Data.SqlClient.SNI.runtime": {"type": "Transitive", "resolved": "6.0.2", "contentHash": "f+pRODTWX7Y67jXO3T5S2dIPZ9qMJNySjlZT/TKmWVNWe19N8jcWmHaqHnnchaq3gxEKv1SWVY5EFzOD06l41w=="}, "Microsoft.EntityFrameworkCore": {"type": "Transitive", "resolved": "9.0.6", "contentHash": "r5hzM6Bhw4X3z28l5vmsaCPjk9VsQP4zaaY01THh1SAYjgTMVadYIvpNkCfmrv/Klks6aIf2A9eY7cpGZab/hg==", "dependencies": {"Microsoft.EntityFrameworkCore.Abstractions": "9.0.6", "Microsoft.EntityFrameworkCore.Analyzers": "9.0.6", "Microsoft.Extensions.Caching.Memory": "9.0.6", "Microsoft.Extensions.Logging": "9.0.6"}}, "Microsoft.EntityFrameworkCore.Abstractions": {"type": "Transitive", "resolved": "9.0.6", "contentHash": "7MkhPK8emb8hfOx/mFVvHuIHxQ+mH2YdlK4sFUXgsGlvR0A44vsmd2wcHavZOTTzaKhN+aFUVy3zmkztKmTo+A=="}, "Microsoft.EntityFrameworkCore.Analyzers": {"type": "Transitive", "resolved": "9.0.6", "contentHash": "VKggHNQC5FCn3/vooaIM/4aEjGmrmWm78IrdRLz9lLV0Rm9bVHEr/jiWApDkU0U9ec2xGAilvQqJ5mMX7QC2cw=="}, "Microsoft.EntityFrameworkCore.Relational": {"type": "Transitive", "resolved": "9.0.6", "contentHash": "Ht6OT17sYnO31Dx+hX72YHrc5kZt53g5napaw0FpyIekXCvb+gUVvufEG55Fa7taFm8ccy0Vzs+JVNR9NL0JlA==", "dependencies": {"Microsoft.EntityFrameworkCore": "9.0.6", "Microsoft.Extensions.Caching.Memory": "9.0.6", "Microsoft.Extensions.Configuration.Abstractions": "9.0.6", "Microsoft.Extensions.Logging": "9.0.6"}}, "Microsoft.EntityFrameworkCore.SqlServer": {"type": "Transitive", "resolved": "9.0.6", "contentHash": "hr8vJSL1KXkXdgvNYY5peSygSZkoKQ+r6umXGMLoggBQ9NMbf0jo8p13Hy0biON2IS03ixOl0g4Mgw0hjgTksw==", "dependencies": {"Microsoft.Data.SqlClient": "5.1.6", "Microsoft.EntityFrameworkCore.Relational": "9.0.6", "Microsoft.Extensions.Caching.Memory": "9.0.6", "Microsoft.Extensions.Configuration.Abstractions": "9.0.6", "Microsoft.Extensions.Logging": "9.0.6", "System.Formats.Asn1": "9.0.6", "System.Text.Json": "9.0.6"}}, "Microsoft.Extensions.AmbientMetadata.Application": {"type": "Transitive", "resolved": "9.6.0", "contentHash": "voKvEpXEsYtEhSiIVrYrZsMP7zEkBjquhqcvhxOCUen1i9TwdSwBmz7tN93IthTPA1nzXzWnz9huCZyegiYM8A==", "dependencies": {"Microsoft.Extensions.Configuration": "8.0.0", "Microsoft.Extensions.Hosting.Abstractions": "8.0.1", "Microsoft.Extensions.Options.ConfigurationExtensions": "8.0.0"}}, "Microsoft.Extensions.ApiDescription.Server": {"type": "Transitive", "resolved": "6.0.5", "contentHash": "Ckb5EDBUNJdFWyajfXzUIMRkhf52fHZOQuuZg/oiu8y7zDCVwD0iHhew6MnThjHmevanpxL3f5ci2TtHQEN6bw=="}, "Microsoft.Extensions.Caching.Abstractions": {"type": "Transitive", "resolved": "9.0.6", "contentHash": "bL/xQsVNrdVkzjP5yjX4ndkQ03H3+Bk3qPpl+AMCEJR2RkfgAYmoQ/xXffPV7is64+QHShnhA12YAaFmNbfM+A==", "dependencies": {"Microsoft.Extensions.Primitives": "9.0.6"}}, "Microsoft.Extensions.Caching.Memory": {"type": "Transitive", "resolved": "9.0.6", "contentHash": "qPW2d798tBPZcRmrlaBJqyChf2+0odDdE+0Lxvrr0ywkSNl1oNMK8AKrOfDwyXyjuLCv0ua7p6nrUExCeXhCcg==", "dependencies": {"Microsoft.Extensions.Caching.Abstractions": "9.0.6", "Microsoft.Extensions.DependencyInjection.Abstractions": "9.0.6", "Microsoft.Extensions.Logging.Abstractions": "9.0.6", "Microsoft.Extensions.Options": "9.0.6", "Microsoft.Extensions.Primitives": "9.0.6"}}, "Microsoft.Extensions.Caching.StackExchangeRedis": {"type": "Transitive", "resolved": "9.0.6", "contentHash": "gqKiiGz5JylPuIy6/jl/j0ufeNvdKTAKcp+Q487cxxtcDciBHSywNh7qrmygCnFCA2ytnJtjdgRZg0AzkCK2oQ==", "dependencies": {"Microsoft.Extensions.Caching.Abstractions": "9.0.6", "Microsoft.Extensions.Logging.Abstractions": "9.0.6", "Microsoft.Extensions.Options": "9.0.6", "StackExchange.Redis": "2.7.27"}}, "Microsoft.Extensions.Compliance.Abstractions": {"type": "Transitive", "resolved": "9.6.0", "contentHash": "EsW9aUhkHYfb75wkx24BuusOQbh2BRTSh052Fki2APn3puH1q9owynut1jWMq0Rm/C4zhyw6LAd+F6PX8HUi4Q==", "dependencies": {"Microsoft.Extensions.DependencyInjection.Abstractions": "8.0.2", "Microsoft.Extensions.ObjectPool": "8.0.17"}}, "Microsoft.Extensions.Configuration": {"type": "Transitive", "resolved": "9.0.6", "contentHash": "VWB5jdkxHsRiuoniTqwOL32R4OWyp5If/bAucLjRJczRVNcwb8iCXKLjn3Inv8fv+jHMVMnvQLg7xhSys+y5PA==", "dependencies": {"Microsoft.Extensions.Configuration.Abstractions": "9.0.6", "Microsoft.Extensions.Primitives": "9.0.6"}}, "Microsoft.Extensions.Configuration.Abstractions": {"type": "Transitive", "resolved": "9.0.6", "contentHash": "3GgMIi2jP8g1fBW93Z9b9Unamc0SIsgyhiCmC91gq4loTixK9vQMuxxUsfJ1kRGwn+/FqLKwOHqmn0oYWn3Fvw==", "dependencies": {"Microsoft.Extensions.Primitives": "9.0.6"}}, "Microsoft.Extensions.Configuration.Binder": {"type": "Transitive", "resolved": "9.0.6", "contentHash": "Opl/7SIrwDy9WjHn/vU2thQ8CUtrIWHLr+89I7/0VYNEJQvpL24zvqYrh83cH38RzNKHji0WGVkCVP6HJChVVw==", "dependencies": {"Microsoft.Extensions.Configuration.Abstractions": "9.0.6"}}, "Microsoft.Extensions.Configuration.FileExtensions": {"type": "Transitive", "resolved": "9.0.6", "contentHash": "pCEueasI5JhJ24KYzMFxtG40zyLnWpcQYawpARh9FNq9XbWozuWgexmdkPa8p8YoVNlpi3ecKfcjfoRMkKAufw==", "dependencies": {"Microsoft.Extensions.Configuration": "9.0.6", "Microsoft.Extensions.Configuration.Abstractions": "9.0.6", "Microsoft.Extensions.FileProviders.Abstractions": "9.0.6", "Microsoft.Extensions.FileProviders.Physical": "9.0.6", "Microsoft.Extensions.Primitives": "9.0.6"}}, "Microsoft.Extensions.Configuration.Json": {"type": "Transitive", "resolved": "9.0.6", "contentHash": "N0dgOYQ9tDzJouL9Tyx2dgMCcHV2pBaY8yVtorbDqYYwiDRS2zd1TbhTA2FMHqXF3SMjBoO+gONZcDoA79gdSA==", "dependencies": {"Microsoft.Extensions.Configuration": "9.0.6", "Microsoft.Extensions.Configuration.Abstractions": "9.0.6", "Microsoft.Extensions.Configuration.FileExtensions": "9.0.6", "Microsoft.Extensions.FileProviders.Abstractions": "9.0.6", "System.Text.Json": "9.0.6"}}, "Microsoft.Extensions.DependencyInjection": {"type": "Transitive", "resolved": "9.0.6", "contentHash": "vS65HMo5RS10DD543fknsyVDxihMcVxVn3/hNaILgBxWYnOLxWIeCIO9X0QFuCvPRNjClvXe9Aj8KaQNx7vFkQ==", "dependencies": {"Microsoft.Extensions.DependencyInjection.Abstractions": "9.0.6"}}, "Microsoft.Extensions.DependencyInjection.Abstractions": {"type": "Transitive", "resolved": "9.0.6", "contentHash": "0Zn6nR/6g+90MxskZyOOMPQvnPnrrGu6bytPwkV+azDcTtCSuQ1+GJUrg8Klmnrjk1i6zMpw2lXijl+tw7Q3kA=="}, "Microsoft.Extensions.DependencyInjection.AutoActivation": {"type": "Transitive", "resolved": "9.6.0", "contentHash": "71KqPTemVxSAYf4iv4lYFrL684MLwcTciLOHfoaWzxHG0U7ASWy/cQG8mNGB5Wy59H7eKTeuiNjvKXTebxlKWA==", "dependencies": {"Microsoft.Extensions.Hosting.Abstractions": "8.0.1"}}, "Microsoft.Extensions.DependencyModel": {"type": "Transitive", "resolved": "9.0.6", "contentHash": "grVU1ixgMHp+kuhIgvEzhE73jXRY6XmxNBPWrotmbjB9AvJvkwHnIzm1JlOsPpyixFgnzreh/bFBMJAjveX+fQ==", "dependencies": {"System.Text.Encodings.Web": "9.0.6", "System.Text.Json": "9.0.6"}}, "Microsoft.Extensions.Diagnostics": {"type": "Transitive", "resolved": "9.0.6", "contentHash": "mIqCzZseDK9SqTRy4LxtjLwjlUu6aH5UdA6j0vgVER14yki9oRqLF+SmBiF6OlwsBSeL6dMQ8dmq02JMeE2puQ==", "dependencies": {"Microsoft.Extensions.Configuration": "9.0.6", "Microsoft.Extensions.Diagnostics.Abstractions": "9.0.6", "Microsoft.Extensions.Options.ConfigurationExtensions": "9.0.6"}}, "Microsoft.Extensions.Diagnostics.Abstractions": {"type": "Transitive", "resolved": "9.0.6", "contentHash": "GIoXX7VDcTEsNM6yvffTBaOwnPQELGI5dzExR7L2O7AUkDsHBYIZawUbuwfq3cYzz8dIAAJotQYJMzH7qy27Ng==", "dependencies": {"Microsoft.Extensions.DependencyInjection.Abstractions": "9.0.6", "Microsoft.Extensions.Options": "9.0.6", "System.Diagnostics.DiagnosticSource": "9.0.6"}}, "Microsoft.Extensions.Diagnostics.ExceptionSummarization": {"type": "Transitive", "resolved": "9.6.0", "contentHash": "cquw9eHjO7sJ+t6hC++Zd+UjelvxfAnmmfwIq7KnGllcxBg24VEsmIq5gODxYhxXN4rWOvmnIwix0ze2p5GbgA==", "dependencies": {"Microsoft.Extensions.DependencyInjection.Abstractions": "8.0.2"}}, "Microsoft.Extensions.Features": {"type": "Transitive", "resolved": "8.0.14", "contentHash": "JQMMXJ3OUQUepRe3VnfrPFnOg+1ZN/nqrf4pZeJ3kiRsrefDYZTzrHYcbbYNcJx8SZ0dwACTOuyvJKz9z9teyw=="}, "Microsoft.Extensions.FileProviders.Abstractions": {"type": "Transitive", "resolved": "9.0.6", "contentHash": "q9FPkSGVA9ipI255p3PBAvWNXas5Tzjyp/DwYSwT+46mIFw9fWZahsF6vHpoxLt5/vtANotH2sAm7HunuFIx9g==", "dependencies": {"Microsoft.Extensions.Primitives": "9.0.6"}}, "Microsoft.Extensions.FileProviders.Embedded": {"type": "Transitive", "resolved": "8.0.0", "contentHash": "TuRh62KcoOvaSDCbtHT8K0WYptZysYQHPRRNfOgqF7ZUtUL4O0WMV8RdxbtDFJDsg3jv9bgHwXbrgwTeI9+5uQ==", "dependencies": {"Microsoft.Extensions.FileProviders.Abstractions": "8.0.0"}}, "Microsoft.Extensions.FileProviders.Physical": {"type": "Transitive", "resolved": "9.0.6", "contentHash": "l+dFA0NRl90vSIiJNy5d7V0kpTEOWHTqbgoWYzlTwF5uiM5sWJ953haaELKE05jkyJdnemVTnqjrlgo4wo7oyg==", "dependencies": {"Microsoft.Extensions.FileProviders.Abstractions": "9.0.6", "Microsoft.Extensions.FileSystemGlobbing": "9.0.6", "Microsoft.Extensions.Primitives": "9.0.6"}}, "Microsoft.Extensions.FileSystemGlobbing": {"type": "Transitive", "resolved": "9.0.6", "contentHash": "1HJCAbwukNEoYbHgHbKHmenU0V/0huw8+i7Qtf5rLUG1E+3kEwRJQxpwD3wbTEagIgPSQisNgJTvmUX9yYVc6g=="}, "Microsoft.Extensions.Hosting.Abstractions": {"type": "Transitive", "resolved": "9.0.6", "contentHash": "G9T95JbcG/wQpeVIzg0IMwxI+uTywDmbxWUWN2P0mdna35rmuTqgTrZ4SU5rcfUT3EJfbI9N4K8UyCAAc6QK8Q==", "dependencies": {"Microsoft.Extensions.Configuration.Abstractions": "9.0.6", "Microsoft.Extensions.DependencyInjection.Abstractions": "9.0.6", "Microsoft.Extensions.Diagnostics.Abstractions": "9.0.6", "Microsoft.Extensions.FileProviders.Abstractions": "9.0.6", "Microsoft.Extensions.Logging.Abstractions": "9.0.6"}}, "Microsoft.Extensions.Http": {"type": "Transitive", "resolved": "9.0.6", "contentHash": "YoCEkjHHeeKsOzaJaGKuwsi1Ijckkm/+bv5RXmsKA0/qW4veY0eh5lVtkOXxkqQbVRuK3sObhxRM0UeuF6yAgA==", "dependencies": {"Microsoft.Extensions.Configuration.Abstractions": "9.0.6", "Microsoft.Extensions.DependencyInjection.Abstractions": "9.0.6", "Microsoft.Extensions.Diagnostics": "9.0.6", "Microsoft.Extensions.Logging": "9.0.6", "Microsoft.Extensions.Logging.Abstractions": "9.0.6", "Microsoft.Extensions.Options": "9.0.6"}}, "Microsoft.Extensions.Http.Diagnostics": {"type": "Transitive", "resolved": "9.6.0", "contentHash": "FEhMpnH7OANl7ux2wuByvRYqqdRQGC7l2RKOd5FDFXySeWhqJnYWEaQPMqgNk1v108N3fIFmIEnGTOBHDpVP+Q==", "dependencies": {"Microsoft.Extensions.DependencyInjection.AutoActivation": "9.6.0", "Microsoft.Extensions.Http": "8.0.1", "Microsoft.Extensions.Options.ConfigurationExtensions": "8.0.0", "Microsoft.Extensions.Telemetry": "9.6.0", "System.IO.Pipelines": "8.0.0"}}, "Microsoft.Extensions.Http.Polly": {"type": "Transitive", "resolved": "9.0.6", "contentHash": "eui0JinN+aOSMt6oeAGhSk+wAw5nJbeHt+JyOltn4+zjA7FhYDDLdsUW+Md3x18p+NxUdTwh7W/4EbB5RJ90Aw==", "dependencies": {"Microsoft.Extensions.Http": "9.0.6", "Polly": "7.2.4", "Polly.Extensions.Http": "3.0.0"}}, "Microsoft.Extensions.Http.Resilience": {"type": "Transitive", "resolved": "9.6.0", "contentHash": "Np2a8u0ttPzqSrfVlVNRavKNzrzrbLAEsd0gR0KX5jIVOp7SVlPQdAHBTBh8/Hd7Amni9STSBWE2hoxq2pu3XA==", "dependencies": {"Microsoft.Extensions.Configuration.Binder": "8.0.2", "Microsoft.Extensions.Http.Diagnostics": "9.6.0", "Microsoft.Extensions.ObjectPool": "8.0.17", "Microsoft.Extensions.Resilience": "9.6.0"}}, "Microsoft.Extensions.Identity.Core": {"type": "Transitive", "resolved": "8.0.0", "contentHash": "hnXHyIQc+uc2uNMcIbr43+oNBAPEhMpW6lE8ux3MOegRz50WBna4AItlZDY7Y+Id1LLBbf73osUqeTw7CQ371w==", "dependencies": {"Microsoft.AspNetCore.Cryptography.KeyDerivation": "8.0.0", "Microsoft.Extensions.Logging": "8.0.0", "Microsoft.Extensions.Options": "8.0.0"}}, "Microsoft.Extensions.Identity.Stores": {"type": "Transitive", "resolved": "8.0.0", "contentHash": "DmDCpSpngZDBm44wVmxCeYs4HGJr/m32jMItp6pfb7KKtqWYw2vybHRg880j18k/eSFyM4v9uONsnEPgDdi9lg==", "dependencies": {"Microsoft.Extensions.Caching.Abstractions": "8.0.0", "Microsoft.Extensions.Identity.Core": "8.0.0", "Microsoft.Extensions.Logging": "8.0.0"}}, "Microsoft.Extensions.Localization": {"type": "Transitive", "resolved": "8.0.12", "contentHash": "Ef7P8kpJzX/khXB8oYxt3vFHXw48uWY+NirCrh8u8gokCHxr/Noc3OxME+Ji1ugqoMvUVSPu73mYctmUMDlOCA==", "dependencies": {"Microsoft.Extensions.DependencyInjection.Abstractions": "8.0.2", "Microsoft.Extensions.Localization.Abstractions": "8.0.12", "Microsoft.Extensions.Logging.Abstractions": "8.0.2", "Microsoft.Extensions.Options": "8.0.2"}}, "Microsoft.Extensions.Localization.Abstractions": {"type": "Transitive", "resolved": "8.0.12", "contentHash": "bgwe0gy9v12hr6gLAXIeEWaTYm295Nfmp7B/DLmS75GBJXvs4JHn7pi8gn3jXMNNkNIkx9iWG+pXKmLAmjdGZg=="}, "Microsoft.Extensions.Logging": {"type": "Transitive", "resolved": "9.0.6", "contentHash": "XBzjitTFaQhF8EbJ645vblZezV1p52ePTxKHoVkRidHF11Xkjxg94qr0Rvp2qyxK2vBJ4OIZ41NB15YUyxTGMQ==", "dependencies": {"Microsoft.Extensions.DependencyInjection": "9.0.6", "Microsoft.Extensions.Logging.Abstractions": "9.0.6", "Microsoft.Extensions.Options": "9.0.6"}}, "Microsoft.Extensions.Logging.Abstractions": {"type": "Transitive", "resolved": "9.0.6", "contentHash": "LFnyBNK7WtFmKdnHu3v0HOYQ8BcjYuy0jdC9pgCJ/rbLKoJEG9/dBzSKMEeeWDbDeoWS0TIxOC8a9CM5ufca3A==", "dependencies": {"Microsoft.Extensions.DependencyInjection.Abstractions": "9.0.6", "System.Diagnostics.DiagnosticSource": "9.0.6"}}, "Microsoft.Extensions.Logging.Configuration": {"type": "Transitive", "resolved": "8.0.1", "contentHash": "QWwTrsgOnJMmn+XUslm8D2H1n3PkP/u/v52FODtyBc/k4W9r3i2vcXXeeX/upnzllJYRRbrzVzT0OclfNJtBJA==", "dependencies": {"Microsoft.Extensions.Configuration": "8.0.0", "Microsoft.Extensions.Configuration.Abstractions": "8.0.0", "Microsoft.Extensions.Configuration.Binder": "8.0.2", "Microsoft.Extensions.DependencyInjection.Abstractions": "8.0.2", "Microsoft.Extensions.Logging": "8.0.1", "Microsoft.Extensions.Logging.Abstractions": "8.0.2", "Microsoft.Extensions.Options": "8.0.2", "Microsoft.Extensions.Options.ConfigurationExtensions": "8.0.0"}}, "Microsoft.Extensions.ObjectPool": {"type": "Transitive", "resolved": "8.0.17", "contentHash": "oJ5DKTHz+tOypwwppLmtNaffUVDg9ouMndOdnERRRv2BeipuoLTW7i9HIbztGDU7fs/th9wFSJWM5ELw1GiGtA=="}, "Microsoft.Extensions.Options": {"type": "Transitive", "resolved": "9.0.6", "contentHash": "wUPhNM1zsI58Dy10xRdF2+pnsisiUuETg5ZBncyAEEUm/CQ9Q1vmivyUWH8RDbAlqyixf2dJNQ2XZb7HsKUEQw==", "dependencies": {"Microsoft.Extensions.DependencyInjection.Abstractions": "9.0.6", "Microsoft.Extensions.Primitives": "9.0.6"}}, "Microsoft.Extensions.Options.ConfigurationExtensions": {"type": "Transitive", "resolved": "9.0.6", "contentHash": "2lnp8nrvfzyp+5zvfeULm/hkZsDsKkl2ziBt5T8EZKoON5q+XRpRLoWcSPo8mP7GNZXpxKMBVjFNIZNbBIcnRw==", "dependencies": {"Microsoft.Extensions.Configuration.Abstractions": "9.0.6", "Microsoft.Extensions.Configuration.Binder": "9.0.6", "Microsoft.Extensions.DependencyInjection.Abstractions": "9.0.6", "Microsoft.Extensions.Options": "9.0.6", "Microsoft.Extensions.Primitives": "9.0.6"}}, "Microsoft.Extensions.Primitives": {"type": "Transitive", "resolved": "9.0.6", "contentHash": "BHniU24QV67qp1pJknqYSofAPYGmijGI8D+ci9yfw33iuFdyOeB9lWTg78ThyYLyQwZw3s0vZ36VMb0MqbUuLw=="}, "Microsoft.Extensions.Resilience": {"type": "Transitive", "resolved": "9.6.0", "contentHash": "JhfQk0u4XYGD21fMUvAxmzzVM3CMN2Xy3yemutEBECoSP5ND/7jEG4daL0NODSPtq6rd9Pk7SumnBfxyV3+zxw==", "dependencies": {"Microsoft.Extensions.Diagnostics": "8.0.1", "Microsoft.Extensions.Diagnostics.ExceptionSummarization": "9.6.0", "Microsoft.Extensions.Options.ConfigurationExtensions": "8.0.0", "Microsoft.Extensions.Telemetry.Abstractions": "9.6.0", "Polly.Extensions": "8.4.2", "Polly.RateLimiting": "8.4.2"}}, "Microsoft.Extensions.Telemetry": {"type": "Transitive", "resolved": "9.6.0", "contentHash": "4k56GlByl+4gxwMHDMJ/MglbmjPPddLgd21RHZlSfx4WWLqiES/GJ/sHVCrKVjdIQHdcR5MLWvplfuqgj4H+VQ==", "dependencies": {"Microsoft.Extensions.AmbientMetadata.Application": "9.6.0", "Microsoft.Extensions.DependencyInjection.AutoActivation": "9.6.0", "Microsoft.Extensions.Logging.Configuration": "8.0.1", "Microsoft.Extensions.ObjectPool": "8.0.17", "Microsoft.Extensions.Telemetry.Abstractions": "9.6.0"}}, "Microsoft.Extensions.Telemetry.Abstractions": {"type": "Transitive", "resolved": "9.6.0", "contentHash": "LKkpXv0KCFC7oPzkqwNMgBfBImd8I57e6W1mtnvw5KCwMZ/1iS5PsWQiSxp17J91crAyKv5KosRF6lNK2j9EBQ==", "dependencies": {"Microsoft.Extensions.Compliance.Abstractions": "9.6.0", "Microsoft.Extensions.Logging.Abstractions": "8.0.3", "Microsoft.Extensions.ObjectPool": "8.0.17", "Microsoft.Extensions.Options": "8.0.2"}}, "Microsoft.Identity.Client": {"type": "Transitive", "resolved": "4.69.1", "contentHash": "CCay3mDf1czztGx92KdXNW/0sUbZC7OKh0B3OLV67YmtF09RKQHbvE3cSr0cKzAhVLv+UK3jS24ViZh0L3lWnQ==", "dependencies": {"Microsoft.IdentityModel.Abstractions": "6.35.0", "System.Diagnostics.DiagnosticSource": "6.0.1"}}, "Microsoft.Identity.Client.Extensions.Msal": {"type": "Transitive", "resolved": "4.67.2", "contentHash": "DKs+Lva6csEUZabw+JkkjtFgVmcXh4pJeQy5KH5XzPOaKNoZhAMYj1qpKd97qYTZKXIFH12bHPk0DA+6krw+Cw==", "dependencies": {"Microsoft.Identity.Client": "4.67.2", "System.Security.Cryptography.ProtectedData": "4.5.0"}}, "Microsoft.IdentityModel.Abstractions": {"type": "Transitive", "resolved": "8.12.0", "contentHash": "V7fHMFpfzvx7twWMV3jrf3OFVmYn3QhUYtvfMRD9yUPs9gxnQSaRMZh5NzCsnW3ZZ80J09EE7yyjM71y9JC6hQ=="}, "Microsoft.IdentityModel.JsonWebTokens": {"type": "Transitive", "resolved": "8.12.0", "contentHash": "gOup2SmdwaXooLR0POKfrkyrw+R+G8nc8Nk80TL0196kFQK7Bg7Qr4x3jvOCm3TR8QLqU8cVpSVqBLtUaosPEg==", "dependencies": {"Microsoft.IdentityModel.Tokens": "8.12.0"}}, "Microsoft.IdentityModel.Logging": {"type": "Transitive", "resolved": "8.12.0", "contentHash": "IakwNWUTy5RlcDcKwtL5TyfDLZmA8FFnSDhfr+wfGGPMON9GkpkUY8NakIJjdy6mv6C1lM/Jkn3IuaeS9MXesA==", "dependencies": {"Microsoft.IdentityModel.Abstractions": "8.12.0"}}, "Microsoft.IdentityModel.Protocols": {"type": "Transitive", "resolved": "7.5.0", "contentHash": "ugyb0Nm+I+UrHGYg28mL8oCV31xZrOEbs8fQkcShUoKvgk22HroD2odCnqEf56CoAFYTwoDExz8deXzrFC+TyA==", "dependencies": {"Microsoft.IdentityModel.Tokens": "7.5.0"}}, "Microsoft.IdentityModel.Protocols.OpenIdConnect": {"type": "Transitive", "resolved": "7.5.0", "contentHash": "/U3I/8uutTqZr2n/zt0q08bluYklq+5VWP7ZuOGpTUR1ln5bSbrexAzdSGzrhxTxNNbHMCU8Mn2bNQvcmehAxg==", "dependencies": {"Microsoft.IdentityModel.Protocols": "7.5.0", "System.IdentityModel.Tokens.Jwt": "7.5.0"}}, "Microsoft.IdentityModel.Tokens": {"type": "Transitive", "resolved": "8.12.0", "contentHash": "WCU3wv5sioX5hhp3oHw8sCdygnfZJtRKJGCg+AckP6nbp0QGKK3VohTrxIF0dpuziLAPg57CPfS9X8UERroKxA==", "dependencies": {"Microsoft.Extensions.Logging.Abstractions": "8.0.0", "Microsoft.IdentityModel.Logging": "8.12.0"}}, "Microsoft.IO.RecyclableMemoryStream": {"type": "Transitive", "resolved": "3.0.1", "contentHash": "s/s20YTVY9r9TPfTrN5g8zPF1YhwxyqO6PxUkrYTGI2B+OGPe9AdajWZrLhFqXIvqIW23fnUE4+ztrUWNU1+9g=="}, "Microsoft.JSInterop": {"type": "Transitive", "resolved": "8.0.17", "contentHash": "vzWgD6toH1Y9L9TxTQ8M1mKff59Kl1z2fOFg8XHAan1k6yBaE5SLePOihAUJaUn0SpWiUszz5Ctl5cEL4x0SHw=="}, "Microsoft.JSInterop.WebAssembly": {"type": "Transitive", "resolved": "8.0.17", "contentHash": "M5Sio9AI3C5CBZ7SeeQ+m/iFoTdhscRkZYQx66yelyXy2m7d9jhWRuRvbvuN3gce3aAWDAuLLHASmz0ttr2I6Q==", "dependencies": {"Microsoft.JSInterop": "8.0.17"}}, "Microsoft.Net.Http.Headers": {"type": "Transitive", "resolved": "8.0.14", "contentHash": "JXEPNgWSjrUAfYkUt04kpENDt+HlXwlSwJOef6c4bAm1X/BrZOrQngdQfm6FRByC37NeXnzNppzFZUbPWy0j1w==", "dependencies": {"Microsoft.Extensions.Primitives": "8.0.0"}}, "Microsoft.NETCore.Platforms": {"type": "Transitive", "resolved": "1.1.0", "contentHash": "kz0PEW2lhqygehI/d6XsPCQzD7ff7gUJaVGPVETX611eadGsA3A877GdSlU0LRVMCTH/+P3o2iDTak+S08V2+A=="}, "Microsoft.NETCore.Targets": {"type": "Transitive", "resolved": "1.1.0", "contentHash": "aOZA3BWfz9RXjpzt0sRJJMjAscAUm3Hoa4UWAfceV9UTYxgwZ1lZt5nO2myFf+/jetYQo4uTP7zS8sJY67BBxg=="}, "Microsoft.OpenApi": {"type": "Transitive", "resolved": "1.6.23", "contentHash": "tZ1I0KXnn98CWuV8cpI247A17jaY+ILS9vvF7yhI0uPPEqF4P1d7BWL5Uwtel10w9NucllHB3nTkfYTAcHAh8g=="}, "Microsoft.SqlServer.Server": {"type": "Transitive", "resolved": "1.0.0", "contentHash": "N4KeF3cpcm1PUHym1RmakkzfkEv3GRMyofVv40uXsQhCQeglr2OHNcUk2WOG51AKpGO8ynGpo9M/kFXSzghwug=="}, "MimeKit": {"type": "Transitive", "resolved": "4.11.0", "contentHash": "6p0RC1qwBGBHxf7hvzuR1GngzigF+Q6HQUTbD2RbmDrnS2m1qO2rgqOhYtn8n8JH7WGZ+7RthS8lfMuMzeg8AA==", "dependencies": {"BouncyCastle.Cryptography": "2.5.1", "System.Security.Cryptography.Pkcs": "8.0.1"}}, "Mono.TextTemplating": {"type": "Transitive", "resolved": "3.0.0", "contentHash": "YqueG52R/Xej4VVbKuRIodjiAhV0HR/XVbLbNrJhCZnzjnSjgMJ/dCdV0akQQxavX6hp/LC6rqLGLcXeQYU7XA==", "dependencies": {"System.CodeDom": "6.0.0"}}, "MudBlazor": {"type": "Transitive", "resolved": "8.5.0", "contentHash": "PoXtdWHHnSonH1ake6CtoYwTM1l1o2rhQ4C47TRsOQZnnSeiWl7GAVmzpgbFsc0aSv+VZTFX7uXwkRUBR1DCgQ==", "dependencies": {"Microsoft.AspNetCore.Components": "8.0.12", "Microsoft.AspNetCore.Components.Web": "8.0.12", "Microsoft.Extensions.Localization": "8.0.12"}}, "Namotion.Reflection": {"type": "Transitive", "resolved": "3.2.0", "contentHash": "YfGjDPzMEs3vRMI4CMkJwOg75SV5JEDV72kMccD2GCo++TfgOYb5ZTpABYq3dUQqFtnAPpqMKM+deLlrUwnN1g=="}, "Newtonsoft.Json": {"type": "Transitive", "resolved": "13.0.3", "contentHash": "HrC5BXdl00IP9zeV+0Z848QWPAoCr9P3bDEZguI+gkLcBKAOxix/tLEAAHC+UvDNPv4a2d18lOReHMOagPa+zQ=="}, "NJsonSchema": {"type": "Transitive", "resolved": "11.1.0", "contentHash": "H7QO+bM/2uzF81mVyy4U8ha4MXS9eOX06rTvBgJKquzIuLUGuiOTc4nknkCFKW7mr+xnWgzY7Spevz5ZEK8fGg==", "dependencies": {"NJsonSchema.Annotations": "11.1.0", "Namotion.Reflection": "3.2.0", "Newtonsoft.Json": "13.0.3"}}, "NJsonSchema.Annotations": {"type": "Transitive", "resolved": "11.1.0", "contentHash": "2gU72pKhMLrQt7TjHv+nrb8CxxgUaBk2SD/CaB5f00SxuWdOT4YVQaGy+jTVx+8IgQit+9WWMvBWU9f6U2HNiQ=="}, "NJsonSchema.NewtonsoftJson": {"type": "Transitive", "resolved": "11.1.0", "contentHash": "7eC9PNAqHt2RU1CWz4xzZsBdTWCOVH1dpytV2UF5vifi8gOOEAftPdTRUw9O0txNTY65AQUvHU0+P7yuCGWo/g==", "dependencies": {"NJsonSchema": "11.1.0", "Newtonsoft.Json": "13.0.3"}}, "NJsonSchema.Yaml": {"type": "Transitive", "resolved": "11.1.0", "contentHash": "SUQMwNageNORSEsP0/CUhHSf1TT5iZ2dNN6CFQppVEI2hqo1VRFVs17wFxlKI8TUOks4kgXP3Kn5p7atgNzGUA==", "dependencies": {"NJsonSchema": "11.1.0", "YamlDotNet": "16.2.0"}}, "NodaTime": {"type": "Transitive", "resolved": "3.2.0", "contentHash": "yoRA3jEJn8NM0/rQm78zuDNPA3DonNSZdsorMUj+dltc1D+/Lc5h9YXGqbEEZozMGr37lAoYkcSM/KjTVqD0ow=="}, "Npgsql": {"type": "Transitive", "resolved": "9.0.3", "contentHash": "tPvY61CxOAWxNsKLEBg+oR646X4Bc8UmyQ/tJszL/7mEmIXQnnBhVJZrZEEUv0Bstu0mEsHZD5At3EO8zQRAYw==", "dependencies": {"Microsoft.Extensions.Logging.Abstractions": "8.0.2"}}, "Npgsql.EntityFrameworkCore.PostgreSQL": {"type": "Transitive", "resolved": "9.0.4", "contentHash": "mw5vcY2IEc7L+IeGrxpp/J5OSnCcjkjAgJYCm/eD52wpZze8zsSifdqV7zXslSMmfJG2iIUGZyo3KuDtEFKwMQ==", "dependencies": {"Microsoft.EntityFrameworkCore": "[9.0.1, 10.0.0)", "Microsoft.EntityFrameworkCore.Relational": "[9.0.1, 10.0.0)", "Npgsql": "9.0.3"}}, "Npgsql.EntityFrameworkCore.PostgreSQL.NodaTime": {"type": "Transitive", "resolved": "9.0.4", "contentHash": "QZ80CL3c9xzC83eVMWYWa1RcFZA6HJtpMAKFURlmz+1p0OyysSe8R6f/4sI9vk/nwqF6Fkw3lDgku/xH6HcJYg==", "dependencies": {"Npgsql.EntityFrameworkCore.PostgreSQL": "9.0.4", "Npgsql.NodaTime": "9.0.3"}}, "Npgsql.NodaTime": {"type": "Transitive", "resolved": "9.0.3", "contentHash": "PMWXCft/iw+5A7eCeMcy6YZXBst6oeisbCkv2JMQVG4SAFa5vQaf6K2voXzUJCqzwOFcCWs+oT42w2uMDFpchw==", "dependencies": {"NodaTime": "3.2.0", "Npgsql": "9.0.3"}}, "NSwag.Annotations": {"type": "Transitive", "resolved": "14.2.0", "contentHash": "156Gc3UluDj75KQOpBuh/72r8Nv3InNDkrfNNL8mqPLqhIkTCAGkkhD3lsW8V3i7Cz23Aqu1D0aJIWrEVzt+Uw=="}, "NSwag.AspNetCore": {"type": "Transitive", "resolved": "14.2.0", "contentHash": "K0qYdx4MmsIPyKlwVQLhAWgb5gE596e1qzIgpO+xOMV88En+lONQ49n1zxGiT7kYa1UoOIZd714fEo9BpRe2wg==", "dependencies": {"Microsoft.Extensions.ApiDescription.Server": "6.0.3", "Microsoft.Extensions.FileProviders.Embedded": "8.0.0", "NSwag.Annotations": "14.2.0", "NSwag.Core": "14.2.0", "NSwag.Core.Yaml": "14.2.0", "NSwag.Generation": "14.2.0", "NSwag.Generation.AspNetCore": "14.2.0"}}, "NSwag.Core": {"type": "Transitive", "resolved": "14.2.0", "contentHash": "1dcOFGimKGRZnSEyu/cLaWX7vPJrHpZzFs3uSNE8x/48SFBK6zzjBsXdhNvSaInvQp2dDJHcAIy90I7fOp+IIw==", "dependencies": {"NJsonSchema": "11.1.0"}}, "NSwag.Core.Yaml": {"type": "Transitive", "resolved": "14.2.0", "contentHash": "ULOD9qNCI25oy5Ke9CfNmJM2nI4fDxWWI6BOZaiTaAJdFsC9N6/Lt7pNQw6cKVl1XXQV5WdtUFeBqP0ZRwDxiA==", "dependencies": {"NJsonSchema.Yaml": "11.1.0", "NSwag.Core": "14.2.0"}}, "NSwag.Generation": {"type": "Transitive", "resolved": "14.2.0", "contentHash": "FNUSJgqR1u+rsVKwSKsPXF4mIZb6Woac/Ucui5m+dfsuCFZe1VgMsTYCeGpmDqpjIwtTNUaIcGuwqMHH9Fstlw==", "dependencies": {"NJsonSchema.NewtonsoftJson": "11.1.0", "NSwag.Core": "14.2.0"}}, "NSwag.Generation.AspNetCore": {"type": "Transitive", "resolved": "14.2.0", "contentHash": "9VO43wD5IK9O79ZESXD44QtNhoWZi2/5oLHV2JbCd0dR6bJ+9SHqcn+LK3VJFvcRZjFry0JSNd6WBDQL+AH72A==", "dependencies": {"Microsoft.Extensions.DependencyInjection.Abstractions": "8.0.0", "Microsoft.Extensions.Options": "8.0.0", "NSwag.Generation": "14.2.0"}}, "Open.Linq.AsyncExtensions": {"type": "Transitive", "resolved": "1.2.0", "contentHash": "bqH1CfR6aEygglNbIwTglBugn9tplvOi2Urn7qiwjW5fO/n3OLSgdIQ09MptMVPa0F/HHG3fcsjNP9Zp2CxUPg=="}, "Parlot": {"type": "Transitive", "resolved": "1.3.2", "contentHash": "UcUHG0i+62SU+Y/axFHDErdo/x+ofyQjMF7sEUanIViUb58vVc32FsTHqg+UNRheGppIzXB51Mpwbz73TAvzvg=="}, "Pipelines.Sockets.Unofficial": {"type": "Transitive", "resolved": "2.2.8", "contentHash": "zG2FApP5zxSx6OcdJQLbZDk2AVlN2BNQD6MorwIfV6gVj0RRxWPEp2LXAxqDGZqeNV1Zp0BNPcNaey/GXmTdvQ==", "dependencies": {"System.IO.Pipelines": "5.0.1"}}, "Polly": {"type": "Transitive", "resolved": "8.6.0", "contentHash": "Lj8xVTtETAEUB0z0/c75vanP2mhKnuPXtyJlDWGgvv7v5i+MQcNK3K8Zvl7VjCOO4Ko4cxUlQr9Hb76DsV/khA==", "dependencies": {"Polly.Core": "8.6.0"}}, "Polly.Core": {"type": "Transitive", "resolved": "8.6.0", "contentHash": "3CY5h0gITrx+Ei5JvzZpuYLj0yEJquLeGLT9to4isjR7CN017ePDe1Fyp5bwEytu473CbWYiYDGEeY4uE8Iujw=="}, "Polly.Extensions": {"type": "Transitive", "resolved": "8.4.2", "contentHash": "GZ9vRVmR0jV2JtZavt+pGUsQ1O1cuRKG7R7VOZI6ZDy9y6RNPvRvXK1tuS4ffUrv8L0FTea59oEuQzgS0R7zSA==", "dependencies": {"Microsoft.Extensions.Logging.Abstractions": "8.0.0", "Microsoft.Extensions.Options": "8.0.0", "Polly.Core": "8.4.2"}}, "Polly.Extensions.Http": {"type": "Transitive", "resolved": "3.0.0", "contentHash": "drrG+hB3pYFY7w1c3BD+lSGYvH2oIclH8GRSehgfyP5kjnFnHKQuuBhuHLv+PWyFuaTDyk/vfRpnxOzd11+J8g==", "dependencies": {"Polly": "7.1.0"}}, "Polly.RateLimiting": {"type": "Transitive", "resolved": "8.4.2", "contentHash": "ehTImQ/eUyO07VYW2WvwSmU9rRH200SKJ/3jku9rOkyWE0A2JxNFmAVms8dSn49QLSjmjFRRSgfNyOgr/2PSmA==", "dependencies": {"Polly.Core": "8.4.2", "System.Threading.RateLimiting": "8.0.0"}}, "QuestPDF": {"type": "Transitive", "resolved": "2024.3.0", "contentHash": "3UsHztlheCEa+EgGaND8jSwQrAduayBwwerzAfb40AJM+epRCz/jome2qadEeFrmAC7bqSsggalsSsAriCKHnA=="}, "Radzen.Blazor": {"type": "Transitive", "resolved": "6.2.9", "contentHash": "54A4YhZyyXnXthhEVBsCsvgJ9kU1pL7ChKCAVD/nQ22x1K2eaB2rlQ7hm5wm0NAkhTOfKgOF37OEW42F3JWtkA==", "dependencies": {"Microsoft.AspNetCore.Components": "8.0.0", "Microsoft.AspNetCore.Components.Web": "8.0.0", "Microsoft.CodeAnalysis.CSharp": "4.8.0"}}, "Refit": {"type": "Transitive", "resolved": "8.0.0", "contentHash": "gXRmIy3Va0mwwr8usnqVRQFoFIJaJGlWTTXBlwxIkHB/xwXnq1Ybs1YNA2BM1O4G46JLLlGjg6YOwrTZusuY3Q=="}, "Refit.HttpClientFactory": {"type": "Transitive", "resolved": "8.0.0", "contentHash": "dwtmrqHkhuwDmG9qE8+rNnv6YFo132G1Ma+GCXupZQsdQcMN4U1uYaxoJVanRIPdieQGkQRcdxjkFhwO1LuRoA==", "dependencies": {"Microsoft.Extensions.Http": "8.0.1", "Refit": "8.0.0"}}, "Scrutor": {"type": "Transitive", "resolved": "6.0.1", "contentHash": "5xKT6ND5GqnFzwSaYozHCJe75GFL8sPy4yw/iRFqeBFGlmqPNFpOg1T9Q0Gl2h76Cklt0ZTg6Ypkri5iUBKXsA==", "dependencies": {"Microsoft.Extensions.DependencyInjection.Abstractions": "8.0.1", "Microsoft.Extensions.DependencyModel": "8.0.2"}}, "Serilog": {"type": "Transitive", "resolved": "4.2.0", "contentHash": "gmoWVOvKgbME8TYR+gwMf7osROiWAURterc6Rt2dQyX7wtjZYpqFiA/pY6ztjGQKKV62GGCyOcmtP1UKMHgSmA=="}, "Serilog.Extensions.Hosting": {"type": "Transitive", "resolved": "9.0.0", "contentHash": "u2TRxuxbjvTAldQn7uaAwePkWxTHIqlgjelekBtilAGL5sYyF3+65NWctN4UrwwGLsDC7c3Vz3HnOlu+PcoxXg==", "dependencies": {"Microsoft.Extensions.DependencyInjection.Abstractions": "9.0.0", "Microsoft.Extensions.Hosting.Abstractions": "9.0.0", "Microsoft.Extensions.Logging.Abstractions": "9.0.0", "Serilog": "4.2.0", "Serilog.Extensions.Logging": "9.0.0"}}, "Serilog.Extensions.Logging": {"type": "Transitive", "resolved": "9.0.0", "contentHash": "NwSSYqPJeKNzl5AuXVHpGbr6PkZJFlNa14CdIebVjK3k/76kYj/mz5kiTRNVSsSaxM8kAIa1kpy/qyT9E4npRQ==", "dependencies": {"Microsoft.Extensions.Logging": "9.0.0", "Serilog": "4.2.0"}}, "Serilog.Formatting.Compact": {"type": "Transitive", "resolved": "3.0.0", "contentHash": "wQsv14w9cqlfB5FX2MZpNsTawckN4a8dryuNGbebB/3Nh1pXnROHZov3swtu3Nj5oNG7Ba+xdu7Et/ulAUPanQ==", "dependencies": {"Serilog": "4.0.0"}}, "Serilog.Settings.Configuration": {"type": "Transitive", "resolved": "9.0.0", "contentHash": "4/Et4Cqwa+F88l5SeFeNZ4c4Z6dEAIKbu3MaQb2Zz9F/g27T5a3wvfMcmCOaAiACjfUb4A6wrlTVfyYUZk3RRQ==", "dependencies": {"Microsoft.Extensions.Configuration.Binder": "9.0.0", "Microsoft.Extensions.DependencyModel": "9.0.0", "Serilog": "4.2.0"}}, "Serilog.Sinks.Console": {"type": "Transitive", "resolved": "6.0.0", "contentHash": "fQGWqVMClCP2yEyTXPIinSr5c+CBGUvBybPxjAGcf7ctDhadFhrQw03Mv8rJ07/wR5PDfFjewf2LimvXCDzpbA==", "dependencies": {"Serilog": "4.0.0"}}, "Serilog.Sinks.Debug": {"type": "Transitive", "resolved": "3.0.0", "contentHash": "4BzXcdrgRX7wde9PmHuYd9U6YqycCC28hhpKonK7hx0wb19eiuRj16fPcPSVp0o/Y1ipJuNLYQ00R3q2Zs8FDA==", "dependencies": {"Serilog": "4.0.0"}}, "Serilog.Sinks.File": {"type": "Transitive", "resolved": "6.0.0", "contentHash": "lxjg89Y8gJMmFxVkbZ+qDgjl+T4yC5F7WSLTvA+5q0R04tfKVLRL/EHpYoJ/MEQd2EeCKDuylBIVnAYMotmh2A==", "dependencies": {"Serilog": "4.0.0"}}, "ShortGuid": {"type": "Transitive", "resolved": "2.0.1", "contentHash": "I/bXxaY4BDC0mUCJvF9ZlUfWsq3nRswv53gyNrxqg5SmOObDkggNl7v21tHHMQaPC3PW6xdMS1IHeGzFi2GuAQ=="}, "Snappier": {"type": "Transitive", "resolved": "1.1.6", "contentHash": "aLJu7Q0mVk0e9QwjJLEh70tXQ0Url8fHITrHXwqF+eq7N20jGMOhkmTXUUjpPim+rCm0I4fARcVBRzJPSipN+w=="}, "StackExchange.Redis": {"type": "Transitive", "resolved": "2.7.27", "contentHash": "Uqc2OQHglqj9/FfGQ6RkKFkZfHySfZlfmbCl+hc+u2I/IqunfelQ7QJi7ZhvAJxUtu80pildVX6NPLdDaUffOw==", "dependencies": {"Microsoft.Extensions.Logging.Abstractions": "6.0.0", "Pipelines.Sockets.Unofficial": "2.2.8"}}, "Swashbuckle.AspNetCore.Swagger": {"type": "Transitive", "resolved": "8.1.4", "contentHash": "w83aYEBJYNa6ZYomziwZWwXhqQPLKhZH0n8MzqqNhF1ElCGBKm71kd7W6pgIr/yu0i6ymQzrZUFSZLdvH1kY5w==", "dependencies": {"Microsoft.OpenApi": "1.6.23"}}, "Swashbuckle.AspNetCore.SwaggerGen": {"type": "Transitive", "resolved": "8.1.4", "contentHash": "aBwO2MF1HHAaWgdBwX8tlSqxycOKTKmCT6pEpb0oSY1pn7mUdmzJvHZA0HxWx9nfmKP0eOGQcLC9ZnN/MuehRQ==", "dependencies": {"Swashbuckle.AspNetCore.Swagger": "8.1.4"}}, "Swashbuckle.AspNetCore.SwaggerUI": {"type": "Transitive", "resolved": "8.1.4", "contentHash": "mTn6OwB43ETrN6IgAZd7ojWGhTwBZ98LT3QwbAn6Gg3wJStQV4znU0mWiHaKFlD/+Qhj1uhAUOa52rmd6xmbzg=="}, "System.ClientModel": {"type": "Transitive", "resolved": "1.1.0", "contentHash": "UocOlCkxLZrG2CKMAAImPcldJTxeesHnHGHwhJ0pNlZEvEXcWKuQvVOER2/NiOkJGRJk978SNdw3j6/7O9H1lg==", "dependencies": {"System.Memory.Data": "1.0.2", "System.Text.Json": "6.0.9"}}, "System.CodeDom": {"type": "Transitive", "resolved": "6.0.0", "contentHash": "CPc6tWO1LAer3IzfZufDBRL+UZQcj5uS207NHALQzP84Vp/z6wF0Aa0YZImOQY8iStY0A2zI/e3ihKNPfUm8XA=="}, "System.Collections.Immutable": {"type": "Transitive", "resolved": "8.0.0", "contentHash": "AurL6Y5BA1WotzlEvVaIDpqzpIPvYnnldxru8oXJU2yFxFUy3+pNXjXd1ymO+RA0rq0+590Q8gaz2l3Sr7fmqg=="}, "System.CommandLine": {"type": "Transitive", "resolved": "2.0.0-beta4.22272.1", "contentHash": "1uqED/q2H0kKoLJ4+hI2iPSBSEdTuhfCYADeJrAqERmiGQ2NNacYKRNEQ+gFbU4glgVyK8rxI+ZOe1onEtr/Pg=="}, "System.Composition": {"type": "Transitive", "resolved": "7.0.0", "contentHash": "tRwgcAkDd85O8Aq6zHDANzQaq380cek9lbMg5Qma46u5BZXq/G+XvIYmu+UI+BIIZ9zssXLYrkTykEqxxvhcmg==", "dependencies": {"System.Composition.AttributedModel": "7.0.0", "System.Composition.Convention": "7.0.0", "System.Composition.Hosting": "7.0.0", "System.Composition.Runtime": "7.0.0", "System.Composition.TypedParts": "7.0.0"}}, "System.Composition.AttributedModel": {"type": "Transitive", "resolved": "7.0.0", "contentHash": "2QzClqjElKxgI1jK1Jztnq44/8DmSuTSGGahXqQ4TdEV0h9s2KikQZIgcEqVzR7OuWDFPGLHIprBJGQEPr8fAQ=="}, "System.Composition.Convention": {"type": "Transitive", "resolved": "7.0.0", "contentHash": "IMhTlpCs4HmlD8B+J8/kWfwX7vrBBOs6xyjSTzBlYSs7W4OET4tlkR/Sg9NG8jkdJH9Mymq0qGdYS1VPqRTBnQ==", "dependencies": {"System.Composition.AttributedModel": "7.0.0"}}, "System.Composition.Hosting": {"type": "Transitive", "resolved": "7.0.0", "contentHash": "eB6gwN9S+54jCTBJ5bpwMOVerKeUfGGTYCzz3QgDr1P55Gg/Wb27ShfPIhLMjmZ3MoAKu8uUSv6fcCdYJTN7Bg==", "dependencies": {"System.Composition.Runtime": "7.0.0"}}, "System.Composition.Runtime": {"type": "Transitive", "resolved": "7.0.0", "contentHash": "aZJ1Zr5Txe925rbo4742XifEyW0MIni1eiUebmcrP3HwLXZ3IbXUj4MFMUH/RmnJOAQiS401leg/2Sz1MkApDw=="}, "System.Composition.TypedParts": {"type": "Transitive", "resolved": "7.0.0", "contentHash": "ZK0KNPfbtxVceTwh+oHNGUOYV2WNOHReX2AXipuvkURC7s/jPwoWfsu3SnDBDgofqbiWr96geofdQ2erm/KTHg==", "dependencies": {"System.Composition.AttributedModel": "7.0.0", "System.Composition.Hosting": "7.0.0", "System.Composition.Runtime": "7.0.0"}}, "System.Configuration.ConfigurationManager": {"type": "Transitive", "resolved": "8.0.1", "contentHash": "gPYFPDyohW2gXNhdQRSjtmeS6FymL2crg4Sral1wtvEJ7DUqFCDWDVbbLobASbzxfic8U1hQEdC7hmg9LHncMw==", "dependencies": {"System.Diagnostics.EventLog": "8.0.1", "System.Security.Cryptography.ProtectedData": "8.0.0"}}, "System.Diagnostics.DiagnosticSource": {"type": "Transitive", "resolved": "9.0.6", "contentHash": "nikkwAKqpwWUvV5J8S9fnOPYg8k75Lf9fAI4bd6pyhyqNma0Py9kt+zcqXbe4TjJ4sTPcdYpPg81shYTrXnUZQ=="}, "System.Diagnostics.EventLog": {"type": "Transitive", "resolved": "8.0.1", "contentHash": "n1ZP7NM2Gkn/MgD8+eOT5MulMj6wfeQMNS2Pizvq5GHCZfjlFMXV2irQlQmJhwA2VABC57M0auudO89Iu2uRLg=="}, "System.Formats.Asn1": {"type": "Transitive", "resolved": "9.0.6", "contentHash": "8LbKs3WVqyDSszFZJA9Uxg9z+C6WbPbFTSPm/HjFEsWx49XWs0ueqaAKPWncvFJ8yl4H4C/RTnUMhCKoXkddkg=="}, "System.IdentityModel.Tokens.Jwt": {"type": "Transitive", "resolved": "8.12.0", "contentHash": "JpkST6AQlxrXXQ05jVNqoPsU9fjIfERJdCWMxIBWzGhuNH4q/TkP5suPdlNFtHhIN+ngWQ8rmaCNY35EhACHwg==", "dependencies": {"Microsoft.IdentityModel.JsonWebTokens": "8.12.0", "Microsoft.IdentityModel.Tokens": "8.12.0"}}, "System.IO.Pipelines": {"type": "Transitive", "resolved": "9.0.6", "contentHash": "0nlr0reXrRmkZNKifKqh2DgGhQgfkT7Qa3gQxIn/JI7/y3WDiTz67M+Sq3vFhUqcG8O5zVrpqHvIHeGPGUBsEw=="}, "System.Linq.Async": {"type": "Transitive", "resolved": "6.0.1", "contentHash": "0YhHcaroWpQ9UCot3Pizah7ryAzQhNvobLMSxeDIGmnXfkQn8u5owvpOH0K6EVB+z9L7u6Cc4W17Br/+jyttEQ==", "dependencies": {"Microsoft.Bcl.AsyncInterfaces": "6.0.0"}}, "System.Linq.Dynamic.Core": {"type": "Transitive", "resolved": "*******", "contentHash": "rtfRierS5ZjG4xBfDKerRPImwDbav7q2hgf88jUZKfIjQb16PIQqzFCpPVWMb+7fS2ECXnPSmmBbssPz7WUg6g=="}, "System.Memory": {"type": "Transitive", "resolved": "4.5.5", "contentHash": "XIWiDvKPXaTveaB7HVganDlOCRoj03l+jrwNvcge/t8vhGYKvqV+dMv6G4SAX2NoNmN0wZfVPTAlFwZcZvVOUw=="}, "System.Memory.Data": {"type": "Transitive", "resolved": "6.0.0", "contentHash": "ntFHArH3I4Lpjf5m4DCXQHJuGwWPNVJPaAvM95Jy/u+2Yzt2ryiyIN04LAogkjP9DeRcEOiviAjQotfmPq/FrQ==", "dependencies": {"System.Text.Json": "6.0.0"}}, "System.Numerics.Vectors": {"type": "Transitive", "resolved": "4.5.0", "contentHash": "QQTlPTl06J/iiDbJCiepZ4H//BVraReU4O4EoRw1U02H5TLUIT7xn3GnDp9AXPSlJUDyFs4uWjWafNX6WrAojQ=="}, "System.Reflection.Metadata": {"type": "Transitive", "resolved": "8.0.0", "contentHash": "ptvgrFh7PvWI8bcVqG5rsA/weWM09EnthFHR5SCnS6IN+P4mj6rE1lBDC4U8HL9/57htKAqy4KQ3bBj84cfYyQ==", "dependencies": {"System.Collections.Immutable": "8.0.0"}}, "System.Runtime": {"type": "Transitive", "resolved": "4.3.0", "contentHash": "JufQi0vPQ0xGnAczR13AUFglDyVYt4Kqnz1AZaiKZ5+GICq0/1MH/mO/eAJHt/mHW1zjKBJd7kV26SrxddAhiw==", "dependencies": {"Microsoft.NETCore.Platforms": "1.1.0", "Microsoft.NETCore.Targets": "1.1.0"}}, "System.Security.Cryptography.Pkcs": {"type": "Transitive", "resolved": "8.0.1", "contentHash": "CoCRHFym33aUSf/NtWSVSZa99dkd0Hm7OCZUxORBjRB16LNhIEOf8THPqzIYlvKM0nNDAPTRBa1FxEECrgaxxA=="}, "System.Security.Cryptography.ProtectedData": {"type": "Transitive", "resolved": "8.0.0", "contentHash": "+TUFINV2q2ifyXauQXRwy4CiBhqvDEDZeVJU7qfxya4aRYOKzVBpN+4acx25VcPB9ywUN6C0n8drWl110PhZEg=="}, "System.Text.Encodings.Web": {"type": "Transitive", "resolved": "9.0.6", "contentHash": "uWRgViw2yJAUyGxrzDLCc6fkzE2dZIoXxs8V6YjCujKsJuP0pnpYSlbm2/7tKd0SjBnMtwfDQhLenk3bXonVOA=="}, "System.Threading.Channels": {"type": "Transitive", "resolved": "8.0.0", "contentHash": "CMaFr7v+57RW7uZfZkPExsPB6ljwzhjACWW1gfU35Y56rk72B/Wu+sTqxVmGSk4SFUlPc3cjeKND0zktziyjBA=="}, "System.Threading.RateLimiting": {"type": "Transitive", "resolved": "8.0.0", "contentHash": "7mu9v0QDv66ar3DpGSZHg9NuNcxDaaAcnMULuZlaTpP9+hwXhrxNGsF5GmLkSHxFdb5bBc1TzeujsRgTrPWi+Q=="}, "System.Threading.Tasks.Extensions": {"type": "Transitive", "resolved": "4.5.4", "contentHash": "zteT+G8xuGu6mS+mzDzYXbzS7rd3K6Fjb9RiZlYlJPam2/hU7JCBZBVEcywNuR+oZ1ncTvc/cq0faRr3P01OVg=="}, "System.Threading.Thread": {"type": "Transitive", "resolved": "4.3.0", "contentHash": "OHmbT+Zz065NKII/ZHcH9XO1dEuLGI1L2k7uYss+9C1jLxTC9kTZZuzUOyXHayRk+dft9CiDf3I/QZ0t8JKyBQ==", "dependencies": {"System.Runtime": "4.3.0"}}, "TestableIO.System.IO.Abstractions": {"type": "Transitive", "resolved": "21.0.22", "contentHash": "yq3I8rUjNoo4JTOT3jbGfTM5VfowaSYiooDO2bfvQe6XGVyZiSIzaKyCmSnuUEyuktw6zzuVE1lJnUeDhJWtyQ=="}, "TestableIO.System.IO.Abstractions.Wrappers": {"type": "Transitive", "resolved": "21.0.22", "contentHash": "ETpw1B6VOsQ5zbfqxqLKjITnMvB9M0K5/bK8qJc6jih+tnlKQmtnnOPSS+OeKCqsZDYeHxPNp4ytQPJS3R+WXA==", "dependencies": {"TestableIO.System.IO.Abstractions": "21.0.22"}}, "ThrottleDebounce": {"type": "Transitive", "resolved": "2.0.0", "contentHash": "/lt2PLUjE1bXCkPDVXXhZDzqaK3SmKwJ2EOq/a6ZbsgAWnRz3TqkqU0VyUncbh8bUIJQHCoPUxbwmjWeAbeIbw=="}, "TimeZoneConverter": {"type": "Transitive", "resolved": "6.1.0", "contentHash": "UGdtyKWJqXXinyvGB9X6NVoIYbTAidoZYmn3aXzxeEYC9+OL8vF36eDt1qjb6RqBkWDl4v7iE84ecI+dFhA80A=="}, "YamlDotNet": {"type": "Transitive", "resolved": "16.2.0", "contentHash": "iKVYog7JNiFOTr4gMHSjCqrGFD2NyABUHgSkRq9XR8A2+fsWepSWTpjHAOnKo1LVAHjy33o6I0u6lo5Ng0NtAw=="}, "ZstdSharp.Port": {"type": "Transitive", "resolved": "0.8.1", "contentHash": "19tNz33kn2EkyViFXuxfVn338UJaRmkwBphVqP2dVJIYQUQgFrgG5h061mxkRRg1Ax6r+6WOj1FxaFZ5qaWqqg=="}, "ZXing.Net": {"type": "Transitive", "resolved": "0.16.9", "contentHash": "7WaVMHklpT3Ye2ragqRIwlFRsb6kOk63BOGADV0fan3ulVfGLUYkDi5yNUsZS/7FVNkWbtHAlDLmu4WnHGfqvQ=="}, "harmoni360.application": {"type": "Project", "dependencies": {"AutoMapper.Extensions.Microsoft.DependencyInjection": "[12.0.1, )", "FluentValidation.DependencyInjectionExtensions": "[12.0.0, )", "Harmoni360.Domain": "[1.0.0, )", "MediatR": "[12.5.0, )", "Microsoft.AspNetCore.Http.Features": "[5.0.17, )", "Microsoft.EntityFrameworkCore": "[9.0.1, )", "Microsoft.Extensions.Caching.Memory": "[9.0.5, )", "Microsoft.Extensions.Logging.Abstractions": "[9.0.5, )", "QuestPDF": "[2024.3.0, )"}}, "harmoni360.domain": {"type": "Project", "dependencies": {"MediatR.Contracts": "[2.0.1, )"}}, "harmoni360.elsastudio": {"type": "Project", "dependencies": {"Elsa.Api.Client": "[3.4.2, )", "Elsa.Studio": "[3.4.0, )", "Elsa.Studio.Core.BlazorWasm": "[3.4.0, )", "Elsa.Studio.Dashboard": "[3.4.0, )", "Elsa.Studio.Login.BlazorWasm": "[3.4.0, )", "Elsa.Studio.Shell": "[3.4.0, )", "Elsa.Studio.Workflows": "[3.4.0, )", "Microsoft.AspNetCore.Components.WebAssembly": "[8.0.17, )"}}, "harmoni360.infrastructure": {"type": "Project", "dependencies": {"Elsa": "[3.4.2, )", "Elsa.CSharp": "[3.4.2, )", "Elsa.EntityFrameworkCore": "[3.4.2, )", "Elsa.EntityFrameworkCore.PostgreSql": "[3.4.2, )", "Elsa.Http": "[3.4.2, )", "Elsa.Identity": "[3.4.2, )", "Elsa.JavaScript": "[3.4.2, )", "Elsa.Liquid": "[3.4.2, )", "Elsa.Scheduling": "[3.4.2, )", "Elsa.Workflows.Api": "[3.4.2, )", "Elsa.Workflows.Management": "[3.4.2, )", "Elsa.Workflows.Runtime": "[3.4.2, )", "Harmoni360.Application": "[1.0.0, )", "Microsoft.AspNetCore.Identity.EntityFrameworkCore": "[8.0.0, )", "Microsoft.CodeAnalysis.Common": "[4.13.0, )", "Microsoft.EntityFrameworkCore": "[9.0.6, )", "Microsoft.Extensions.Caching.StackExchangeRedis": "[9.0.6, )", "Microsoft.Extensions.DependencyInjection.Abstractions": "[9.0.6, )", "Microsoft.Extensions.Hosting.Abstractions": "[9.0.6, )", "Npgsql.EntityFrameworkCore.PostgreSQL": "[9.0.4, )", "Npgsql.EntityFrameworkCore.PostgreSQL.NodaTime": "[9.0.4, )", "System.IdentityModel.Tokens.Jwt": "[8.12.0, )", "System.Text.Encodings.Web": "[9.0.6, )"}}}}}