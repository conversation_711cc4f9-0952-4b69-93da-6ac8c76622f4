using Elsa.Workflows;
using Harmoni360.Application.Common.Interfaces;
using Harmoni360.Domain.Interfaces;
using Microsoft.Extensions.Logging;

namespace Harmoni360.Application.Features.WorkflowManagement.Common;

/// <summary>
/// Base class for incident workflow activities providing common logging and repository access
/// </summary>
public abstract class IncidentActivityBase : CodeActivity
{
    protected readonly ILogger Logger;
    protected readonly IIncidentRepository IncidentRepository;
    protected readonly ICurrentUserService CurrentUserService;

    protected IncidentActivityBase(
        ILogger logger,
        IIncidentRepository incidentRepository,
        ICurrentUserService currentUserService)
    {
        Logger = logger;
        IncidentRepository = incidentRepository;
        CurrentUserService = currentUserService;
    }

    /// <summary>
    /// Logs activity execution information
    /// </summary>
    protected void LogActivity(string activityName, string message, params object[] args)
    {
        Logger?.LogInformation($"[{activityName}] {message}", args);
    }

    /// <summary>
    /// Logs activity errors
    /// </summary>
    protected void LogActivityError(string activityName, Exception exception, string message, params object[] args)
    {
        Logger?.LogError(exception, $"[{activityName}] {message}", args);
    }
}

/// <summary>
/// Base class for incident workflow activities with typed result output
/// </summary>
public abstract class IncidentActivityBase<T> : CodeActivity<T>
{
    protected readonly ILogger Logger;
    protected readonly IIncidentRepository IncidentRepository;
    protected readonly ICurrentUserService CurrentUserService;

    protected IncidentActivityBase(
        ILogger logger,
        IIncidentRepository incidentRepository,
        ICurrentUserService currentUserService)
    {
        Logger = logger;
        IncidentRepository = incidentRepository;
        CurrentUserService = currentUserService;
    }

    /// <summary>
    /// Logs activity execution information
    /// </summary>
    protected void LogActivity(string activityName, string message, params object[] args)
    {
        Logger?.LogInformation($"[{activityName}] {message}", args);
    }

    /// <summary>
    /// Logs activity errors
    /// </summary>
    protected void LogActivityError(string activityName, Exception exception, string message, params object[] args)
    {
        Logger?.LogError(exception, $"[{activityName}] {message}", args);
    }
}