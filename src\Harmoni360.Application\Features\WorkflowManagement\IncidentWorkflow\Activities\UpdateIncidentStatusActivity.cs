using Elsa.Workflows;
using Elsa.Workflows.Attributes;
using Harmoni360.Application.Common.Interfaces;
using Harmoni360.Application.Features.WorkflowManagement.Common;
using Harmoni360.Application.Features.WorkflowManagement.IncidentWorkflow.Models;
using Harmoni360.Domain.Entities;
using Harmoni360.Domain.Interfaces;
using Microsoft.Extensions.Logging;
using System.ComponentModel;
using Elsa.Extensions;
using Elsa.Workflows.Models;

namespace Harmoni360.Application.Features.WorkflowManagement.IncidentWorkflow.Activities;

/// <summary>
/// Activity that updates incident status during workflow progression
/// </summary>
[Activity("Incident Management", "Update Incident Status", "Updates the incident status in the database")]
public class UpdateIncidentStatusActivity : IncidentActivityBase
{
    public UpdateIncidentStatusActivity(
        ILogger<UpdateIncidentStatusActivity> logger,
        IIncidentRepository incidentRepository,
        ICurrentUserService currentUserService)
        : base(logger, incidentRepository, currentUserService)
    {
    }
    
    /// <summary>
    /// The workflow context containing incident information
    /// </summary>
    [Input(
        Description = "The incident workflow context",
        DisplayName = "Incident Context"
    )]
    public Input<IncidentWorkflowContext> Context { get; set; } = default!;
    
    /// <summary>
    /// The new status to set
    /// </summary>
    [Input(
        Description = "The new status to set for the incident",
        DisplayName = "New Status"
    )]
    public Input<string> NewStatus { get; set; } = default!;
    
    /// <summary>
    /// Optional status notes/comments
    /// </summary>
    [Input(
        Description = "Optional notes about the status change",
        DisplayName = "Status Notes"
    )]
    public Input<string?> StatusNotes { get; set; } = default!;
    
    protected override async ValueTask ExecuteAsync(ActivityExecutionContext context)
    {
        const string activityName = nameof(UpdateIncidentStatusActivity);
        LogActivity(activityName, "Starting incident status update");
        
        try
        {
            var workflowContext = Context.Get(context);
            var newStatus = NewStatus.Get(context);
            var notes = StatusNotes.Get(context);
            
            if (string.IsNullOrEmpty(newStatus))
            {
                LogActivity(activityName, "Warning: New status is empty, skipping update");
                return;
            }
            
            // Parse incident ID
            if (!int.TryParse(workflowContext.IncidentId, out var incidentId))
            {
                LogActivityError(activityName, "Invalid incident ID: {IncidentId}", workflowContext.IncidentId);
                return;
            }
            
            // Get incident from database
            var incident = await IncidentRepository.GetByIdAsync(incidentId);
            if (incident == null)
            {
                LogActivityError(activityName, "Incident not found: {IncidentId}", incidentId);
                return;
            }
            
            // Map string status to enum
            var incidentStatus = MapStringToIncidentStatus(newStatus);
            
            // Update incident status
            incident.UpdateStatus(incidentStatus, notes ?? $"Status updated by workflow at {DateTime.UtcNow:yyyy-MM-dd HH:mm:ss}");
            
            // Save changes
            await IncidentRepository.UpdateAsync(incident);
            
            // Update workflow context
            workflowContext.Status = newStatus;
            workflowContext.LastUpdated = DateTime.UtcNow;
            
            LogActivity(activityName, "Successfully updated incident {IncidentId} status from {OldStatus} to {NewStatus}", 
                incidentId, incident.Status.ToString(), newStatus);
        }
        catch (Exception ex)
        {
            LogActivityError(activityName, ex, "Failed to update incident status");
        }
    }
    
    private static IncidentStatus MapStringToIncidentStatus(string status)
    {
        return status.ToLowerInvariant() switch
        {
            "reported" => IncidentStatus.Reported,
            "processing" => IncidentStatus.UnderInvestigation,
            "under investigation" => IncidentStatus.UnderInvestigation,
            "investigating" => IncidentStatus.UnderInvestigation,
            "analyzing" => IncidentStatus.UnderInvestigation,
            "planning controls" => IncidentStatus.UnderInvestigation,
            "actions assigned" => IncidentStatus.UnderInvestigation,
            "in progress" => IncidentStatus.UnderInvestigation,
            "verifying" => IncidentStatus.UnderReview,
            "pending review" => IncidentStatus.PendingReview,
            "approved" => IncidentStatus.Approved,
            "closed" => IncidentStatus.Closed,
            "resolved" => IncidentStatus.Resolved,
            _ => IncidentStatus.Reported
        };
    }
}