{"version": 3, "file": "files.entry.js", "mappings": ";;;;;;;;;;;;AAAO,MAAM,sBAAsB,GAAG,KAAK,EAAE,QAAQ,EAAE,sBAAsB,EAAE,EAAE;IAC7E,MAAM,WAAW,GAAG,MAAM,sBAAsB,CAAC,WAAW,EAAE,CAAC;IAC/D,MAAM,IAAI,GAAG,IAAI,IAAI,CAAC,CAAC,WAAW,CAAC,CAAC,CAAC;IACrC,MAAM,GAAG,GAAG,GAAG,CAAC,eAAe,CAAC,IAAI,CAAC,CAAC;IACtC,MAAM,aAAa,GAAG,QAAQ,CAAC,aAAa,CAAC,GAAG,CAAC,CAAC;IAClD,aAAa,CAAC,IAAI,GAAG,GAAG,CAAC;IACzB,aAAa,CAAC,QAAQ,GAAG,QAAQ,IAAI,EAAE,CAAC;IACxC,aAAa,CAAC,KAAK,EAAE,CAAC;IACtB,aAAa,CAAC,MAAM,EAAE,CAAC;IACvB,GAAG,CAAC,eAAe,CAAC,GAAG,CAAC,CAAC;AAC7B,CAAC;;;;;;;;;;;;;;;;ACV2C;;;;;;;SCA5C;SACA;;SAEA;SACA;SACA;SACA;SACA;SACA;SACA;SACA;SACA;SACA;SACA;SACA;SACA;;SAEA;SACA;;SAEA;SACA;SACA;;;;;UCtBA;UACA;UACA;UACA;UACA,yCAAyC,wCAAwC;UACjF;UACA;UACA;;;;;UCPA;;;;;UCAA;UACA;UACA;UACA,uDAAuD,iBAAiB;UACxE;UACA,gDAAgD,aAAa;UAC7D;;;;;;;;;;;;;;;ACNyB", "sources": ["webpack://elsa-studio-dom-interop/./src/files/download-file-from-stream.ts", "webpack://elsa-studio-dom-interop/./src/files/index.ts", "webpack://elsa-studio-dom-interop/webpack/bootstrap", "webpack://elsa-studio-dom-interop/webpack/runtime/define property getters", "webpack://elsa-studio-dom-interop/webpack/runtime/hasOwnProperty shorthand", "webpack://elsa-studio-dom-interop/webpack/runtime/make namespace object", "webpack://elsa-studio-dom-interop/./src/files.ts"], "sourcesContent": ["export const downloadFileFromStream = async (fileName, contentStreamReference) => {\n    const arrayBuffer = await contentStreamReference.arrayBuffer();\n    const blob = new Blob([arrayBuffer]);\n    const url = URL.createObjectURL(blob);\n    const anchorElement = document.createElement('a');\n    anchorElement.href = url;\n    anchorElement.download = fileName ?? '';\n    anchorElement.click();\n    anchorElement.remove();\n    URL.revokeObjectURL(url);\n}", "export * from './download-file-from-stream';", "// The module cache\nvar __webpack_module_cache__ = {};\n\n// The require function\nfunction __webpack_require__(moduleId) {\n\t// Check if module is in cache\n\tvar cachedModule = __webpack_module_cache__[moduleId];\n\tif (cachedModule !== undefined) {\n\t\treturn cachedModule.exports;\n\t}\n\t// Create a new module (and put it into the cache)\n\tvar module = __webpack_module_cache__[moduleId] = {\n\t\t// no module.id needed\n\t\t// no module.loaded needed\n\t\texports: {}\n\t};\n\n\t// Execute the module function\n\t__webpack_modules__[moduleId](module, module.exports, __webpack_require__);\n\n\t// Return the exports of the module\n\treturn module.exports;\n}\n\n", "// define getter functions for harmony exports\n__webpack_require__.d = (exports, definition) => {\n\tfor(var key in definition) {\n\t\tif(__webpack_require__.o(definition, key) && !__webpack_require__.o(exports, key)) {\n\t\t\tObject.defineProperty(exports, key, { enumerable: true, get: definition[key] });\n\t\t}\n\t}\n};", "__webpack_require__.o = (obj, prop) => (Object.prototype.hasOwnProperty.call(obj, prop))", "// define __esModule on exports\n__webpack_require__.r = (exports) => {\n\tif(typeof Symbol !== 'undefined' && Symbol.toStringTag) {\n\t\tObject.defineProperty(exports, Symbol.toStringTag, { value: 'Module' });\n\t}\n\tObject.defineProperty(exports, '__esModule', { value: true });\n};", "export * from \"./files/\";"], "names": [], "sourceRoot": ""}