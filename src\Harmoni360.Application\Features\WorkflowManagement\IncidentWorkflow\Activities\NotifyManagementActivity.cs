using Elsa.Workflows;
using Elsa.Workflows.Attributes;
using Harmoni360.Application.Common.Interfaces;
using Harmoni360.Application.Features.WorkflowManagement.Common;
using Harmoni360.Application.Features.WorkflowManagement.IncidentWorkflow.Models;
using Harmoni360.Domain.Interfaces;
using Microsoft.Extensions.Logging;
using System.ComponentModel;
using Elsa.Extensions;
using Elsa.Workflows.Models;

namespace Harmoni360.Application.Features.WorkflowManagement.IncidentWorkflow.Activities;

/// <summary>
/// Activity that notifies management about the incident
/// </summary>
[Activity("Incident Management", "Notify Management", "Sends notification to management about the incident")]
public class NotifyManagementActivity : IncidentActivityBase
{
    private readonly INotificationService _notificationService;
    
    public NotifyManagementActivity(
        ILogger<NotifyManagementActivity> logger,
        IIncidentRepository incidentRepository,
        ICurrentUserService currentUserService,
        INotificationService notificationService)
        : base(logger, incidentRepository, currentUserService)
    {
        _notificationService = notificationService;
    }
    
    /// <summary>
    /// The workflow context containing incident information
    /// </summary>
    [Input(
        Description = "The incident workflow context",
        DisplayName = "Incident Context"
    )]
    public Input<IncidentWorkflowContext> Context { get; set; } = default!;
    
    protected override async ValueTask ExecuteAsync(ActivityExecutionContext context)
    {
        const string activityName = nameof(NotifyManagementActivity);
        LogActivity(activityName, "Starting management notification");
        
        try
        {
            var workflowContext = Context.Get(context);
            
            // Parse incident ID
            if (!int.TryParse(workflowContext.IncidentId, out var incidentId))
            {
                LogActivityError(activityName, "Invalid incident ID: {IncidentId}", workflowContext.IncidentId);
                return;
            }
            
            // Get incident from database
            var incident = await IncidentRepository.GetByIdAsync(incidentId);
            if (incident == null)
            {
                LogActivityError(activityName, "Incident not found: {IncidentId}", incidentId);
                return;
            }
            
            // Create notification for management
            var notificationTitle = $"New HSE Incident Reported: {incident.Title}";
            var notificationMessage = $@"
A new HSE incident has been reported and requires management attention.

Incident Details:
- ID: {incident.Id}
- Type: {incident.Type}
- Severity: {incident.Severity}
- Location: {incident.Location}
- Reported By: {incident.ReporterName}
- Reported At: {incident.IncidentDate:yyyy-MM-dd HH:mm}

Description: {incident.Description}

Please review and assign appropriate investigation resources.
";
            
            // Send notification to management roles
            await _notificationService.NotifyRoleAsync(
                "HSEManager",
                notificationTitle,
                notificationMessage,
                cancellationToken: default);
            
            await _notificationService.NotifyRoleAsync(
                "SuperAdmin", 
                notificationTitle,
                notificationMessage,
                cancellationToken: default);
            
            LogActivity(activityName, "Successfully sent management notification for incident {IncidentId}", incidentId);
        }
        catch (Exception ex)
        {
            LogActivityError(activityName, ex, "Failed to send management notification");
        }
    }
}