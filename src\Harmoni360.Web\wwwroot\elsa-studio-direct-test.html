<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="utf-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>Elsa Studio Direct Test</title>
    <base href="/elsa-studio/" />
    <link href="/elsa-studio/css/bootstrap/bootstrap.min.css" rel="stylesheet" />
    <link href="/elsa-studio/css/app.css" rel="stylesheet" />
    <link rel="icon" type="image/png" href="/elsa-studio/favicon.png" />
    <link href="/elsa-studio/Harmoni360.ElsaStudio.styles.css" rel="stylesheet" />
    
    <!-- MudBlazor CSS -->
    <link href="/elsa-studio/_content/8.5.0/staticwebassets/MudBlazor.min.css" rel="stylesheet" />
    
    <style>
        .loading-progress {
            width: 100px;
            height: 100px;
            position: relative;
            margin: 100px auto;
            display: block;
        }
        
        .loading-progress circle {
            fill: none;
            stroke: #e0e0e0;
            stroke-width: 6;
        }
        
        .loading-progress circle:last-child {
            stroke: #1976d2;
            stroke-dasharray: calc(3.141 * 80);
            stroke-dashoffset: calc(3.141 * 80);
            animation: progress 1s ease-in-out forwards;
        }
        
        @keyframes progress {
            to {
                stroke-dashoffset: 0;
            }
        }
        
        .loading-progress-text {
            text-align: center;
            margin-top: 20px;
            font-weight: bold;
            font-size: 14px;
        }
    </style>
</head>

<body>
    <div id="app">
        <svg class="loading-progress">
            <circle r="40%" cx="50%" cy="50%" />
            <circle r="40%" cx="50%" cy="50%" />
        </svg>
        <div class="loading-progress-text">Loading...</div>
    </div>

    <div id="blazor-error-ui" style="display: none;">
        An unhandled error has occurred.
        <a href="" class="reload">Reload</a>
        <a class="dismiss">🗙</a>
    </div>
    
    <!-- Initialize MudBlazor stubs first -->
    <script>
        window.mudElementRef = {
            getBoundingClientRect: function() {
                return { width: 0, height: 0, top: 0, left: 0, right: 0, bottom: 0, x: 0, y: 0 };
            },
            saveFocus: function() { },
            addOnBlurEvent: function() { },
            restoreFocus: function() { },
            focus: function() { },
            select: function() { },
            selectRange: function() { }
        };
        
        window.mudKeyInterceptor = {
            connect: function() { },
            disconnect: function() { }
        };
        
        window.mudScrollManager = {
            lockScroll: function() { },
            unlockScroll: function() { }
        };
        
        window.mudWindow = {
            copyToClipboard: function(text) {
                return navigator.clipboard.writeText(text);
            },
            getBoundingClientRect: function(selector) {
                const element = document.querySelector(selector);
                return element ? element.getBoundingClientRect() : null;
            }
        };
        
        window.mudDragAndDrop = {
            initDropZone: function() { },
            dispose: function() { }
        };
        
        window.mudResizeObserver = {
            connect: function() { },
            disconnect: function() { },
            observe: function() { },
            unobserve: function() { }
        };
        
        window.mudEventProjections = {
            subscribe: function() { },
            unsubscribe: function() { }
        };
    </script>
    
    <!-- MudBlazor JS -->
    <script src="/elsa-studio/_content/8.5.0/staticwebassets/MudBlazor.min.js"></script>
    
    <!-- Blazor WebAssembly -->
    <script src="/elsa-studio/_framework/blazor.webassembly.js"></script>
</body>
</html>