// Enhanced Dashboard Responsive Design System
// Mobile-first approach with progressive enhancement

// ===================================================================
// BREAKPOINTS & VARIABLES
// ===================================================================

$mobile-sm: 480px;
$mobile: 576px;
$tablet: 768px;
$desktop: 992px;
$desktop-lg: 1200px;
$desktop-xl: 1400px;

// Touch target sizes (WCAG AA compliance)
$touch-target-min: 44px;
$touch-target-comfortable: 48px;

// ===================================================================
// DASHBOARD CONTAINER & LAYOUT
// ===================================================================

.dashboard-container {
  padding: 0.75rem;
  
  @media (min-width: $tablet) {
    padding: 1rem;
  }
  
  @media (min-width: $desktop) {
    padding: 1.5rem;
  }
  
  @media (min-width: $desktop-lg) {
    padding: 2rem;
  }
}

// Responsive grid system for dashboard widgets
.dashboard-grid {
  display: grid;
  gap: 1rem;
  grid-template-columns: 1fr;
  
  @media (min-width: $mobile) {
    gap: 1.25rem;
  }
  
  @media (min-width: $tablet) {
    grid-template-columns: repeat(2, 1fr);
    gap: 1.5rem;
  }
  
  @media (min-width: $desktop) {
    grid-template-columns: repeat(3, 1fr);
    gap: 1.75rem;
  }
  
  @media (min-width: $desktop-lg) {
    grid-template-columns: repeat(4, 1fr);
    gap: 2rem;
  }
  
  // Grid item sizing variants
  &.dashboard-grid-2col {
    @media (min-width: $tablet) {
      grid-template-columns: repeat(2, 1fr);
    }
  }
  
  &.dashboard-grid-3col {
    @media (min-width: $desktop) {
      grid-template-columns: repeat(3, 1fr);
    }
  }
}

// ===================================================================
// CARD COMPONENTS RESPONSIVE DESIGN
// ===================================================================

.card {
  transition: transform 0.2s ease-in-out, box-shadow 0.2s ease-in-out;
  
  // Responsive padding
  .card-body {
    padding: 1rem;
    
    @media (min-width: $tablet) {
      padding: 1.25rem;
    }
    
    @media (min-width: $desktop) {
      padding: 1.5rem;
    }
  }
  
  .card-header {
    padding: 0.75rem 1rem;
    
    @media (min-width: $tablet) {
      padding: 1rem 1.25rem;
    }
    
    @media (min-width: $desktop) {
      padding: 1rem 1.5rem;
    }
    
    h5, h6 {
      font-size: 1rem;
      line-height: 1.3;
      
      @media (min-width: $tablet) {
        font-size: 1.1rem;
      }
      
      @media (min-width: $desktop) {
        font-size: 1.25rem;
      }
    }
  }
}

// ===================================================================
// CHART COMPONENTS RESPONSIVE DESIGN
// ===================================================================

// Chart container with responsive aspect ratios
.chart-container {
  position: relative;
  width: 100%;
  
  // Default mobile aspect ratio
  aspect-ratio: 16/10;
  
  @media (min-width: $tablet) {
    aspect-ratio: 16/9;
  }
  
  @media (min-width: $desktop) {
    aspect-ratio: 2/1;
  }
  
  canvas {
    position: absolute !important;
    top: 0;
    left: 0;
    width: 100% !important;
    height: 100% !important;
  }
}

// Gauge chart responsive sizing
.gauge-chart-container {
  .gauge-sm {
    height: 120px;
    
    @media (min-width: $tablet) {
      height: 140px;
    }
    
    @media (min-width: $desktop) {
      height: 160px;
    }
  }
  
  .gauge-md {
    height: 160px;
    
    @media (min-width: $tablet) {
      height: 180px;
    }
    
    @media (min-width: $desktop) {
      height: 220px;
    }
  }
  
  .gauge-lg {
    height: 200px;
    
    @media (min-width: $tablet) {
      height: 240px;
    }
    
    @media (min-width: $desktop) {
      height: 280px;
    }
  }
}

// Bar chart responsive design
.bar-chart {
  &.horizontal {
    max-height: 250px;
    overflow-y: auto;
    
    @media (min-width: $tablet) {
      max-height: 300px;
    }
    
    @media (min-width: $desktop) {
      max-height: 400px;
    }
    
    // Custom scrollbar for horizontal bar charts
    &::-webkit-scrollbar {
      width: 4px;
    }
    
    &::-webkit-scrollbar-track {
      background: rgba(0, 0, 0, 0.1);
      border-radius: 2px;
    }
    
    &::-webkit-scrollbar-thumb {
      background: rgba(0, 0, 0, 0.3);
      border-radius: 2px;
      
      &:hover {
        background: rgba(0, 0, 0, 0.5);
      }
    }
  }
  
  &.vertical {
    .bar-chart-svg {
      width: 100%;
      height: auto;
      
      text {
        font-size: 10px;
        
        @media (min-width: $tablet) {
          font-size: 11px;
        }
        
        @media (min-width: $desktop) {
          font-size: 12px;
        }
      }
    }
  }
}

// ===================================================================
// STATS CARDS RESPONSIVE DESIGN
// ===================================================================

.stats-card {
  min-height: 120px;
  
  @media (min-width: $tablet) {
    min-height: 140px;
  }
  
  @media (min-width: $desktop) {
    min-height: 160px;
  }
  
  .stats-value {
    font-size: 1.5rem;
    font-weight: 700;
    line-height: 1.2;
    
    @media (min-width: $tablet) {
      font-size: 1.75rem;
    }
    
    @media (min-width: $desktop) {
      font-size: 2rem;
    }
    
    @media (min-width: $desktop-lg) {
      font-size: 2.25rem;
    }
  }
  
  .stats-title {
    font-size: 0.875rem;
    font-weight: 500;
    
    @media (min-width: $tablet) {
      font-size: 0.9rem;
    }
    
    @media (min-width: $desktop) {
      font-size: 1rem;
    }
  }
  
  .stats-icon {
    font-size: 1.5rem;
    
    @media (min-width: $tablet) {
      font-size: 1.75rem;
    }
    
    @media (min-width: $desktop) {
      font-size: 2rem;
    }
  }
  
  // Hover effects (disabled on touch devices)
  @media (hover: hover) and (pointer: fine) {
    &:hover {
      transform: translateY(-2px);
      box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
    }
  }
}

// Responsive stats card sizing
.stats-card-sm {
  min-height: 100px;
  
  @media (min-width: $tablet) {
    min-height: 120px;
  }
  
  .stats-value {
    font-size: 1.25rem;
    
    @media (min-width: $tablet) {
      font-size: 1.5rem;
    }
  }
}

.stats-card-lg {
  min-height: 140px;
  
  @media (min-width: $tablet) {
    min-height: 180px;
  }
  
  @media (min-width: $desktop) {
    min-height: 200px;
  }
  
  .stats-value {
    font-size: 2rem;
    
    @media (min-width: $tablet) {
      font-size: 2.5rem;
    }
    
    @media (min-width: $desktop) {
      font-size: 3rem;
    }
  }
}

// ===================================================================
// RESPONSIVE TABLES & DATA GRIDS
// ===================================================================

.table-responsive-dashboard {
  .table {
    font-size: 0.875rem;
    
    @media (min-width: $tablet) {
      font-size: 0.9rem;
    }
    
    th {
      font-size: 0.8rem;
      font-weight: 600;
      padding: 0.5rem;
      
      @media (min-width: $tablet) {
        font-size: 0.85rem;
        padding: 0.75rem;
      }
      
      @media (min-width: $desktop) {
        font-size: 0.9rem;
        padding: 1rem;
      }
    }
    
    td {
      padding: 0.5rem;
      
      @media (min-width: $tablet) {
        padding: 0.75rem;
      }
      
      @media (min-width: $desktop) {
        padding: 1rem;
      }
    }
  }
  
  // Mobile table stacking
  @media (max-width: #{$tablet - 1px}) {
    .table {
      thead {
        display: none;
      }
      
      tbody tr {
        display: block;
        border: 1px solid #dee2e6;
        border-radius: 0.375rem;
        margin-bottom: 0.75rem;
        padding: 0.75rem;
      }
      
      tbody td {
        display: block;
        border: none;
        text-align: left;
        padding: 0.25rem 0;
        
        &::before {
          content: attr(data-label) ': ';
          font-weight: 600;
          color: #6c757d;
          display: inline-block;
          min-width: 100px;
        }
      }
    }
  }
}

// ===================================================================
// DASHBOARD FILTERS & CONTROLS
// ===================================================================

.dashboard-filters {
  margin-bottom: 1.5rem;
  
  .filter-row {
    display: flex;
    flex-direction: column;
    gap: 1rem;
    
    @media (min-width: $tablet) {
      flex-direction: row;
      align-items: center;
      justify-content: space-between;
    }
  }
  
  .filter-controls {
    display: flex;
    flex-direction: column;
    gap: 0.75rem;
    
    @media (min-width: $mobile) {
      flex-direction: row;
      flex-wrap: wrap;
    }
    
    .form-select {
      min-width: 150px;
      
      @media (max-width: #{$mobile - 1px}) {
        width: 100%;
      }
    }
    
    .btn-group {
      .btn {
        min-height: $touch-target-min;
        font-size: 0.875rem;
        
        @media (max-width: #{$mobile - 1px}) {
          flex: 1;
        }
      }
    }
  }
  
  .refresh-controls {
    display: flex;
    align-items: center;
    justify-content: space-between;
    
    @media (max-width: #{$tablet - 1px}) {
      flex-direction: column;
      align-items: flex-start;
      gap: 0.5rem;
    }
    
    .btn {
      min-height: $touch-target-min;
    }
  }
}

// ===================================================================
// KPI INPUT CONTROLS
// ===================================================================

.kpi-inputs {
  .input-group {
    margin-bottom: 1rem;
    
    .form-control {
      font-size: 0.875rem;
      min-height: $touch-target-min;
      
      @media (min-width: $tablet) {
        font-size: 0.9rem;
      }
    }
    
    .input-group-text {
      font-size: 0.8rem;
      
      @media (min-width: $tablet) {
        font-size: 0.85rem;
      }
    }
  }
}

// ===================================================================
// PROGRESSIVE LOADING & SKELETON STATES
// ===================================================================

.skeleton-loader {
  animation: skeleton-loading 1.5s ease-in-out infinite alternate;
  background: linear-gradient(90deg, #f0f0f0 25%, #e0e0e0 50%, #f0f0f0 75%);
  background-size: 200% 100%;
  border-radius: 0.375rem;
}

@keyframes skeleton-loading {
  0% {
    background-position: -200% 0;
  }
  100% {
    background-position: 200% 0;
  }
}

.chart-skeleton {
  @extend .skeleton-loader;
  height: 200px;
  
  @media (min-width: $tablet) {
    height: 250px;
  }
  
  @media (min-width: $desktop) {
    height: 300px;
  }
}

.stats-skeleton {
  @extend .skeleton-loader;
  height: 120px;
  
  @media (min-width: $tablet) {
    height: 140px;
  }
  
  @media (min-width: $desktop) {
    height: 160px;
  }
}

// ===================================================================
// TOUCH-FRIENDLY INTERACTIONS
// ===================================================================

// Touch device optimizations
@media (hover: none) and (pointer: coarse) {
  .btn {
    min-height: $touch-target-comfortable;
    padding: 0.625rem 1rem;
  }
  
  .card {
    &.clickable {
      cursor: pointer;
      
      &:active {
        transform: scale(0.98);
        transition: transform 0.1s ease-in-out;
      }
    }
  }
  
  .dropdown-item {
    min-height: $touch-target-min;
    padding: 0.75rem 1rem;
  }
  
  // Larger touch targets for chart interactions
  .chart-container {
    canvas {
      touch-action: manipulation;
    }
  }
}

// ===================================================================
// ACCESSIBILITY ENHANCEMENTS
// ===================================================================

// Focus states for keyboard navigation
.card:focus-within {
  outline: 2px solid var(--cui-primary);
  outline-offset: 2px;
}

.chart-container:focus-within {
  outline: 2px solid var(--cui-primary);
  outline-offset: 2px;
  border-radius: 0.375rem;
}

// Reduced motion support
@media (prefers-reduced-motion: reduce) {
  .card,
  .stats-card,
  .chart-container,
  .skeleton-loader {
    transition: none !important;
    animation: none !important;
  }
}

// High contrast mode support
@media (prefers-contrast: high) {
  .card {
    border: 2px solid currentColor;
  }
  
  .stats-card {
    .stats-value {
      font-weight: 900;
    }
  }
  
  .chart-container {
    canvas {
      filter: contrast(1.2);
    }
  }
}

// ===================================================================
// PRINT STYLES
// ===================================================================

@media print {
  .dashboard-container {
    padding: 0;
  }
  
  .dashboard-filters,
  .refresh-controls,
  .btn,
  .dropdown {
    display: none !important;
  }
  
  .card {
    break-inside: avoid;
    margin-bottom: 1rem;
    box-shadow: none;
    border: 1px solid #000;
  }
  
  .chart-container {
    height: auto !important;
    max-height: 400px;
  }
  
  .stats-card {
    .stats-value {
      color: #000 !important;
    }
  }
}

// ===================================================================
// UTILITY CLASSES
// ===================================================================

// Responsive visibility utilities
.hide-mobile {
  @media (max-width: #{$tablet - 1px}) {
    display: none !important;
  }
}

.show-mobile {
  display: none !important;
  
  @media (max-width: #{$tablet - 1px}) {
    display: block !important;
  }
}

.hide-tablet {
  @media (min-width: $tablet) and (max-width: #{$desktop - 1px}) {
    display: none !important;
  }
}

.show-tablet {
  display: none !important;
  
  @media (min-width: $tablet) and (max-width: #{$desktop - 1px}) {
    display: block !important;
  }
}

.hide-desktop {
  @media (min-width: $desktop) {
    display: none !important;
  }
}

.show-desktop {
  display: none !important;
  
  @media (min-width: $desktop) {
    display: block !important;
  }
}

// Responsive text utilities
.text-truncate-mobile {
  @media (max-width: #{$tablet - 1px}) {
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
  }
}

.text-wrap-mobile {
  @media (max-width: #{$tablet - 1px}) {
    word-wrap: break-word;
    hyphens: auto;
  }
}

// Responsive spacing utilities
.p-responsive {
  padding: 0.75rem;
  
  @media (min-width: $tablet) {
    padding: 1rem;
  }
  
  @media (min-width: $desktop) {
    padding: 1.5rem;
  }
}

.m-responsive {
  margin: 0.75rem;
  
  @media (min-width: $tablet) {
    margin: 1rem;
  }
  
  @media (min-width: $desktop) {
    margin: 1.5rem;
  }
}

// ===================================================================
// DARK MODE SUPPORT
// ===================================================================

[data-coreui-theme="dark"] {
  .skeleton-loader {
    background: linear-gradient(90deg, #2d2d30 25%, #3a3a3c 50%, #2d2d30 75%);
  }
  
  .chart-container {
    canvas {
      filter: brightness(0.9);
    }
  }
  
  .bar-chart {
    &.horizontal {
      &::-webkit-scrollbar-track {
        background: rgba(255, 255, 255, 0.1);
      }
      
      &::-webkit-scrollbar-thumb {
        background: rgba(255, 255, 255, 0.3);
        
        &:hover {
          background: rgba(255, 255, 255, 0.5);
        }
      }
    }
  }
}