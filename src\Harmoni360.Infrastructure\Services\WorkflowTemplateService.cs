using Elsa.Workflows.Management;
using Elsa.Workflows.Management.Entities;
using Elsa.Workflows.Management.Filters;
using Elsa.Common.Models;
using Microsoft.Extensions.Logging;
using System.Text.Json;
using Harmoni360.Application.Common.Interfaces;
using Elsa.Workflows;

namespace Harmoni360.Infrastructure.Services;

/// <summary>
/// Service implementing the template pattern for workflow management
/// Follows Elsa Workflow v3 best practices for allowing safe user customization
/// </summary>
public class WorkflowTemplateService : IWorkflowTemplateService
{
    private readonly IWorkflowDefinitionStore _workflowDefinitionStore;
    private readonly ICurrentUserService _currentUserService;
    private readonly IWorkflowAuditService _auditService;
    private readonly ILogger<WorkflowTemplateService> _logger;

    public WorkflowTemplateService(
        IWorkflowDefinitionStore workflowDefinitionStore,
        ICurrentUserService currentUserService,
        IWorkflowAuditService auditService,
        ILogger<WorkflowTemplateService> logger)
    {
        _workflowDefinitionStore = workflowDefinitionStore;
        _currentUserService = currentUserService;
        _auditService = auditService;
        _logger = logger;
    }

    public async Task<WorkflowTemplateResult> CreateOrUpdateTemplateAsync(Type workflowType, CancellationToken cancellationToken = default)
    {
        var workflowName = workflowType.Name;
        var templateName = $"{workflowName}_Template";
        
        _logger.LogInformation("🔧 Creating/updating template for workflow: {WorkflowName}", workflowName);

        try
        {
            // Create workflow instance to get definition
            var workflow = CreateWorkflowInstance(workflowType);
            
            // Check existing template and user versions
            var existingTemplate = await FindWorkflowByNameAsync(templateName, cancellationToken);
            var existingUserVersion = await FindWorkflowByNameAsync(workflowName, cancellationToken);
            
            // Check if user has customizations
            var hasCustomizations = existingUserVersion != null && await HasUserCustomizationsAsync(workflowName, cancellationToken);

            // Create or update template (readonly)
            var templateResult = await CreateOrUpdateTemplateDefinition(
                workflowType, workflow, existingTemplate, templateName, cancellationToken);

            // Create or update user version (editable)
            var userResult = await CreateOrUpdateUserVersion(
                workflowType, workflow, existingUserVersion, workflowName, hasCustomizations, templateResult.Version, cancellationToken);

            var result = new WorkflowTemplateResult
            {
                WorkflowName = workflowName,
                TemplateId = templateResult.DefinitionId,
                UserVersionId = userResult.DefinitionId,
                TemplateVersion = templateResult.Version,
                UserVersion = userResult.Version,
                IsNewTemplate = existingTemplate == null,
                UserVersionUpdated = !hasCustomizations || existingUserVersion == null,
                HasUserCustomizations = hasCustomizations,
                UpdatedProperties = GetUpdatedProperties(existingTemplate, templateResult)
            };

            // Log audit trail
            await _auditService.LogUserActionAsync(
                workflowInstanceId: result.TemplateId,
                userId: _currentUserService.UserIdAsString ?? "System",
                action: result.IsNewTemplate ? "TemplateCreated" : "TemplateUpdated",
                resourceType: "WorkflowTemplate",
                resourceId: workflowName,
                actionData: new Dictionary<string, object> { ["TemplateVersion"] = result.TemplateVersion, ["UserVersion"] = result.UserVersion },
                cancellationToken);

            _logger.LogInformation("✅ Template operation completed for {WorkflowName}: Template v{TemplateVersion}, User v{UserVersion}",
                workflowName, result.TemplateVersion, result.UserVersion);

            return result;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "❌ Failed to create/update template for workflow {WorkflowName}", workflowName);
            throw;
        }
    }

    public async Task<bool> HasUserCustomizationsAsync(string workflowName, CancellationToken cancellationToken = default)
    {
        try
        {
            var userWorkflow = await FindWorkflowByNameAsync(workflowName, cancellationToken);
            if (userWorkflow == null) return false;

            return GetCustomizationMetadata(userWorkflow).HasCustomizations;
        }
        catch (Exception ex)
        {
            _logger.LogWarning(ex, "Failed to check customizations for workflow {WorkflowName}", workflowName);
            return false;
        }
    }

    public async Task<CustomizationPreservationResult> PreserveUserCustomizationsAsync(
        string workflowName, 
        int newTemplateVersion, 
        CancellationToken cancellationToken = default)
    {
        _logger.LogInformation("🛡️ Preserving user customizations for {WorkflowName} (template v{Version})", 
            workflowName, newTemplateVersion);

        try
        {
            var userWorkflow = await FindWorkflowByNameAsync(workflowName, cancellationToken);
            if (userWorkflow == null)
            {
                return new CustomizationPreservationResult
                {
                    CustomizationsPreserved = false
                };
            }

            var customizationInfo = GetCustomizationMetadata(userWorkflow);
            if (!customizationInfo.HasCustomizations)
            {
                return new CustomizationPreservationResult
                {
                    CustomizationsPreserved = false
                };
            }

            // Analyze customizations vs template changes
            var templateWorkflow = await FindWorkflowByNameAsync($"{workflowName}_Template", cancellationToken);
            var preservationAnalysis = AnalyzeCustomizationConflicts(userWorkflow, templateWorkflow, customizationInfo);

            // Update user workflow metadata to indicate preserved customizations
            await UpdateCustomizationMetadata(userWorkflow, newTemplateVersion, preservationAnalysis, cancellationToken);

            // Log preservation details
            await _auditService.LogUserActionAsync(
                workflowInstanceId: userWorkflow.DefinitionId,
                userId: _currentUserService.UserIdAsString ?? "System",
                action: "CustomizationsPreserved",
                resourceType: "WorkflowTemplate",
                resourceId: workflowName,
                actionData: new Dictionary<string, object> { ["PreservationResult"] = preservationAnalysis },
                cancellationToken);

            return preservationAnalysis;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "❌ Failed to preserve customizations for workflow {WorkflowName}", workflowName);
            throw;
        }
    }

    public async Task<TemplateUpdateNotification> GetUpdateNotificationAsync(string workflowName, CancellationToken cancellationToken = default)
    {
        try
        {
            var templateName = $"{workflowName}_Template";
            var template = await FindWorkflowByNameAsync(templateName, cancellationToken);
            var userVersion = await FindWorkflowByNameAsync(workflowName, cancellationToken);

            if (template == null || userVersion == null)
            {
                return new TemplateUpdateNotification
                {
                    WorkflowName = workflowName,
                    RequiresUserAction = false
                };
            }

            var templateMetadata = GetWorkflowMetadata(template);
            var userMetadata = GetWorkflowMetadata(userVersion);
            var customizationInfo = GetCustomizationMetadata(userVersion);

            var changedFeatures = CompareWorkflowFeatures(template, userVersion);

            return new TemplateUpdateNotification
            {
                WorkflowName = workflowName,
                CurrentTemplateVersion = template.Version,
                NewTemplateVersion = template.Version, // Would be incremented in actual update
                HasUserCustomizations = customizationInfo.HasCustomizations,
                ChangedFeatures = changedFeatures,
                LastTemplateUpdate = template.CreatedAt.DateTime,
                UpdateSummary = GenerateUpdateSummary(changedFeatures),
                RequiresUserAction = customizationInfo.HasCustomizations && changedFeatures.Length > 0
            };
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to generate update notification for workflow {WorkflowName}", workflowName);
            throw;
        }
    }

    public async Task<WorkflowResetResult> ResetToTemplateAsync(string workflowName, string userId, CancellationToken cancellationToken = default)
    {
        _logger.LogInformation("🔄 Resetting workflow {WorkflowName} to template for user {UserId}", workflowName, userId);

        try
        {
            var templateName = $"{workflowName}_Template";
            var template = await FindWorkflowByNameAsync(templateName, cancellationToken);
            var userVersion = await FindWorkflowByNameAsync(workflowName, cancellationToken);

            if (template == null)
            {
                throw new InvalidOperationException($"Template not found for workflow {workflowName}");
            }

            var lostCustomizations = userVersion != null ? 
                GetCustomizationMetadata(userVersion).CustomizedElements : Array.Empty<string>();

            // Create new user version from template
            var newUserVersion = await CreateUserVersionFromTemplate(template, workflowName, cancellationToken);

            // Log reset operation
            await _auditService.LogUserActionAsync(
                workflowInstanceId: newUserVersion.DefinitionId,
                userId: userId,
                action: "WorkflowReset",
                resourceType: "WorkflowTemplate",
                resourceId: workflowName,
                actionData: new Dictionary<string, object> { ["LostCustomizations"] = lostCustomizations, ["NewVersion"] = newUserVersion.Version },
                cancellationToken);

            return new WorkflowResetResult
            {
                Success = true,
                WorkflowName = workflowName,
                NewVersion = newUserVersion.Version,
                LostCustomizations = lostCustomizations,
                ResetBy = userId,
                ResetAt = DateTime.UtcNow
            };
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "❌ Failed to reset workflow {WorkflowName} to template", workflowName);
            return new WorkflowResetResult
            {
                Success = false,
                WorkflowName = workflowName
            };
        }
    }

    #region Private Helper Methods

    private IWorkflow CreateWorkflowInstance(Type workflowType)
    {
        var workflow = Activator.CreateInstance(workflowType) as IWorkflow;
        if (workflow == null)
        {
            throw new InvalidOperationException($"Could not create instance of workflow {workflowType.Name}");
        }
        return workflow;
    }

    private async Task<WorkflowDefinition?> FindWorkflowByNameAsync(string name, CancellationToken cancellationToken)
    {
        var filter = new WorkflowDefinitionFilter
        {
            Name = name,
            VersionOptions = VersionOptions.Latest
        };
        var results = await _workflowDefinitionStore.FindManyAsync(filter, cancellationToken);
        return results.FirstOrDefault();
    }

    private async Task<WorkflowDefinition> CreateOrUpdateTemplateDefinition(
        Type workflowType,
        IWorkflow workflow,
        WorkflowDefinition? existing,
        string templateName,
        CancellationToken cancellationToken)
    {
        var definition = existing ?? new WorkflowDefinition
        {
            Id = Guid.NewGuid().ToString(),
            DefinitionId = GenerateDeterministicGuid(templateName),
            Name = templateName,
            Version = 1,
            IsLatest = true,
            IsPublished = true,
            CreatedAt = DateTimeOffset.UtcNow
        };

        if (existing != null)
        {
            definition.Version += 1;
        }

        // Set template metadata (these need to be in StringData for Elsa v3)
        var displayName = $"🔒 {GetDisplayName(workflowType)} (Template)";
        var description = $"Template for {GetDescription(workflowType)} - This is a read-only template.";
        definition.IsReadonly = true; // Templates are readonly

        // Build workflow structure
        var workflowModel = BuildWorkflowDefinition(workflowType, workflow, definition, isTemplate: true);
        definition.StringData = JsonSerializer.Serialize(workflowModel, GetJsonOptions());

        await _workflowDefinitionStore.SaveAsync(definition, cancellationToken);
        
        _logger.LogDebug("✅ {Action} template: {TemplateName} v{Version}",
            existing == null ? "Created" : "Updated", templateName, definition.Version);

        return definition;
    }

    private async Task<WorkflowDefinition> CreateOrUpdateUserVersion(
        Type workflowType,
        IWorkflow workflow,
        WorkflowDefinition? existing,
        string workflowName,
        bool hasCustomizations,
        int templateVersion,
        CancellationToken cancellationToken)
    {
        var definition = existing ?? new WorkflowDefinition
        {
            Id = Guid.NewGuid().ToString(),
            DefinitionId = GenerateDeterministicGuid(workflowName),
            Name = workflowName,
            Version = 1,
            IsLatest = true,
            IsPublished = true,
            CreatedAt = DateTimeOffset.UtcNow
        };

        // Only update user version if no customizations exist or it's new
        if (!hasCustomizations || existing == null)
        {
            if (existing != null)
            {
                definition.Version += 1;
            }

                // Set user version metadata (these need to be in StringData for Elsa v3)
            var displayName = GetDisplayName(workflowType);
            var description = GetDescription(workflowType);
            definition.IsReadonly = false; // User versions are editable

            // Build workflow structure
            var workflowModel = BuildWorkflowDefinition(workflowType, workflow, definition, isTemplate: false);
            
            // Add template tracking metadata
            workflowModel = AddTemplateTrackingMetadata(workflowModel, templateVersion);
            
            definition.StringData = JsonSerializer.Serialize(workflowModel, GetJsonOptions());

            await _workflowDefinitionStore.SaveAsync(definition, cancellationToken);

            _logger.LogDebug("✅ {Action} user version: {WorkflowName} v{Version}",
                existing == null ? "Created" : "Updated", workflowName, definition.Version);
        }
        else
        {
            _logger.LogDebug("🛡️ Preserved user customizations for: {WorkflowName} v{Version}",
                workflowName, definition.Version);
        }

        return definition;
    }

    private async Task<WorkflowDefinition> CreateUserVersionFromTemplate(
        WorkflowDefinition template,
        string workflowName,
        CancellationToken cancellationToken)
    {
        var userDefinition = new WorkflowDefinition
        {
            Id = Guid.NewGuid().ToString(),
            DefinitionId = GenerateDeterministicGuid(workflowName),
            Name = workflowName,
            Version = 1,
            IsLatest = true,
            IsPublished = true,
            CreatedAt = DateTimeOffset.UtcNow,
            // DisplayName and Description will be set in StringData
            IsReadonly = false
        };

        // Copy template data but make it editable
        if (!string.IsNullOrEmpty(template.StringData))
        {
            var templateData = JsonSerializer.Deserialize<JsonElement>(template.StringData);
            // Remove template-specific metadata and make editable
            userDefinition.StringData = JsonSerializer.Serialize(templateData, GetJsonOptions());
        }

        await _workflowDefinitionStore.SaveAsync(userDefinition, cancellationToken);
        return userDefinition;
    }

    private object BuildWorkflowDefinition(Type workflowType, IWorkflow workflow, WorkflowDefinition definition, bool isTemplate)
    {
        var displayName = isTemplate ? $"🔒 {GetDisplayName(workflowType)} (Template)" : GetDisplayName(workflowType);
        var description = isTemplate ? $"Template for {GetDescription(workflowType)} - This is a read-only template." : GetDescription(workflowType);

        return new
        {
            id = definition.DefinitionId,
            name = definition.Name,
            displayName = displayName,
            description = description,
            version = definition.Version,
            isPublished = true,
            isLatest = true,
            isReadonly = isTemplate,
            customProperties = new
            {
                category = "HSE",
                module = "IncidentManagement",
                tags = GetWorkflowTags(workflowType),
                isTemplate = isTemplate,
                sourceWorkflow = isTemplate ? workflowType.Name : $"{workflowType.Name}_Template",
                createdAt = DateTimeOffset.UtcNow,
                lastSynchronized = DateTimeOffset.UtcNow
            },
            root = new
            {
                type = "Elsa.Sequence",
                id = Guid.NewGuid().ToString(),
                name = "Root",
                displayName = displayName,
                description = description,
                activities = new object[] { } // Will be populated by Elsa based on the actual workflow
            }
        };
    }

    private object AddTemplateTrackingMetadata(object workflowModel, int templateVersion)
    {
        var metadata = new Dictionary<string, object>();
        
        // Serialize and deserialize to get a mutable dictionary
        var json = JsonSerializer.Serialize(workflowModel);
        var existing = JsonSerializer.Deserialize<Dictionary<string, object>>(json);
        
        if (existing != null)
        {
            foreach (var kvp in existing)
            {
                metadata[kvp.Key] = kvp.Value;
            }
        }

        // Update custom properties
        if (metadata.ContainsKey("customProperties"))
        {
            var customPropsJson = JsonSerializer.Serialize(metadata["customProperties"]);
            var customProps = JsonSerializer.Deserialize<Dictionary<string, object>>(customPropsJson) ?? new Dictionary<string, object>();
            
            customProps["templateVersion"] = templateVersion;
            customProps["lastTemplateSync"] = DateTimeOffset.UtcNow;
            customProps["hasCustomizations"] = false;
            customProps["customizedElements"] = Array.Empty<string>();
            
            metadata["customProperties"] = customProps;
        }

        return metadata;
    }

    private (bool HasCustomizations, string[] CustomizedElements) GetCustomizationMetadata(WorkflowDefinition definition)
    {
        try
        {
            if (string.IsNullOrEmpty(definition.StringData)) 
                return (false, Array.Empty<string>());

            var doc = JsonDocument.Parse(definition.StringData);
            if (doc.RootElement.TryGetProperty("customProperties", out var customProps))
            {
                var hasCustomizations = customProps.TryGetProperty("hasCustomizations", out var hasCustom) && hasCustom.GetBoolean();
                var customizedElements = customProps.TryGetProperty("customizedElements", out var elements) ?
                    elements.EnumerateArray().Select(e => e.GetString() ?? "").ToArray() :
                    Array.Empty<string>();

                return (hasCustomizations, customizedElements);
            }
        }
        catch (Exception ex)
        {
            _logger.LogWarning(ex, "Failed to parse customization metadata for workflow {WorkflowName}", definition.Name);
        }

        return (false, Array.Empty<string>());
    }

    private CustomizationPreservationResult AnalyzeCustomizationConflicts(
        WorkflowDefinition userWorkflow,
        WorkflowDefinition? templateWorkflow,
        (bool HasCustomizations, string[] CustomizedElements) customizationInfo)
    {
        if (!customizationInfo.HasCustomizations)
        {
            return new CustomizationPreservationResult
            {
                CustomizationsPreserved = false
            };
        }

        // For now, preserve all customizations - in a real implementation,
        // this would analyze conflicts between user changes and template updates
        return new CustomizationPreservationResult
        {
            CustomizationsPreserved = true,
            PreservedElements = customizationInfo.CustomizedElements,
            ConflictingElements = Array.Empty<string>(),
            RequiresManualReview = false,
            ReviewNotes = "All user customizations preserved successfully"
        };
    }

    private async Task UpdateCustomizationMetadata(
        WorkflowDefinition userWorkflow,
        int newTemplateVersion,
        CustomizationPreservationResult preservation,
        CancellationToken cancellationToken)
    {
        if (string.IsNullOrEmpty(userWorkflow.StringData)) return;

        try
        {
            var doc = JsonDocument.Parse(userWorkflow.StringData);
            var root = new Dictionary<string, object>();

            foreach (var prop in doc.RootElement.EnumerateObject())
            {
                if (prop.Name != "customProperties")
                {
                    root[prop.Name] = prop.Value.Clone();
                }
            }

            var customProps = new Dictionary<string, object>();
            if (doc.RootElement.TryGetProperty("customProperties", out var existingProps))
            {
                foreach (var prop in existingProps.EnumerateObject())
                {
                    customProps[prop.Name] = prop.Value.Clone();
                }
            }

            customProps["templateVersion"] = newTemplateVersion;
            customProps["lastTemplateSync"] = DateTimeOffset.UtcNow;
            customProps["preservationResult"] = preservation;

            root["customProperties"] = customProps;

            userWorkflow.StringData = JsonSerializer.Serialize(root, GetJsonOptions());
            await _workflowDefinitionStore.SaveAsync(userWorkflow, cancellationToken);
        }
        catch (Exception ex)
        {
            _logger.LogWarning(ex, "Failed to update customization metadata for workflow {WorkflowName}", userWorkflow.Name);
        }
    }

    private object GetWorkflowMetadata(WorkflowDefinition definition)
    {
        try
        {
            if (!string.IsNullOrEmpty(definition.StringData))
            {
                var doc = JsonDocument.Parse(definition.StringData);
                if (doc.RootElement.TryGetProperty("customProperties", out var customProps))
                {
                    return customProps;
                }
            }
        }
        catch { }

        return new { };
    }

    private string[] CompareWorkflowFeatures(WorkflowDefinition template, WorkflowDefinition userVersion)
    {
        // In a real implementation, this would compare workflow structures
        // and identify changed activities, connections, variables, etc.
        return Array.Empty<string>();
    }

    private string GenerateUpdateSummary(string[] changedFeatures)
    {
        if (changedFeatures.Length == 0)
        {
            return "No changes detected in template";
        }

        return $"Template updated with {changedFeatures.Length} changes: {string.Join(", ", changedFeatures)}";
    }

    private string[] GetUpdatedProperties(WorkflowDefinition? existing, WorkflowDefinition current)
    {
        var updated = new List<string>();

        if (existing == null)
        {
            updated.Add("Created");
        }
        else
        {
            // Note: DisplayName and Description are stored in StringData, not as direct properties
            // This comparison would need to parse the JSON to be accurate
            if (existing.Version != current.Version) updated.Add("Version");
        }

        return updated.ToArray();
    }

    private string GenerateDeterministicGuid(string input)
    {
        using var md5 = System.Security.Cryptography.MD5.Create();
        var hash = md5.ComputeHash(System.Text.Encoding.UTF8.GetBytes(input));
        return new Guid(hash).ToString();
    }

    private string GetDisplayName(Type workflowType)
    {
        return workflowType.Name switch
        {
            "IncidentProcessWorkflow" => "Incident Management Workflow",
            // Note: Other workflow types not yet implemented
            "IncidentEscalationWorkflow" => "Incident Escalation Workflow",
            "IncidentInvestigationWorkflow" => "Incident Investigation Workflow",
            "IncidentReviewWorkflow" => "Incident Review Workflow",
            _ => workflowType.Name.Replace("Workflow", " Workflow")
        };
    }

    private string GetDescription(Type workflowType)
    {
        return workflowType.Name switch
        {
            "IncidentProcessWorkflow" => "Main workflow orchestrating the complete incident lifecycle from reporting to closure",
            // Note: Other workflow types not yet implemented
            "IncidentEscalationWorkflow" => "Handles escalation of incidents based on severity and response time",
            "IncidentInvestigationWorkflow" => "Manages the investigation process for incidents",
            "IncidentReviewWorkflow" => "Handles review and approval of incident resolutions",
            _ => $"Workflow for {workflowType.Name}"
        };
    }

    private string[] GetWorkflowTags(Type workflowType)
    {
        var tags = new List<string> { "hse", "template-managed" };

        if (workflowType.Name.Contains("Incident")) tags.Add("incident-management");
        if (workflowType.Name.Contains("Escalation")) tags.Add("escalation");
        if (workflowType.Name.Contains("Investigation")) tags.Add("investigation");
        if (workflowType.Name.Contains("Review")) tags.Add("review");

        return tags.ToArray();
    }

    private JsonSerializerOptions GetJsonOptions()
    {
        return new JsonSerializerOptions
        {
            WriteIndented = true,
            PropertyNamingPolicy = JsonNamingPolicy.CamelCase
        };
    }

    #endregion
}