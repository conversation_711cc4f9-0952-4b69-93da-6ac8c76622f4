#!/bin/bash

echo "=== Quick Build Test ==="
echo "Testing file generation for Elsa Studio and Monaco Editor"
echo ""

cd src/Harmoni360.Web

# 1. Clean directories
echo "1. Cleaning directories..."
rm -rf wwwroot/elsa-studio
rm -rf ClientApp/public/elsa-studio
rm -rf ClientApp/public/monaco-editor
rm -rf ClientApp/public/min

# 2. Restore packages
echo "2. Restoring packages..."
dotnet restore

# 3. Build (this should trigger MSBuild targets)
echo "3. Building..."
dotnet build

# 4. Check results
echo ""
echo "4. Checking generated files:"
echo "============================"

if [ -d "wwwroot/elsa-studio" ]; then
    echo "✓ wwwroot/elsa-studio exists"
    echo "  Files: $(find wwwroot/elsa-studio -type f | wc -l)"
else
    echo "✗ wwwroot/elsa-studio missing"
fi

if [ -d "ClientApp/public/monaco-editor" ]; then
    echo "✓ ClientApp/public/monaco-editor exists"
    echo "  Files: $(find ClientApp/public/monaco-editor -type f | wc -l)"
else
    echo "✗ ClientApp/public/monaco-editor missing"
fi

if [ -d "ClientApp/public/min" ]; then
    echo "✓ ClientApp/public/min exists"
    echo "  Files: $(find ClientApp/public/min -type f | wc -l)"
else
    echo "✗ ClientApp/public/min missing"
fi

echo ""
echo "Test complete!"