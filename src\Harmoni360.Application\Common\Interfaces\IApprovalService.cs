using Harmoni360.Domain.Entities;
using Harmoni360.Domain.Enums;

namespace Harmoni360.Application.Common.Interfaces;

/// <summary>
/// Service for handling approval workflows and authorization
/// </summary>
public interface IApprovalService
{
    /// <summary>
    /// Checks if a user with the given role can approve a specific status transition
    /// </summary>
    Task<bool> CanApproveStatusTransitionAsync(
        RoleType userRole, 
        IncidentStatus fromStatus, 
        IncidentStatus toStatus);
    
    /// <summary>
    /// Gets the required roles for a specific status transition
    /// </summary>
    Task<IEnumerable<RoleType>> GetRequiredRolesForTransitionAsync(
        IncidentStatus fromStatus, 
        IncidentStatus toStatus);
    
    /// <summary>
    /// Records an approval decision
    /// </summary>
    Task RecordApprovalAsync(
        int incidentId,
        string approverUserId,
        RoleType approverRole,
        IncidentStatus fromStatus,
        IncidentStatus toStatus,
        bool isApproved,
        string? comments = null);
    
    /// <summary>
    /// Checks if the current user can perform an action based on their roles
    /// </summary>
    Task<bool> IsUserAuthorizedAsync(string userId, params RoleType[] requiredRoles);
    
    /// <summary>
    /// Gets the user's highest role for authorization purposes
    /// </summary>
    Task<RoleType?> GetUserHighestRoleAsync(string userId);
}