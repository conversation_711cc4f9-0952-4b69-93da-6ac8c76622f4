using Microsoft.AspNetCore.Mvc;
using Microsoft.AspNetCore.Authorization;
using Microsoft.EntityFrameworkCore;
using Elsa.EntityFrameworkCore.Modules.Management;
using Elsa.EntityFrameworkCore.Modules.Runtime;
using Microsoft.Extensions.Logging;

namespace Harmoni360.Web.Controllers;

[ApiController]
[Route("api/[controller]")]
[Authorize(Roles = "SuperAdmin,Developer")]
public class ElsaDatabaseController : ControllerBase
{
    private readonly ManagementElsaDbContext _managementContext;
    private readonly RuntimeElsaDbContext _runtimeContext;
    private readonly ILogger<ElsaDatabaseController> _logger;

    public ElsaDatabaseController(
        ManagementElsaDbContext managementContext,
        RuntimeElsaDbContext runtimeContext,
        ILogger<ElsaDatabaseController> logger)
    {
        _managementContext = managementContext;
        _runtimeContext = runtimeContext;
        _logger = logger;
    }

    [HttpPost("ensure-database")]
    public async Task<IActionResult> EnsureDatabaseCreated()
    {
        try
        {
            _logger.LogInformation("Creating Elsa database tables...");

            // Ensure Management database is created
            var managementCreated = await _managementContext.Database.EnsureCreatedAsync();
            _logger.LogInformation("Management database - Created: {Created}", managementCreated);

            // Ensure Runtime database is created  
            var runtimeCreated = await _runtimeContext.Database.EnsureCreatedAsync();
            _logger.LogInformation("Runtime database - Created: {Created}", runtimeCreated);

            // Get table information
            var managementTables = await GetTableInfo(_managementContext);
            var runtimeTables = await GetTableInfo(_runtimeContext);

            return Ok(new
            {
                Success = true,
                Message = "Elsa database tables ensured",
                ManagementDatabaseCreated = managementCreated,
                RuntimeDatabaseCreated = runtimeCreated,
                ManagementTables = managementTables,
                RuntimeTables = runtimeTables
            });
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to ensure Elsa database");
            return StatusCode(500, new { 
                Error = ex.Message,
                StackTrace = ex.StackTrace
            });
        }
    }

    [HttpGet("table-status")]
    public async Task<IActionResult> GetTableStatus()
    {
        try
        {
            var managementTables = await GetTableInfo(_managementContext);
            var runtimeTables = await GetTableInfo(_runtimeContext);

            return Ok(new
            {
                ManagementTables = managementTables,
                RuntimeTables = runtimeTables,
                ConnectionStrings = new
                {
                    Management = _managementContext.Database.GetConnectionString(),
                    Runtime = _runtimeContext.Database.GetConnectionString()
                }
            });
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to get table status");
            return StatusCode(500, new { Error = ex.Message });
        }
    }

    private async Task<object> GetTableInfo(DbContext context)
    {
        try
        {
            var connectionString = context.Database.GetConnectionString();
            var sql = @"
                SELECT table_name, table_schema 
                FROM information_schema.tables 
                WHERE table_type = 'BASE TABLE' 
                AND (table_name LIKE '%Workflow%' OR table_name LIKE '%Elsa%')
                ORDER BY table_schema, table_name";

            var tables = await context.Database
                .SqlQueryRaw<TableInfo>(sql)
                .ToListAsync();

            return new
            {
                ConnectionString = connectionString,
                Tables = tables,
                TableCount = tables.Count
            };
        }
        catch (Exception ex)
        {
            return new { Error = ex.Message };
        }
    }

    public class TableInfo
    {
        public string table_name { get; set; } = "";
        public string table_schema { get; set; } = "";
    }
}