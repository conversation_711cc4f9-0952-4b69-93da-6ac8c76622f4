using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using Elsa.Workflows.Management;
using Elsa.Workflows.Management.Entities;
using Elsa.Workflows.Management.Filters;
using Elsa.Workflows;
using Elsa.Workflows.Management.Models;
using Elsa.Common.Models;
using System.Text.Json;
using Harmoni360.Infrastructure.Services;

namespace Harmoni360.Web.Controllers;

[ApiController]
[Route("api/[controller]")]
[AllowAnonymous] // Temporarily disable auth for testing
public class WorkflowRegistrationController : ControllerBase
{
    private readonly IWorkflowDefinitionStore _workflowDefinitionStore;
    private readonly IServiceProvider _serviceProvider;
    private readonly ILogger<WorkflowRegistrationController> _logger;

    public WorkflowRegistrationController(
        IWorkflowDefinitionStore workflowDefinitionStore,
        IServiceProvider serviceProvider,
        ILogger<WorkflowRegistrationController> logger)
    {
        _workflowDefinitionStore = workflowDefinitionStore;
        _serviceProvider = serviceProvider;
        _logger = logger;
    }

    [HttpPost("register-all")]
    public async Task<IActionResult> RegisterAllWorkflows()
    {
        try
        {
            var results = new List<object>();

            // List of workflows to register - only include actual existing types
            var workflowTypes = new[]
            {
                typeof(Harmoni360.Application.Features.WorkflowManagement.IncidentWorkflow.Workflows.IncidentManagementWorkflow)
                // Note: Other workflow types will be added when implemented
            };

            foreach (var workflowType in workflowTypes)
            {
                try
                {
                    var result = await RegisterWorkflowAsync(workflowType);
                    results.Add(result);
                }
                catch (Exception ex)
                {
                    _logger.LogError(ex, "Failed to register workflow {WorkflowType}", workflowType.Name);
                    results.Add(new { workflow = workflowType.Name, status = "failed", error = ex.Message });
                }
            }

            return Ok(new { message = "Workflow registration completed", results });
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to register workflows");
            return StatusCode(500, new { error = "Failed to register workflows", details = ex.Message });
        }
    }

    [HttpPost("synchronize")]
    public async Task<IActionResult> SynchronizeAllWorkflows()
    {
        return await PerformSynchronization();
    }

    [HttpPost("synchronize-internal")]
    [AllowAnonymous] // Allow internal calls without authentication
    public async Task<IActionResult> SynchronizeAllWorkflowsInternal()
    {
        // Check for internal service header (production-safe)
        if (!HttpContext.Request.Headers.TryGetValue("X-Internal-Service", out var headerValue) || 
            headerValue != "WorkflowAutoDiscovery")
        {
            return Forbid("Internal service endpoint only");
        }
        
        return await PerformSynchronization();
    }

    private async Task<IActionResult> PerformSynchronization()
    {
        try
        {
            _logger.LogInformation("Manual workflow synchronization triggered");

            // Perform the synchronization directly
            await PerformManualSynchronization(HttpContext.RequestAborted);
            
            return Ok(new { 
                message = "Workflow synchronization completed successfully",
                timestamp = DateTimeOffset.UtcNow
            });
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to synchronize workflows");
            return StatusCode(500, new { error = "Failed to synchronize workflows", details = ex.Message });
        }
    }

    [HttpGet("status")]
    public async Task<IActionResult> GetWorkflowStatus()
    {
        try
        {
            var allDefinitions = await _workflowDefinitionStore.FindManyAsync(
                new WorkflowDefinitionFilter(),
                cancellationToken: HttpContext.RequestAborted);

            var codeBasedWorkflows = allDefinitions.Where(IsCodeBasedWorkflow).ToList();
            var designerWorkflows = allDefinitions.Where(d => !IsCodeBasedWorkflow(d)).ToList();

            var workflowStatus = allDefinitions.Select(d => new
            {
                id = d.Id,
                definitionId = d.DefinitionId,
                name = d.Name,
                displayName = GetDisplayNameFromStringData(d),
                version = d.Version,
                isPublished = d.IsPublished,
                isLatest = d.IsLatest,
                createdAt = d.CreatedAt,
                type = IsCodeBasedWorkflow(d) ? "code-based" : "designer",
                lastSynchronized = GetLastSynchronizedTime(d)
            }).ToList();

            return Ok(new { 
                totalWorkflows = workflowStatus.Count,
                codeBasedCount = codeBasedWorkflows.Count,
                designerCount = designerWorkflows.Count,
                workflows = workflowStatus 
            });
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to get workflow status");
            return StatusCode(500, new { error = "Failed to get workflow status", details = ex.Message });
        }
    }

    private async Task PerformManualSynchronization(CancellationToken cancellationToken)
    {
        _logger.LogInformation("🔄 Performing manual workflow synchronization");

        // 1. Register/update code-based workflows
        var workflowTypes = DiscoverWorkflowTypes();
        foreach (var workflowType in workflowTypes)
        {
            try
            {
                await RegisterWorkflowAsync(workflowType);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Failed to register workflow {WorkflowType}", workflowType.Name);
            }
        }

        // 2. Validate all existing workflows
        var allDefinitions = await _workflowDefinitionStore.FindManyAsync(
            new WorkflowDefinitionFilter(),
            cancellationToken);

        foreach (var definition in allDefinitions)
        {
            try
            {
                await EnsureWorkflowMetadata(definition, cancellationToken);
            }
            catch (Exception ex)
            {
                _logger.LogWarning(ex, "Failed to update metadata for workflow {WorkflowName}", definition.Name);
            }
        }

        _logger.LogInformation("✅ Manual workflow synchronization completed");
    }

    private List<Type> DiscoverWorkflowTypes()
    {
        var workflowInterface = typeof(IWorkflow);
        var applicationAssembly = typeof(Harmoni360.Application.Features.WorkflowManagement.IncidentWorkflow.Workflows.IncidentManagementWorkflow).Assembly;

        return applicationAssembly.GetTypes()
            .Where(t => workflowInterface.IsAssignableFrom(t) && 
                       !t.IsAbstract && 
                       !t.IsInterface &&
                       t.GetConstructor(Type.EmptyTypes) != null)
            .ToList();
    }

    private bool IsCodeBasedWorkflow(WorkflowDefinition definition)
    {
        try
        {
            if (!string.IsNullOrEmpty(definition.StringData))
            {
                var doc = System.Text.Json.JsonDocument.Parse(definition.StringData);
                if (doc.RootElement.TryGetProperty("customProperties", out var customProps))
                {
                    if (customProps.TryGetProperty("autoDiscovered", out var autoDiscovered))
                    {
                        return autoDiscovered.GetBoolean();
                    }
                }
            }
        }
        catch { }

        // Also check by naming convention - only include existing workflows
        var codeBasedNames = new[] { 
            "IncidentManagementWorkflow"
            // Note: Other workflow types will be added when implemented
        };
        
        return codeBasedNames.Contains(definition.Name);
    }

    private DateTimeOffset? GetLastSynchronizedTime(WorkflowDefinition definition)
    {
        try
        {
            if (!string.IsNullOrEmpty(definition.StringData))
            {
                var doc = System.Text.Json.JsonDocument.Parse(definition.StringData);
                if (doc.RootElement.TryGetProperty("customProperties", out var customProps))
                {
                    if (customProps.TryGetProperty("lastSynchronized", out var lastSync))
                    {
                        if (DateTimeOffset.TryParse(lastSync.GetString(), out var syncTime))
                        {
                            return syncTime;
                        }
                    }
                }
            }
        }
        catch { }

        return null;
    }

    private async Task EnsureWorkflowMetadata(WorkflowDefinition workflow, CancellationToken cancellationToken)
    {
        bool needsUpdate = false;

        // Ensure display name is set in StringData (Elsa v3 pattern)
        var currentDisplayName = GetDisplayNameFromStringData(workflow);
        if (string.IsNullOrEmpty(currentDisplayName))
        {
            workflow.StringData = UpdateDisplayNameInStringData(workflow.StringData ?? "", FormatDisplayName(workflow.Name ?? ""));
            needsUpdate = true;
        }

        // Update synchronization metadata
        if (!string.IsNullOrEmpty(workflow.StringData))
        {
            try
            {
                var updatedData = AddSynchronizationMetadata(workflow.StringData, workflow);
                if (updatedData != workflow.StringData)
                {
                    workflow.StringData = updatedData;
                    needsUpdate = true;
                }
            }
            catch (Exception ex)
            {
                _logger.LogWarning(ex, "Could not update metadata for workflow {Name}", workflow.Name);
            }
        }

        if (needsUpdate)
        {
            await _workflowDefinitionStore.SaveAsync(workflow, cancellationToken);
            _logger.LogDebug("Updated metadata for workflow {Name}", workflow.Name);
        }
    }

    private string AddSynchronizationMetadata(string originalData, WorkflowDefinition workflow)
    {
        try
        {
            var doc = System.Text.Json.JsonDocument.Parse(originalData);
            var root = doc.RootElement.Clone();

            // Create new object with updated custom properties
            var customProperties = new Dictionary<string, object>();
            
            if (root.TryGetProperty("customProperties", out var existingProps))
            {
                foreach (var prop in existingProps.EnumerateObject())
                {
                    customProperties[prop.Name] = prop.Value.Clone();
                }
            }

            // Add synchronization metadata
            customProperties["lastSynchronized"] = DateTimeOffset.UtcNow;
            customProperties["syncVersion"] = 1;
            
            if (IsCodeBasedWorkflow(workflow))
            {
                customProperties["autoDiscovered"] = true;
                customProperties["category"] = "HSE";
                customProperties["module"] = "IncidentManagement";
            }

            // Recreate the JSON structure
            var newRoot = new Dictionary<string, object>();
            foreach (var prop in root.EnumerateObject())
            {
                if (prop.Name != "customProperties")
                {
                    newRoot[prop.Name] = prop.Value.Clone();
                }
            }
            newRoot["customProperties"] = customProperties;

            var options = new System.Text.Json.JsonSerializerOptions
            {
                WriteIndented = true,
                PropertyNamingPolicy = System.Text.Json.JsonNamingPolicy.CamelCase
            };

            return System.Text.Json.JsonSerializer.Serialize(newRoot, options);
        }
        catch
        {
            return originalData; // Return original if we can't parse/update
        }
    }

    private string FormatDisplayName(string name)
    {
        if (string.IsNullOrEmpty(name)) return "Unnamed Workflow";
        
        // Convert PascalCase to readable format
        var result = System.Text.RegularExpressions.Regex.Replace(name, 
            "([A-Z])", " $1", System.Text.RegularExpressions.RegexOptions.Compiled).Trim();
        
        return result;
    }

    private async Task<object> RegisterWorkflowAsync(Type workflowType)
    {
        // Create workflow instance
        var workflow = Activator.CreateInstance(workflowType) as IWorkflow;
        if (workflow == null)
        {
            throw new InvalidOperationException($"Could not create instance of workflow {workflowType.Name}");
        }

        // Note: In Elsa v3, we don't directly build workflows in registration
        // The workflow definition structure is managed through StringData JSON
        
        // Create a simple workflow definition - Elsa Studio expects specific format
        var workflowName = workflowType.Name;
        var definitionId = Guid.NewGuid().ToString();
        
        // Check if already exists using correct Elsa v3 API
        var filter = new WorkflowDefinitionFilter
        {
            Name = workflowName,
            VersionOptions = VersionOptions.Latest
        };
        var existingWorkflows = await _workflowDefinitionStore.FindManyAsync(filter, HttpContext.RequestAborted);
        var existing = existingWorkflows.FirstOrDefault();

        WorkflowDefinition definition;
        bool isUpdate = false;

        if (existing != null)
        {
            definition = existing;
            definition.Version += 1;
            isUpdate = true;
        }
        else
        {
            definition = new WorkflowDefinition
            {
                Id = Guid.NewGuid().ToString(),
                DefinitionId = definitionId,
                Name = workflowName,
                Version = 1,
                IsLatest = true,
                IsPublished = true,
                CreatedAt = DateTimeOffset.UtcNow
            };
        }

        // Set metadata - DisplayName and Description are stored in StringData for Elsa v3
        definition.IsReadonly = false;
        
        // Store as proper Elsa v3 workflow definition JSON
        var displayName = GetDisplayName(workflowType);
        var description = GetDescription(workflowType);
        
        definition.StringData = JsonSerializer.Serialize(new
        {
            id = definition.DefinitionId,
            name = workflowName,
            displayName = displayName,
            description = description,
            version = definition.Version,
            isPublished = true,
            isLatest = true,
            isReadonly = false,
            customProperties = new
            {
                category = "HSE",
                module = "IncidentManagement",
                autoDiscovered = true,
                lastSynchronized = DateTimeOffset.UtcNow,
                tags = GetWorkflowTags(workflowType)
            },
            root = new
            {
                type = "Elsa.Sequence",
                id = Guid.NewGuid().ToString(),
                name = "Root",
                displayName = displayName,
                description = description,
                activities = new object[] { } // Will be populated by actual workflow execution
            }
        }, new JsonSerializerOptions
        {
            WriteIndented = true,
            PropertyNamingPolicy = JsonNamingPolicy.CamelCase
        });

        await _workflowDefinitionStore.SaveAsync(definition, HttpContext.RequestAborted);

        _logger.LogInformation("Successfully {Action} workflow: {WorkflowName} v{Version}", 
            isUpdate ? "updated" : "registered", 
            workflowName, 
            definition.Version);

        return new
        {
            workflow = workflowName,
            status = "success",
            action = isUpdate ? "updated" : "created",
            version = definition.Version,
            definitionId = definition.DefinitionId
        };
    }

    private string GetDisplayName(Type workflowType)
    {
        return workflowType.Name switch
        {
            "IncidentProcessWorkflow" => "Incident Management Workflow",
            "TestWorkflow" => "Test Workflow",
            "SimpleIncidentWorkflow" => "Simple Incident Workflow",
            _ => workflowType.Name.Replace("Workflow", " Workflow")
        };
    }

    private string GetDescription(Type workflowType)
    {
        return workflowType.Name switch
        {
            "IncidentProcessWorkflow" => "Main workflow orchestrating the complete incident lifecycle from reporting to closure",
            "TestWorkflow" => "Simple test workflow for validation",
            "SimpleIncidentWorkflow" => "Simple incident workflow for testing",
            _ => $"Workflow for {workflowType.Name}"
        };
    }

    private string[] GetWorkflowTags(Type workflowType)
    {
        var tags = new List<string> { "hse", "auto-discovered" };

        if (workflowType.Name.Contains("Incident")) tags.Add("incident-management");
        if (workflowType.Name.Contains("Escalation")) tags.Add("escalation");
        if (workflowType.Name.Contains("Investigation")) tags.Add("investigation");
        if (workflowType.Name.Contains("Review")) tags.Add("review");

        return tags.ToArray();
    }

    private string GetDisplayNameFromStringData(WorkflowDefinition definition)
    {
        try
        {
            if (!string.IsNullOrEmpty(definition.StringData))
            {
                var doc = JsonDocument.Parse(definition.StringData);
                if (doc.RootElement.TryGetProperty("displayName", out var displayName))
                {
                    return displayName.GetString() ?? "";
                }
            }
        }
        catch { }

        return "";
    }

    private string UpdateDisplayNameInStringData(string originalData, string newDisplayName)
    {
        try
        {
            if (string.IsNullOrEmpty(originalData))
            {
                return JsonSerializer.Serialize(new { displayName = newDisplayName });
            }

            var doc = JsonDocument.Parse(originalData);
            var root = new Dictionary<string, object>();

            foreach (var prop in doc.RootElement.EnumerateObject())
            {
                if (prop.Name != "displayName")
                {
                    root[prop.Name] = prop.Value.Clone();
                }
            }

            root["displayName"] = newDisplayName;

            return JsonSerializer.Serialize(root, new JsonSerializerOptions
            {
                WriteIndented = true,
                PropertyNamingPolicy = JsonNamingPolicy.CamelCase
            });
        }
        catch
        {
            return originalData;
        }
    }
}