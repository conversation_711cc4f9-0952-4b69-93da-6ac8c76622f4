import React, { useState } from 'react';
import {
  Box,
  Paper,
  Tabs,
  Tab,
  Typography,
  Alert,
  AlertTitle,
} from '@mui/material';
import {
  Dashboard as DashboardIcon,
  AccountTree as WorkflowIcon,
  Speed as PerformanceIcon,
  CloudQueue as IntegrationsIcon,
} from '@mui/icons-material';
import SystemMonitoringDashboard from './SystemMonitoringDashboard';
import WorkflowMonitoringDashboard from './WorkflowMonitoringDashboard';
import IntegrationsMonitoringDashboard from './IntegrationsMonitoringDashboard';
import { useAuth } from '../../hooks/useAuth';

interface TabPanelProps {
  children?: React.ReactNode;
  index: number;
  value: number;
}

function TabPanel(props: TabPanelProps) {
  const { children, value, index, ...other } = props;

  return (
    <div
      role="tabpanel"
      hidden={value !== index}
      id={`monitoring-tabpanel-${index}`}
      aria-labelledby={`monitoring-tab-${index}`}
      {...other}
    >
      {value === index && <Box sx={{ py: 3 }}>{children}</Box>}
    </div>
  );
}

const MonitoringDashboard: React.FC = () => {
  const { user } = useAuth();
  const [activeTab, setActiveTab] = useState(0);

  const handleTabChange = (event: React.SyntheticEvent, newValue: number) => {
    setActiveTab(newValue);
  };

  // Check if user has monitoring permissions
  const canViewMonitoring = user?.roles?.some(role => 
    ['SuperAdmin', 'Developer', 'HSEManager'].includes(role)
  );

  if (!canViewMonitoring) {
    return (
      <Box sx={{ p: 3 }}>
        <Alert severity="error">
          <AlertTitle>Access Denied</AlertTitle>
          You do not have permission to view monitoring dashboards. Please contact your system administrator.
        </Alert>
      </Box>
    );
  }

  return (
    <Box sx={{ width: '100%' }}>
      <Typography variant="h3" sx={{ mb: 3 }}>
        System Monitoring & Analytics
      </Typography>
      
      <Paper sx={{ width: '100%' }}>
        <Tabs
          value={activeTab}
          onChange={handleTabChange}
          aria-label="monitoring dashboard tabs"
          sx={{ borderBottom: 1, borderColor: 'divider' }}
        >
          <Tab 
            icon={<DashboardIcon />} 
            iconPosition="start" 
            label="System Overview" 
          />
          <Tab 
            icon={<WorkflowIcon />} 
            iconPosition="start" 
            label="Workflow Monitoring" 
          />
          <Tab 
            icon={<IntegrationsIcon />} 
            iconPosition="start" 
            label="External Integrations" 
          />
        </Tabs>

        <Box sx={{ p: 3 }}>
          <TabPanel value={activeTab} index={0}>
            <SystemMonitoringDashboard />
          </TabPanel>
          
          <TabPanel value={activeTab} index={1}>
            <WorkflowMonitoringDashboard />
          </TabPanel>
          
          <TabPanel value={activeTab} index={2}>
            <IntegrationsMonitoringDashboard />
          </TabPanel>
        </Box>
      </Paper>
    </Box>
  );
};

export default MonitoringDashboard;