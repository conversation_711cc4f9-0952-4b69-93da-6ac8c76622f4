
class MudThrottledEventManager{constructor(){this.mapper={};}
subscribe(eventName,elementId,projection,throotleInterval,key,properties,dotnetReference){const handlerRef=this.throttleEventHandler.bind(this,key);let elem=document.getElementById(elementId);if(elem){elem.addEventListener(eventName,handlerRef,false);let projector=null;if(projection){const parts=projection.split('.');let functionPointer=window;let functionReferenceFound=true;if(parts.length==0||parts.length==1){functionPointer=functionPointer[projection];}
else{for(let i=0;i<parts.length;i++){functionPointer=functionPointer[parts[i]];if(!functionPointer){functionReferenceFound=false;break;}}}
if(functionReferenceFound===true){projector=functionPointer;}}
this.mapper[key]={eventName:eventName,handler:handlerRef,delay:throotleInterval,timerId:-1,reference:dotnetReference,elementId:elementId,properties:properties,projection:projector,};}}
subscribeGlobal(eventName,throotleInterval,key,properties,dotnetReference){let handlerRef=throotleInterval>0?this.throttleEventHandler.bind(this,key):this.eventHandler.bind(this,key);document.addEventListener(eventName,handlerRef,false);this.mapper[key]={eventName:eventName,handler:handlerRef,delay:throotleInterval,timerId:-1,reference:dotnetReference,elementId:document,properties:properties,projection:null,};}
throttleEventHandler(key,event){const entry=this.mapper[key];if(!entry){return;}
clearTimeout(entry.timerId);entry.timerId=window.setTimeout(this.eventHandler.bind(this,key,event),entry.delay);}
eventHandler(key,event){const entry=this.mapper[key];if(!entry){return;}
var elem=document.getElementById(entry.elementId);if(elem!=event.srcElement&&entry.elementId!=document){return;}
const eventEntry={};for(var i=0;i<entry.properties.length;i++){const propertyName=entry.properties[i];const propertyValue=event[entry.properties[i]];if(propertyValue==null){eventEntry[propertyName]=propertyValue;}
else if(["touchstart","touchmove","touchend","touchcancel"].includes(event.type)&&["touches","changedTouches","targetTouches"].includes(propertyName)){eventEntry[propertyName]=Array.from(propertyValue,touchPoint=>({identifier:touchPoint.identifier,screenX:touchPoint.screenX,screenY:touchPoint.screenY,clientX:touchPoint.clientX,clientY:touchPoint.clientY,pageX:touchPoint.pageX,pageY:touchPoint.pageY,}));}
else{eventEntry[propertyName]=propertyValue;}}
if(entry.projection){if(typeof entry.projection==="function"){entry.projection.apply(null,[eventEntry,event]);}}
entry.reference.invokeMethodAsync('OnEventOccur',key,JSON.stringify(eventEntry));}
unsubscribe(key){const entry=this.mapper[key];if(!entry){return;}
entry.reference=null;if(document==entry.elementId){document.removeEventListener(entry.eventName,entry.handler,false);}else{const elem=document.getElementById(entry.elementId);if(elem){elem.removeEventListener(entry.eventName,entry.handler,false);}}
delete this.mapper[key];}};window.mudThrottledEventManager=new MudThrottledEventManager();window.mudEventProjections={correctOffset:function(eventEntry,event){var target=event.target.getBoundingClientRect();eventEntry.offsetX=event.clientX-target.x;eventEntry.offsetY=event.clientY-target.y;}};class MudElementReference{constructor(){this.listenerId=0;this.eventListeners={};}
focus(element){if(element)
{element.focus();}}
blur(element){if(element){element.blur();}}
focusFirst(element,skip=0,min=0){if(element)
{let tabbables=getTabbableElements(element);if(tabbables.length<=min)
element.focus();else
tabbables[skip].focus();}}
focusLast(element,skip=0,min=0){if(element)
{let tabbables=getTabbableElements(element);if(tabbables.length<=min)
element.focus();else
tabbables[tabbables.length-skip-1].focus();}}
saveFocus(element){if(element)
{element['mudblazor_savedFocus']=document.activeElement;}}
restoreFocus(element){if(element)
{let previous=element['mudblazor_savedFocus'];delete element['mudblazor_savedFocus']
if(previous)
previous.focus();}}
selectRange(element,pos1,pos2){if(element)
{if(element.createTextRange){let selRange=element.createTextRange();selRange.collapse(true);selRange.moveStart('character',pos1);selRange.moveEnd('character',pos2);selRange.select();}else if(element.setSelectionRange){element.setSelectionRange(pos1,pos2);}else if(element.selectionStart){element.selectionStart=pos1;element.selectionEnd=pos2;}
element.focus();}}
select(element){if(element)
{element.select();}}
getBoundingClientRect(element){if(!element)return;var rect=JSON.parse(JSON.stringify(element.getBoundingClientRect()));rect.scrollY=window.scrollY||document.documentElement.scrollTop;rect.scrollX=window.scrollX||document.documentElement.scrollLeft;rect.windowHeight=window.innerHeight;rect.windowWidth=window.innerWidth;return rect;}
changeCss(element,css){if(element)
{element.className=css;}}
removeEventListener(element,event,eventId){element.removeEventListener(event,this.eventListeners[eventId]);delete this.eventListeners[eventId];}
addDefaultPreventingHandler(element,eventName){let listener=function(e){if(!e.defaultPrevented){e.preventDefault();}};element.addEventListener(eventName,listener,{passive:false});this.eventListeners[++this.listenerId]=listener;return this.listenerId;}
removeDefaultPreventingHandler(element,eventName,listenerId){this.removeEventListener(element,eventName,listenerId);}
addDefaultPreventingHandlers(element,eventNames){let listeners=[];for(const eventName of eventNames){let listenerId=this.addDefaultPreventingHandler(element,eventName);listeners.push(listenerId);}
return listeners;}
removeDefaultPreventingHandlers(element,eventNames,listenerIds){for(let index=0;index<eventNames.length;++index){const eventName=eventNames[index];const listenerId=listenerIds[index];this.removeDefaultPreventingHandler(element,eventName,listenerId);}}
addOnBlurEvent(element,dotNetReference){element._mudBlurHandler=function(e){if(!element)return;e.preventDefault();element.blur();if(dotNetReference){dotNetReference.invokeMethodAsync('CallOnBlurredAsync');}
else{console.error("No dotNetReference found for iosKeyboardFocus");}}
element.addEventListener('blur',element._mudBlurHandler);}
removeOnBlurEvent(element,dotnetRef){if(!element||!element._mudBlurHandler)return;element.removeEventListener('blur',element._mudBlurHandler);delete element._mudBlurHandler;}};window.mudElementRef=new MudElementReference();const darkThemeMediaQuery=window.matchMedia("(prefers-color-scheme: dark)");window.darkModeChange=()=>{return darkThemeMediaQuery.matches;};function darkModeChangeListener(e){dotNetHelperTheme.invokeMethodAsync('SystemPreferenceChanged',e.matches);}
function watchDarkThemeMedia(dotNetHelper){dotNetHelperTheme=dotNetHelper;darkThemeMediaQuery.addEventListener('change',darkModeChangeListener);}
function stopWatchingDarkThemeMedia(){darkThemeMediaQuery.removeEventListener('change',darkModeChangeListener);}
class MudScrollManager{scrollToYear(elementId,offset){let element=document.getElementById(elementId);if(element){element.parentNode.scrollTop=element.offsetTop-element.parentNode.offsetTop-element.scrollHeight*3;}}
scrollToListItem(elementId){let element=document.getElementById(elementId);if(element){let parent=element.parentElement;if(parent){parent.scrollTop=element.offsetTop;}}}
scrollTo(selector,left,top,behavior){let element=document.querySelector(selector)||document.documentElement;element.scrollTo({left,top,behavior});}
scrollIntoView(selector,behavior){let element=document.querySelector(selector)||document.documentElement;if(element)
element.scrollIntoView({behavior,block:'center',inline:'start'});}
scrollToBottom(selector,behavior){let element=document.querySelector(selector);if(element){element.scrollTo({top:element.scrollHeight,behavior:behavior});}else{window.scrollTo({top:document.body.scrollHeight,behavior:behavior});}}
lockScroll(selector,lockclass){let element=document.querySelector(selector)||document.body;let hasScrollBar=window.innerWidth>document.body.clientWidth;if(hasScrollBar){element.classList.add(lockclass);}else{let lockClassNoPadding=lockclass+"-no-padding";element.classList.add(lockClassNoPadding);}}
unlockScroll(selector,lockclass){let element=document.querySelector(selector)||document.body;element.classList.remove(lockclass);element.classList.remove(lockclass+"-no-padding");}};window.mudScrollManager=new MudScrollManager();window.getTabbableElements=(element)=>{return element.querySelectorAll("a[href]:not([tabindex='-1']),"+"area[href]:not([tabindex='-1']),"+"button:not([disabled]):not([tabindex='-1']),"+"input:not([disabled]):not([tabindex='-1']):not([type='hidden']),"+"select:not([disabled]):not([tabindex='-1']),"+"textarea:not([disabled]):not([tabindex='-1']),"+"iframe:not([tabindex='-1']),"+"details:not([tabindex='-1']),"+"[tabindex]:not([tabindex='-1']),"+"[contentEditable=true]:not([tabindex='-1'])");};window.serializeParameter=(data,spec)=>{if(typeof data=="undefined"||data===null){return null;}
if(typeof data==="number"||typeof data==="string"||typeof data=="boolean"){return data;}
let res=(Array.isArray(data))?[]:{};if(!spec){spec="*";}
for(let i in data){let currentMember=data[i];if(typeof currentMember==='function'||currentMember===null){continue;}
let currentMemberSpec;if(spec!="*"){currentMemberSpec=Array.isArray(data)?spec:spec[i];if(!currentMemberSpec){continue;}}else{currentMemberSpec="*"}
if(typeof currentMember==='object'){if(Array.isArray(currentMember)||currentMember.length){res[i]=[];for(let j=0;j<currentMember.length;j++){const arrayItem=currentMember[j];if(typeof arrayItem==='object'){res[i].push(this.serializeParameter(arrayItem,currentMemberSpec));}else{res[i].push(arrayItem);}}}else{if(currentMember.length===0){res[i]=[];}else{res[i]=this.serializeParameter(currentMember,currentMemberSpec);}}}else{if(currentMember===Infinity){currentMember="Infinity";}
if(currentMember!==null){res[i]=currentMember;}}}
return res;};window.mudGetSvgBBox=(svgElement)=>{if(svgElement==null)return null;const bbox=svgElement.getBBox();return{x:bbox.x,y:bbox.y,width:bbox.width,height:bbox.height};};window.mudObserveElementSize=(dotNetReference,element,functionName='OnElementSizeChanged',debounceMillis=200)=>{if(!element)return;let lastNotifiedTime=0;let scheduledCall=null;const throttledNotify=(width,height)=>{const timestamp=Date.now();const timeSinceLast=timestamp-lastNotifiedTime;if(timeSinceLast>=debounceMillis){lastNotifiedTime=timestamp;try{dotNetReference.invokeMethodAsync(functionName,{width,height,timestamp});}
catch(error){this.logger("[MudBlazor] Error in mudObserveElementSize:",{error});}}else{if(scheduledCall!==null){clearTimeout(scheduledCall);}
scheduledCall=setTimeout(()=>{lastNotifiedTime=Date.now();scheduledCall=null;try{dotNetReference.invokeMethodAsync(functionName,{width,height,timestamp});}
catch(error){this.logger("[MudBlazor] Error in mudObserveElementSize:",{error});}},debounceMillis-timeSinceLast);}};const resizeObserver=new ResizeObserver(entries=>{if(element.isConnected===false){return;}
let width=element.clientWidth;let height=element.clientHeight;for(const entry of entries){width=entry.contentRect.width;height=entry.contentRect.height;}
width=Math.floor(width);height=Math.floor(height);throttledNotify(width,height);});resizeObserver.observe(element);let mutationObserver=null;const parent=element.parentNode;if(parent){mutationObserver=new MutationObserver(mutations=>{for(const mutation of mutations){for(const removedNode of mutation.removedNodes){if(removedNode===element){cleanup();}}}});mutationObserver.observe(parent,{childList:true});}
function cleanup(){resizeObserver.disconnect();if(mutationObserver){mutationObserver.disconnect();}
if(scheduledCall!==null){clearTimeout(scheduledCall);}}
return{width:element.clientWidth,height:element.clientHeight};};window.mudDragAndDrop={initDropZone:(id)=>{const elem=document.getElementById(id);elem.addEventListener('dragover',()=>event.preventDefault());elem.addEventListener('dragstart',()=>event.dataTransfer.setData('',event.target.id));},makeDropZonesNotRelative:()=>{var firstDropItems=Array.from(document.getElementsByClassName('mud-drop-item')).filter(x=>x.getAttribute('index')=="-1");for(let dropItem of firstDropItems){dropItem.style.position='static';}
const dropZones=document.getElementsByClassName('mud-drop-zone');for(let dropZone of dropZones){dropZone.style.position='unset';}},getDropZoneIdentifierOnPosition:(x,y)=>{const elems=document.elementsFromPoint(x,y);const dropZones=elems.filter(e=>e.classList.contains('mud-drop-zone'))
const dropZone=dropZones[0];if(dropZone){return dropZone.getAttribute('identifier')||"";}
return"";},getDropIndexOnPosition:(x,y,id)=>{const elems=document.elementsFromPoint(x,y);const dropItems=elems.filter(e=>e.classList.contains('mud-drop-item')&&e.id!=id)
const dropItem=dropItems[0];if(dropItem){return dropItem.getAttribute('index')||"";}
return"";},makeDropZonesRelative:()=>{const dropZones=document.getElementsByClassName('mud-drop-zone');for(let dropZone of dropZones){dropZone.style.position='relative';}
var firstDropItems=Array.from(document.getElementsByClassName('mud-drop-item')).filter(x=>x.getAttribute('index')=="-1");for(let dropItem of firstDropItems){dropItem.style.position='relative';}},moveItemByDifference:(id,dx,dy)=>{const elem=document.getElementById(id);var tx=(parseFloat(elem.getAttribute('data-x'))||0)+dx;var ty=(parseFloat(elem.getAttribute('data-y'))||0)+dy;elem.style.webkitTransform=elem.style.transform='translate3d('+tx+'px, '+ty+'px, 10px)';elem.setAttribute('data-x',tx);elem.setAttribute('data-y',ty);},resetItem:(id)=>{const elem=document.getElementById(id);if(elem){elem.style.webkitTransform=elem.style.transform='';elem.setAttribute('data-x',0);elem.setAttribute('data-y',0);}}};class MudWindow{copyToClipboard(text){navigator.clipboard.writeText(text);}
changeCssById(id,css){var element=document.getElementById(id);if(element){element.className=css;}}
updateStyleProperty(elementId,propertyName,value){const element=document.getElementById(elementId);if(element){element.style.setProperty(propertyName,value);}}
changeGlobalCssVariable(name,newValue){document.documentElement.style.setProperty(name,newValue);}
open(args){window.open(args);}}
window.mudWindow=new MudWindow();class MudJsEventFactory{connect(dotNetRef,elementId,options){if(!elementId)
throw"[MudBlazor | JsEvent] elementId: expected element id!";var element=document.getElementById(elementId);if(!element)
throw"[MudBlazor | JsEvent] no element found for id: "+elementId;if(!element.mudJsEvent)
element.mudJsEvent=new MudJsEvent(dotNetRef,options);element.mudJsEvent.connect(element);}
disconnect(elementId){var element=document.getElementById(elementId);if(!element||!element.mudJsEvent)
return;element.mudJsEvent.disconnect();}
subscribe(elementId,eventName){if(!elementId)
throw"[MudBlazor | JsEvent] elementId: expected element id!";var element=document.getElementById(elementId);if(!element)
throw"[MudBlazor | JsEvent] no element found for id: "+elementId;if(!element.mudJsEvent)
throw"[MudBlazor | JsEvent] please connect before subscribing"
element.mudJsEvent.subscribe(eventName);}
unsubscribe(elementId,eventName){var element=document.getElementById(elementId);if(!element||!element.mudJsEvent)
return;element.mudJsEvent.unsubscribe(element,eventName);}}
window.mudJsEvent=new MudJsEventFactory();class MudJsEvent{constructor(dotNetRef,options){this._dotNetRef=dotNetRef;this._options=options||{};this.logger=options.enableLogging?console.log:(message)=>{};this.logger('[MudBlazor | JsEvent] Initialized',{options});this._subscribedEvents={};}
connect(element){if(!this._options)
return;if(!this._options.targetClass)
throw"_options.targetClass: css class name expected";if(this._observer){return;}
var targetClass=this._options.targetClass;this.logger('[MudBlazor | JsEvent] Start observing DOM of element for changes to child with class ',{element,targetClass});this._element=element;this._observer=new MutationObserver(this.onDomChanged);this._observer.mudJsEvent=this;this._observer.observe(this._element,{attributes:false,childList:true,subtree:true});this._observedChildren=[];}
disconnect(){if(!this._observer)
return;this.logger('[MudBlazor | JsEvent] disconnect mutation observer and event handler ');this._observer.disconnect();this._observer=null;for(const child of this._observedChildren)
this.detachHandlers(child);}
subscribe(eventName){if(this._subscribedEvents[eventName]){return;}
var element=this._element;var targetClass=this._options.targetClass;this._subscribedEvents[eventName]=true;for(const child of element.getElementsByClassName(targetClass)){this.attachHandlers(child);}}
unsubscribe(eventName){if(!this._observer)
return;this.logger('[MudBlazor | JsEvent] unsubscribe event handler '+eventName);this._observer.disconnect();this._observer=null;this._subscribedEvents[eventName]=false;for(const child of this._observedChildren){this.detachHandler(child,eventName);}}
attachHandlers(child){child.mudJsEvent=this;for(var eventName of Object.getOwnPropertyNames(this._subscribedEvents)){if(!this._subscribedEvents[eventName])
continue;this.logger('[MudBlazor | JsEvent] attaching event '+eventName,child);child.addEventListener(eventName,this.eventHandler);}
if(this._observedChildren.indexOf(child)<0)
this._observedChildren.push(child);}
detachHandler(child,eventName){this.logger('[MudBlazor | JsEvent] detaching handler '+eventName,child);child.removeEventListener(eventName,this.eventHandler);}
detachHandlers(child){this.logger('[MudBlazor | JsEvent] detaching handlers ',child);for(var eventName of Object.getOwnPropertyNames(this._subscribedEvents)){if(!this._subscribedEvents[eventName])
continue;child.removeEventListener(eventName,this.eventHandler);}
this._observedChildren=this._observedChildren.filter(x=>x!==child);}
onDomChanged(mutationsList,observer){var self=this.mudJsEvent;var targetClass=self._options.targetClass;for(const mutation of mutationsList){for(const element of mutation.addedNodes){if(element.classList&&element.classList.contains(targetClass)){if(!self._options.TagName||element.tagName==self._options.TagName)
self.attachHandlers(element);}}
for(const element of mutation.removedNodes){if(element.classList&&element.classList.contains(targetClass)){if(!self._options.tagName||element.tagName==self._options.tagName)
self.detachHandlers(element);}}}}
eventHandler(e){var self=this.mudJsEvent;var eventName=e.type;self.logger('[MudBlazor | JsEvent] "'+eventName+'"',e);self["on"+eventName](self,e);}
onkeyup(self,e){const caretPosition=e.target.selectionStart;const invoke=self._subscribedEvents["keyup"];if(invoke){self._dotNetRef.invokeMethodAsync('OnCaretPositionChanged',caretPosition);}}
onclick(self,e){const caretPosition=e.target.selectionStart;const invoke=self._subscribedEvents["click"];if(invoke){self._dotNetRef.invokeMethodAsync('OnCaretPositionChanged',caretPosition);}}
onpaste(self,e){const invoke=self._subscribedEvents["paste"];if(invoke){e.preventDefault();e.stopPropagation();const clipboardData=((e.originalEvent||e).clipboardData||window.clipboardData);if(!clipboardData){self.logger('[MudBlazor | JsEvent] clipboardData is null',e);return;}
const text=clipboardData.getData('text/plain');self._dotNetRef.invokeMethodAsync('OnPaste',text);}}
onselect(self,e){const invoke=self._subscribedEvents["select"];if(invoke){const start=e.target.selectionStart;const end=e.target.selectionEnd;if(start===end)
return;self._dotNetRef.invokeMethodAsync('OnSelect',start,end);}}}
window.mudTimePicker={initPointerEvents:(clock,dotNetHelper)=>{let isPointerDown=false;const startHandler=(event)=>{if(event.button!==0){return;}
isPointerDown=true;event.target.releasePointerCapture(event.pointerId);if(event.target.classList.contains('mud-hour')||event.target.classList.contains('mud-minute')){let attributeValue=event.target.getAttribute('data-stick-value');let stickValue=attributeValue?parseInt(attributeValue):-1;dotNetHelper.invokeMethodAsync('SelectTimeFromStick',stickValue,false);}
event.preventDefault();};const endHandler=(event)=>{if(event.button!==0){return;}
isPointerDown=false;if(event.target.classList.contains('mud-hour')||event.target.classList.contains('mud-minute')){let attributeValue=event.target.getAttribute('data-stick-value');let stickValue=attributeValue?parseInt(attributeValue):-1;dotNetHelper.invokeMethodAsync('OnStickClick',stickValue);}
event.preventDefault();};const moveHandler=(event)=>{if(!isPointerDown||(!event.target.classList.contains('mud-hour')&&!event.target.classList.contains('mud-minute'))){return;}
let attributeValue=event.target.getAttribute('data-stick-value');let stickValue=attributeValue?parseInt(attributeValue):-1;dotNetHelper.invokeMethodAsync('SelectTimeFromStick',stickValue,true);event.preventDefault();};clock.addEventListener('pointerdown',startHandler);clock.addEventListener('pointerup',endHandler);clock.addEventListener('pointercancel',endHandler);clock.addEventListener('pointerover',moveHandler);clock.destroy=()=>{clock.removeEventListener('pointerdown',startHandler);clock.removeEventListener('pointerup',endHandler);clock.removeEventListener('pointercancel',endHandler);clock.removeEventListener('pointerover',moveHandler);};},destroyPointerEvents:(container)=>{if(container==null){return;}
if(typeof container.destroy==='function'){container.destroy();}}};class MudResizeObserverFactory{constructor(){this._maps={};}
connect(id,dotNetRef,elements,elementIds,options){var existingEntry=this._maps[id];if(!existingEntry){var observer=new MudResizeObserver(dotNetRef,options);this._maps[id]=observer;}
var result=this._maps[id].connect(elements,elementIds);return result;}
disconnect(id,element){var existingEntry=this._maps[id];if(existingEntry){existingEntry.disconnect(element);}}
cancelListener(id){var existingEntry=this._maps[id];if(existingEntry){existingEntry.cancelListener();delete this._maps[id];}}}
class MudResizeObserver{constructor(dotNetRef,options){this.logger=options.enableLogging?console.log:(message)=>{};this.options=options;this._dotNetRef=dotNetRef
var delay=(this.options||{}).reportRate||200;this.throttleResizeHandlerId=-1;var observervedElements=[];this._observervedElements=observervedElements;this.logger('[MudBlazor | ResizeObserver] Observer initialized');this._resizeObserver=new ResizeObserver(entries=>{var changes=[];this.logger('[MudBlazor | ResizeObserver] changes detected');for(let entry of entries){var target=entry.target;var affectedObservedElement=observervedElements.find((x)=>x.element==target);if(affectedObservedElement){var size=entry.target.getBoundingClientRect();if(affectedObservedElement.isInitialized==true){changes.push({id:affectedObservedElement.id,size:size});}
else{affectedObservedElement.isInitialized=true;}}}
if(changes.length>0){if(this.throttleResizeHandlerId>=0){clearTimeout(this.throttleResizeHandlerId);}
this.throttleResizeHandlerId=window.setTimeout(this.resizeHandler.bind(this,changes),delay);}});}
resizeHandler(changes){try{this.logger("[MudBlazor | ResizeObserver] OnSizeChanged handler invoked");this._dotNetRef.invokeMethodAsync("OnSizeChanged",changes);}catch(error){this.logger("[MudBlazor | ResizeObserver] Error in OnSizeChanged handler:",{error});}}
connect(elements,ids){var result=[];this.logger('[MudBlazor | ResizeObserver] Start observing elements...');for(var i=0;i<elements.length;i++){var newEntry={element:elements[i],id:ids[i],isInitialized:false,};this.logger("[MudBlazor | ResizeObserver] Start observing element:",{newEntry});result.push(elements[i].getBoundingClientRect());this._observervedElements.push(newEntry);this._resizeObserver.observe(elements[i]);}
return result;}
disconnect(elementId){this.logger('[MudBlazor | ResizeObserver] Try to unobserve element with id',{elementId});var affectedObservedElement=this._observervedElements.find((x)=>x.id==elementId);if(affectedObservedElement){var element=affectedObservedElement.element;this._resizeObserver.unobserve(element);this.logger('[MudBlazor | ResizeObserver] Element found. Ubobserving size changes of element',{element});var index=this._observervedElements.indexOf(affectedObservedElement);this._observervedElements.splice(index,1);}}
cancelListener(){this.logger('[MudBlazor | ResizeObserver] Closing ResizeObserver. Detaching all observed elements');this._resizeObserver.disconnect();this._dotNetRef=undefined;}}
window.mudResizeObserver=new MudResizeObserverFactory();window.mudpopoverHelper={debounce:function(func,wait){let timeout;return function executedFunction(...args){const later=()=>{clearTimeout(timeout);func(...args);};clearTimeout(timeout);timeout=setTimeout(later,wait);};},rafThrottle:function(func){let ticking=false;return function(...args){if(!ticking){window.requestAnimationFrame(()=>{func.apply(this,args);ticking=false;});ticking=true;}};},calculatePopoverPosition:function(list,boundingRect,selfRect){let top=0;let left=0;if(list.indexOf('mud-popover-anchor-top-left')>=0){left=boundingRect.left;top=boundingRect.top;}else if(list.indexOf('mud-popover-anchor-top-center')>=0){left=boundingRect.left+boundingRect.width/2;top=boundingRect.top;}else if(list.indexOf('mud-popover-anchor-top-right')>=0){left=boundingRect.left+boundingRect.width;top=boundingRect.top;}else if(list.indexOf('mud-popover-anchor-center-left')>=0){left=boundingRect.left;top=boundingRect.top+boundingRect.height/2;}else if(list.indexOf('mud-popover-anchor-center-center')>=0){left=boundingRect.left+boundingRect.width/2;top=boundingRect.top+boundingRect.height/2;}else if(list.indexOf('mud-popover-anchor-center-right')>=0){left=boundingRect.left+boundingRect.width;top=boundingRect.top+boundingRect.height/2;}else if(list.indexOf('mud-popover-anchor-bottom-left')>=0){left=boundingRect.left;top=boundingRect.top+boundingRect.height;}else if(list.indexOf('mud-popover-anchor-bottom-center')>=0){left=boundingRect.left+boundingRect.width/2;top=boundingRect.top+boundingRect.height;}else if(list.indexOf('mud-popover-anchor-bottom-right')>=0){left=boundingRect.left+boundingRect.width;top=boundingRect.top+boundingRect.height;}
let offsetX=0;let offsetY=0;if(list.indexOf('mud-popover-top-left')>=0){offsetX=0;offsetY=0;}else if(list.indexOf('mud-popover-top-center')>=0){offsetX=-selfRect.width/2;offsetY=0;}else if(list.indexOf('mud-popover-top-right')>=0){offsetX=-selfRect.width;offsetY=0;}
else if(list.indexOf('mud-popover-center-left')>=0){offsetX=0;offsetY=-selfRect.height/2;}else if(list.indexOf('mud-popover-center-center')>=0){offsetX=-selfRect.width/2;offsetY=-selfRect.height/2;}else if(list.indexOf('mud-popover-center-right')>=0){offsetX=-selfRect.width;offsetY=-selfRect.height/2;}
else if(list.indexOf('mud-popover-bottom-left')>=0){offsetX=0;offsetY=-selfRect.height;}else if(list.indexOf('mud-popover-bottom-center')>=0){offsetX=-selfRect.width/2;offsetY=-selfRect.height;}else if(list.indexOf('mud-popover-bottom-right')>=0){offsetX=-selfRect.width;offsetY=-selfRect.height;}
return{top:top,left:left,offsetX:offsetX,offsetY:offsetY};},flipClassReplacements:{'top':{'mud-popover-top-left':'mud-popover-bottom-left','mud-popover-top-center':'mud-popover-bottom-center','mud-popover-anchor-bottom-center':'mud-popover-anchor-top-center','mud-popover-top-right':'mud-popover-bottom-right',},'left':{'mud-popover-top-left':'mud-popover-top-right','mud-popover-center-left':'mud-popover-center-right','mud-popover-anchor-center-right':'mud-popover-anchor-center-left','mud-popover-bottom-left':'mud-popover-bottom-right',},'right':{'mud-popover-top-right':'mud-popover-top-left','mud-popover-center-right':'mud-popover-center-left','mud-popover-anchor-center-left':'mud-popover-anchor-center-right','mud-popover-bottom-right':'mud-popover-bottom-left',},'bottom':{'mud-popover-bottom-left':'mud-popover-top-left','mud-popover-bottom-center':'mud-popover-top-center','mud-popover-anchor-top-center':'mud-popover-anchor-bottom-center','mud-popover-bottom-right':'mud-popover-top-right',},'top-and-left':{'mud-popover-top-left':'mud-popover-bottom-right',},'top-and-right':{'mud-popover-top-right':'mud-popover-bottom-left',},'bottom-and-left':{'mud-popover-bottom-left':'mud-popover-top-right',},'bottom-and-right':{'mud-popover-bottom-right':'mud-popover-top-left',},},flipMargin:0,basePopoverZIndex:parseInt(getComputedStyle(document.documentElement).getPropertyValue('--mud-zindex-popover'))||1200,baseTooltipZIndex:parseInt(getComputedStyle(document.documentElement).getPropertyValue('--mud-zindex-tooltip'))||1600,getPositionForFlippedPopver:function(inputArray,selector,boundingRect,selfRect){const classList=[];for(var i=0;i<inputArray.length;i++){const item=inputArray[i];const replacments=window.mudpopoverHelper.flipClassReplacements[selector][item];if(replacments){classList.push(replacments);}
else{classList.push(item);}}
return window.mudpopoverHelper.calculatePopoverPosition(classList,boundingRect,selfRect);},placePopover:function(popoverNode,classSelector){if(popoverNode&&popoverNode.parentNode){const id=popoverNode.id.substr(8);const popoverContentNode=document.getElementById('popovercontent-'+id);if(!popoverContentNode){return;}
const classList=popoverContentNode.classList;if(classList.contains('mud-popover-open')==false){return;}
if(classSelector){if(classList.contains(classSelector)==false){return;}}
let boundingRect=popoverNode.parentNode.getBoundingClientRect();popoverContentNode.style['max-width']='none';popoverContentNode.style['min-width']='none';if(classList.contains('mud-popover-relative-width')){popoverContentNode.style['max-width']=(boundingRect.width)+'px';}
else if(classList.contains('mud-popover-adaptive-width')){popoverContentNode.style['min-width']=(boundingRect.width)+'px';}
const selfRect=popoverContentNode.getBoundingClientRect();const classListArray=Array.from(classList);const postion=window.mudpopoverHelper.calculatePopoverPosition(classListArray,boundingRect,selfRect);let left=postion.left;let top=postion.top;let offsetX=postion.offsetX;let offsetY=postion.offsetY;if(classList.contains('mud-popover-position-override')){left=parseInt(popoverContentNode.style['left'])||left;top=parseInt(popoverContentNode.style['top'])||top;offsetX=0;offsetY=0;boundingRect={left:left,top:top,right:left+selfRect.width,bottom:top+selfRect.height,width:selfRect.width,height:selfRect.height};}
if(classList.contains('mud-popover-overflow-flip-onopen')||classList.contains('mud-popover-overflow-flip-always')){const appBarElements=document.getElementsByClassName("mud-appbar mud-appbar-fixed-top");let appBarOffset=0;if(appBarElements.length>0){appBarOffset=appBarElements[0].getBoundingClientRect().height;}
const graceMargin=window.mudpopoverHelper.flipMargin;const deltaToLeft=left+offsetX;const deltaToRight=window.innerWidth-left-selfRect.width;const deltaTop=top-selfRect.height-appBarOffset;const spaceToTop=top-appBarOffset;const deltaBottom=window.innerHeight-top-selfRect.height;let selector=popoverContentNode.mudPopoverFliped;if(!selector){if(classList.contains('mud-popover-top-left')){if(deltaBottom<graceMargin&&deltaToRight<graceMargin&&spaceToTop>=selfRect.height&&deltaToLeft>=selfRect.width){selector='top-and-left';}else if(deltaBottom<graceMargin&&spaceToTop>=selfRect.height){selector='top';}else if(deltaToRight<graceMargin&&deltaToLeft>=selfRect.width){selector='left';}}else if(classList.contains('mud-popover-top-center')){if(deltaBottom<graceMargin&&spaceToTop>=selfRect.height){selector='top';}}else if(classList.contains('mud-popover-top-right')){if(deltaBottom<graceMargin&&deltaToLeft<graceMargin&&spaceToTop>=selfRect.height&&deltaToRight>=selfRect.width){selector='top-and-right';}else if(deltaBottom<graceMargin&&spaceToTop>=selfRect.height){selector='top';}else if(deltaToLeft<graceMargin&&deltaToRight>=selfRect.width){selector='right';}}
else if(classList.contains('mud-popover-center-left')){if(deltaToRight<graceMargin&&deltaToLeft>=selfRect.width){selector='left';}}
else if(classList.contains('mud-popover-center-right')){if(deltaToLeft<graceMargin&&deltaToRight>=selfRect.width){selector='right';}}
else if(classList.contains('mud-popover-bottom-left')){if(deltaTop<graceMargin&&deltaToRight<graceMargin&&deltaBottom>=0&&deltaToLeft>=selfRect.width){selector='bottom-and-left';}else if(deltaTop<graceMargin&&deltaBottom>=0){selector='bottom';}else if(deltaToRight<graceMargin&&deltaToLeft>=selfRect.width){selector='left';}}else if(classList.contains('mud-popover-bottom-center')){if(deltaTop<graceMargin&&deltaBottom>=0){selector='bottom';}}else if(classList.contains('mud-popover-bottom-right')){if(deltaTop<graceMargin&&deltaToLeft<graceMargin&&deltaBottom>=0&&deltaToRight>=selfRect.width){selector='bottom-and-right';}else if(deltaTop<graceMargin&&deltaBottom>=0){selector='bottom';}else if(deltaToLeft<graceMargin&&deltaToRight>=selfRect.width){selector='right';}}}
if(selector&&selector!='none'){const newPosition=window.mudpopoverHelper.getPositionForFlippedPopver(classListArray,selector,boundingRect,selfRect);left=newPosition.left;top=newPosition.top;offsetX=newPosition.offsetX;offsetY=newPosition.offsetY;popoverContentNode.setAttribute('data-mudpopover-flip','flipped');}
else{if(left+offsetX<0&&Math.abs(left+offsetX)<selfRect.width){left=Math.max(0,left+offsetX);offsetX=0;}
if(top+offsetY<appBarOffset&&appBarElements.length>0){this.updatePopoverZIndex(popoverContentNode,appBarElements[0]);}
if(top+offsetY<0&&Math.abs(top+offsetY)<selfRect.height){top=Math.max(0,top+offsetY);offsetY=0;}
const list=popoverContentNode.querySelector('.mud-list');const listPadding=24;const listMaxHeight=(window.innerHeight-top-offsetY);if(list&&list.offsetHeight>listMaxHeight){list.style.maxHeight=(listMaxHeight-listPadding)+'px';}
popoverContentNode.removeAttribute('data-mudpopover-flip');}
if(classList.contains('mud-popover-overflow-flip-onopen')){if(!popoverContentNode.mudPopoverFliped){popoverContentNode.mudPopoverFliped=selector||'none';}}}
if(window.getComputedStyle(popoverNode).position=='fixed'){popoverContentNode.style['position']='fixed';}
else if(!classList.contains('mud-popover-fixed')){offsetX+=window.scrollX;offsetY+=window.scrollY}
if(classList.contains('mud-popover-position-override')){offsetX=0;offsetY=0;}
popoverContentNode.style['left']=(left+offsetX)+'px';popoverContentNode.style['top']=(top+offsetY)+'px';this.updatePopoverZIndex(popoverContentNode,popoverNode.parentNode);if(window.getComputedStyle(popoverNode).getPropertyValue('z-index')!='auto'){popoverContentNode.style['z-index']=Math.max(window.getComputedStyle(popoverNode).getPropertyValue('z-index'),popoverContentNode.style['z-index']);popoverContentNode.skipZIndex=true;}}
else{}},popoverScrollListener:function(node){let currentNode=node.parentNode;while(currentNode){const isScrollable=(currentNode.scrollHeight>currentNode.clientHeight)||(currentNode.scrollWidth>currentNode.clientWidth);if(isScrollable){currentNode.addEventListener('scroll',()=>{window.mudpopoverHelper.placePopoverByClassSelector('mud-popover-fixed');window.mudpopoverHelper.placePopoverByClassSelector('mud-popover-overflow-flip-always');});}
if(currentNode.tagName==="BODY"){break;}
currentNode=currentNode.parentNode;}},placePopoverByClassSelector:function(classSelector=null){var items=window.mudPopover.getAllObservedContainers();for(let i=0;i<items.length;i++){const popoverNode=document.getElementById('popover-'+items[i]);window.mudpopoverHelper.placePopover(popoverNode,classSelector);}},placePopoverByNode:function(target){const id=target.id.substr(15);const popoverNode=document.getElementById('popover-'+id);window.mudpopoverHelper.placePopover(popoverNode);},countProviders:function(){return document.querySelectorAll(".mud-popover-provider").length;},updatePopoverOverlay:function(popoverContentNode){if(popoverContentNode.classList.contains("mud-tooltip")){return;}
const provider=popoverContentNode.closest('.mud-popover-provider');if(provider&&popoverContentNode.classList.contains("mud-popover")){const overlay=provider.querySelector('.mud-overlay');if(overlay&&!overlay.classList.contains('mud-skip-overlay-positioning')){if(popoverContentNode&&overlay.style['z-index']!==popoverContentNode.style['z-index']){overlay.style['z-index']=popoverContentNode.style['z-index'];}}}},updatePopoverZIndex:function(popoverContentNode,parentNode){const parentPopover=parentNode.closest('.mud-popover');const parentOfPopover=popoverContentNode.parentNode;let newZIndex=window.mudpopoverHelper.basePopoverZIndex+1;const origZIndex=parseInt(popoverContentNode.style['z-index'])||1;const contentZIndex=popoverContentNode.style['z-index'];if(parentPopover){const computedStyle=window.getComputedStyle(parentPopover);const parentZIndexValue=computedStyle.getPropertyValue('z-index');if(parentZIndexValue!=='auto'){newZIndex=parseInt(parentZIndexValue)+1;}
popoverContentNode.style['z-index']=newZIndex;}
else if(parentOfPopover){const computedStyle=window.getComputedStyle(parentOfPopover);const tooltipZIndexValue=computedStyle.getPropertyValue('z-index');if(tooltipZIndexValue!=='auto'){newZIndex=parseInt(tooltipZIndexValue)+1;}
popoverContentNode.style['z-index']=Math.max(newZIndex,window.mudpopoverHelper.baseTooltipZIndex+1,origZIndex);}
else if(parentNode&&parentNode.classList.contains("mud-tooltip-root")){const computedStyle=window.getComputedStyle(parentNode);const tooltipZIndexValue=computedStyle.getPropertyValue('z-index');if(tooltipZIndexValue!=='auto'){newZIndex=parseInt(tooltipZIndexValue)+1;}
popoverContentNode.style['z-index']=Math.max(newZIndex,window.mudpopoverHelper.baseTooltipZIndex+1);}
else if(parentNode&&parentNode.classList.contains("mud-appbar")){const computedStyle=window.getComputedStyle(parentNode);const appBarZIndexValue=computedStyle.getPropertyValue('z-index');if(appBarZIndexValue!=='auto'){newZIndex=parseInt(appBarZIndexValue)+1;}
popoverContentNode.style['z-index']=newZIndex;}
else if(!contentZIndex||parseInt(contentZIndex)<1){popoverContentNode.style['z-index']=newZIndex;}},}
class MudPopover{constructor(){this.map={};this.contentObserver=null;this.mainContainerClass=null;}
callback(id,mutationsList,observer){for(const mutation of mutationsList){if(mutation.type==='attributes'){const target=mutation.target
if(mutation.attributeName=='class'){if(target.classList.contains('mud-popover-overflow-flip-onopen')&&target.classList.contains('mud-popover-open')==false){target.mudPopoverFliped=null;target.removeAttribute('data-mudpopover-flip');}
window.mudpopoverHelper.placePopoverByNode(target);}
else if(mutation.attributeName=='data-ticks'){const tickAttribute=target.getAttribute('data-ticks');const tickValues=[];let max=-1;if(parent&&parent.children){for(let i=0;i<parent.children.length;i++){const childNode=parent.children[i];const tickValue=parseInt(childNode.getAttribute('data-ticks'));if(tickValue==0){continue;}
if(tickValues.indexOf(tickValue)>=0){continue;}
tickValues.push(tickValue);if(tickValue>max){max=tickValue;}}}
let highestTickItem=null;let highestTickValue=-1;for(const mapItem of Object.values(this.map)){const popoverContentNode=mapItem.popoverContentNode;if(popoverContentNode){const tickValue=Number(popoverContentNode.getAttribute('data-ticks'));if(tickValue>highestTickValue){highestTickValue=tickValue;highestTickItem=popoverContentNode;}}}
if(highestTickItem){window.mudpopoverHelper.updatePopoverOverlay(highestTickItem);}
if(tickValues.length==0){continue;}
const sortedTickValues=tickValues.sort((x,y)=>x-y);continue;for(let i=0;i<parent.children.length;i++){const childNode=parent.children[i];const tickValue=parseInt(childNode.getAttribute('data-ticks'));if(tickValue==0){continue;}
if(childNode.skipZIndex==true){continue;}
const newIndex=window.mudpopoverHelper.basePopoverZIndex+sortedTickValues.indexOf(tickValue)+3;childNode.style['z-index']=newIndex;}}}}}
initialize(containerClass,flipMargin){const mainContent=document.getElementsByClassName(containerClass);if(mainContent.length==0){return;}
if(flipMargin){window.mudpopoverHelper.flipMargin=flipMargin;}
this.mainContainerClass=containerClass;if(!mainContent[0].mudPopoverMark){mainContent[0].mudPopoverMark="mudded";if(this.contentObserver!=null){this.contentObserver.disconnect();this.contentObserver=null;}
this.contentObserver=new ResizeObserver(entries=>{window.mudpopoverHelper.placePopoverByClassSelector();});this.contentObserver.observe(mainContent[0]);}}
connect(id){this.initialize(this.mainContainerClass);const popoverNode=document.getElementById('popover-'+id);mudpopoverHelper.popoverScrollListener(popoverNode);const popoverContentNode=document.getElementById('popovercontent-'+id);if(popoverNode&&popoverNode.parentNode&&popoverContentNode){window.mudpopoverHelper.placePopover(popoverNode);const config={attributeFilter:['class','data-ticks']};const observer=new MutationObserver(this.callback.bind(this,id));observer.observe(popoverContentNode,config);const resizeObserver=new ResizeObserver(entries=>{for(let entry of entries){const target=entry.target;for(const childNode of target.childNodes){if(childNode.id&&childNode.id.startsWith('popover-')){window.mudpopoverHelper.placePopover(childNode);}}}});resizeObserver.observe(popoverNode.parentNode);const contentNodeObserver=new ResizeObserver(entries=>{for(let entry of entries){const target=entry.target;if(target)
window.mudpopoverHelper.placePopoverByNode(target);}});contentNodeObserver.observe(popoverContentNode);this.map[id]={popoverContentNode:popoverContentNode,mutationObserver:observer,resizeObserver:resizeObserver,contentNodeObserver:contentNodeObserver};}}
disconnect(id){if(this.map[id]){const item=this.map[id]
item.mutationObserver.disconnect();item.resizeObserver.disconnect();item.contentNodeObserver.disconnect();delete this.map[id];}}
dispose(){for(var i in this.map){disconnect(i);}
this.contentObserver.disconnect();this.contentObserver=null;}
getAllObservedContainers(){const result=[];for(var i in this.map){result.push(i);}
return result;}}
window.mudPopover=new MudPopover();const debouncedResize=window.mudpopoverHelper.debounce(()=>{window.mudpopoverHelper.placePopoverByClassSelector();},100);const throttledScroll=window.mudpopoverHelper.rafThrottle(()=>{window.mudpopoverHelper.placePopoverByClassSelector('mud-popover-fixed');window.mudpopoverHelper.placePopoverByClassSelector('mud-popover-overflow-flip-always');});window.addEventListener('resize',debouncedResize,{passive:true});window.addEventListener('scroll',throttledScroll,{passive:true});class MudScrollListener{constructor(){this.throttleScrollHandlerId=-1;this.handlerRef=null;}
listenForScroll(dotnetReference,selector){let element=selector?document.querySelector(selector):document;this.handlerRef=this.throttleScrollHandler.bind(this,dotnetReference);element.addEventListener('scroll',this.handlerRef,false);}
throttleScrollHandler(dotnetReference,event){clearTimeout(this.throttleScrollHandlerId);this.throttleScrollHandlerId=window.setTimeout(this.scrollHandler.bind(this,dotnetReference,event),100);}
scrollHandler(dotnetReference,event){try{let element=event.target;let scrollTop=element.scrollTop;let scrollHeight=element.scrollHeight;let scrollWidth=element.scrollWidth;let scrollLeft=element.scrollLeft;let nodeName=element.nodeName;let firstChild=element.firstElementChild;let firstChildBoundingClientRect=firstChild.getBoundingClientRect();dotnetReference.invokeMethodAsync('RaiseOnScroll',{firstChildBoundingClientRect,scrollLeft,scrollTop,scrollHeight,scrollWidth,nodeName,});}catch(error){console.log('[MudBlazor] Error in scrollHandler:',{error});}}
cancelListener(selector){let element=selector?document.querySelector(selector):document;element.removeEventListener('scroll',this.handlerRef);}};window.mudScrollListener=new MudScrollListener();window.mudInputAutoGrow={initAutoGrow:(elem,maxLines)=>{const compStyle=getComputedStyle(elem);const lineHeight=parseFloat(compStyle.getPropertyValue('line-height'));const paddingTop=parseFloat(compStyle.getPropertyValue('padding-top'));let maxHeight=0;elem.updateParameters=function(newMaxLines){if(newMaxLines>0){maxHeight=lineHeight*newMaxLines+paddingTop;}else{maxHeight=0;}}
elem.adjustAutoGrowHeight=function(didReflow=false){const scrollTops=[];let curElem=elem;while(curElem&&curElem.parentNode&&curElem.parentNode instanceof Element){if(curElem.parentNode.scrollTop){scrollTops.push([curElem.parentNode,curElem.parentNode.scrollTop]);}
curElem=curElem.parentNode;}
elem.style.height=0;if(didReflow){elem.style.textAlign=null;}
let minHeight=lineHeight*elem.rows+paddingTop;let newHeight=Math.max(minHeight,elem.scrollHeight);let initialOverflowY=elem.style.overflowY;if(maxHeight>0&&newHeight>maxHeight){elem.style.overflowY='auto';newHeight=maxHeight;}else{elem.style.overflowY='hidden';}
elem.style.height=newHeight+"px";scrollTops.forEach(([node,scrollTop])=>{node.style.scrollBehavior='auto';node.scrollTop=scrollTop;node.style.scrollBehavior=null;});if(!didReflow&&initialOverflowY!==elem.style.overflowY&&elem.style.overflowY==='hidden'){elem.style.textAlign='end';elem.adjustAutoGrowHeight(true);}}
elem.restoreToInitialState=function(){elem.removeEventListener('input',elem.adjustAutoGrowHeight);elem.style.overflowY=null;elem.style.height=null;}
elem.addEventListener('input',elem.adjustAutoGrowHeight);window.addEventListener('resize',elem.adjustAutoGrowHeight);elem.updateParameters(maxLines);elem.adjustAutoGrowHeight();},adjustHeight:(elem)=>{if(typeof elem.adjustAutoGrowHeight==='function'){elem.adjustAutoGrowHeight();}},updateParams:(elem,maxLines)=>{if(typeof elem.updateParameters==='function'){elem.updateParameters(maxLines);}
if(typeof elem.adjustAutoGrowHeight==='function'){elem.adjustAutoGrowHeight();}},destroy:(elem)=>{if(elem==null){return;}
window.removeEventListener('resize',elem.adjustAutoGrowHeight);if(typeof elem.restoreToInitialState==='function'){elem.restoreToInitialState();}}};class MudInput{resetValue(id){const input=document.getElementById(id);if(input){input.value='';}}}
window.mudInput=new MudInput();class MudFileUpload{openFilePicker(id){const element=document.getElementById(id);if(!element){return;}
try{element.showPicker();}catch(error){element.click();}}}
window.mudFileUpload=new MudFileUpload();class MudScrollSpy{constructor(){this.lastKnowElement=null;this.handlerRef=null;}
spying(dotnetReference,containerSelector,sectionClassSelector){this.lastKnowElement=null;this.handlerRef=this.handleScroll.bind(this,dotnetReference,containerSelector,sectionClassSelector);document.addEventListener('scroll',this.handlerRef,true);window.addEventListener('resize',this.handlerRef,true);}
handleScroll(dotnetReference,containerSelector,sectionClassSelector,event){const container=document.querySelector(containerSelector);if(container===null){return;}
const elements=document.getElementsByClassName(sectionClassSelector);if(elements.length===0){return;}
const containerTop=container.tagName==='HTML'?0:container.getBoundingClientRect().top;const containerHeight=container.clientHeight;const center=containerTop+containerHeight/2.0;let minDifference=Number.MAX_SAFE_INTEGER;let foundAbove=false;let elementId='';for(let i=0;i<elements.length;i++){const element=elements[i];const rect=element.getBoundingClientRect();const diff=Math.abs(rect.top-center);if(!foundAbove&&rect.top<center){foundAbove=true;minDifference=diff;elementId=element.id;continue;}
if(foundAbove&&rect.top>=center){continue;}
if(diff<minDifference){minDifference=diff;elementId=element.id;}}
if(elementId!==this.lastKnowElement){this.lastKnowElement=elementId;history.replaceState(null,'',window.location.pathname+"#"+elementId);dotnetReference.invokeMethodAsync('SectionChangeOccured',elementId);}}
activateSection(sectionId){const element=document.getElementById(sectionId);if(element){this.lastKnowElement=sectionId;history.replaceState(null,'',window.location.pathname+"#"+sectionId);}}
scrollToSection(sectionId){if(sectionId){let element=document.getElementById(sectionId);if(element){element.scrollIntoView({behavior:'smooth',block:'center',inline:'start'});}}
else{window.scrollTo({top:0,behavior:'smooth'});}}
unspy(){document.removeEventListener('scroll',this.handlerRef,true);window.removeEventListener('resize',this.handlerRef,true);}};window.mudScrollSpy=new MudScrollSpy();class MudResizeListener{constructor(id){this.logger=function(message){};this.options={};this.throttleResizeHandlerId=-1;this.dotnet=undefined;this.breakpoint=-1;this.id=id;this.handleResize=this.throttleResizeHandler.bind(this);}
listenForResize(dotnetRef,options){if(this.dotnet){this.options=options;return;}
this.options=options;this.dotnet=dotnetRef;this.logger=options.enableLogging?console.log:(message)=>{};this.logger(`[MudBlazor]Reporting resize events at rate of:${this.options.reportRate}ms`);window.addEventListener("resize",this.handleResize,false);if(!this.options.suppressInitEvent){this.resizeHandler();}
this.breakpoint=this.getBreakpoint(window.innerWidth);}
throttleResizeHandler(){clearTimeout(this.throttleResizeHandlerId);this.throttleResizeHandlerId=window.setTimeout(this.resizeHandler.bind(this),this.options.reportRate);}
resizeHandler(){if(this.options.notifyOnBreakpointOnly){let bp=this.getBreakpoint(window.innerWidth);if(bp==this.breakpoint){return;}
this.breakpoint=bp;}
try{if(this.id){this.dotnet.invokeMethodAsync('RaiseOnResized',{height:window.innerHeight,width:window.innerWidth},this.getBreakpoint(window.innerWidth),this.id);}
else{this.dotnet.invokeMethodAsync('RaiseOnResized',{height:window.innerHeight,width:window.innerWidth},this.getBreakpoint(window.innerWidth));}}catch(error){this.logger("[MudBlazor] Error in resizeHandler:",{error});}}
cancelListener(){this.dotnet=undefined;window.removeEventListener("resize",this.handleResize);}
matchMedia(query){let m=window.matchMedia(query).matches;return m;}
getBrowserWindowSize(){return{height:window.innerHeight,width:window.innerWidth};}
getBreakpoint(width){if(width>=this.options.breakpointDefinitions["Xxl"])
return 5;else if(width>=this.options.breakpointDefinitions["Xl"])
return 4;else if(width>=this.options.breakpointDefinitions["Lg"])
return 3;else if(width>=this.options.breakpointDefinitions["Md"])
return 2;else if(width>=this.options.breakpointDefinitions["Sm"])
return 1;else
return 0;}};window.mudResizeListener=new MudResizeListener();window.mudResizeListenerFactory={mapping:{},listenForResize:(dotnetRef,options,id)=>{var map=window.mudResizeListenerFactory.mapping;if(map[id]){return;}
var listener=new MudResizeListener(id);listener.listenForResize(dotnetRef,options);map[id]=listener;},cancelListener:(id)=>{var map=window.mudResizeListenerFactory.mapping;if(!map[id]){return;}
var listener=map[id];listener.cancelListener();delete map[id];},cancelListeners:(ids)=>{for(let i=0;i<ids.length;i++){window.mudResizeListenerFactory.cancelListener(ids[i]);}},dispose(){var map=window.mudResizeListenerFactory.mapping;for(var id in map){window.mudResizeListenerFactory.cancelListener(id);}}}
function setRippleOffset(event,target){const rect=target.getBoundingClientRect();const x=event.clientX-rect.left-rect.width/2;const y=event.clientY-rect.top-rect.height/2;target.style.setProperty("--mud-ripple-offset-x",`${x}px`);target.style.setProperty("--mud-ripple-offset-y",`${y}px`);}
document.addEventListener("click",function(event){const target=event.target.closest(".mud-ripple");if(target){setRippleOffset(event,target);}});class MudKeyInterceptorFactory{connect(dotNetRef,elementId,options){if(!elementId)
throw"elementId: expected element id!";var element=document.getElementById(elementId);if(!element)
throw"no element found for id: "+elementId;if(!element.mudKeyInterceptor)
element.mudKeyInterceptor=new MudKeyInterceptor(dotNetRef,options);element.mudKeyInterceptor.connect(element);}
updatekey(elementId,option){var element=document.getElementById(elementId);if(!element||!element.mudKeyInterceptor)
return;element.mudKeyInterceptor.updatekey(option);}
disconnect(elementId){var element=document.getElementById(elementId);if(!element||!element.mudKeyInterceptor)
return;element.mudKeyInterceptor.disconnect();}}
window.mudKeyInterceptor=new MudKeyInterceptorFactory();class MudKeyInterceptor{constructor(dotNetRef,options){this._dotNetRef=dotNetRef;this._options=options;this.logger=options.enableLogging?console.log:(message)=>{};this.logger('[MudBlazor | KeyInterceptor] Interceptor initialized',{options});}
connect(element){if(!this._options)
return;if(!this._options.keys)
throw"_options.keys: array of KeyOptions expected";if(!this._options.targetClass)
throw"_options.targetClass: css class name expected";if(this._observer){return;}
var targetClass=this._options.targetClass;this.logger('[MudBlazor | KeyInterceptor] Start observing DOM of element for changes to child with class ',{element,targetClass});this._element=element;this._observer=new MutationObserver(this.onDomChanged);this._observer.mudKeyInterceptor=this;this._observer.observe(this._element,{attributes:false,childList:true,subtree:true});this._observedChildren=[];this._keyOptions={};this._regexOptions=[];for(const keyOption of this._options.keys){if(!keyOption||!keyOption.key){this.logger('[MudBlazor | KeyInterceptor] got invalid key options: ',keyOption);continue;}
this.setKeyOption(keyOption)}
this.logger('[MudBlazor | KeyInterceptor] key options: ',this._keyOptions);if(this._regexOptions.size>0)
this.logger('[MudBlazor | KeyInterceptor] regex options: ',this._regexOptions);for(const child of this._element.getElementsByClassName(targetClass)){this.attachHandlers(child);}}
setKeyOption(keyOption){if(keyOption.key.length>2&&keyOption.key.startsWith('/')&&keyOption.key.endsWith('/')){keyOption.regex=new RegExp(keyOption.key.substring(1,keyOption.key.length-1));this._regexOptions.push(keyOption);}
else
this._keyOptions[keyOption.key.toLowerCase()]=keyOption;var whitespace=new RegExp("\\s","g");keyOption.preventDown=(keyOption.preventDown||"none").replace(whitespace,"").toLowerCase();keyOption.preventUp=(keyOption.preventUp||"none").replace(whitespace,"").toLowerCase();keyOption.stopDown=(keyOption.stopDown||"none").replace(whitespace,"").toLowerCase();keyOption.stopUp=(keyOption.stopUp||"none").replace(whitespace,"").toLowerCase();}
updatekey(updatedOption){var option=this._keyOptions[updatedOption.key.toLowerCase()];option||this.logger('[MudBlazor | KeyInterceptor] updating option failed: key not registered');this.setKeyOption(updatedOption);this.logger('[MudBlazor | KeyInterceptor] updated option ',{option,updatedOption});}
disconnect(){if(!this._observer)
return;this.logger('[MudBlazor | KeyInterceptor] disconnect mutation observer and event handlers');this._observer.disconnect();this._observer=null;for(const child of this._observedChildren)
this.detachHandlers(child);}
attachHandlers(child){this.logger('[MudBlazor | KeyInterceptor] attaching handlers ',{child});if(this._observedChildren.indexOf(child)>-1){return;}
child.mudKeyInterceptor=this;child.addEventListener('keydown',this.onKeyDown);child.addEventListener('keyup',this.onKeyUp);this._observedChildren.push(child);}
detachHandlers(child){this.logger('[MudBlazor | KeyInterceptor] detaching handlers ',{child});child.removeEventListener('keydown',this.onKeyDown);child.removeEventListener('keyup',this.onKeyUp);this._observedChildren=this._observedChildren.filter(x=>x!==child);}
onDomChanged(mutationsList,observer){var self=this.mudKeyInterceptor;var targetClass=self._options.targetClass;for(const mutation of mutationsList){for(const element of mutation.addedNodes){if(element.classList&&element.classList.contains(targetClass))
self.attachHandlers(element);}
for(const element of mutation.removedNodes){if(element.classList&&element.classList.contains(targetClass))
self.detachHandlers(element);}}}
matchesKeyCombination(option,args){if(!option||option==="none")
return false;if(option==="any")
return true;var shift=args.shiftKey;var ctrl=args.ctrlKey;var alt=args.altKey;var meta=args.metaKey;var any=shift||ctrl||alt||meta;if(any&&option==="key+any")
return true;if(!any&&option.includes("key+none"))
return true;if(!any)
return false;var combi=`key${shift?"+shift":""}${ctrl?"+ctrl":""}${alt?"+alt":""}${meta?"+meta":""}`;return option.includes(combi);}
onKeyDown(args){var self=this.mudKeyInterceptor;if(!args.key){self.logger('[MudBlazor | KeyInterceptor] key is undefined',args);return;}
var key=args.key.toLowerCase();self.logger('[MudBlazor | KeyInterceptor] down "'+key+'"',args);var invoke=false;if(self._keyOptions.hasOwnProperty(key)){var keyOptions=self._keyOptions[key];self.logger('[MudBlazor | KeyInterceptor] options for "'+key+'"',keyOptions);self.processKeyDown(args,keyOptions);if(keyOptions.subscribeDown)
invoke=true;}
for(const keyOptions of self._regexOptions){if(keyOptions.regex.test(key)){self.logger('[MudBlazor | KeyInterceptor] regex options for "'+key+'"',keyOptions);self.processKeyDown(args,keyOptions);if(keyOptions.subscribeDown)
invoke=true;}}
if(invoke){var eventArgs=self.toKeyboardEventArgs(args);eventArgs.Type="keydown";self._dotNetRef.invokeMethodAsync('OnKeyDown',self._element.id,eventArgs);}}
processKeyDown(args,keyOptions){if(this.matchesKeyCombination(keyOptions.preventDown,args))
args.preventDefault();if(this.matchesKeyCombination(keyOptions.stopDown,args))
args.stopPropagation();}
onKeyUp(args){var self=this.mudKeyInterceptor;if(!args.key){self.logger('[MudBlazor | KeyInterceptor] key is undefined',args);return;}
var key=args.key.toLowerCase();self.logger('[MudBlazor | KeyInterceptor] up "'+key+'"',args);var invoke=false;if(self._keyOptions.hasOwnProperty(key)){var keyOptions=self._keyOptions[key];self.processKeyUp(args,keyOptions);if(keyOptions.subscribeUp)
invoke=true;}
for(const keyOptions of self._regexOptions){if(keyOptions.regex.test(key)){self.processKeyUp(args,keyOptions);if(keyOptions.subscribeUp)
invoke=true;}}
if(invoke){var eventArgs=self.toKeyboardEventArgs(args);eventArgs.Type="keyup";self._dotNetRef.invokeMethodAsync('OnKeyUp',self._element.id,eventArgs);}}
processKeyUp(args,keyOptions){if(this.matchesKeyCombination(keyOptions.preventUp,args))
args.preventDefault();if(this.matchesKeyCombination(keyOptions.stopUp,args))
args.stopPropagation();}
toKeyboardEventArgs(args){return{Key:args.key,Code:args.code,Location:args.location,Repeat:args.repeat,CtrlKey:args.ctrlKey,ShiftKey:args.shiftKey,AltKey:args.altKey,MetaKey:args.metaKey};}}