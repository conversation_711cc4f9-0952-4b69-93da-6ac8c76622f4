using Harmoni360.Domain.Entities;
using Harmoni360.Domain.Interfaces;
using Harmoni360.Infrastructure.Persistence;
using Harmoni360.Infrastructure.Persistence.Repositories;
using Microsoft.EntityFrameworkCore;

namespace Harmoni360.Infrastructure.Repositories;

public class WorkflowBookmarkRepository : Repository<WorkflowBookmark>, IWorkflowBookmarkRepository
{
    public WorkflowBookmarkRepository(ApplicationDbContext context) : base(context)
    {
    }

    public async Task<IEnumerable<WorkflowBookmark>> GetByWorkflowExecutionIdAsync(
        int workflowExecutionId, 
        CancellationToken cancellationToken = default)
    {
        return await _context.WorkflowBookmarks
            .Where(b => b.WorkflowExecutionId == workflowExecutionId)
            .OrderByDescending(b => b.CreatedAt)
            .ToListAsync(cancellationToken);
    }

    public async Task<IEnumerable<WorkflowBookmark>> GetActiveBookmarksAsync(
        CancellationToken cancellationToken = default)
    {
        return await _context.WorkflowBookmarks
            .Where(b => b.IsActive)
            .OrderBy(b => b.Priority)
            .ThenBy(b => b.CreatedAt)
            .ToListAsync(cancellationToken);
    }

    public async Task<IEnumerable<WorkflowBookmark>> GetByActivityIdAsync(
        string activityId, 
        CancellationToken cancellationToken = default)
    {
        return await _context.WorkflowBookmarks
            .Where(b => b.ActivityId == activityId)
            .OrderByDescending(b => b.CreatedAt)
            .ToListAsync(cancellationToken);
    }

    public async Task<IEnumerable<WorkflowBookmark>> GetByBookmarkNameAsync(
        string bookmarkName, 
        CancellationToken cancellationToken = default)
    {
        return await _context.WorkflowBookmarks
            .Where(b => b.BookmarkName == bookmarkName)
            .OrderByDescending(b => b.CreatedAt)
            .ToListAsync(cancellationToken);
    }

    public async Task<WorkflowBookmark?> GetActiveBookmarkAsync(
        int workflowExecutionId, 
        string activityId, 
        string bookmarkName, 
        CancellationToken cancellationToken = default)
    {
        return await _context.WorkflowBookmarks
            .FirstOrDefaultAsync(b => 
                b.WorkflowExecutionId == workflowExecutionId &&
                b.ActivityId == activityId &&
                b.BookmarkName == bookmarkName &&
                b.IsActive, 
                cancellationToken);
    }

    public async Task<IEnumerable<WorkflowBookmark>> GetExpiredBookmarksAsync(
        DateTime cutoffDate, 
        CancellationToken cancellationToken = default)
    {
        return await _context.WorkflowBookmarks
            .Where(b => b.IsActive && b.CreatedAt < cutoffDate)
            .ToListAsync(cancellationToken);
    }

    public async Task<int> GetActiveBookmarkCountAsync(
        int? workflowExecutionId = null, 
        CancellationToken cancellationToken = default)
    {
        var query = _context.WorkflowBookmarks.Where(b => b.IsActive);

        if (workflowExecutionId.HasValue)
        {
            query = query.Where(b => b.WorkflowExecutionId == workflowExecutionId.Value);
        }

        return await query.CountAsync(cancellationToken);
    }

    public async Task ResumeBookmarkAsync(
        int bookmarkId, 
        string resumedBy, 
        CancellationToken cancellationToken = default)
    {
        var bookmark = await _context.WorkflowBookmarks
            .FirstOrDefaultAsync(b => b.Id == bookmarkId && b.IsActive, cancellationToken);

        if (bookmark != null)
        {
            bookmark.Resume(resumedBy);
            await _context.SaveChangesAsync(cancellationToken);
        }
    }

    public async Task ResumeBookmarksAsync(
        IEnumerable<int> bookmarkIds, 
        string resumedBy, 
        CancellationToken cancellationToken = default)
    {
        var bookmarks = await _context.WorkflowBookmarks
            .Where(b => bookmarkIds.Contains(b.Id) && b.IsActive)
            .ToListAsync(cancellationToken);

        foreach (var bookmark in bookmarks)
        {
            bookmark.Resume(resumedBy);
        }

        await _context.SaveChangesAsync(cancellationToken);
    }

    public async Task<IEnumerable<WorkflowBookmark>> GetBookmarksByTagAsync(
        string tag, 
        CancellationToken cancellationToken = default)
    {
        return await _context.WorkflowBookmarks
            .Where(b => b.Tag != null && b.Tag.Contains(tag))
            .OrderByDescending(b => b.CreatedAt)
            .ToListAsync(cancellationToken);
    }
}