using Elsa.Extensions;
using Elsa.Workflows;
using Elsa.Workflows.Attributes;
using Elsa.Workflows.Models;
using Harmoni360.Application.Common.Interfaces;
using Harmoni360.Domain.Interfaces;
using Microsoft.Extensions.Logging;
using System.ComponentModel;
using System.Text.Json.Serialization;

namespace Harmoni360.Application.Features.WorkflowManagement.IncidentWorkflow.Activities;

/// <summary>
/// Base class for all incident management workflow activities
/// </summary>
[Category("Incident Management")]
public abstract class IncidentActivityBase : CodeActivity
{
    /// <summary>
    /// Gets services from the activity execution context
    /// </summary>
    protected ILogger GetLogger(ActivityExecutionContext context) => context.GetRequiredService<ILogger<IncidentActivityBase>>();
    protected IIncidentRepository GetIncidentRepository(ActivityExecutionContext context) => context.GetRequiredService<IIncidentRepository>();
    protected ICurrentUserService GetCurrentUserService(ActivityExecutionContext context) => context.GetRequiredService<ICurrentUserService>();
    
    /// <summary>
    /// Gets the current user identifier
    /// </summary>
    protected string GetCurrentUserId(ActivityExecutionContext context)
    {
        var currentUserService = GetCurrentUserService(context);
        return currentUserService.UserIdAsString ?? "system";
    }
    
    /// <summary>
    /// Gets the current user name
    /// </summary>
    protected string GetCurrentUserName(ActivityExecutionContext context)
    {
        var currentUserService = GetCurrentUserService(context);
        return currentUserService.UserName ?? "System User";
    }
    
    /// <summary>
    /// Logs activity execution with context
    /// </summary>
    protected void LogActivity(ActivityExecutionContext context, string activityName, string message, params object[] args)
    {
        var logger = GetLogger(context);
        var enrichedArgs = new object[] { activityName, GetCurrentUserId(context) }.Concat(args).ToArray();
        logger.LogInformation("[{ActivityName}] User: {UserId} - " + message, enrichedArgs);
    }
    
    /// <summary>
    /// Logs activity errors with context
    /// </summary>
    protected void LogActivityError(ActivityExecutionContext context, string activityName, Exception ex, string message, params object[] args)
    {
        var logger = GetLogger(context);
        var enrichedArgs = new object[] { activityName, GetCurrentUserId(context) }.Concat(args).ToArray();
        logger.LogError(ex, "[{ActivityName}] User: {UserId} - " + message, enrichedArgs);
    }
}

/// <summary>
/// Base class for activities that output a result
/// </summary>
[Category("Incident Management")]
public abstract class IncidentActivityBase<TOutput> : CodeActivity<TOutput>
{
    /// <summary>
    /// Gets services from the activity execution context
    /// </summary>
    protected ILogger GetLogger(ActivityExecutionContext context) => context.GetRequiredService<ILogger<IncidentActivityBase<TOutput>>>();
    protected IIncidentRepository GetIncidentRepository(ActivityExecutionContext context) => context.GetRequiredService<IIncidentRepository>();
    protected ICurrentUserService GetCurrentUserService(ActivityExecutionContext context) => context.GetRequiredService<ICurrentUserService>();
    
    /// <summary>
    /// Gets the current user identifier
    /// </summary>
    protected string GetCurrentUserId(ActivityExecutionContext context)
    {
        var currentUserService = GetCurrentUserService(context);
        return currentUserService.UserIdAsString ?? "system";
    }
    
    /// <summary>
    /// Gets the current user name
    /// </summary>
    protected string GetCurrentUserName(ActivityExecutionContext context)
    {
        var currentUserService = GetCurrentUserService(context);
        return currentUserService.UserName ?? "System User";
    }
    
    /// <summary>
    /// Logs activity execution with context
    /// </summary>
    protected void LogActivity(ActivityExecutionContext context, string activityName, string message, params object[] args)
    {
        var logger = GetLogger(context);
        var enrichedArgs = new object[] { activityName, GetCurrentUserId(context) }.Concat(args).ToArray();
        logger.LogInformation("[{ActivityName}] User: {UserId} - " + message, enrichedArgs);
    }
    
    /// <summary>
    /// Logs activity errors with context
    /// </summary>
    protected void LogActivityError(ActivityExecutionContext context, string activityName, Exception ex, string message, params object[] args)
    {
        var logger = GetLogger(context);
        var enrichedArgs = new object[] { activityName, GetCurrentUserId(context) }.Concat(args).ToArray();
        logger.LogError(ex, "[{ActivityName}] User: {UserId} - " + message, enrichedArgs);
    }
}