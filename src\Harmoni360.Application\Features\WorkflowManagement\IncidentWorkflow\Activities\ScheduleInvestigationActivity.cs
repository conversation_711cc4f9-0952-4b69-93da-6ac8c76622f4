using Elsa.Workflows;
using Elsa.Workflows.Attributes;
using Harmoni360.Application.Common.Interfaces;
using Harmoni360.Application.Features.WorkflowManagement.Common;
using Harmoni360.Application.Features.WorkflowManagement.IncidentWorkflow.Models;
using Harmoni360.Domain.Interfaces;
using Microsoft.Extensions.Logging;
using System.ComponentModel;
using Elsa.Extensions;
using Elsa.Workflows.Models;

namespace Harmoni360.Application.Features.WorkflowManagement.IncidentWorkflow.Activities;

/// <summary>
/// Activity that schedules HSE investigation for the incident
/// </summary>
[Activity("Incident Management", "Schedule Investigation", "Schedules HSE investigation team and sends calendar invites")]
public class ScheduleInvestigationActivity : IncidentActivityBase
{
    private readonly INotificationService _notificationService;
    
    public ScheduleInvestigationActivity(
        ILogger<ScheduleInvestigationActivity> logger,
        IIncidentRepository incidentRepository,
        ICurrentUserService currentUserService,
        INotificationService notificationService)
        : base(logger, incidentRepository, currentUserService)
    {
        _notificationService = notificationService;
    }
    
    /// <summary>
    /// The workflow context containing incident information
    /// </summary>
    [Input(
        Description = "The incident workflow context",
        DisplayName = "Incident Context"
    )]
    public Input<IncidentWorkflowContext> Context { get; set; } = default!;
    
    protected override async ValueTask ExecuteAsync(ActivityExecutionContext context)
    {
        const string activityName = nameof(ScheduleInvestigationActivity);
        LogActivity(activityName, "Starting investigation scheduling");
        
        try
        {
            var workflowContext = Context.Get(context);
            
            // Parse incident ID
            if (!int.TryParse(workflowContext.IncidentId, out var incidentId))
            {
                LogActivityError(activityName, "Invalid incident ID: {IncidentId}", workflowContext.IncidentId);
                return;
            }
            
            // Get incident from database
            var incident = await IncidentRepository.GetByIdAsync(incidentId);
            if (incident == null)
            {
                LogActivityError(activityName, "Incident not found: {IncidentId}", incidentId);
                return;
            }
            
            // Calculate investigation schedule based on severity
            var investigationDate = CalculateInvestigationDate(incident.Severity);
            
            // Create investigation schedule notification
            var notificationTitle = $"HSE Investigation Scheduled: {incident.Title}";
            var notificationMessage = $@"
An HSE investigation has been scheduled for the following incident:

Incident Details:
- ID: {incident.Id}
- Number: {incident.IncidentNumber ?? "TBD"}
- Type: {incident.Type}
- Severity: {incident.Severity}
- Location: {incident.Location}
- Scheduled Investigation Date: {investigationDate:yyyy-MM-dd HH:mm}

Investigation Team Requirements:
- HSE Lead Investigator (Required)
- Department Representative (Required)
- Subject Matter Expert (If applicable)
- Witness Interviews (If applicable)

Investigation Methodology:
- HFACS (Human Factor Analysis Classification System) or
- ICAM (Incident Causative Analysis Method)

Please confirm your attendance and prepare necessary documentation.
";
            
            // Notify HSE team about investigation scheduling
            await _notificationService.NotifyRoleAsync(
                "HSEManager",
                notificationTitle,
                notificationMessage,
                cancellationToken: default);
            
            // Update workflow context with investigation details
            workflowContext.InvestigationScheduledDate = investigationDate;
            workflowContext.InvestigationStatus = "Scheduled";
            
            LogActivity(activityName, "Successfully scheduled investigation for incident {IncidentId} on {InvestigationDate}", 
                incidentId, investigationDate);
        }
        catch (Exception ex)
        {
            LogActivityError(activityName, ex, "Failed to schedule investigation");
        }
    }
    
    private static DateTime CalculateInvestigationDate(Domain.Entities.IncidentSeverity severity)
    {
        // Investigation scheduling based on severity
        return severity switch
        {
            Domain.Entities.IncidentSeverity.Fatal => DateTime.UtcNow.AddHours(2), // Immediate for fatalities
            Domain.Entities.IncidentSeverity.Major => DateTime.UtcNow.AddHours(24), // Within 24 hours for major
            Domain.Entities.IncidentSeverity.Minor => DateTime.UtcNow.AddDays(3), // Within 3 days for minor
            _ => DateTime.UtcNow.AddDays(2) // Default 2 days
        };
    }
}