using Harmoni360.Domain.Entities;
using Harmoni360.Domain.Enums;
using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;

namespace Harmoni360.Infrastructure.Persistence.Configurations;

public class WorkflowExecutionConfiguration : IEntityTypeConfiguration<WorkflowExecution>
{
    public void Configure(EntityTypeBuilder<WorkflowExecution> builder)
    {
        builder.ToTable("WorkflowExecutions");

        builder.HasKey(e => e.Id);

        builder.Property(e => e.Id)
            .ValueGeneratedOnAdd();

        builder.Property(e => e.WorkflowDefinitionId)
            .HasMaxLength(100)
            .IsRequired();

        builder.Property(e => e.WorkflowName)
            .HasMaxLength(200)
            .IsRequired();

        builder.Property(e => e.WorkflowVersion)
            .HasMaxLength(50)
            .IsRequired()
            .HasDefaultValue("1.0");

        builder.Property(e => e.InstanceId)
            .HasMaxLength(100)
            .IsRequired();

        builder.Property(e => e.Status)
            .HasConversion<int>()
            .IsRequired()
            .HasDefaultValue(WorkflowStatus.Idle);

        builder.Property(e => e.SubStatus)
            .HasConversion<int>()
            .IsRequired()
            .HasDefaultValue(WorkflowSubStatus.Executing);

        builder.Property(e => e.StartedAt)
            .IsRequired();

        builder.Property(e => e.CompletedAt)
            .IsRequired(false);

        builder.Property(e => e.FaultedAt)
            .IsRequired(false);

        builder.Property(e => e.CancelledAt)
            .IsRequired(false);

        builder.Property(e => e.FinishedAt)
            .IsRequired(false);

        builder.Property(e => e.CorrelationId)
            .HasMaxLength(100)
            .IsRequired(false);

        builder.Property(e => e.ContextType)
            .HasMaxLength(1000)
            .IsRequired(false);

        builder.Property(e => e.Name)
            .HasMaxLength(500)
            .IsRequired(false);

        builder.Property(e => e.Input)
            .HasColumnType("text")
            .IsRequired(false);

        builder.Property(e => e.Output)
            .HasColumnType("text")
            .IsRequired(false);

        builder.Property(e => e.FaultMessage)
            .HasMaxLength(2000)
            .IsRequired(false);

        builder.Property(e => e.Exception)
            .HasColumnType("text")
            .IsRequired(false);

        builder.Property(e => e.InitiatedBy)
            .HasMaxLength(100)
            .IsRequired();

        builder.Property(e => e.CurrentActivityId)
            .HasMaxLength(100)
            .IsRequired(false);

        builder.Property(e => e.CurrentActivityName)
            .HasMaxLength(500)
            .IsRequired(false);

        builder.Property(e => e.Variables)
            .HasColumnType("text")
            .IsRequired(false);

        builder.Property(e => e.Metadata)
            .HasColumnType("text")
            .IsRequired(false);

        builder.Property(e => e.Tags)
            .HasColumnType("text")
            .IsRequired(false);

        // Indexes
        builder.HasIndex(e => e.WorkflowDefinitionId)
            .HasDatabaseName("IX_WorkflowExecutions_WorkflowDefinitionId");

        builder.HasIndex(e => e.InstanceId)
            .IsUnique()
            .HasDatabaseName("IX_WorkflowExecutions_InstanceId");

        builder.HasIndex(e => e.Status)
            .HasDatabaseName("IX_WorkflowExecutions_Status");

        builder.HasIndex(e => e.CorrelationId)
            .HasDatabaseName("IX_WorkflowExecutions_CorrelationId");

        builder.HasIndex(e => e.InitiatedBy)
            .HasDatabaseName("IX_WorkflowExecutions_InitiatedBy");

        builder.HasIndex(e => e.StartedAt)
            .HasDatabaseName("IX_WorkflowExecutions_StartedAt");

        builder.HasIndex(e => new { e.WorkflowDefinitionId, e.Status })
            .HasDatabaseName("IX_WorkflowExecutions_WorkflowDefinitionId_Status");

        // Relationships
        builder.HasMany(e => e.ExecutionLogs)
            .WithOne(l => l.WorkflowExecution)
            .HasForeignKey(l => l.WorkflowExecutionId)
            .OnDelete(DeleteBehavior.Cascade);

        builder.HasMany(e => e.Bookmarks)
            .WithOne(b => b.WorkflowExecution)
            .HasForeignKey(b => b.WorkflowExecutionId)
            .OnDelete(DeleteBehavior.Cascade);

        builder.HasMany(e => e.ExecutionContexts)
            .WithOne(c => c.WorkflowExecution)
            .HasForeignKey(c => c.WorkflowExecutionId)
            .OnDelete(DeleteBehavior.Cascade);
    }
}