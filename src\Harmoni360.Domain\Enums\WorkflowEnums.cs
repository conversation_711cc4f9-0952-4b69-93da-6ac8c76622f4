namespace Harmoni360.Domain.Enums;

/// <summary>
/// Represents different types of workflow logs
/// </summary>
public enum WorkflowLogType
{
    WorkflowStarted = 1,
    WorkflowCompleted = 2,
    WorkflowFailed = 3,
    WorkflowCancelled = 4,
    WorkflowSuspended = 5,
    WorkflowResumed = 6,
    ActivityStarted = 10,
    ActivityCompleted = 11,
    ActivityFailed = 12,
    ActivitySkipped = 13,
    UserAction = 20,
    ApprovalAction = 21,
    DataAccess = 22,
    SecurityEvent = 30,
    PermissionCheck = 31,
    AuthenticationEvent = 32,
    PerformanceMetric = 40,
    ResourceUsage = 41,
    ExternalIntegration = 50,
    NotificationSent = 51
}

/// <summary>
/// Represents the severity levels for workflow log entries
/// </summary>
public enum WorkflowLogSeverity
{
    Debug = 0,
    Information = 1,
    Warning = 2,
    Error = 3,
    Critical = 4
}

/// <summary>
/// Represents the type of workflow bookmark (suspension point)
/// </summary>
public enum BookmarkType
{
    UserTask = 1,
    Timer = 2,
    ExternalEvent = 3,
    Approval = 4,
    HttpRequest = 5,
    Signal = 6,
    Custom = 99
}


/// <summary>
/// Represents different types of workflow triggers
/// </summary>
public enum WorkflowTriggerType
{
    Manual = 1,
    Timer = 2,
    Event = 3,
    HttpRequest = 4,
    Signal = 5,
    FileWatcher = 6,
    DatabaseChange = 7,
    ExternalSystem = 8
}

/// <summary>
/// Represents the execution context state
/// </summary>
public enum ExecutionContextStatus
{
    Active = 1,
    Suspended = 2,
    Completed = 3,
    Faulted = 4,
    Cancelled = 5
}